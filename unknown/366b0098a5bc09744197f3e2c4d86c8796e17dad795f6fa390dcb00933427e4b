﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class ProformaInvoiceDataFn
    {
        public List<ProformaInvoiceTableVm> GetAllProformaInvoice()
        {
            List<ProformaInvoiceTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProformaInvoiceTables
                       select new ProformaInvoiceTableVm
                       {
                           ProformaInvoiceId = a.ProformaInvoiceId,
                           CustomerId = a.CustomerId,
                           Gstn = a.Gstn,
                           ProformaInvoiceNumber = a.ProformaInvoiceNumber,
                           ProformaInvoiceDate = a.ProformaInvoiceDate,
                           ConsignorReference = a.ConsignorReference,
                           ReferenceType = a.ReferenceType,
                           BuyerReferenceNumber = a.BuyerReferenceNumber,
                           CountryOfOrigin = a.CountryOfOrigin,
                           CountryOfDestinaton = a.<PERSON>f<PERSON>est<PERSON>,
                           MaterialType = a.MaterialType,
                           Hsncode = a.Hsncode,
                           BankName = a.BankName,
                           BankBranch = a.BankBranch,
                           BankAccountNumber = a.BankAccountNumber,
                           Ifsccode = a.Ifsccode,
                           SwiftCode = a.SwiftCode,
                           BeneficiaryName = a.BeneficiaryName,
                           PortOfLoading = a.PortOfLoading,
                           PortOfDischarge = a.PortOfDischarge,
                           FinalDestination = a.FinalDestination,
                           ModeOfTransport = a.ModeOfTransport,
                           TermsCondition = a.TermsCondition,
                           Moq = a.Moq,
                           Moqtotal = a.Moqtotal,
                           Gst = a.Gst,
                           TotalPrice = a.TotalPrice,
                           Currency = a.Currency,
                           Discount = a.Discount,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           BankId = a.BankId,
                           Customer = (from c in db.CustomerMasters
                                       where c.CustomerId == a.CustomerId
                                       select new CustomerMasterVm
                                       {
                                           CustomerId = c.CustomerId,
                                           Address = c.Address,
                                           CustomerContactNumber = c.CustomerContactNumber,
                                           CustomerName = c.CustomerName,
                                           Email = c.Email,
                                           Gstnumber = c.Gstnumber,
                                           State = c.State,
                                           Country = c.Country
                                       }).FirstOrDefault(),
                           ProformaInvoiceItem = (from op in db.ProformaInvoiceItemTables
                                                  where op.ProformaInvoiceId == a.ProformaInvoiceId
                                                  from fc in db.SaleFormulationCodeMasters
                                                  where op.SaleFormulationCodeId == fc.SaleFormulationCodeId
                                                  select new ProformaInvoiceItemTableVm
                                                  {
                                                      ProformaInvoiceItemId = op.ProformaInvoiceItemId,
                                                      ProformaInvoiceId = op.ProformaInvoiceId,
                                                      SaleFormulationCodeId = op.SaleFormulationCodeId,
                                                      SaleFormulationCodeProductName = fc.SaleFormulationCode,
                                                      Quantity = op.Quantity,
                                                      Price = op.Price,
                                                      DescriptionOfGoods = op.DescriptionOfGoods,
                                                      ArticleName = op.ArticleName
                                                  }).ToList()
                       }).OrderByDescending(x => x.ProformaInvoiceId).ToList();
            }
            return res;
        }
        public ProformaInvoiceTableVm GetProformaInvoiceById(long proformaInvoiceId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var result = (from a in db.ProformaInvoiceTables
                              where a.ProformaInvoiceId == proformaInvoiceId
                              select new ProformaInvoiceTableVm
                              {
                                  ProformaInvoiceId = a.ProformaInvoiceId,
                                  CustomerId = a.CustomerId,
                                  Gstn = a.Gstn,
                                  ProformaInvoiceNumber = a.ProformaInvoiceNumber,
                                  ProformaInvoiceDate = a.ProformaInvoiceDate,
                                  ConsignorReference = a.ConsignorReference,
                                  ReferenceType = a.ReferenceType,
                                  BuyerReferenceNumber = a.BuyerReferenceNumber,
                                  CountryOfOrigin = a.CountryOfOrigin,
                                  CountryOfDestinaton = a.CountryOfDestinaton,
                                  MaterialType = a.MaterialType,
                                  Hsncode = a.Hsncode,
                                  BankName = a.BankName,
                                  BankBranch = a.BankBranch,
                                  BankAccountNumber = a.BankAccountNumber,
                                  Ifsccode = a.Ifsccode,
                                  SwiftCode = a.SwiftCode,
                                  BeneficiaryName = a.BeneficiaryName,
                                  PortOfLoading = a.PortOfLoading,
                                  PortOfDischarge = a.PortOfDischarge,
                                  FinalDestination = a.FinalDestination,
                                  ModeOfTransport = a.ModeOfTransport,
                                  TermsCondition = a.TermsCondition,
                                  Moq = a.Moq,
                                  Moqtotal = a.Moqtotal,
                                  Gst = a.Gst,
                                  TotalPrice = a.TotalPrice,
                                  Currency = a.Currency,
                                  Discount = a.Discount,
                                  AddedBy = a.AddedBy,
                                  AddedDate = a.AddedDate,
                                  BankId = a.BankId,
                                  Customer = (from c in db.CustomerMasters
                                              where c.CustomerId == a.CustomerId
                                              select new CustomerMasterVm
                                              {
                                                  CustomerId = c.CustomerId,
                                                  Address = c.Address,
                                                  CustomerContactNumber = c.CustomerContactNumber,
                                                  CustomerName = c.CustomerName,
                                                  Email = c.Email,
                                                  Gstnumber = c.Gstnumber,
                                                  State = c.State,
                                                  Country = c.Country
                                              }).FirstOrDefault(),
                                  ProformaInvoiceItem = (from op in db.ProformaInvoiceItemTables
                                                         where op.ProformaInvoiceId == proformaInvoiceId
                                                         from fc in db.SaleFormulationCodeMasters
                                                         where op.SaleFormulationCodeId == fc.SaleFormulationCodeId
                                                         select new ProformaInvoiceItemTableVm
                                                         {
                                                             ProformaInvoiceItemId = op.ProformaInvoiceItemId,
                                                             ProformaInvoiceId = op.ProformaInvoiceId,
                                                             SaleFormulationCodeId = op.SaleFormulationCodeId,
                                                             SaleFormulationCodeProductName = fc.SaleFormulationCode,
                                                             Quantity = op.Quantity,
                                                             Price = op.Price,
                                                             DescriptionOfGoods = op.DescriptionOfGoods,
                                                             ArticleName = op.ArticleName
                                                         }).ToList()
                              }).FirstOrDefault();
                return result;
            }
        }

        public ApiFunctionResponseVm AddProformaInvoice(ProformaInvoiceTableVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                ProformaInvoiceTable proformaInvoiceTable = new ProformaInvoiceTable()
                {
                    CustomerId = mix.CustomerId,
                    Gstn = mix.Gstn,
                    ProformaInvoiceDate = mix.ProformaInvoiceDate,
                    ConsignorReference = mix.ConsignorReference,
                    ReferenceType = mix.ReferenceType,
                    BuyerReferenceNumber = mix.BuyerReferenceNumber,
                    CountryOfOrigin = mix.CountryOfOrigin,
                    CountryOfDestinaton = mix.CountryOfDestinaton,
                    MaterialType = mix.MaterialType,
                    Hsncode = mix.Hsncode,
                    BankName = mix.BankName,
                    BankBranch = mix.BankBranch,
                    BankAccountNumber = mix.BankAccountNumber,
                    Ifsccode = mix.Ifsccode,
                    SwiftCode = mix.SwiftCode,
                    BeneficiaryName = mix.BeneficiaryName,
                    PortOfLoading = mix.PortOfLoading,
                    PortOfDischarge = mix.PortOfDischarge,
                    FinalDestination = mix.FinalDestination,
                    ModeOfTransport = mix.ModeOfTransport,
                    TermsCondition = mix.TermsCondition,
                    Moq = mix.Moq,
                    Moqtotal = mix.Moqtotal,
                    Gst = mix.Gst,
                    TotalPrice = mix.TotalPrice,
                    Currency = mix.Currency,
                    Discount = mix.Discount,
                    BankId = mix.BankId,
                    AddedBy =  mix.AddedBy,
                    AddedDate = System.DateTime.Now
                };
                db.ProformaInvoiceTables.Add(proformaInvoiceTable);
                db.SaveChanges();
                proformaInvoiceTable.ProformaInvoiceNumber = "Z-PI-" + (1000 + proformaInvoiceTable.ProformaInvoiceId);
                db.SaveChanges();

                foreach (var item in mix.ProformaInvoiceItem)
                {
                    ProformaInvoiceItemTable spt = new ProformaInvoiceItemTable()
                    {
                        ProformaInvoiceId = proformaInvoiceTable.ProformaInvoiceId,
                        SaleFormulationCodeId = item.SaleFormulationCodeId,
                        Quantity = item.Quantity,
                        Price = item.Price,
                        DescriptionOfGoods = item.DescriptionOfGoods,
                        ArticleName = item.ArticleName
                    };

                    db.ProformaInvoiceItemTables.Add(spt);
                }
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public ApiFunctionResponseVm UpdateProformaInvoice(ProformaInvoiceTableVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                ProformaInvoiceTable mm = db.ProformaInvoiceTables.FirstOrDefault(x => x.ProformaInvoiceId == mix.ProformaInvoiceId);
                mm.Gstn = mix.Gstn;
                mm.CustomerId = mix.CustomerId;
                mm.ProformaInvoiceNumber = mix.ProformaInvoiceNumber;
                mm.ProformaInvoiceDate = mix.ProformaInvoiceDate;
                mm.ConsignorReference = mix.ConsignorReference;
                mm.ReferenceType = mix.ReferenceType;
                mm.BuyerReferenceNumber = mix.BuyerReferenceNumber;
                mm.CountryOfOrigin = mix.CountryOfOrigin;
                mm.CountryOfDestinaton = mix.CountryOfDestinaton;
                mm.MaterialType = mix.MaterialType;
                mm.Hsncode = mix.Hsncode;
                mm.BankName = mix.BankName;
                mm.BankBranch = mix.BankBranch;
                mm.BankAccountNumber = mix.BankAccountNumber;
                mm.Ifsccode = mix.Ifsccode;
                mm.SwiftCode = mix.SwiftCode;
                mm.BeneficiaryName = mix.BeneficiaryName;
                mm.PortOfLoading = mix.PortOfLoading;
                mm.PortOfDischarge = mix.PortOfDischarge;
                mm.FinalDestination = mix.FinalDestination;
                mm.ModeOfTransport = mix.ModeOfTransport;
                mm.TermsCondition = mix.TermsCondition;
                mm.Moq = mix.Moq;
                mm.Moqtotal = mix.Moqtotal;
                mm.Gst = mix.Gst;
                mm.TotalPrice = mix.TotalPrice;
                mm.Currency = mix.Currency;
                mm.Discount = mix.Discount;
                mm.BankId = mix.BankId;
                mm.AddedBy = mix.AddedBy;
                mm.AddedDate = System.DateTime.Now;
                db.SaveChanges();
                List<ProformaInvoiceItemTable> stAllList = db.ProformaInvoiceItemTables.Where(x => x.ProformaInvoiceId == mix.ProformaInvoiceId).ToList();
                var deleteRecords = stAllList.Except(stAllList.Where(o => mix.ProformaInvoiceItem.Select(s => s.ProformaInvoiceItemId).ToList().Contains(o.ProformaInvoiceItemId))).ToList();
                var AddRecords = mix.ProformaInvoiceItem.Where(x => x.ProformaInvoiceItemId == 0).ToList();
                if (AddRecords.Count > 0)
                {
                    foreach (var item in AddRecords)
                    {
                        ProformaInvoiceItemTable spt = new ProformaInvoiceItemTable()
                        {
                            ProformaInvoiceId = mix.ProformaInvoiceId,
                            SaleFormulationCodeId = item.SaleFormulationCodeId,
                            Quantity = item.Quantity,
                            Price = item.Price,
                            DescriptionOfGoods = item.DescriptionOfGoods,
                            ArticleName = item.ArticleName
                        };

                        db.ProformaInvoiceItemTables.Add(spt);
                    }
                }

                var resrec = mix.ProformaInvoiceItem.Where(x => x.ProformaInvoiceItemId > 0).ToList();
                foreach (var itm in resrec)
                {
                    var rec = db.ProformaInvoiceItemTables.Where(x => x.ProformaInvoiceItemId == itm.ProformaInvoiceItemId).FirstOrDefault();
                    if (rec != null)
                    {
                        rec.SaleFormulationCodeId = itm.SaleFormulationCodeId;
                        rec.Quantity = itm.Quantity;
                        rec.Price = itm.Price;
                    }
                }
                db.SaveChanges();
                if (deleteRecords.Count > 0)
                {
                    foreach (var item in deleteRecords)
                    {
                        var dr = db.ProformaInvoiceItemTables.SingleOrDefault(x => x.ProformaInvoiceItemId == item.ProformaInvoiceItemId);
                        if (dr != null)
                            db.ProformaInvoiceItemTables.Remove(dr);
                    }
                }
                if (deleteRecords.Count > 0)
                    db.ProformaInvoiceItemTables.RemoveRange(deleteRecords);
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public List<ProformaInvoiceTableCustomerVm> GetProformaInvoicesByCustomerId(long customerId)
        {
            List<ProformaInvoiceTableCustomerVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProformaInvoiceTables
                       where a.CustomerId == customerId
                       select new ProformaInvoiceTableCustomerVm
                       {
                           ProformaInvoiceId = a.ProformaInvoiceId,
                           ProformaInvoiceNumber = a.ProformaInvoiceNumber
                       }).OrderByDescending(x => x.ProformaInvoiceId).ToList();
            }
            return res;
        }
        public List<ProformaInvoiceTableVm> GetAllProformaInvoiceByFilter(ProformaInvoiceRequestFilter filters)
        {
            List<ProformaInvoiceTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProformaInvoiceTables
                       where ((((String.IsNullOrEmpty(filters.DateType) || filters.DateType.ToLowerInvariant() == "addeddate") && (filters.FromAddedDate == null || a.AddedDate >= filters.FromAddedDate))
                      && ((String.IsNullOrEmpty(filters.DateType) || filters.DateType.ToLowerInvariant() == "addeddate") && (filters.ToAddedDate == null || a.AddedDate <= filters.ToAddedDate)))
                      || (((String.IsNullOrEmpty(filters.DateType) || filters.DateType.ToLowerInvariant() == "performainvoicedate") && (filters.ProformaInvoiceFromDate == null || a.ProformaInvoiceDate >= filters.ProformaInvoiceFromDate))
                      && ((String.IsNullOrEmpty(filters.DateType) || filters.DateType.ToLowerInvariant() == "performainvoicedate") && (filters.ProformaInvoiceDateToDate == null || a.ProformaInvoiceDate <= filters.ProformaInvoiceDateToDate))))
                      && (filters.ProformaInvoiceId == 0 || filters.ProformaInvoiceId == null || filters.ProformaInvoiceId == a.ProformaInvoiceId)
                      && (filters.CustomerId == 0 || filters.CustomerId == null || filters.CustomerId == a.CustomerId)
                      && (String.IsNullOrEmpty(filters.Currency) || a.Currency.ToLower().Contains(filters.Currency))
                       select new ProformaInvoiceTableVm
                       {
                           ProformaInvoiceId = a.ProformaInvoiceId,
                           CustomerId = a.CustomerId,
                           Gstn = a.Gstn,
                           ProformaInvoiceNumber = a.ProformaInvoiceNumber,
                           ProformaInvoiceDate = a.ProformaInvoiceDate,
                           ConsignorReference = a.ConsignorReference,
                           ReferenceType = a.ReferenceType,
                           BuyerReferenceNumber = a.BuyerReferenceNumber,
                           CountryOfOrigin = a.CountryOfOrigin,
                           CountryOfDestinaton = a.CountryOfDestinaton,
                           MaterialType = a.MaterialType,
                           Hsncode = a.Hsncode,
                           BankName = a.BankName,
                           BankBranch = a.BankBranch,
                           BankAccountNumber = a.BankAccountNumber,
                           Ifsccode = a.Ifsccode,
                           SwiftCode = a.SwiftCode,
                           BeneficiaryName = a.BeneficiaryName,
                           PortOfLoading = a.PortOfLoading,
                           PortOfDischarge = a.PortOfDischarge,
                           FinalDestination = a.FinalDestination,
                           ModeOfTransport = a.ModeOfTransport,
                           TermsCondition = a.TermsCondition,
                           Moq = a.Moq,
                           Moqtotal = a.Moqtotal,
                           Gst = a.Gst,
                           TotalPrice = a.TotalPrice,
                           Currency = a.Currency,
                           Discount = a.Discount,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           BankId = a.BankId,
                           Customer = (from c in db.CustomerMasters
                                       where c.CustomerId == a.CustomerId
                                       && (filters.CustomerId == 0 || filters.CustomerId == null || filters.CustomerId == a.CustomerId)
                                       select new CustomerMasterVm
                                       {
                                           CustomerId = c.CustomerId,
                                           Address = c.Address,
                                           CustomerContactNumber = c.CustomerContactNumber,
                                           CustomerName = c.CustomerName,
                                           Email = c.Email,
                                           Gstnumber = c.Gstnumber,
                                           State = c.State,
                                           Country = c.Country
                                       }).FirstOrDefault(),
                           ProformaInvoiceItem = (from op in db.ProformaInvoiceItemTables
                                                  where op.ProformaInvoiceId == a.ProformaInvoiceId
                                                  from fc in db.SaleFormulationCodeMasters
                                                  where op.SaleFormulationCodeId == fc.SaleFormulationCodeId
                                                  select new ProformaInvoiceItemTableVm
                                                  {
                                                      ProformaInvoiceItemId = op.ProformaInvoiceItemId,
                                                      ProformaInvoiceId = op.ProformaInvoiceId,
                                                      SaleFormulationCodeId = op.SaleFormulationCodeId,
                                                      SaleFormulationCodeProductName = fc.SaleFormulationCode,
                                                      Quantity = op.Quantity,
                                                      Price = op.Price,
                                                      DescriptionOfGoods = op.DescriptionOfGoods,
                                                      ArticleName = op.ArticleName
                                                  }).ToList()
                       }).OrderByDescending(x => x.ProformaInvoiceId).ToList();
            }
            return res;
        }

    }
}
