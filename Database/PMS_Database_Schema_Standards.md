# PMS Database Schema Standards and Guidelines

## Overview

This document outlines the standards and best practices for database schema design in the Production Management System (PMS). All future database changes must follow these guidelines to ensure consistency, integrity, and performance.

## 1. Foreign Key Constraints

### 1.1 Mandatory Foreign Keys

All columns ending with "Id" that reference another table MUST have foreign key constraints:

```sql
-- Standard Pattern
ALTER TABLE [ChildTable]
ADD CONSTRAINT FK_[ChildTable]_[ColumnName]_[ParentTable]
FOREIGN KEY ([ColumnName]) REFERENCES [ParentTable]([PrimaryKeyColumn]);
```

### 1.2 Audit Column Foreign Keys

All audit columns MUST reference UserMaster.UserId:

- **CreatedById** → UserMaster.UserId
- **UpdatedById** → UserMaster.UserId
- **DisabledById** → UserMaster.UserId
- **ApprovedById** → UserMaster.UserId
- **CancelledById** → UserMaster.UserId

```sql
-- Example for audit columns
ALTER TABLE [TableName]
ADD CONSTRAINT FK_[TableName]_UpdatedById_UserMaster
FOREIGN KEY (UpdatedById) REFERENCES UserMaster(UserId);

ALTER TABLE [TableName]
ADD CONSTRAINT FK_[TableName]_DisabledById_UserMaster
FOREIGN KEY (DisabledById) REFERENCES UserMaster(UserId);
```

### 1.3 Naming Conventions

Foreign key constraint names MUST follow this pattern:

```
FK_[ChildTable]_[ColumnName]_[ParentTable]
```

Examples:

- `FK_InvoiceMaster_SupplierId_SupplierMaster`
- `FK_InvoiceMaster_UpdatedById_UserMaster`
- `FK_StockMaster_ProductId_ProductMaster`

### 1.4 NULL Handling

- Audit columns (UpdatedById, DisabledById) should allow NULL values
- Business reference columns should be evaluated case-by-case
- Use appropriate ON DELETE and ON UPDATE actions

## 2. Index Standards

### 2.1 Mandatory Indexes

Create indexes for all foreign key columns:

```sql
-- Pattern for foreign key indexes
CREATE NONCLUSTERED INDEX IX_[TableName]_[ColumnName]
ON [TableName] ([ColumnName]);
```

### 2.2 Composite Indexes

Create composite indexes for common query patterns:

```sql
-- Example: Active records with date sorting
CREATE NONCLUSTERED INDEX IX_[TableName]_Active_[DateColumn]
ON [TableName] (Active, [DateColumn] DESC);
```

### 2.3 Index Naming Convention

```
IX_[TableName]_[Column1]_[Column2]_...
```

## 3. Standard Audit Columns

### 3.1 Required Audit Columns

Every master table MUST include these audit columns:

```sql
-- Creation audit
CreatedById BIGINT NULL,
CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

-- Update audit
UpdatedById BIGINT NULL,
UpdatedDate DATETIME NULL,

-- Soft delete audit
Active BIT NOT NULL DEFAULT 1,
DisabledById BIGINT NULL,
DisabledDate DATETIME NULL
```

### 3.2 Optional Audit Columns (based on business needs)

```sql
-- Approval workflow
ApprovedById BIGINT NULL,
ApprovedDate DATETIME NULL,

-- Cancellation workflow
CancelledById BIGINT NULL,
CancelledDate DATETIME NULL,
CancellationReason NVARCHAR(500) NULL
```

## 4. Data Types Standards

### 4.1 ID Columns

- Primary Keys: `BIGINT IDENTITY(1,1)`
- Foreign Keys: `BIGINT`

### 4.2 Text Columns

- Short text (codes, names): `NVARCHAR(100)`
- Medium text (descriptions): `NVARCHAR(500)`
- Long text (comments, notes): `NVARCHAR(MAX)`

### 4.3 Date/Time Columns

- Standard datetime: `DATETIME`
- Date only: `DATE`
- Time only: `TIME`

### 4.4 Decimal/Money Columns

- Currency amounts: `DECIMAL(18,2)`
- Quantities: `DECIMAL(18,4)`
- Percentages: `DECIMAL(5,2)`

### 4.5 Boolean Columns

- Use `BIT` data type
- Always provide default value: `DEFAULT 0` or `DEFAULT 1`

## 5. Table Creation Template

```sql
CREATE TABLE [TableName] (
    -- Primary Key
    [TableName]Id BIGINT IDENTITY(1,1) NOT NULL,

    -- Business Columns
    [BusinessColumn1] NVARCHAR(100) NOT NULL,
    [BusinessColumn2] DECIMAL(18,2) NULL,
    [ForeignKeyId] BIGINT NOT NULL,

    -- Standard Audit Columns
    CreatedById BIGINT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    UpdatedById BIGINT NULL,
    UpdatedDate DATETIME NULL,
    Active BIT NOT NULL DEFAULT 1,
    DisabledById BIGINT NULL,
    DisabledDate DATETIME NULL,

    -- Primary Key Constraint
    CONSTRAINT PK_[TableName] PRIMARY KEY ([TableName]Id),

    -- Foreign Key Constraints
    CONSTRAINT FK_[TableName]_ForeignKeyId_[ParentTable]
        FOREIGN KEY ([ForeignKeyId]) REFERENCES [ParentTable]([ParentTableId]),
    CONSTRAINT FK_[TableName]_CreatedById_UserMaster
        FOREIGN KEY (CreatedById) REFERENCES UserMaster(UserId),
    CONSTRAINT FK_[TableName]_UpdatedById_UserMaster
        FOREIGN KEY (UpdatedById) REFERENCES UserMaster(UserId),
    CONSTRAINT FK_[TableName]_DisabledById_UserMaster
        FOREIGN KEY (DisabledById) REFERENCES UserMaster(UserId)
);

-- Indexes
CREATE NONCLUSTERED INDEX IX_[TableName]_ForeignKeyId
ON [TableName] ([ForeignKeyId]);

CREATE NONCLUSTERED INDEX IX_[TableName]_Active_CreatedDate
ON [TableName] (Active, CreatedDate DESC);
```

## 6. Schema Change Process

### 6.1 Before Making Changes

1. Review existing table structure
2. Identify all reference columns
3. Plan foreign key constraints
4. Plan necessary indexes
5. Consider performance impact

### 6.2 Change Script Structure

```sql
-- 1. Add columns (if needed)
-- 2. Add foreign key constraints
-- 3. Add indexes
-- 4. Verify constraints and indexes
-- 5. Update documentation
```

### 6.3 Verification Steps

Always include verification queries:

```sql
-- Verify foreign keys
SELECT fk.name, tp.name AS ParentTable, tr.name AS ReferencedTable
FROM sys.foreign_keys fk
INNER JOIN sys.tables tp ON fk.parent_object_id = tp.object_id
INNER JOIN sys.tables tr ON fk.referenced_object_id = tr.object_id
WHERE tp.name = '[TableName]';

-- Verify indexes
SELECT i.name, STRING_AGG(c.name, ', ') AS IndexColumns
FROM sys.indexes i
INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE t.name = '[TableName]' AND i.name IS NOT NULL
GROUP BY i.name;
```

## 7. Common Patterns

### 7.1 Master-Detail Relationships

```sql
-- Master table with standard audit
-- Detail table referencing master with cascade options
ALTER TABLE [DetailTable]
ADD CONSTRAINT FK_[DetailTable]_[MasterId]_[MasterTable]
FOREIGN KEY ([MasterId]) REFERENCES [MasterTable]([MasterId])
ON DELETE CASCADE;  -- Use carefully based on business rules
```

### 7.2 Lookup Table References

```sql
-- Reference to lookup/master tables (usually no cascade)
ALTER TABLE [TransactionTable]
ADD CONSTRAINT FK_[TransactionTable]_[LookupId]_[LookupTable]
FOREIGN KEY ([LookupId]) REFERENCES [LookupTable]([LookupId]);
```

### 7.3 Self-Referencing Tables

```sql
-- For hierarchical data
ALTER TABLE [TableName]
ADD CONSTRAINT FK_[TableName]_ParentId_[TableName]
FOREIGN KEY (ParentId) REFERENCES [TableName]([TableName]Id);
```

## 8. Performance Considerations

### 8.1 Index Strategy

- Index all foreign key columns
- Create composite indexes for common WHERE clauses
- Consider covering indexes for frequently accessed columns
- Monitor index usage and remove unused indexes

### 8.2 Foreign Key Performance

- Foreign key constraints add overhead on INSERT/UPDATE/DELETE
- Benefits: Data integrity, query optimization
- Ensure referenced tables have proper indexes on referenced columns

## 9. Maintenance

### 9.1 Regular Reviews

- Quarterly review of foreign key constraints
- Monitor constraint violation errors
- Review index usage statistics
- Update documentation for schema changes

### 9.2 Documentation Updates

- Update this document for new patterns
- Document any exceptions to standards
- Maintain change log for major schema modifications

## 10. Validation Scripts

### 10.1 Check Missing Foreign Keys

```sql
-- Find columns ending with 'Id' that might need foreign key constraints
SELECT
    t.name AS TableName,
    c.name AS ColumnName,
    c.is_nullable,
    CASE
        WHEN fk.name IS NULL THEN 'MISSING FK'
        ELSE 'HAS FK: ' + fk.name
    END AS ForeignKeyStatus
FROM sys.tables t
INNER JOIN sys.columns c ON t.object_id = c.object_id
LEFT JOIN sys.foreign_key_columns fkc ON c.object_id = fkc.parent_object_id AND c.column_id = fkc.parent_column_id
LEFT JOIN sys.foreign_keys fk ON fkc.constraint_object_id = fk.object_id
WHERE c.name LIKE '%Id'
    AND c.name NOT LIKE '%Identity%'
    AND t.name NOT LIKE 'sys%'
ORDER BY t.name, c.name;
```

### 10.2 Check Audit Column Constraints

```sql
-- Verify audit columns have proper foreign key constraints to UserMaster
SELECT
    t.name AS TableName,
    c.name AS AuditColumn,
    CASE
        WHEN fk.name IS NULL THEN 'MISSING FK TO UserMaster'
        ELSE 'OK: ' + fk.name
    END AS ConstraintStatus
FROM sys.tables t
INNER JOIN sys.columns c ON t.object_id = c.object_id
LEFT JOIN sys.foreign_key_columns fkc ON c.object_id = fkc.parent_object_id AND c.column_id = fkc.parent_column_id
LEFT JOIN sys.foreign_keys fk ON fkc.constraint_object_id = fk.object_id
LEFT JOIN sys.tables rt ON fk.referenced_object_id = rt.object_id
WHERE c.name IN ('CreatedById', 'UpdatedById', 'DisabledById', 'ApprovedById', 'CancelledById')
    AND (rt.name != 'UserMaster' OR rt.name IS NULL)
ORDER BY t.name, c.name;
```

---

**Note**: All database schema changes must be reviewed and approved before deployment to production. This ensures consistency with PMS standards and maintains system integrity.
