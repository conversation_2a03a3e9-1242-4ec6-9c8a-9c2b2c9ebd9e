# PowerShell script to create dashboard tables
$connectionString = "Server=tcp:pms-mssqlserver-dev.database.windows.net,1433;Initial Catalog=pmsdb;Persist Security Info=False;User ID=pmssqluser;Password=*****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"

$sql = @"
-- Check and create UserDashboardConfig table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'UserDashboardConfig')
BEGIN
    CREATE TABLE UserDashboardConfig (
        ConfigId BIGINT IDENTITY(1,1) PRIMARY KEY,
        UserId NVARCHAR(100) NOT NULL,
        DashboardType NVARCHAR(50) NOT NULL,
        ConfigJson NVARCHAR(MAX) NOT NULL,
        ConfigName NVARCHAR(200),
        Description NVARCHAR(500),
        IsDefault BIT DEFAULT 0,
        Disabled BIT DEFAULT 0,
        Version INT DEFAULT 1,
        Tags NVARCHAR(200),
        AddedBy NVARCHAR(100),
        AddedDate DATETIME2 DEFAULT GETDATE(),
        ModifiedBy NVARCHAR(100),
        ModifiedDate DATETIME2 DEFAULT GETDATE()
    );
    
    CREATE INDEX IX_UserDashboardConfig_User_Type 
    ON UserDashboardConfig(UserId, DashboardType);
    
    CREATE INDEX IX_UserDashboardConfig_Default 
    ON UserDashboardConfig(UserId, DashboardType, IsDefault) 
    WHERE IsDefault = 1;
    
    PRINT 'UserDashboardConfig table created successfully';
END
ELSE
BEGIN
    PRINT 'UserDashboardConfig table already exists';
END

-- Check and create SystemDashboardDefaults table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SystemDashboardDefaults')
BEGIN
    CREATE TABLE SystemDashboardDefaults (
        DefaultId BIGINT IDENTITY(1,1) PRIMARY KEY,
        DashboardType NVARCHAR(50) NOT NULL,
        ConfigJson NVARCHAR(MAX) NOT NULL,
        Version INT DEFAULT 1,
        Disabled BIT DEFAULT 0,
        CreatedDate DATETIME2 DEFAULT GETDATE()
    );
    
    PRINT 'SystemDashboardDefaults table created successfully';
END
ELSE
BEGIN
    PRINT 'SystemDashboardDefaults table already exists';
END

PRINT 'Dashboard configuration tables setup completed';
"@

try {
    # Load SQL Server module if available
    Import-Module SqlServer -ErrorAction SilentlyContinue
    
    # Execute SQL
    Invoke-Sqlcmd -ConnectionString $connectionString -Query $sql -Verbose
    Write-Host "Tables created successfully!" -ForegroundColor Green
}
catch {
    Write-Host "Error creating tables: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Attempting alternative method..." -ForegroundColor Yellow
    
    # Alternative method using .NET SqlConnection
    try {
        Add-Type -AssemblyName System.Data
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        $command = New-Object System.Data.SqlClient.SqlCommand($sql, $connection)
        $command.ExecuteNonQuery()
        
        $connection.Close()
        Write-Host "Tables created successfully using .NET method!" -ForegroundColor Green
    }
    catch {
        Write-Host "Error with .NET method: $($_.Exception.Message)" -ForegroundColor Red
    }
}
