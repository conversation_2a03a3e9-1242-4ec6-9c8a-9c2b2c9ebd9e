# PowerShell script to insert default dashboard data
$connectionString = "Server=tcp:pms-mssqlserver-dev.database.windows.net,1433;Initial Catalog=pmsdb;Persist Security Info=False;User ID=pmssqluser;Password=*****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"

$gateConfig = @'
{
    "sections": [
        {
            "id": "operations",
            "title": "Gate Operations",
            "description": "Gate management and monitoring",
            "isVisible": true,
            "order": 0,
            "isCollapsed": false,
            "tiles": [
                {
                    "id": "pending-gate-out",
                    "title": "Pending Gate-Out",
                    "description": "Items pending gate-out approval",
                    "value": 0,
                    "icon": "exit_to_app",
                    "color": "#ff9800",
                    "isVisible": true,
                    "order": 0,
                    "sectionId": "operations",
                    "actionRoute": "/home/<USER>/gateout",
                    "actionLabel": "View Gate Out"
                },
                {
                    "id": "pending-gate-passes",
                    "title": "Pending Gate Passes",
                    "description": "Gate passes awaiting approval",
                    "value": 0,
                    "icon": "assignment",
                    "color": "#2196f3",
                    "isVisible": true,
                    "order": 1,
                    "sectionId": "operations",
                    "actionRoute": "/home/<USER>/gatepass",
                    "actionLabel": "View Gate Pass"
                }
            ]
        },
        {
            "id": "analytics",
            "title": "Analytics",
            "description": "Gate performance metrics and analytics",
            "isVisible": true,
            "order": 1,
            "isCollapsed": false,
            "tiles": [
                {
                    "id": "invoices-without-po",
                    "title": "Invoice Gate-ins Without Purchase Order",
                    "description": "Invoices processed without purchase orders",
                    "value": 0,
                    "icon": "warning",
                    "color": "#95de64",
                    "isVisible": true,
                    "order": 0,
                    "sectionId": "analytics",
                    "actionRoute": "/home/<USER>/gatein",
                    "actionLabel": "View Gate In"
                }
            ]
        }
    ],
    "lastModified": "2024-01-01T00:00:00.000Z",
    "version": 1
}
'@

$productionConfig = @'
{
    "sections": [
        {
            "id": "production-overview",
            "title": "Production Overview",
            "description": "Production metrics and status",
            "isVisible": true,
            "order": 0,
            "isCollapsed": false,
            "tiles": []
        }
    ],
    "lastModified": "2024-01-01T00:00:00.000Z",
    "version": 1
}
'@

$salesConfig = @'
{
    "sections": [
        {
            "id": "sales-overview",
            "title": "Sales Overview",
            "description": "Sales metrics and performance",
            "isVisible": true,
            "order": 0,
            "isCollapsed": false,
            "tiles": []
        }
    ],
    "lastModified": "2024-01-01T00:00:00.000Z",
    "version": 1
}
'@

$sql = @"
-- Insert system default configurations if they don't exist
IF NOT EXISTS (SELECT 1 FROM SystemDashboardDefaults WHERE DashboardType = 'gate')
BEGIN
    INSERT INTO SystemDashboardDefaults (DashboardType, ConfigJson, Version) 
    VALUES ('gate', @gateConfig, 1);
    PRINT 'Gate default configuration inserted';
END

IF NOT EXISTS (SELECT 1 FROM SystemDashboardDefaults WHERE DashboardType = 'production')
BEGIN
    INSERT INTO SystemDashboardDefaults (DashboardType, ConfigJson, Version) 
    VALUES ('production', @productionConfig, 1);
    PRINT 'Production default configuration inserted';
END

IF NOT EXISTS (SELECT 1 FROM SystemDashboardDefaults WHERE DashboardType = 'sales')
BEGIN
    INSERT INTO SystemDashboardDefaults (DashboardType, ConfigJson, Version) 
    VALUES ('sales', @salesConfig, 1);
    PRINT 'Sales default configuration inserted';
END

PRINT 'Default configurations setup completed';
"@

try {
    Add-Type -AssemblyName System.Data
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    $command = New-Object System.Data.SqlClient.SqlCommand($sql, $connection)
    $command.Parameters.AddWithValue("@gateConfig", $gateConfig)
    $command.Parameters.AddWithValue("@productionConfig", $productionConfig)
    $command.Parameters.AddWithValue("@salesConfig", $salesConfig)
    
    $command.ExecuteNonQuery()
    $connection.Close()
    
    Write-Host "Default configurations inserted successfully!" -ForegroundColor Green
}
catch {
    Write-Host "Error inserting default data: $($_.Exception.Message)" -ForegroundColor Red
}
