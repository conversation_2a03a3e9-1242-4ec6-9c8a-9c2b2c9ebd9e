-- Dashboard Configuration Tables Creation Script
-- Following PMS naming conventions and patterns

-- Check and create UserDashboardConfig table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'UserDashboardConfig')
BEGIN
    CREATE TABLE UserDashboardConfig (
        ConfigId BIGINT IDENTITY(1,1) PRIMARY KEY,
        UserId NVARCHAR(100) NOT NULL,
        DashboardType NVARCHAR(50) NOT NULL,
        ConfigJson NVARCHAR(MAX) NOT NULL,
        ConfigName NVARCHAR(200),
        Description NVARCHAR(500),
        IsDefault BIT DEFAULT 0,
        Disabled BIT DEFAULT 0,                  -- PMS soft delete pattern
        Version INT DEFAULT 1,
        Tags NVARCHAR(200),
        AddedBy NVARCHAR(100),                   -- PMS audit pattern
        AddedDate DATETIME2 DEFAULT GETDATE(),   -- PMS audit pattern
        ModifiedBy NVARCHAR(100),                -- PMS audit pattern
        ModifiedDate DATETIME2 DEFAULT GETDATE() -- PMS audit pattern
    );
    
    -- Create indexes for performance
    CREATE INDEX IX_UserDashboardConfig_User_Type 
    ON UserDashboardConfig(UserId, DashboardType);
    
    CREATE INDEX IX_UserDashboardConfig_Default 
    ON UserDashboardConfig(UserId, DashboardType, IsDefault) 
    WHERE IsDefault = 1;
    
    PRINT 'UserDashboardConfig table created successfully';
END
ELSE
BEGIN
    PRINT 'UserDashboardConfig table already exists';
END

-- Check and create SystemDashboardDefaults table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SystemDashboardDefaults')
BEGIN
    CREATE TABLE SystemDashboardDefaults (
        DefaultId BIGINT IDENTITY(1,1) PRIMARY KEY,
        DashboardType NVARCHAR(50) NOT NULL,
        ConfigJson NVARCHAR(MAX) NOT NULL,
        Version INT DEFAULT 1,
        Disabled BIT DEFAULT 0,
        CreatedDate DATETIME2 DEFAULT GETDATE()
    );
    
    -- Insert system default configurations
    INSERT INTO SystemDashboardDefaults (DashboardType, ConfigJson, Version) VALUES
    ('gate', '{
        "sections": [
            {
                "id": "operations",
                "title": "Gate Operations",
                "description": "Gate management and monitoring",
                "isVisible": true,
                "order": 0,
                "isCollapsed": false,
                "tiles": [
                    {
                        "id": "pending-gate-out",
                        "title": "Pending Gate-Out",
                        "description": "Items pending gate-out approval",
                        "value": 0,
                        "icon": "exit_to_app",
                        "color": "#ff9800",
                        "isVisible": true,
                        "order": 0,
                        "sectionId": "operations",
                        "actionRoute": "/home/<USER>/gateout",
                        "actionLabel": "View Gate Out"
                    },
                    {
                        "id": "pending-gate-passes",
                        "title": "Pending Gate Passes",
                        "description": "Gate passes awaiting approval",
                        "value": 0,
                        "icon": "assignment",
                        "color": "#2196f3",
                        "isVisible": true,
                        "order": 1,
                        "sectionId": "operations",
                        "actionRoute": "/home/<USER>/gatepass",
                        "actionLabel": "View Gate Pass"
                    }
                ]
            },
            {
                "id": "analytics",
                "title": "Analytics",
                "description": "Gate performance metrics and analytics",
                "isVisible": true,
                "order": 1,
                "isCollapsed": false,
                "tiles": [
                    {
                        "id": "invoices-without-po",
                        "title": "Invoice Gate-ins Without Purchase Order",
                        "description": "Invoices processed without purchase orders",
                        "value": 0,
                        "icon": "warning",
                        "color": "#95de64",
                        "isVisible": true,
                        "order": 0,
                        "sectionId": "analytics",
                        "actionRoute": "/home/<USER>/gatein",
                        "actionLabel": "View Gate In"
                    }
                ]
            }
        ],
        "lastModified": "' + FORMAT(GETDATE(), 'yyyy-MM-ddTHH:mm:ss.fffZ') + '",
        "version": 1
    }', 1),
    ('production', '{
        "sections": [
            {
                "id": "production-overview",
                "title": "Production Overview",
                "description": "Production metrics and status",
                "isVisible": true,
                "order": 0,
                "isCollapsed": false,
                "tiles": []
            }
        ],
        "lastModified": "' + FORMAT(GETDATE(), 'yyyy-MM-ddTHH:mm:ss.fffZ') + '",
        "version": 1
    }', 1),
    ('sales', '{
        "sections": [
            {
                "id": "sales-overview",
                "title": "Sales Overview",
                "description": "Sales metrics and performance",
                "isVisible": true,
                "order": 0,
                "isCollapsed": false,
                "tiles": []
            }
        ],
        "lastModified": "' + FORMAT(GETDATE(), 'yyyy-MM-ddTHH:mm:ss.fffZ') + '",
        "version": 1
    }', 1);
    
    PRINT 'SystemDashboardDefaults table created and populated successfully';
END
ELSE
BEGIN
    PRINT 'SystemDashboardDefaults table already exists';
END

PRINT 'Dashboard configuration tables setup completed';
