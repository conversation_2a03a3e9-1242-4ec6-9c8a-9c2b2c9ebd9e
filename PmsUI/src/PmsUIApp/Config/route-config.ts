/**
 * Centralized Route Configuration System for PMS Application
 * 
 * This file contains all route definitions, menu structures, permissions,
 * and hierarchical relationships in a single, maintainable location.
 */

export interface RouteConfig {
  path: string;
  label: string;
  icon?: string;
  permission?: {
    module?: string;
    responsibility?: string;
    action?: string;
  };
  children?: RouteConfig[];
  isLazyLoaded?: boolean;
  component?: string;
  title?: string;
}

export interface MenuStructure {
  id: string;
  label: string;
  icon: string;
  permission: {
    module: string;
  };
  openMapKey: string;
  children: RouteConfig[];
  dataLabel: string;
}

// Type-safe route constants
export const ROUTES = {
  // Root paths
  HOME: '/home',
  LOGIN: '/login',
  LOGOUT: '/logout',
  WELCOME: '/welcome',

  // Dashboard routes
  DASHBOARD: {
    WELCOME: '/home/<USER>',
    REPORTING: '/home/<USER>/reporting',
  },

  // Reports routes
  REPORTS: {
    SALES: {
      SALES_REPORT: '/home/<USER>/sales/salesreport',
      SALES_ORDER_TRAILS: '/home/<USER>/sales/salesordertrails',
    },
    STOCK: {
      STOCK_AVAILABILITY: '/home/<USER>/stock/productwisestockwithsupplier',
      CATEGORY_WISE_STOCK: '/home/<USER>/stock/categorywisestock',
      STOCK_CONSUMPTION: '/home/<USER>/stock/stockconsumptionreport',
      STOCK_REPORT: '/home/<USER>/stock/stockreport',
      STORE_WISE_STOCK: '/home/<USER>/stock/storewisestock',
      PRODUCT_WISE_STOCK: '/home/<USER>/stock/productwiseStock',
      PRODUCT_STOCK_HISTORY: '/home/<USER>/stock/productstockhistory',
      PRODUCT_STOCK_SUMMARY: '/home/<USER>/stock/productstocksummaryreport',
    },
    PURCHASE: {
      PURCHASE_REPORT: '/home/<USER>/purchase/purchasereport',
      SUPPLIER_PRODUCT_MAPPING: '/home/<USER>/purchase/supplierproductmapping',
    },
    PRODUCTION: {
      PRODUCTION_PLANNING: '/home/<USER>/production/productionplanningreport',
      PRODUCTION_STATUS: '/home/<USER>/production/productionstatusreport',
      PRODUCTION_HEARTBEAT: '/home/<USER>/production/productionheartbeat',
      PASTE_CONSUMPTION: '/home/<USER>/production/pasteconsumptionreport',
      POST_PROCESS: '/home/<USER>/production/postprocessreport',
      YIELD: '/home/<USER>/production/yieldreport',
      WASTAGE: '/home/<USER>/production/wastagereport',
    },
  },

  // Sales routes
  SALES: {
    ORDER: {
      ADD: '/home/<USER>/add',
      LIST: '/home/<USER>/list',
      EDIT: '/home/<USER>/:type/:id',
      EDIT_BY_ID: '/home/<USER>/edit/:id',
    },
    PROFORMA: {
      ADD: '/home/<USER>/proformaInvoice/add',
      LIST: '/home/<USER>/proformaInvoice/list',
      EDIT: '/home/<USER>/proformaInvoice/:type/:id',
      PRINT: '/home/<USER>/:id',
    },
  },

  // Demand routes
  DEMAND: {
    LIST: '/home/<USER>/demandlist',
  },

  // Purchase Order routes
  PURCHASE_ORDER: {
    ADD: '/home/<USER>/add',
    LIST: '/home/<USER>/list',
    EDIT: '/home/<USER>/:type/:id',
    INVOICES: '/home/<USER>/invoicelist',
    PRINT: '/home/<USER>/:id',
    PRINT_WITH_TYPE: '/home/<USER>/:id/:type',
    EMAIL: '/home/<USER>/:id',
  },

  // Production routes
  PRODUCTION: {
    FORMULATION_CODE: {
      ADD: '/home/<USER>/formulationcode/add',
      LIST: '/home/<USER>/formulationcode/list',
      EDIT: '/home/<USER>/formulationcode/edit/:id',
      PRINT: '/home/<USER>/formulationprint/:id',
    },
    PIGMENT_MB: {
      ADD: '/home/<USER>/pigmentmb/add',
    },
    MIXING: {
      ADD: '/home/<USER>/mixing/add',
      LIST: '/home/<USER>/mixing/list',
      EDIT: '/home/<USER>/mixing/add/:id',
      PRINT: '/home/<USER>/mixingprint/:id',
    },
    WORKPLAN: '/home/<USER>/workplan',
    START_PRODUCTION: '/home/<USER>/start',
    JUMBO: {
      ADD: '/home/<USER>/jumbo/add',
      LIST: '/home/<USER>/jumbo/list',
      PRINT: '/home/<USER>/:id/:JumboId',
      MASTER: '/home/<USER>',
    },
    POST_PROCESS: {
      ADD: '/home/<USER>/postprocess',
      LAMINATION: '/home/<USER>/postprocess/lamination',
      PRINT: '/home/<USER>/processprint/:id',
    },
    FINAL_INSPECTION: {
      ADD: '/home/<USER>/finalInspection/add',
      ADD_WITH_JUMBO: '/home/<USER>/finalInspection/add/:JumboNo',
      LIST: '/home/<USER>/finalInspection/list',
    },
    INSPECTION: {
      PRINT: '/home/<USER>/:id/:JumboId/:InspectionId',
      PRINT_ALL: '/home/<USER>/:id/:JumboId',
      DETAILS_PRINT: '/home/<USER>/:id',
    },
    DOWNTIME: '/home/<USER>/downtime',
    ACTIVITY_LOG: '/home/<USER>/ActivityLog',
  },

  // Dispatch routes
  DISPATCH: {
    PACKAGING: {
      ADD: '/home/<USER>/packaging/add',
      LIST: '/home/<USER>/packaging/list',
      EDIT: '/home/<USER>/packaging/:type/:id',
      PRINT: '/home/<USER>/:id',
      EMAIL: '/home/<USER>/:id',
    },
  },

  // Issue routes
  ISSUE: {
    ADD: '/home/<USER>/add',
    LIST: '/home/<USER>/list',
    SLIP_PRINT: '/home/<USER>/:id',
  },

  // Consumption routes
  CONSUMPTION: {
    ADD: '/home/<USER>/add',
    ADD_WITH_PARAMS: '/home/<USER>/add/:workplanid/:saleorderid/:consumedorder',
    LIST: '/home/<USER>/list',
    PENDING_ORDERS: '/home/<USER>/pendingorders',
  },

  // Costing routes
  COSTING: {
    ADD: '/home/<USER>/add',
    LIST: '/home/<USER>/list',
    LIST_PRINT: '/home/<USER>',
    ESTIMATION: {
      ADD: '/home/<USER>/estimation/add',
      ADD_WITH_ID: '/home/<USER>/estimation/add/:id',
      LIST: '/home/<USER>/estimation/list',
      LIST_PRINT: '/home/<USER>',
      PRINT: '/home/<USER>/:id',
    },
    ORDER_COSTING_PRINT: '/home/<USER>/:id/:type',
    OVERHEAD: '/home/<USER>/overhead',
  },

  // Inventory routes
  INVENTORY: {
    OPENING_STOCK: '/home/<USER>',
    ADD_STOCK: '/home/<USER>',
    UPDATE_STOCK: '/home/<USER>/:id',
    KNITTING_DIVISION_STOCK: '/home/<USER>',
    STOCK_LIST: '/home/<USER>',
    STOCK_DETAILS: '/home/<USER>/:id',
    STOCK_LABEL_LIST: '/home/<USER>/list',
    STOCK_QUALITY_INSPECTION_LIST: '/home/<USER>',
    STOCK_QUALITY_INSPECTION: '/home/<USER>/:id',
    STOCK_INSPECTION_LIST: '/home/<USER>',
    STOCK_INSPECTION: '/home/<USER>/:id',
    STOCK_ALLOCATION_LIST: '/home/<USER>',
    STOCK_PRODUCT_ALLOCATION: '/home/<USER>/:id',
    STOCK_REJECTED_LIST: '/home/<USER>',
    STOCK_MANAGE_REJECTED_ITEMS: '/home/<USER>/:id',
  },

  // Gate routes
  GATE: {
    GATE_IN: '/home/<USER>',
    GATE_OUT: '/home/<USER>',
  },

  // Gate Pass routes
  GATE_PASS: {
    ISSUE: '/home/<USER>',
    PRINT: '/home/<USER>/:id',
  },

  // Out Pass routes
  OUT_PASS: {
    ADD: '/home/<USER>/add',
    LIST: '/home/<USER>/list',
    PRINT: '/home/<USER>/print/:id',
    PACKING_LIST_PRINT: '/home/<USER>/packinglistprint/:id',
  },

  // Quick Tools routes
  QUICK_TOOLS: {
    MEASUREMENT_CONVERSION: '/home/<USER>/measurementconversion-new',
    BARCODE_SCANNER: '/home/<USER>/barcodescanner',
  },

  // Barcode routes
  BARCODE: {
    LABEL_PRINT: '/home/<USER>/:StockProductId/:StockId/:ProductId/:NumberOfLabels/:action',
    LABEL_PRINT_ACTION: '/home/<USER>/:action',
  },

  // Standalone utility routes
  UTILITY: {
    LOGIN: '/login',
    LOGOUT: '/logout',
    UNAUTHORIZED: '/unauthorized',
    PRINT_PREVIEW: '/print-preview',
  },

  // Additional print routes
  PRINT: {
    SALES_ORDER: '/home/<USER>/:id',
    SALES_ORDER_EMAIL: '/home/<USER>/:id',
    INVOICE_PRINT: '/home/<USER>/:id',
    INVOICE_EMAIL: '/home/<USER>/:id',
    STOCK_LABEL_PRINT: '/home/<USER>/:id',
    CONSUMPTION_PRINT: '/home/<USER>/:id',
  },

  // IoT Devices routes
  IOT_DEVICES: {
    DIGITAL_WEIGHT_MACHINE: '/home/<USER>',
  },

  // Post Process routes (separate from Production)
  POST_PROCESS: {
    PRINT: '/home/<USER>/postprocessprint',
    EMBOSSING: '/home/<USER>/embossinglist',
    VACCUM: '/home/<USER>/vaccumlist',
    TUMBLING: '/home/<USER>/tumblinglist',
    LACQUER: '/home/<USER>/lacquerlist',
  },

  // Transport routes
  TRANSPORT: {
    LIST: '/home/<USER>',
  },

  // Supplier routes (separate from Master)
  SUPPLIER: {
    LIST: '/home/<USER>',
  },

  // Customer routes
  CUSTOMER: {
    LIST: '/home/<USER>',
  },

  // Product routes (separate from Master)
  PRODUCT: {
    LIST: '/home/<USER>',
    CATEGORY: '/home/<USER>',
    FIRST_SUB_CATEGORY: '/home/<USER>',
    SEC_SUB_CATEGORY: '/home/<USER>',
    TRANSFER: '/home/<USER>/producttransfer',
  },

  // Master routes
  MASTER: {
    GENERAL: {
      BRANCH: '/home/<USER>',
      BANK_DETAILS: '/home/<USER>',
      DEPARTMENT: '/home/<USER>',
      STORE: '/home/<USER>',
      RACK: '/home/<USER>',
      TAG: '/home/<USER>',
      PAYMENT_TERM: '/home/<USER>',
      DELIVERY_TERM: '/home/<USER>',
      MEASUREMENT_UNIT: '/home/<USER>',
      MEASUREMENT_CONVERSION: '/home/<USER>',
      OUT_PASS_PURPOSE: '/home/<USER>',
    },
    PRODUCTION: {
      DOWNTIME_REASON: '/home/<USER>/downtimereason',
      DESIGNATION: '/home/<USER>',
      FACTORY_WORKERS: '/home/<USER>',
      PACKAGING_TYPE: '/home/<USER>',
      COLOR: '/home/<USER>',
      GRAIN: '/home/<USER>',
      THICKNESS: '/home/<USER>',
      WIDTH: '/home/<USER>',
    },
  },

  // Admin routes
  ADMIN: {
    USERS: '/home/<USER>/users',
    OPERATIONS: '/home/<USER>/operations',
    ROLE_MASTER: '/home/<USER>/rolemaster',
    GENERAL_CONFIG: '/home/<USER>/generalconfig',
    USERS_LOGIN_HISTORY: '/home/<USER>/usersloginhistory',
    VALIDATION_RULES: '/home/<USER>/validationrules',
  },

  // Notification routes
  NOTIFICATION: {
    PRODUCTION_STAGES_LIST: '/home/<USER>',
    TYPE_CONFIGURATION: '/home/<USER>',
    GROUP_LIST: '/home/<USER>',
    WHATSAPP_TEMPLATE_LIST: '/home/<USER>',
    WHATSAPP_CONFIG_LIST: '/home/<USER>',
    EMAIL_GROUP_MAPPING: '/home/<USER>',
  },
} as const;

// Menu structure configuration
export const MENU_STRUCTURE: MenuStructure[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'dashboard',
    permission: { module: 'Dashboard' },
    openMapKey: 'sub1',
    dataLabel: 'Dashboard',
    children: [
      {
        path: ROUTES.DASHBOARD.WELCOME,
        label: 'Welcome',
        permission: { module: 'Dashboard - Welcome', responsibility: 'View' }
      },
      {
        path: ROUTES.DASHBOARD.REPORTING,
        label: 'Reporting Dashboard',
        permission: { module: 'Reporting Dashboard', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: 'bar-chart',
    permission: { module: 'Reports' },
    openMapKey: 'sub2',
    dataLabel: 'Reports',
    children: [
      {
        path: '',
        label: 'Sales Reports',
        icon: 'line-chart',
        permission: { module: 'Reports - Sales' },
        children: [
          {
            path: ROUTES.REPORTS.SALES.SALES_REPORT,
            label: 'Sales Report',
            permission: { module: 'Reports - Sales' }
          },
          {
            path: ROUTES.REPORTS.SALES.SALES_ORDER_TRAILS,
            label: 'Sales Order Trails',
            permission: { module: 'Reports - Sales Order Trails' }
          }
        ]
      },
      {
        path: '',
        label: 'Materials Stock Reports',
        icon: 'bar-chart',
        children: [
          {
            path: ROUTES.REPORTS.STOCK.STOCK_AVAILABILITY,
            label: 'Stock Availability',
            permission: { module: 'Reports - Stock Availability' }
          },
          {
            path: ROUTES.REPORTS.STOCK.CATEGORY_WISE_STOCK,
            label: 'Category Wise Stock',
            permission: { module: 'Reports - Category Wise Stock' }
          },
          {
            path: ROUTES.REPORTS.STOCK.STOCK_CONSUMPTION,
            label: 'Stock Consumption',
            permission: { module: 'Reports - Stock Consumption' }
          },
          {
            path: ROUTES.REPORTS.STOCK.STOCK_REPORT,
            label: 'Stock',
            permission: { module: 'Reports - Stock' }
          },
          {
            path: ROUTES.REPORTS.STOCK.STORE_WISE_STOCK,
            label: 'Store Wise Stock',
            permission: { module: 'Reports - Store Wise Stock' }
          },
          {
            path: ROUTES.REPORTS.STOCK.PRODUCT_WISE_STOCK,
            label: 'Product Wise Stock',
            permission: { module: 'Reports - Product Wise Stock' }
          },
          {
            path: ROUTES.REPORTS.STOCK.PRODUCT_STOCK_HISTORY,
            label: 'Product Stock History',
            permission: { module: 'Reports - Product Stock History' }
          }
        ]
      },
      {
        path: '',
        label: 'Product Stock Reports',
        icon: 'bar-chart',
        children: [
          {
            path: ROUTES.REPORTS.STOCK.PRODUCT_STOCK_SUMMARY,
            label: 'Product Stock Summary',
            permission: { module: 'Reports - Product Stock Summary' }
          }
        ]
      },
      {
        path: '',
        label: 'Purchase Reports',
        icon: 'bar-chart',
        children: [
          {
            path: ROUTES.REPORTS.PURCHASE.PURCHASE_REPORT,
            label: 'Purchase',
            permission: { module: 'Reports - Purchase' }
          },
          {
            path: ROUTES.REPORTS.PURCHASE.SUPPLIER_PRODUCT_MAPPING,
            label: 'Supplier Product Mapping',
            permission: { module: 'Reports - Supplier Product Mapping' }
          }
        ]
      },
      {
        path: '',
        label: 'Production Reports',
        icon: 'bar-chart',
        children: [
          {
            path: ROUTES.REPORTS.PRODUCTION.PRODUCTION_PLANNING,
            label: 'Production Planning',
            permission: { module: 'Reports - Production Planning' }
          },
          {
            path: ROUTES.REPORTS.PRODUCTION.PRODUCTION_STATUS,
            label: 'Production Status',
            permission: { module: 'Reports - Production Status' }
          },
          {
            path: ROUTES.REPORTS.PRODUCTION.PRODUCTION_HEARTBEAT,
            label: 'Production Heartbeat',
            permission: { module: 'Reports - Manufacturing Heartbeat' }
          },
          {
            path: ROUTES.REPORTS.PRODUCTION.PASTE_CONSUMPTION,
            label: 'Paste Consumption',
            permission: { module: 'Reports - Paste Consumption' }
          },
          {
            path: ROUTES.REPORTS.PRODUCTION.POST_PROCESS,
            label: 'Post Process Report',
            permission: { module: 'Reports - Post Process' }
          },
          {
            path: ROUTES.REPORTS.PRODUCTION.YIELD,
            label: 'Yield',
            permission: { module: 'Reports - Yield' }
          },
          {
            path: ROUTES.REPORTS.PRODUCTION.WASTAGE,
            label: 'Wastage Report',
            permission: { module: 'Reports - Wastage' }
          }
        ]
      }
    ]
  },
  {
    id: 'sales',
    label: 'Sales',
    icon: 'fund',
    permission: { module: 'Sales' },
    openMapKey: 'sub3',
    dataLabel: 'Sales',
    children: [
      {
        path: '',
        label: 'Sales Order',
        icon: 'solution',
        permission: { module: 'Sales - Sales Order' },
        children: [
          {
            path: ROUTES.SALES.ORDER.ADD,
            label: 'Add',
            permission: { module: 'Sales - Sales Order', responsibility: 'Add' }
          },
          {
            path: ROUTES.SALES.ORDER.LIST,
            label: 'List',
            permission: { module: 'Sales - Sales Order', responsibility: 'View' },
            children: [
              {
                path: ROUTES.SALES.ORDER.EDIT,
                label: 'Edit',
                permission: { module: 'Sales - Sales Order', responsibility: 'Edit' }
              }
            ]
          }
        ]
      },
      {
        path: '',
        label: 'Proforma Invoice',
        icon: 'file-text',
        permission: { module: 'Sales - Proforma' },
        children: [
          {
            path: ROUTES.SALES.PROFORMA.LIST,
            label: 'List',
            permission: { module: 'Sales - Proforma', responsibility: 'View' },
            children: [
              {
                path: ROUTES.SALES.PROFORMA.EDIT,
                label: 'Edit',
                permission: { module: 'Sales - Proforma', responsibility: 'Edit' }
              }
            ]
          },
          {
            path: ROUTES.SALES.PROFORMA.ADD,
            label: 'Add',
            permission: { module: 'Sales - Proforma', responsibility: 'Add' }
          }
        ]
      }
    ]
  },
  {
    id: 'demand',
    label: 'Demand',
    icon: 'alert',
    permission: { module: 'Demand' },
    openMapKey: 'sub4',
    dataLabel: 'Demand',
    children: [
      {
        path: ROUTES.DEMAND.LIST,
        label: 'List',
        permission: { module: 'Demand', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'purchase-order',
    label: 'Purchase Order',
    icon: 'shopping',
    permission: { module: 'Purchase Order' },
    openMapKey: 'sub5',
    dataLabel: 'Purchase',
    children: [
      {
        path: ROUTES.PURCHASE_ORDER.LIST,
        label: 'List',
        permission: { module: 'Purchase Order', responsibility: 'View' },
        children: [
          {
            path: ROUTES.PURCHASE_ORDER.EDIT,
            label: 'Edit',
            permission: { module: 'Purchase Order', responsibility: 'Edit' }
          }
        ]
      },
      {
        path: ROUTES.PURCHASE_ORDER.ADD,
        label: 'Add',
        permission: { module: 'Purchase Order', responsibility: 'Add' }
      },
      {
        path: ROUTES.PURCHASE_ORDER.INVOICES,
        label: 'Invoices',
        permission: { module: 'Invoice', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'production',
    label: 'Production',
    icon: 'build',
    permission: { module: 'Production' },
    openMapKey: 'sub6',
    dataLabel: 'Production',
    children: [
      {
        path: '',
        label: 'Formulation Code',
        icon: 'calculator',
        permission: { module: 'Production - Formulation Code' },
        children: [
          {
            path: ROUTES.PRODUCTION.FORMULATION_CODE.ADD,
            label: 'Add',
            permission: { module: 'Production - Formulation Code', responsibility: 'Add' }
          },
          {
            path: ROUTES.PRODUCTION.FORMULATION_CODE.LIST,
            label: 'List',
            permission: { module: 'Production - Formulation Code', responsibility: 'View' },
            children: [
              {
                path: ROUTES.PRODUCTION.FORMULATION_CODE.EDIT,
                label: 'Edit',
                permission: { module: 'Production - Formulation Code', responsibility: 'Edit' }
              }
            ]
          }
        ]
      },
      {
        path: '',
        label: 'Pigment MB',
        icon: 'bg-colors',
        permission: { module: 'Production - Pigment MB' },
        children: [
          {
            path: ROUTES.PRODUCTION.PIGMENT_MB.ADD,
            label: 'Add',
            permission: { module: 'Production - Pigment MB', responsibility: 'Add' }
          }
        ]
      },
      {
        path: '',
        label: 'Mixing',
        icon: 'experiment',
        permission: { module: 'Production - Mixing' },
        children: [
          {
            path: ROUTES.PRODUCTION.MIXING.ADD,
            label: 'Add',
            permission: { module: 'Production - Mixing', responsibility: 'Add' }
          },
          {
            path: ROUTES.PRODUCTION.MIXING.LIST,
            label: 'List',
            permission: { module: 'Production - Mixing', responsibility: 'View' }
          }
        ]
      },
      {
        path: ROUTES.PRODUCTION.WORKPLAN,
        label: 'WorkPlan List',
        icon: 'calendar',
        permission: { module: 'Production - Workplan List' }
      },
      {
        path: ROUTES.PRODUCTION.START_PRODUCTION,
        label: 'Start Production',
        icon: 'play-circle',
        permission: { module: 'Production - Start Production' }
      },
      {
        path: '',
        label: 'Jumbo Master',
        icon: 'file',
        permission: { module: 'Production - Jumbo Master' },
        children: [
          {
            path: ROUTES.PRODUCTION.JUMBO.ADD,
            label: 'Jumbo Add',
            permission: { module: 'Production - Jumbo Master', responsibility: 'Add' }
          },
          {
            path: ROUTES.PRODUCTION.JUMBO.LIST,
            label: 'Jumbo List',
            permission: { module: 'Production - Jumbo Master', responsibility: 'View' }
          }
        ]
      },
      {
        path: '',
        label: 'Post Process',
        icon: 'deployment-unit',
        permission: { module: 'Production - Post Process' },
        children: [
          {
            path: ROUTES.PRODUCTION.POST_PROCESS.ADD,
            label: 'Add',
            permission: { module: 'Production - Post Process', responsibility: 'Add' }
          }
        ]
      },
      {
        path: '',
        label: 'Final Inspection',
        icon: 'check-circle',
        permission: { module: 'Production - Final Inspection' },
        children: [
          {
            path: ROUTES.PRODUCTION.FINAL_INSPECTION.ADD,
            label: 'Add',
            permission: { module: 'Production - Final Inspection', responsibility: 'Add' }
          },
          {
            path: ROUTES.PRODUCTION.FINAL_INSPECTION.LIST,
            label: 'List',
            permission: { module: 'Production - Final Inspection', responsibility: 'View' }
          }
        ]
      },
      {
        path: ROUTES.PRODUCTION.DOWNTIME,
        label: 'Downtime',
        icon: 'arrow-down',
        permission: { module: 'Production - Downtime' }
      },
      {
        path: ROUTES.PRODUCTION.ACTIVITY_LOG,
        label: 'Activity Log',
        icon: 'unordered-list',
        permission: { module: 'Production - ActivityLog' }
      }
    ]
  }
];
