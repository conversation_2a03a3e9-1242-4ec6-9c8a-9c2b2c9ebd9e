/**
 * Extended Route Configuration - Continuation of menu structures
 * This file contains the remaining menu structures for the PMS application
 */

import { MenuStructure, ROUTES } from './route-config';

export const EXTENDED_MENU_STRUCTURE: MenuStructure[] = [
  {
    id: 'dispatch',
    label: 'Dispatch',
    icon: 'car',
    permission: { module: 'Dispatch' },
    openMapKey: 'sub7',
    dataLabel: 'Dispatch',
    children: [
      {
        path: '',
        label: 'Packaging',
        icon: 'dropbox',
        permission: { module: 'Dispatch - Packaging' },
        children: [
          {
            path: ROUTES.DISPATCH.PACKAGING.LIST,
            label: 'List',
            permission: { module: 'Dispatch - Packaging', responsibility: 'View' }
          },
          {
            path: ROUTES.DISPATCH.PACKAGING.ADD,
            label: 'Add',
            permission: { module: 'Dispatch - Packaging', responsibility: 'Add' }
          },
          {
            path: ROUTES.DISPATCH.PACKAGING.EDIT,
            label: 'Edit',
            permission: { module: 'Dispatch - Packaging', responsibility: 'Edit' }
          },
          {
            path: ROUTES.DISPATCH.PACKAGING.PRINT,
            label: 'Print',
            permission: { module: 'Dispatch - Packaging', responsibility: 'Print' }
          },
          {
            path: ROUTES.DISPATCH.PACKAGING.EMAIL,
            label: 'Email',
            permission: { module: 'Dispatch - Packaging', responsibility: 'Email' }
          }
        ]
      }
    ]
  },
  {
    id: 'issue',
    label: 'Issue',
    icon: 'inbox',
    permission: { module: 'Issue' },
    openMapKey: 'sub8',
    dataLabel: 'Issue',
    children: [
      {
        path: ROUTES.ISSUE.LIST,
        label: 'List',
        permission: { module: 'Issue', responsibility: 'View' }
      },
      {
        path: ROUTES.ISSUE.ADD,
        label: 'Add',
        permission: { module: 'Issue', responsibility: 'Add' }
      },
      {
        path: ROUTES.ISSUE.SLIP_PRINT,
        label: 'Print Slip',
        permission: { module: 'Issue', responsibility: 'Print' }
      }
    ]
  },
  {
    id: 'consumption',
    label: 'Consumption',
    icon: 'minus-circle',
    permission: { module: 'Consumption' },
    openMapKey: 'sub9',
    dataLabel: 'Consumption',
    children: [
      {
        path: ROUTES.CONSUMPTION.LIST,
        label: 'List',
        permission: { module: 'Consumption', responsibility: 'View' }
      },
      {
        path: ROUTES.CONSUMPTION.PENDING_ORDERS,
        label: 'Pending Orders List',
        permission: { module: 'Consumption', responsibility: 'View' }
      },
      {
        path: ROUTES.CONSUMPTION.ADD,
        label: 'Add',
        permission: { module: 'Consumption', responsibility: 'Add' }
      },
      {
        path: ROUTES.CONSUMPTION.ADD_WITH_PARAMS,
        label: 'Add with Parameters',
        permission: { module: 'Consumption', responsibility: 'Add' }
      }
    ]
  },
  {
    id: 'costing',
    label: 'Costing',
    icon: 'dollar',
    permission: { module: 'Costing' },
    openMapKey: 'sub10',
    dataLabel: 'Costing',
    children: [
      {
        path: ROUTES.COSTING.LIST,
        label: 'List',
        permission: { module: 'Costing', responsibility: 'View' }
      },
      {
        path: '',
        label: 'Estimation',
        icon: 'calculator',
        permission: { module: 'Costing - Estimation', responsibility: 'View' },
        children: [
          {
            path: ROUTES.COSTING.ESTIMATION.ADD,
            label: 'Add',
            permission: { module: 'Costing - Estimation', responsibility: 'Add' }
          },
          {
            path: ROUTES.COSTING.ESTIMATION.LIST,
            label: 'List',
            permission: { module: 'Costing - Estimation', responsibility: 'View' }
          }
        ]
      },
      {
        path: ROUTES.COSTING.OVERHEAD,
        label: 'Overhead',
        permission: { module: 'Costing - Overhead', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: 'database',
    permission: { module: 'Inventory' },
    openMapKey: 'sub11',
    dataLabel: 'Inventory',
    children: [
      {
        path: ROUTES.INVENTORY.OPENING_STOCK,
        label: 'Opening Stock',
        permission: { module: 'Inventory - Opening Stock' }
      },
      {
        path: ROUTES.INVENTORY.ADD_STOCK,
        label: 'Add Stock',
        permission: { module: 'Inventory - Add Stock' }
      },
      {
        path: ROUTES.INVENTORY.KNITTING_DIVISION_STOCK,
        label: 'Knitting Division Stock List',
        permission: { module: 'Inventory - Knitting Division Stock' }
      },
      {
        path: ROUTES.INVENTORY.STOCK_LIST,
        label: 'Stock Listing',
        permission: { module: 'Inventory - Stock List' },
        children: [
          {
            path: ROUTES.INVENTORY.UPDATE_STOCK,
            label: 'Update Stock',
            permission: { module: 'Inventory - Stock', responsibility: 'Edit' }
          },
          {
            path: ROUTES.INVENTORY.STOCK_DETAILS,
            label: 'Stock Details',
            permission: { module: 'Inventory - Stock', responsibility: 'View' }
          },
        ]
      },
      {
        path: ROUTES.INVENTORY.STOCK_LABEL_LIST,
        label: 'Stock Label Listing',
        permission: { module: 'Inventory - Stock Label List' }
      },
      {
        path: ROUTES.INVENTORY.STOCK_QUALITY_INSPECTION_LIST,
        label: 'Stock Quality Inspection',
        permission: { module: 'Inventory - Stock Quality Inspection' },
        children: [
          {
            path: ROUTES.INVENTORY.STOCK_QUALITY_INSPECTION,
            label: 'Manage Product Quality',
            permission: { module: 'Inventory - Stock Quality Inspection' }
          }
        ]
      },
      {
        path: ROUTES.INVENTORY.STOCK_INSPECTION_LIST,
        label: 'Stock Inspection List',
        permission: { module: 'Inventory - Inspection' },
        children: [
          {
            path: ROUTES.INVENTORY.STOCK_INSPECTION,
            label: 'Inspect Stock',
            permission: { module: 'Inventory - Inspection' }
          }
        ]
      },
      {
        path: ROUTES.INVENTORY.STOCK_ALLOCATION_LIST,
        label: 'Stock Allocation',
        permission: { module: 'Inventory - Allocation' },
        children: [
          {
            path: ROUTES.INVENTORY.STOCK_PRODUCT_ALLOCATION,
            label: 'Stock Product Allocation',
            permission: { module: 'Inventory - Allocation' }
          }
        ]
      },
      {
        path: ROUTES.INVENTORY.STOCK_REJECTED_LIST,
        label: 'Stock Rejected Items List',
        permission: { module: 'Inventory - Rejected' },
        children: [
          {
            path: ROUTES.INVENTORY.STOCK_MANAGE_REJECTED_ITEMS,
            label: 'Manage Rejected Items',
            permission: { module: 'Inventory - Rejected' }
          }
        ]
      },
      {
        path: ROUTES.INVENTORY.RETURN_STOCK,
        label: 'Return Stock',
        permission: { module: 'Inventory - Return Stock' }
      }

    ]
  },
  {
    id: 'gate',
    label: 'Gate In/Out',
    icon: 'swap',
    permission: { module: 'Gate' },
    openMapKey: 'sub12',
    dataLabel: 'Gate',
    children: [
      {
        path: ROUTES.GATE.GATE_IN,
        label: 'Gate In',
        permission: { module: 'Gate In' }
      },
      {
        path: ROUTES.GATE.GATE_OUT,
        label: 'Gate Out',
        permission: { module: 'Gate Out' }
      }
    ]
  },
  {
    id: 'gate-pass',
    label: 'Issue Gate Pass',
    icon: 'export',
    permission: { module: 'Issue Gate Pass' },
    openMapKey: 'sub13',
    dataLabel: 'Gate Pass',
    children: [
      {
        path: ROUTES.GATE_PASS.ISSUE,
        label: 'Issue Gate Pass',
        permission: { module: 'Issue Gate Pass' }
      },
      {
        path: ROUTES.GATE_PASS.PRINT,
        label: 'Print Gate Pass',
        permission: { module: 'Issue Gate Pass', responsibility: 'Print' }
      }
    ]
  },
  {
    id: 'out-pass',
    label: 'Out pass',
    icon: 'poweroff',
    permission: { module: 'Out Pass' },
    openMapKey: 'sub14',
    dataLabel: 'Out Pass',
    children: [
      {
        path: ROUTES.OUT_PASS.ADD,
        label: 'Add',
        permission: { module: 'Out Pass', responsibility: 'Add' }
      },
      {
        path: ROUTES.OUT_PASS.LIST,
        label: 'List',
        permission: { module: 'Out Pass', responsibility: 'View' }
      },
      {
        path: ROUTES.OUT_PASS.PRINT,
        label: 'Print',
        permission: { module: 'Out Pass', responsibility: 'Print' }
      },
      {
        path: ROUTES.OUT_PASS.PACKING_LIST_PRINT,
        label: 'Packing List Print',
        permission: { module: 'Out Pass', responsibility: 'Print' }
      }
    ]
  },
  {
    id: 'quick-tools',
    label: 'Quick Tools',
    icon: 'tool',
    permission: { module: 'QuickTools' },
    openMapKey: 'sub15',
    dataLabel: 'Tools',
    children: [
      {
        path: ROUTES.QUICK_TOOLS.MEASUREMENT_CONVERSION,
        label: 'Measurement Conversion',
        permission: { module: 'QuickTools - Measurement Conversion', responsibility: 'View' }
      },
      {
        path: ROUTES.QUICK_TOOLS.BARCODE_SCANNER,
        label: 'Barcode Scanner',
        permission: { module: 'QuickTools - Barcode Scanner', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'barcode',
    label: 'Barcode',
    icon: 'barcode',
    permission: { module: 'Barcode' },
    openMapKey: 'barcode',
    dataLabel: 'Barcode',
    children: [
      {
        path: ROUTES.BARCODE.LABEL_PRINT,
        label: 'Label Print',
        permission: { module: 'Barcode - Label Print', responsibility: 'Print' }
      },
      {
        path: ROUTES.BARCODE.LABEL_PRINT_ACTION,
        label: 'Label Print Action',
        permission: { module: 'Barcode - Label Print', responsibility: 'Print' }
      }
    ]
  },
  {
    id: 'iot-devices',
    label: 'IoT Devices',
    icon: 'cluster',
    permission: { module: 'IoT Devices' },
    openMapKey: 'sub16',
    dataLabel: 'IoT',
    children: [
      {
        path: ROUTES.IOT_DEVICES.DIGITAL_WEIGHT_MACHINE,
        label: 'Digital Weight Machine',
        permission: { module: 'IoT Devices - Digital Weight Machine', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'post-process',
    label: 'Post Process',
    icon: 'deployment-unit',
    permission: { module: 'Post Process' },
    openMapKey: 'sub17',
    dataLabel: 'Post',
    children: [
      {
        path: ROUTES.POST_PROCESS.PRINT,
        label: 'Print',
        permission: { module: 'Post Process - Print' }
      },
      {
        path: ROUTES.POST_PROCESS.EMBOSSING,
        label: 'Embossing',
        permission: { module: 'Post Process - Embossing' }
      },
      {
        path: ROUTES.POST_PROCESS.VACCUM,
        label: 'Vaccum',
        permission: { module: 'Post Process - Vaccum' }
      },
      {
        path: ROUTES.POST_PROCESS.TUMBLING,
        label: 'Tumbling',
        permission: { module: 'Post Process - Tumbling' }
      },
      {
        path: ROUTES.POST_PROCESS.LACQUER,
        label: 'Lacquer',
        permission: { module: 'Post Process - Lacquer' }
      }
    ]
  },
  {
    id: 'transport',
    label: 'Transport',
    icon: 'rocket',
    permission: { module: 'Transport' },
    openMapKey: 'sub18',
    dataLabel: 'Transport',
    children: [
      {
        path: ROUTES.TRANSPORT.LIST,
        label: 'Transport List',
        permission: { module: 'Transport', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'supplier',
    label: 'Supplier',
    icon: 'shop',
    permission: { module: 'Supplier' },
    openMapKey: 'sub19',
    dataLabel: 'Supplier',
    children: [
      {
        path: ROUTES.SUPPLIER.LIST,
        label: 'Supplier List',
        permission: { module: 'Supplier', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'customer',
    label: 'Customer',
    icon: 'user',
    permission: { module: 'Customer' },
    openMapKey: 'sub20',
    dataLabel: 'Customer',
    children: [
      {
        path: ROUTES.CUSTOMER.LIST,
        label: 'Customer List',
        permission: { module: 'Customer', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'product',
    label: 'Product',
    icon: 'tags',
    permission: { module: 'Product' },
    openMapKey: 'sub21',
    dataLabel: 'Product',
    children: [
      {
        path: ROUTES.PRODUCT.LIST,
        label: 'Product List',
        permission: { module: 'Product', responsibility: 'View' }
      },
      {
        path: ROUTES.PRODUCT.CATEGORY,
        label: 'Category',
        permission: { module: 'Product - Category' }
      },
      {
        path: ROUTES.PRODUCT.FIRST_SUB_CATEGORY,
        label: 'Sub Category',
        permission: { module: 'Product - Sub Category' }
      },
      {
        path: ROUTES.PRODUCT.SEC_SUB_CATEGORY,
        label: '2nd Sub Category',
        permission: { module: 'Product - 2nd SubCategory' }
      },
      {
        path: ROUTES.PRODUCT.TRANSFER,
        label: 'Transfer',
        permission: { module: 'Product - Transfer' }
      }
    ]
  }
];

// Helper function to combine all menu structures
export function getAllMenuStructures(): MenuStructure[] {
  // This will be imported and combined in the main route-config file
  return EXTENDED_MENU_STRUCTURE;
}
