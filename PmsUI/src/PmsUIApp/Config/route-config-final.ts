/**
 * Final Route Configuration - Master, Admin, and Notification menus
 * This file contains the final menu structures for the PMS application
 */

import { MenuStructure, ROUTES } from './route-config';

export const FINAL_MENU_STRUCTURE: MenuStructure[] = [
  {
    id: 'master',
    label: 'Master',
    icon: 'setting',
    permission: { module: 'Master' },
    openMapKey: 'sub17',
    dataLabel: 'Master',
    children: [
      {
        path: '',
        label: 'General',
        icon: 'setting',
        permission: { module: 'Master' },
        children: [
          {
            path: ROUTES.MASTER.GENERAL.BRANCH,
            label: 'Branch',
            permission: { module: 'Master - Branch', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.BANK_DETAILS,
            label: 'Bank Details',
            permission: { module: 'Master - BankDetails', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.DEPARTMENT,
            label: 'Department',
            permission: { module: 'Master - Department', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.TAG,
            label: 'Tag',
            permission: { module: 'Master - Tag', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.STORE,
            label: 'Stores',
            permission: { module: 'Master - Store', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.RACK,
            label: 'Rack',
            permission: { module: 'Master - Rack', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.MEASUREMENT_UNIT,
            label: 'Measurement Unit',
            permission: { module: 'Master - Unit', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.PAYMENT_TERM,
            label: 'Payment Term',
            permission: { module: 'Master - PaymentTerm', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.DELIVERY_TERM,
            label: 'Delivery Term',
            permission: { module: 'Master - Delivery Term', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.MEASUREMENT_CONVERSION,
            label: 'Measurement Conversion',
            permission: { module: 'Master - Conversion', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.OUT_PASS_PURPOSE,
            label: 'Out Pass Purpose',
            permission: { module: 'Master - Out Pass Purpose', responsibility: 'View' }
          }
        ]
      },
      {
        path: '',
        label: 'Production',
        icon: 'build',
        permission: { module: 'Master' },
        children: [
          {
            path: ROUTES.MASTER.PRODUCTION.DOWNTIME_REASON,
            label: 'Downtime Reason',
            permission: { module: 'Production Master - Downtime Reason', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCTION.DESIGNATION,
            label: 'Employee Designation',
            permission: { module: 'Master - Designation', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCTION.FACTORY_WORKERS,
            label: 'Factory Employee',
            permission: { module: 'Master - Factory Workers', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCTION.PACKAGING_TYPE,
            label: 'Packaging Type',
            permission: { module: 'Master - Packaging Type', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCTION.COLOR,
            label: 'Color',
            permission: { module: 'Master - Color', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCTION.GRAIN,
            label: 'Grain',
            permission: { module: 'Master - Grain', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCTION.THICKNESS,
            label: 'Thickness',
            permission: { module: 'Master - Thickness', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCTION.WIDTH,
            label: 'Width',
            permission: { module: 'Master - Width', responsibility: 'View' }
          }
        ]
      }
    ]
  },
  {
    id: 'admin',
    label: 'Admin',
    icon: 'user',
    permission: { module: 'Admin' },
    openMapKey: 'sub18',
    dataLabel: 'Admin',
    children: [
      {
        path: ROUTES.ADMIN.USERS,
        label: 'Manage Users',
        permission: { module: 'Admin - Users', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.OPERATIONS,
        label: 'Manage Operations',
        permission: { module: 'Admin - Operations', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.ROLE_MASTER,
        label: 'Manage Roles',
        permission: { module: 'Admin - RoleMaster', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.GENERAL_CONFIG,
        label: 'General Configuration',
        permission: { module: 'Admin - GeneralConfig', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.USERS_LOGIN_HISTORY,
        label: 'Users Login History',
        permission: { module: 'Admin - UsersLoginHistory', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.VALIDATION_RULES,
        label: 'Validation Rules Config',
        permission: { module: 'Admin - ValidationRules', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'notification',
    label: 'Notification',
    icon: 'notification',
    permission: { module: 'Notification' },
    openMapKey: 'sub19',
    dataLabel: 'Notification',
    children: [
      {
        path: ROUTES.NOTIFICATION.PRODUCTION_STAGES_LIST,
        label: 'Production Stages List',
        permission: { module: 'Notification - Production Stages', responsibility: 'View' }
      },
      {
        path: ROUTES.NOTIFICATION.TYPE_CONFIGURATION,
        label: 'Notification Type Config',
        permission: { module: 'Notification - Type Configuration', responsibility: 'View' }
      },
      {
        path: ROUTES.NOTIFICATION.GROUP_LIST,
        label: 'Notification Group List',
        permission: { module: 'Notification - Notification Group', responsibility: 'View' }
      },
      {
        path: ROUTES.NOTIFICATION.WHATSAPP_TEMPLATE_LIST,
        label: 'WhatsApp Template List',
        permission: { module: 'Notification - WhatsApp Template', responsibility: 'View' }
      },
      {
        path: ROUTES.NOTIFICATION.WHATSAPP_CONFIG_LIST,
        label: 'Whatsapp Config List',
        permission: { module: 'Notification - WhatsApp Configuration', responsibility: 'View' }
      },
      {
        path: ROUTES.NOTIFICATION.EMAIL_GROUP_MAPPING,
        label: 'Email Group Mapping',
        permission: { module: 'Notification - Email Group Mapping', responsibility: 'View' }
      }
    ]
  }
];

// Helper function to get final menu structures
export function getFinalMenuStructures(): MenuStructure[] {
  return FINAL_MENU_STRUCTURE;
}
