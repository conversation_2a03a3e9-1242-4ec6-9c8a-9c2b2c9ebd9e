/**
 * Route Helper Functions and Type-Safe Constants
 * 
 * This file provides utility functions and type-safe constants for consistent
 * route usage throughout the PMS application.
 */

import { ROUTES } from '../Config/route-config';

// Type definitions for route parameters
export interface RouteParams {
  [key: string]: string | number | boolean;
}

export interface QueryParams {
  [key: string]: string | number | boolean | string[] | number[] | boolean[];
}

/**
 * Route Helper Class
 * Provides type-safe methods for route construction and navigation
 */
export class RouteHelper {
  
  /**
   * Build route with parameters
   * @param route Base route path
   * @param params Route parameters to replace in path
   * @returns Complete route path with parameters
   */
  static buildRoute(route: string, params?: RouteParams): string {
    if (!params) return route;
    
    let builtRoute = route;
    Object.keys(params).forEach(key => {
      builtRoute = builtRoute.replace(`:${key}`, String(params[key]));
    });
    
    return builtRoute;
  }

  /**
   * Build route with query parameters
   * @param route Base route path
   * @param queryParams Query parameters to append
   * @returns Route path with query string
   */
  static buildRouteWithQuery(route: string, queryParams?: QueryParams): string {
    if (!queryParams || Object.keys(queryParams).length === 0) {
      return route;
    }

    const queryString = Object.keys(queryParams)
      .map(key => {
        const value = queryParams[key];
        if (Array.isArray(value)) {
          return value.map(v => `${encodeURIComponent(key)}=${encodeURIComponent(String(v))}`).join('&');
        }
        return `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`;
      })
      .join('&');

    return `${route}?${queryString}`;
  }

  /**
   * Build complete route with both path and query parameters
   * @param route Base route path
   * @param params Route parameters
   * @param queryParams Query parameters
   * @returns Complete route with parameters and query string
   */
  static buildCompleteRoute(route: string, params?: RouteParams, queryParams?: QueryParams): string {
    const routeWithParams = this.buildRoute(route, params);
    return this.buildRouteWithQuery(routeWithParams, queryParams);
  }

  /**
   * Extract route parameters from current route
   * @param routePattern Route pattern with parameter placeholders
   * @param actualRoute Actual route path
   * @returns Extracted parameters object
   */
  static extractRouteParams(routePattern: string, actualRoute: string): RouteParams {
    const patternParts = routePattern.split('/');
    const routeParts = actualRoute.split('?')[0].split('/'); // Remove query params
    const params: RouteParams = {};

    if (patternParts.length !== routeParts.length) {
      return params;
    }

    patternParts.forEach((part, index) => {
      if (part.startsWith(':')) {
        const paramName = part.substring(1);
        params[paramName] = routeParts[index];
      }
    });

    return params;
  }

  /**
   * Check if route matches pattern
   * @param pattern Route pattern
   * @param route Actual route
   * @returns True if route matches pattern
   */
  static matchesPattern(pattern: string, route: string): boolean {
    const patternParts = pattern.split('/');
    const routeParts = route.split('?')[0].split('/');

    if (patternParts.length !== routeParts.length) {
      return false;
    }

    return patternParts.every((part, index) => {
      return part.startsWith(':') || part === routeParts[index];
    });
  }

  /**
   * Get parent route path
   * @param route Current route path
   * @returns Parent route path
   */
  static getParentRoute(route: string): string {
    const parts = route.split('/');
    if (parts.length <= 2) return '/'; // Root or single level
    
    return parts.slice(0, -1).join('/');
  }

  /**
   * Check if route is child of parent route
   * @param childRoute Child route path
   * @param parentRoute Parent route path
   * @returns True if child route is under parent route
   */
  static isChildRoute(childRoute: string, parentRoute: string): boolean {
    return childRoute.startsWith(parentRoute) && childRoute !== parentRoute;
  }

  /**
   * Get route depth (number of path segments)
   * @param route Route path
   * @returns Number of path segments
   */
  static getRouteDepth(route: string): number {
    return route.split('/').filter(segment => segment.length > 0).length;
  }
}

/**
 * Type-safe route constants with helper methods
 */
export const TypeSafeRoutes = {
  // Dashboard routes
  Dashboard: {
    welcome: () => ROUTES.DASHBOARD.WELCOME,
    reporting: () => ROUTES.DASHBOARD.REPORTING,
  },

  // Reports routes
  Reports: {
    Sales: {
      salesReport: () => ROUTES.REPORTS.SALES.SALES_REPORT,
      salesOrderTrails: (id?: string) => 
        id ? RouteHelper.buildRoute(`${ROUTES.REPORTS.SALES.SALES_ORDER_TRAILS}/:id`, { id }) 
            : ROUTES.REPORTS.SALES.SALES_ORDER_TRAILS,
    },
    Stock: {
      stockAvailability: (productId?: string) => 
        productId ? RouteHelper.buildRoute(`${ROUTES.REPORTS.STOCK.STOCK_AVAILABILITY}/:productid`, { productid: productId })
                  : ROUTES.REPORTS.STOCK.STOCK_AVAILABILITY,
      categoryWiseStock: () => ROUTES.REPORTS.STOCK.CATEGORY_WISE_STOCK,
      stockConsumption: () => ROUTES.REPORTS.STOCK.STOCK_CONSUMPTION,
      stockReport: () => ROUTES.REPORTS.STOCK.STOCK_REPORT,
      storeWiseStock: () => ROUTES.REPORTS.STOCK.STORE_WISE_STOCK,
      productWiseStock: () => ROUTES.REPORTS.STOCK.PRODUCT_WISE_STOCK,
      productStockHistory: () => ROUTES.REPORTS.STOCK.PRODUCT_STOCK_HISTORY,
      productStockSummary: () => ROUTES.REPORTS.STOCK.PRODUCT_STOCK_SUMMARY,
    },
    Purchase: {
      purchaseReport: () => ROUTES.REPORTS.PURCHASE.PURCHASE_REPORT,
      supplierProductMapping: () => ROUTES.REPORTS.PURCHASE.SUPPLIER_PRODUCT_MAPPING,
    },
    Production: {
      productionPlanning: () => ROUTES.REPORTS.PRODUCTION.PRODUCTION_PLANNING,
      productionStatus: () => ROUTES.REPORTS.PRODUCTION.PRODUCTION_STATUS,
      productionHeartbeat: () => ROUTES.REPORTS.PRODUCTION.PRODUCTION_HEARTBEAT,
      pasteConsumption: () => ROUTES.REPORTS.PRODUCTION.PASTE_CONSUMPTION,
      postProcess: () => ROUTES.REPORTS.PRODUCTION.POST_PROCESS,
      yield: () => ROUTES.REPORTS.PRODUCTION.YIELD,
      wastage: () => ROUTES.REPORTS.PRODUCTION.WASTAGE,
    },
  },

  // Sales routes
  Sales: {
    Order: {
      add: () => ROUTES.SALES.ORDER.ADD,
      list: () => ROUTES.SALES.ORDER.LIST,
    },
    Proforma: {
      add: () => ROUTES.SALES.PROFORMA.ADD,
      list: () => ROUTES.SALES.PROFORMA.LIST,
    },
  },

  // Other main routes
  Demand: {
    list: () => ROUTES.DEMAND.LIST,
  },

  PurchaseOrder: {
    add: () => ROUTES.PURCHASE_ORDER.ADD,
    list: () => ROUTES.PURCHASE_ORDER.LIST,
    invoices: () => ROUTES.PURCHASE_ORDER.INVOICES,
  },

  Production: {
    FormulationCode: {
      add: () => ROUTES.PRODUCTION.FORMULATION_CODE.ADD,
      list: () => ROUTES.PRODUCTION.FORMULATION_CODE.LIST,
    },
    PigmentMB: {
      add: () => ROUTES.PRODUCTION.PIGMENT_MB.ADD,
    },
    Mixing: {
      add: () => ROUTES.PRODUCTION.MIXING.ADD,
      list: () => ROUTES.PRODUCTION.MIXING.LIST,
    },
    workplan: () => ROUTES.PRODUCTION.WORKPLAN,
    startProduction: () => ROUTES.PRODUCTION.START_PRODUCTION,
    Jumbo: {
      add: () => ROUTES.PRODUCTION.JUMBO.ADD,
      list: () => ROUTES.PRODUCTION.JUMBO.LIST,
    },
    postProcess: () => ROUTES.PRODUCTION.POST_PROCESS,
    FinalInspection: {
      add: () => ROUTES.PRODUCTION.FINAL_INSPECTION.ADD,
      list: () => ROUTES.PRODUCTION.FINAL_INSPECTION.LIST,
    },
    downtime: () => ROUTES.PRODUCTION.DOWNTIME,
    activityLog: () => ROUTES.PRODUCTION.ACTIVITY_LOG,
  },

  // Additional routes can be added here following the same pattern
} as const;

/**
 * Navigation helper functions
 */
export class NavigationHelper {
  /**
   * Get breadcrumb-friendly route segments
   * @param route Full route path
   * @returns Array of route segments for breadcrumb display
   */
  static getRouteSegments(route: string): string[] {
    return route.split('/').filter(segment => segment.length > 0);
  }

  /**
   * Convert route to display name
   * @param routeSegment Route segment
   * @returns Human-readable display name
   */
  static routeToDisplayName(routeSegment: string): string {
    return routeSegment
      .split(/[-_]/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Check if current route is active
   * @param currentRoute Current route path
   * @param targetRoute Target route path
   * @param exact Whether to match exactly or check if current route starts with target
   * @returns True if route is active
   */
  static isRouteActive(currentRoute: string, targetRoute: string, exact: boolean = false): boolean {
    const cleanCurrent = currentRoute.split('?')[0];
    const cleanTarget = targetRoute.split('?')[0];
    
    return exact ? cleanCurrent === cleanTarget : cleanCurrent.startsWith(cleanTarget);
  }
}
