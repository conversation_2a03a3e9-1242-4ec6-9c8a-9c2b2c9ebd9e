import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HomeComponent } from './Home/Home.component';
import { NZ_I18N } from 'ng-zorro-antd/i18n';
import { PmsUINzAntModule } from './PmsUIApp-NzAnt.module';
import { en_US } from 'ng-zorro-antd/i18n';
import { DatePipe, registerLocaleData } from '@angular/common';
import en from '@angular/common/locales/en';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { PmsUIAppRoutingModule } from './PmsUIApp-routing.module';
import { IconsProviderModule } from './icons-provider.module';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { MyInterceptor } from './Services/HttpInterceptorService';
import { AlertMessageService } from './Services/AlertMessageService';
import { StockListComponent } from './InventoryManagement/StockList/StockList.component';
import { StockDetailsComponent } from './InventoryManagement/StockDetails/StockDetails.component';
import { StockInspectionComponent } from './InventoryManagement/StockInspection/StockInspection.component';
import { StockProductAllocationComponent } from './InventoryManagement/StockProductAllocation/StockProductAllocation.component';
import { StockManageRejectedItemsComponent } from './InventoryManagement/StockManageRejectedItems/StockManageRejectedItems.component';
import { AddStockComponent } from './InventoryManagement/AddStock/AddStock.component';
import { ProductListComponent } from './Product/ProductList/ProductList.component';
import { AuthComponent } from './Authentication/auth/auth.component';
import { LogoutComponent } from './Authentication/logout/logout.component';
import { IndexComponent } from './index/index.component';
import { SupplierListComponent } from './SupplierManagement/Supplier/supplier-list/supplier-list.component';
import { GateInComponent } from './GateManagement/GateIn/GateIn.component';
import { GatePassComponent } from './GateManagement/GatePass/GatePass.component';
import { GateOutComponent } from './GateManagement/GateOut/GateOut.component';
import { TransportManagementComponent } from './transportManagement/transportManagement.component';
import {
  BLOB_STORAGE_TOKEN,
  IAzureStorage,
  IBlobStorage,
} from './azure-storage/azureStorage';
import { BlobStorageService } from './azure-storage/blob-storage.service';
import { NzQRCodeModule } from 'ng-zorro-antd/qr-code';
import { BranchComponent } from './Master/branch/branch.component';
import { DepartmentComponent } from './Master/department/department.component';
import { TagComponent } from './Master/tag/tag.component';
import { StoreComponent } from './Master/store/store.component';
import { RackComponent } from './Master/rack/rack.component';
import { ProductCategoryComponent } from './Product/product-category/product-category.component';
import { ProductFirstSubCategoryComponent } from './Product/product-first-sub-category/product-first-sub-category.component';
import { ProductSecSubCategoryComponent } from './Product/product-sec-sub-category/product-sec-sub-category.component';
import { PaymentTermComponent } from './Master/payment-term/payment-term.component';
import { DeliveryTermComponent } from './Master/delivery-term/delivery-term.component';
import { PurchaseorderComponent } from './PurchaseOrder/Add/purchaseorder.component';
import { POListComponent } from './PurchaseOrder/List/polist.component';
import { MeasurementConversionComponent } from './Master/measurement-conversion/measurement-conversion.component';
import { ColorComponent } from './Master/color/color.component';
import { ElementComponent } from './Master/element/element.component';
import { GrainComponent } from './Master/grain/grain.component';
import { CustomerListComponent } from './CustomerManagement/customer-list/customer-list.component';

import { IssueComponent } from './Issue/Add/Issue.component';

import { IssueListComponent } from './Issue/List/Issuelist.component';
import { MsalModule, MSAL_INSTANCE, MsalService, MsalGuardConfiguration, MSAL_GUARD_CONFIG, MsalGuard, MSAL_INTERCEPTOR_CONFIG, MsalBroadcastService, MsalInterceptorConfiguration, MsalInterceptor, MsalRedirectComponent } from '@azure/msal-angular';
import {
  BrowserCacheLocation,
  InteractionType,
  IPublicClientApplication,
  PublicClientApplication,
} from '@azure/msal-browser';
import { ActiveX } from '../environments/environment';
import { AdminComponent } from './Admin/Users/<USER>';
import { AppConfigService } from './Authentication/configService';
import { WINDOW_PROVIDERS } from '../window.providers';
import { CostingComponent } from './Costing/Add/Costing.component';
import { CostingListComponent } from './Costing/List/Costinglist.component';
import { MixingComponent } from './Mixing/Add/mixing.component';
import { MixingListComponent } from './Mixing/List/mixinglist.component';
// DatetimeConverter and CallbackPipe moved to SharedModule
import { SharedModule } from './Shared/shared.module';

import { GatepassprintComponent } from './GateManagement/GatePassPrint/gatepassprint.component';
import { POprintComponent } from './PurchaseOrder/Print/poprint.component';
import { POEmailComponent } from './PurchaseOrder/Email/poemail.component';
import { RoundOff } from './Services/RoundOff.pipe';
import { EmbossingListComponent } from './Production/Post Process/embossing-list/embossing-list.component';
import { TumblingListComponent } from './Production/Post Process/tumbling-list/tumbling-list.component';
import { VaccumListComponent } from './Production/Post Process/vaccum-list/vaccum-list.component';
import { LacquerListComponent } from './Production/Post Process/lacquer-list/lacquer-list.component';
import { PostprocessprintComponent } from './Production/Post Process/postprocessprint/postprocessprint.component';
import { MixingprintComponent } from './Production/mixingprint/mixingprint.component';
import { ProcessprintComponent } from './Production/processprint/processprint.component';

import { OpeningStockComponent } from './InventoryManagement/OpeningStock/OpeningStock.component';

import { StartscreenComponent } from './startscreen/startscreen.component';

// import { NewmixingComponent } from './Production/newmixing/newmixing.component';
import { ThicknessComponent } from './Master/thickness/thickness.component';
import { LoaderComponent } from './Loader/loader.component';
import { LoadingService } from './Services/loadingService';
import { WidthComponent } from './Master/width/width.component';

import { ConsumptionListComponent } from './Consumption/List/consumptionlist.component';
import { ConsumptionComponent } from './Consumption/Add/consumption.component';
import { ApiHttpService } from './Services/ApiHttpService';
import { ResponsibilityMasterComponent } from './Admin/ResponsibilityMaster/ResponsibilityMaster.component';
import { RolesMasterComponent } from './Admin/RolesMaster/RolesMaster.component';

import { PostprocessComponent } from './Production/Post Process/PostProcess/postprocess.component';

import { FinalInspectionComponent } from './Production/FinalInspection/Add/FinalInspection.component';
import { OutPassPrintComponent } from './OutPass/outpassPrint/outpassprint.component';
import { OutPassPackingListPrintComponent } from './OutPass/outpassPackingListPrint/outpasspackinglistprint.component';
import { OutPassAddComponent } from './OutPass/Add/outpassAdd.component';
import { OutPassListComponent } from './OutPass/list/outpasslist.component';
import { OutPassBarcodeSelectorComponent } from './OutPass/components/OutPassBarcodeSelector/OutPassBarcodeSelector.component';
import { ValidationRulesConfigComponent } from './OutPass/components/ValidationRulesConfig/ValidationRulesConfig.component';

import { StartProductionComponent } from './Production/StartProduction/start-production.component';

import { WorkplanListComponent } from './Production/workplan-list/workplan-list.component';
import { InspectionPrintComponent } from './Production/WorkPlan/inspectionprint/inspectionprint.component';
import { JumboListComponent } from './Production/WorkPlan/Jumbolist/jumbolist.componrnt';
import { JumboMasterComponent } from './Production/WorkPlan/JumboMaster/jumbomaster.component';

import { FormulationCodeListComponent } from './Production/FormulationCode/List/formulation-list.component';
import { FormulationcodeComponent } from './Production/FormulationCode/Add/newformulationcode.component';
import { MeasurementUnitComponent } from './Master/measurement-unit/measurement-unit.component';
import { proformaAddComponent } from './SalesOrder/proformaInvoice/Add/proformaAdd.component';
import { proformaListComponent } from './SalesOrder/proformaInvoice/List/proformalist.component';
import { proformaPrintComponent } from './SalesOrder/proformaInvoice/Print/proformaPrint.component';
import { SaleslistComponent } from './SalesOrder/SalesOrder/List/saleslist.component';
import { PresalesorderComponent } from './SalesOrder/SalesOrder/Add/presalesorder.component';
import { BankDetailsComponent } from './Master/BankDetails/bankDetails.component';
import { UsersLoginHistoryComponent } from './Admin/UsersLoginHistory/UsersLoginHistory.component';
import { DemandPopService } from './Demand/services/DemandPopService';
import { DemandComponent } from './Demand/demand.component';
import { DemandListComponent } from './PurchaseOrder/DemandList/DemandList.component';
import { InvoiceListComponent } from './PurchaseOrder/InvoiceList/invoice-list.component';
import { InvoiceDetailsComponent } from './InventoryManagement/InvoiceDetails/InvoiceDetails.component';


import { InspectionAllPrintComponent } from './Production/WorkPlan/inspectionallprint/inspectionallprint.component';
import { PackagingComponent } from './Dispatch/Packaging/Add/Packaging.component';
import { PackagingListComponent } from './Dispatch/Packaging/List/PackagingList.component';
import { PackingListPrintComponent } from './Dispatch/Packaging/Print/PackingListPrint.component';
import { NgxPrintModule } from 'ngx-print';
import { MBFormulationComponent } from './Production/PigmentMB/Add/MBFormulation.component';
import { FinalInspectionList } from './Production/FinalInspection/List/FinalInspectionList.component';
import { ActivityLogComponent } from './Production/ActivityLog/ActivityLog.component';
import { LinkSaleOrderComponent } from './Production/Post Process/LinkSaleOrder/LinkSaleOrder.component';
import { UnauthorizedComponent } from './pages/unauthorized/unauthorized.component';

import { GeneralConfigurationComponent } from './Admin/GeneralConfiguration/GeneralConfiguration.component';
import { TimelineService } from './Services/TimelineService';
import { TimelineComponent } from './Features/Timeline/Timeline.component';
import { CostingListPrintComponent } from './Costing/CostingListPrint/CostingListPrint.component';
import { ConsumptionPendingListComponent } from './Consumption/PendingList/pendinglist.component';

import { InspectionDetailsPrintComponent } from './Production/WorkPlan/InspectionDetailsPrint/InspectionDetailsPrint.component';
import { PackagingListEmailComponent } from './Dispatch/Packaging/Email/PackagingListEmail.component';

import { DragDropModule } from '@angular/cdk/drag-drop';
import { SaleOrderCostingViewComponent } from './Costing/SaleOrderCostingView/SaleOrderCostingView.component';
import { SingleOrderCostingPrint } from './Costing/SingleOrderCostingPrint/SingleOrderCostingPrint.component';
import { SaleOrderCostingService } from './Costing/services/SaleOrderCostingService';
import { IssueSlipPrintComponent } from './Issue/IssueSlipPrint/issueslipprint.component';
import { FormulationPrintComponent } from './Production/FormulationCode/Print/formulationprint.component';
import { PostProcessOptionsComponent } from './SalesOrder/SalesOrder/post-process-options/post-process-options.component';
import { MeasurementConversionNewComponent } from './QuickTools/measurement-conversion-new/measurement-conversion-new.component';
import { FactoryWorkersComponent } from './Master/factoryworkers/factoryworkers.component';
import { DesignationComponent } from './Master/designation/designation.component';
import { OutPassPurposeComponent } from './Master/OutPassPurpose/outpasspurpose.component';
import { DigitalWeightComponent } from './IoTDevices/DigitalWeight/digital-weight/digital-weight.component';
import { KnittingDivisionStockListComponent } from './InventoryManagement/KnittingDivisionStockList/KnittingDivisionStockList.component';
import { NotificationComponent } from './Notification/ProductionStagesList/notification/notification.component';
import { NotificationTypeConfigurationComponent } from './Notification/NotificationTypeConfiguration/notification-type-configuration.component';
import { NotificationGroupComponent } from './Notification/NotificationGroup/notification-group/notification-group.component';
import { WhatsAppTemplateListComponent } from './Notification/whats-app-template-list/whats-app-template-list.component';
import { WhatsappConfigComponent } from './Notification/whatsapp-config/whatsapp-config.component';
import { EmailGroupMappingComponent } from './Notification/email-group-mapping/email-group-mapping.component';
import { PoTimelineComponent } from './Features/PoTimeLine/PoTimeline.component';
import { PoTimelineService } from './Services/PoTimeLineService';
import { CostEstimationComponent } from './Costing/Estimation/Add/costEstimationList.component';
import { PoDrawerService } from './Features/PoDrawerView/services/PoDrawerService';
import { PoDrawerViewComponent } from './Features/PoDrawerView/PoDrawerView.component';
import { SoDrawerComponent } from './Features/SoDrawer/SoDrawer.component';
import { SoDrawerService } from './Services/SoDrawerService';
import { PIDrawerViewComponent } from './Features/PIDrawerView/PIDrawerView.component';
import { PIDrawerService } from './Services/PIDrawerService';
import { CostEstimationListComponent } from './Costing/Estimation/List/costEstimationList.component';
import { EODrawerComponent } from './Features/EODrawer/EODrawer.component';
import { EODrawerService } from './Services/EODrawerService';
import { CostEstimationListPrintComponent } from './Costing/Estimation/PrintList/costEstimationListPrint.component';
import { CostEstimationPrintComponent } from './Costing/Estimation/Print/costEstimationPrint.component';

import { OverheadCostComponent } from './Costing/Overhead/OverheadCost.component';
import { JumboPrintComponent } from './Production/WorkPlan/jumboprint/jumboprint.component';

import { OutpassTimelineService } from './Services/OutpassTimelineService';
import { OutpassTimelineComponent } from './OutPass/OutpassTimeline/OutpassTimelineView.component';
import { OutPassBarcodeService } from './OutPass/services/OutPassBarcodeService';
import { OutPassApprovalService } from './OutPass/services/OutPassApprovalService';
import { BatchBarcodeValidationService } from './OutPass/services/BatchBarcodeValidationService';
import { BarcodeLabelPrintComponent } from './Features/BarcodeLabelManagement/components/barcodelabelprint/barcodelabelprint.component';
import { BarcodeScannerLivestreamModule, BarcodeScannerLivestreamOverlayModule } from 'ngx-barcode-scanner';
import { BCScannerViewComponent } from './Features/BarcodeLabelManagement/components/BarcodeScannerView/BCScannerView.component';
import { BarcodeScannerService } from './Features/BarcodeLabelManagement/services/BarcodeScannerService';
import { BarcodeLabelUpdateModalComponent } from './Features/BarcodeLabelManagement/components/BarcodeLabelUpdate/BarcodeLabelUpdateModal.component';
import { BarcodeLabelUpdateService } from './Features/BarcodeLabelManagement/services/BarcodeLabelUpdateService';
import { StockLabelTimelineComponent } from './Features/BarcodeLabelManagement/components/StockLabelTimeline/StockLabelTimeline.component';
import { StockLabelTimelineService } from './Features/BarcodeLabelManagement/services/StockLabelTimelineService';
import { LoggingService } from './Services/logging.service';
import { LoggingConfigService } from './Services/logging-config.service';
import { ReturnStockComponent } from './InventoryManagement/ReturnStock/return-stock.component';
import { StockQualityInspectionComponent } from './InventoryManagement/StockQualityInspection/StockQualityInspection.component';
import { PackagingTypeComponent } from './Master/packagingtype/packagingtype.component';
import { StockLabelListComponent } from './InventoryManagement/StockLabel/List/stocklabel.component';
import { SaleOrderPriceHistoryService } from './Features/SaleOrderPriceHistory/services/SaleOrderPriceHistory';
import { SaleOrderPriceHistoryComponent } from './Features/SaleOrderPriceHistory/components/SaleOrderPriceHistory.component';
import { ProductionDowntimeReasonComponent } from './ProductionMasters/production-downtime-reason/production-downtime-reason.component';
import { PrdDowntimeComponent } from './Features/PrdDowntime/components/PrdDowntime/prddowntime.component';
import { ProductionDowntimeListComponent } from './Production/production-downtime/production-downtime-list.component';
import { ProductTransferComponent } from './Product/producttransfer/producttransfer.component';
import { GateDashboardComponent } from './Dashboard/GateDashboard/GateDashboard.component';
import { DashboardSkeletonComponent } from './Dashboard/GateDashboard/dashboard-skeleton.component';

// DateRangePickerComponent now available through SharedModule

registerLocaleData(en);
declare var AzureStorage: IAzureStorage;
declare var ClientID: any;
const appInitializerFn = (appConfig: AppConfigService) => {
  return () => {
    var load = appConfig.loadAppConfig();
    //ClientID = appConfig.getHostname();
    return load;
  };
};
const callClientID = (appConfig: AppConfigService) => {
  return () => {
    return appConfig.getConfig();
  };
};
export function MSALInstanceFactory(): IPublicClientApplication {
  return new PublicClientApplication({
    auth: {
      clientId: ActiveX.clientId,
      authority: ActiveX.authority,
      redirectUri: ActiveX.redirectUri,
      postLogoutRedirectUri: ActiveX.postLogoutRedirectUri,
    },
    cache: {
      cacheLocation: BrowserCacheLocation.LocalStorage,
      storeAuthStateInCookie: false,
    },
  });
}

export function MSALInterceptorConfigFactory(): MsalInterceptorConfiguration {
  const protectedResourceMap = new Map<string, Array<string>>();
  protectedResourceMap.set('https://graph.microsoft.com/v1.0/me', [
    'user.read',
  ]); // Prod environment. Uncomment to use.
  //protectedResourceMap.set('https://graph.microsoft-ppe.com/v1.0/me', ['user.read']);

  return {
    interactionType: InteractionType.Popup,
    protectedResourceMap,
  };
}

export function MSALGuardConfigFactory(): MsalGuardConfiguration {
  return {
    interactionType: InteractionType.Popup,
    authRequest: {
      scopes: [`${ActiveX.clientId}/.default`],
    },
  };
}

@NgModule({
  declarations: [
    HomeComponent,
    StockListComponent,
    StockInspectionComponent,
    StockProductAllocationComponent,
    AddStockComponent,
    StockDetailsComponent,
    ProductListComponent,
    AuthComponent,
    LogoutComponent,
    IndexComponent,
    StockManageRejectedItemsComponent,
    ReturnStockComponent,
    SupplierListComponent,
    GateInComponent,
    GatePassComponent,
    GateOutComponent,
    TransportManagementComponent,
    BranchComponent,
    DepartmentComponent,
    TagComponent,
    StoreComponent,
    RackComponent,
    ProductCategoryComponent,
    ProductFirstSubCategoryComponent,
    ProductSecSubCategoryComponent,
    PaymentTermComponent,
    DeliveryTermComponent,
    PurchaseorderComponent,
    POListComponent,
    MeasurementConversionComponent,
    MeasurementConversionNewComponent,
    ColorComponent,
    ElementComponent,
    GrainComponent,
    CustomerListComponent,
    IssueComponent,
    IssueListComponent,
    CostingComponent,
    CostingListComponent,
    AdminComponent,
    MixingComponent,
    MixingListComponent,
    RoundOff,
    GatepassprintComponent,
    POprintComponent,
    POEmailComponent,
    EmbossingListComponent,
    TumblingListComponent,
    VaccumListComponent,
    LacquerListComponent,
    PostprocessprintComponent,
    MixingprintComponent,
    ProcessprintComponent,
    OpeningStockComponent,
    SaleslistComponent,
    StartscreenComponent,
    PresalesorderComponent,
    CostEstimationComponent,
    CostEstimationListComponent,
    FormulationCodeListComponent,
    FormulationPrintComponent,
    FormulationcodeComponent,
    // NewmixingComponent,
    ThicknessComponent,
    JumboMasterComponent,
    WorkplanListComponent,
    LoaderComponent,
    WidthComponent,
    ConsumptionComponent,
    ConsumptionListComponent,
    ResponsibilityMasterComponent,
    RolesMasterComponent,
    StartProductionComponent,
    PostprocessComponent,
    proformaAddComponent,
    proformaListComponent,
    proformaPrintComponent,
    FinalInspectionComponent,
    FinalInspectionList,
    JumboListComponent,
    InspectionPrintComponent,
    OutPassListComponent,
    OutPassAddComponent,
    OutPassPrintComponent,
    OutPassPackingListPrintComponent,
    OutPassBarcodeSelectorComponent,
    ValidationRulesConfigComponent,
    MeasurementUnitComponent,
    BankDetailsComponent,
    UsersLoginHistoryComponent,
    DemandComponent,
    DemandListComponent,
    PackagingComponent,
    PackagingListComponent,
    InspectionAllPrintComponent,
    PackingListPrintComponent,
    MBFormulationComponent,
    ActivityLogComponent,
    LinkSaleOrderComponent,
    UnauthorizedComponent,
    GeneralConfigurationComponent,
    TimelineComponent,
    PoTimelineComponent,
    PoDrawerViewComponent,
    SoDrawerComponent,
    EODrawerComponent,
    PIDrawerViewComponent,
    CostingListPrintComponent,
    CostEstimationListPrintComponent,
    CostEstimationPrintComponent,
    ConsumptionPendingListComponent,
    PackagingListEmailComponent,
    InspectionDetailsPrintComponent,
    SaleOrderCostingViewComponent,
    SingleOrderCostingPrint,
    IssueSlipPrintComponent,
    OverheadCostComponent,
    PostProcessOptionsComponent,
    DesignationComponent,
    FactoryWorkersComponent,
    OutPassPurposeComponent,
    DigitalWeightComponent,
    KnittingDivisionStockListComponent,
    NotificationComponent,
    NotificationTypeConfigurationComponent,
    NotificationGroupComponent,
    WhatsAppTemplateListComponent,
    WhatsappConfigComponent,
    EmailGroupMappingComponent,
    JumboPrintComponent,
    OutpassTimelineComponent,
    BarcodeLabelPrintComponent,
    BCScannerViewComponent,
    BarcodeLabelUpdateModalComponent,
    StockLabelTimelineComponent,
    StockQualityInspectionComponent,
    PackagingTypeComponent,
    StockLabelListComponent,
    SaleOrderPriceHistoryComponent,
    ProductionDowntimeReasonComponent,
    PrdDowntimeComponent,
    ProductionDowntimeListComponent,
    ProductTransferComponent,
    GateDashboardComponent,
    DashboardSkeletonComponent,
    InvoiceListComponent,
    InvoiceDetailsComponent
  ],
  imports: [
    BrowserModule,
    BarcodeScannerLivestreamModule,
    BarcodeScannerLivestreamOverlayModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    BrowserAnimationsModule,
    PmsUIAppRoutingModule,
    IconsProviderModule,
    NzLayoutModule,
    NzMenuModule,
    PmsUINzAntModule,
    SharedModule,
    MsalModule, NgxPrintModule,
    NzQRCodeModule,
    DragDropModule

    // MsalModule.forRoot(
    //   new PublicClientApplication({
    //     auth: {
    //       clientId: ActiveX.clientId,
    //       authority: ActiveX.authority,
    //       redirectUri: ActiveX.redirectUri,
    //       navigateToLoginRequestUrl : true
    //     },
    //     cache: {
    //       cacheLocation: BrowserCacheLocation.LocalStorage,
    //       storeAuthStateInCookie: false, // Set to true for Internet Explorer 11
    //     },
    //   }),
    //   {
    //     interactionType: InteractionType.Redirect,
    //     authRequest: {
    //       scopes: [`${ActiveX.clientId}/.default`]
    //     },
    //   },
    //   {
    //     interactionType: InteractionType.Redirect,
    //     protectedResourceMap: new Map([
    //       ['https://graph.microsoft.com/v1.0/me', ['user.read']],
    //     ]),
    //   }
    // ),
  ],
  providers: [
    { provide: NZ_I18N, useValue: en_US },
    { provide: HTTP_INTERCEPTORS, useClass: MyInterceptor, multi: true },
    AlertMessageService,
    LoadingService,
    DemandPopService,
    TimelineService,
    PoTimelineService,
    PoDrawerService,
    PIDrawerService,
    SoDrawerService,
    EODrawerService,
    ApiHttpService,
    BlobStorageService,
    SaleOrderCostingService,
    OutpassTimelineService,
    BarcodeScannerService,
    BarcodeLabelUpdateService,
    StockLabelTimelineService,
    LoggingService,
    LoggingConfigService,
    SaleOrderPriceHistoryService,
    OutPassBarcodeService,
    OutPassApprovalService,
    BatchBarcodeValidationService,
    {
      provide: BLOB_STORAGE_TOKEN,
      useValue: AzureStorage.Blob,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: MsalInterceptor,
      multi: true,
    },
    {
      provide: MSAL_INSTANCE,
      useFactory: MSALInstanceFactory,
    },
    {
      provide: MSAL_INTERCEPTOR_CONFIG,
      useFactory: MSALInterceptorConfigFactory,
    },
    {
      provide: MSAL_GUARD_CONFIG,
      useFactory: MSALGuardConfigFactory,
    },
    MsalService,
    MsalGuard,
    MsalBroadcastService,
    WINDOW_PROVIDERS,
    AppConfigService,
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFn,
      multi: true,
      deps: [AppConfigService],
    },
    DatePipe
  ],
  bootstrap: [IndexComponent, MsalRedirectComponent],
})
export class PmsUIAppModule { }
