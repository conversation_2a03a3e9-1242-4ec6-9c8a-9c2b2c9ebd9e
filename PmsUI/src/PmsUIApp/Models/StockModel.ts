import { InvoiceModel } from "./InvoiceModel";
import { StockProductModel } from "./StockProductModel";
import { FileUploadModel } from "./UploadModel";
import { UserModel } from "./UserModel";

export class StockModel {
  StockId: number = 0;
  StockDate: string = '';
  Grn: string = '';

  InvoiceId: number = 0;
  InspectionCompleted: boolean = false;
  IsQualityInspectionCompleted: boolean = false;
  QualityInspectionCompletedBy: UserModel = new UserModel();
  QualityInspectionCompletedDate: string = '';
  ManageRejectedItemsCompleted: boolean = false;
  AllocationCompleted: boolean = false;
  IsOpeningStock: boolean = false;
  Products: string = '';
  Invoice: InvoiceModel = new InvoiceModel();
  Batch: string = '';
  StockProduct: StockProductModel[] = [];
  AddedBy: string = '';
  AddedDate: string;
  IsTransferedStock: boolean = false;
  ProductQuality: string = '';

  // Return Stock fields
  IsReturnStock: boolean = false;
  OriginalSaleOrderId: number = 0;
  OriginalDispatchId: number = 0;
  ReturnReason: string = '';
  ReturnDate: string = '';
  ReturnedBy: string = '';
  CustomerId: number = 0;
}

export interface IUploadProgress {
  filename: string;
  progress: number;
}
export interface IUploadProgressNew {
  filename: string;
  progress: number;
  status: 'active' | 'success' | 'exception';
}
export class StoreWiseStockModel {
  "SNO": number = 0;
  "ProductType": string;
  "ProductName": string;
  "Quantity": number = 0;
  "Unit": string = '';
  "ProductId": number = 0;
  "ProductCode": string;
  "StoreId": number = 0;
  "StoreName": string;
  "StoreCode": string;
  "RackId": number = 0;
  "RackName": string;
  "RackCode": string;
  "QUANTITY PER RACK": number = 0
  "StoreDetails"?: any = []
  "StoreDetailsCopy"?: any = []
  "calculatedQuantity"?: any = null;
  "ProductCategoryId": number = 0;
  "ProductFirstSubCategoryId": number = 0;
  "ProductSecSubCategoryId": number = 0;
}

export class StockPrintModel {

  StockDate: string = '';
  InvoiceNumber: string = '';
  InvoiceDate: string = '';
  GRN: string = '';
  InvoiceTotalPrice: number = 0;
  Products: string = '';
  Count: number = 0;
  Inspection: string = '';
  Allocation: string = '';



}

export class StoreWiseStockViewModel {
  ProductId: number = 0;
  ProductCode: string = '';
  ProductType: string = '';
  ProductName: string = '';
  Quantity: number = 0;
  Unit: string = '';
  ProductCategoryId: number = 0;
  ProductFirstSubCategoryId: number = 0;
  ProductSecSubCategoryId: number = 0;
  StoreDetails: StoreWiseStockStoreViewModel[] = [];
}
export class StoreWiseStockStoreViewModel {
  StoreName: string = '';
  RackName: string = '';
  Quantity: number = 0;
  Unit: string = '';
}
export class CurrentStockModel {
  ProductId: number = 0;
  MinimumQuantity: number = 0;
  Quantity: number = 0;
}

// Return Stock Models
export class ReturnStockModel {
  OriginalSaleOrderId: number = 0;
  OriginalDispatchId: number = 0;
  CustomerId: number = 0;
  CustomerName: string = '';
  ReturnReason: string = '';
  ReturnDate: string = '';
  ReturnedBy: string = '';
  OriginalDispatchInvoiceId: number = 0;
  ReturnedItems: ReturnStockItemModel[] = [];

  // Original order details for reference
  SaleOrderNumber: string = '';
  OriginalDispatchDate: string = '';
  OriginalInvoiceNumber: string = '';
}

export class ReturnStockItemModel {
  ProductId: number = 0;
  ProductName: string = '';
  ReturnedQuantity: number = 0;
  Unit: string = '';
  OriginalDispatchedQuantity: number = 0;
  OriginalManufacturedDate: string = '';

  // Product specifications
  ThicknessId: number = 0;
  GrainId: number = 0;
  WidthId: number = 0;
  ColorId: number = 0;
  PostProcess: string = '';

  // Return specific details
  ReturnCondition: string = 'Good'; // Good, Damaged, Defective
  ReturnNotes: string = '';
}

export class DispatchedItemsForReturnModel {
  SaleOrderId: number = 0;
  SaleOrderNumber: string = '';
  CustomerId: number = 0;
  CustomerName: string = '';
  DispatchDate: string = '';
  InvoiceNumber: string = '';
  InvoiceId: number = 0;
  DispatchedItems: DispatchedItemDetailModel[] = [];
}

export class DispatchedItemDetailModel {
  ProductId: number = 0;
  ProductName: string = '';
  DispatchedQuantity: number = 0;
  Unit: string = '';
  ManufacturedDate: string = '';
  Grade: string = '';

  // Product specifications
  ThicknessId: number = 0;
  GrainId: number = 0;
  WidthId: number = 0;
  ColorId: number = 0;
  PostProcess: string = '';

  // For tracking returns
  PreviouslyReturnedQuantity: number = 0;
  AvailableForReturn: number = 0;
}

export class ReturnStockListModel {
  StockId: number = 0;
  StockDate: string = '';
  InvoiceNumber: string = '';
  OriginalSaleOrderId: number = 0;
  OriginalSaleOrderNumber: string = '';
  CustomerId: number = 0;
  CustomerName: string = '';
  ReturnReason: string = '';
  ReturnDate: string = '';
  ReturnedBy: string = '';
  InspectionCompleted: boolean = false;
  AllocationCompleted: boolean = false;
  Batch: string = '';
  TotalItems: number = 0;
  TotalQuantity: number = 0;
  AddedBy: string = '';
  AddedDate: string = '';
}

export class ReturnStockFilterModel {
  FromReturnDate: string = '';
  ToReturnDate: string = '';
  CustomerId: number = 0;
  OriginalSaleOrderId: number = 0;
  ReturnReason: string = '';
  InspectionCompleted: boolean | null = null;
  AllocationCompleted: boolean | null = null;
  AddedBy: string = '';
}
