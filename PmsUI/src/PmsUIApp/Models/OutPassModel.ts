import { OutpassStatus } from "./Enums";
import { UserModel } from "./UserModel";

export class PaginatedResult<T> {
  Items: T[] = [];
  TotalCount: number = 0;
  PageNumber: number = 1;
  PageSize: number = 20;

  get TotalPages(): number {
    return Math.ceil(this.TotalCount / this.PageSize);
  }
}

export class OutpassFilterVm {
  outpassToCustomerId?: number = 0;
  outpassTo?: string = '';
  outpassNumber?: string = '';
  outpassType?: string = '';
  purposeId?: number = 0;
  fromDate?: Date = null;
  toDate?: Date = null;
  isOutpassIn?: boolean = null;
  outpassProductName?: string = '';
  productId?: number = 0; // Added ProductId filter
  pageNumber: number = 1;
  pageSize: number = 20;
  includeItems: boolean = true;
}

export class OutPassModel {
  SerialNo: number = 0;
  OutpassTo: string = '';
  OutpassToCustomerId: number = 0;
  OutpassNumber: string = '';
  OutpassDate: any = '';
  ExpectedReturnDate!: string;
  OutpassType: string = '';
  Purpose: string = '';
  PurposeId: number = 0;
  Remark: string = '';
  OutpassId: number = 0;
  IsOutpassIn?: boolean;
  AddedBy: string = '';
  AddedDate?: string = '';
  Status: string = '';
  TransportId: number = 0;
  VehicleId: number = 0;
  TransportName: string = '';
  VehicleNumber: string = '';
  IsGateIn: boolean = false;
  ApprovedByName: string = '';
  OutpassItems: OutpassItemModel[] = [];
  CreateMode: string = 'Manual';
  BarcodeItems: OutpassBarcodeItemModel[] = [];
  BarcodeDetails: StockLabelModel[] = [];
}




export class OutpassItemModel {
  OutpassItemId: number = 0;
  OutpassId?: number = 0;
  StockProductId?: number = 0;
  ProductId?: number = 0;
  ProductName: string = '';
  RackName: string = '';
  StoreName: string = '';
  BatchNo: string = '';
  Quantity: number = 0;
  Amount: number = 0;
  Total: number = 0;
  RackId?: number = 0;
  Unit: string = '';
  ReturnedQuantity: number = 0;
  ReturnedStoreId: number = 0;
  ReturnedRackId: number = 0;
  ReturnCompletedBy: UserModel = null;
  ReturnCompletedDate: string = '';
  OutpassType: string = '';
  ReturnedRackName: string = '';
  ReturnedStoreName: string = '';
  // New properties for enhanced return functionality
  EntryType?: string = '';
  SerialNo?: string = '';
  ShortCode?: string = '';
  IsManualItem?: boolean = true;
  // Consumption tracking properties
  ReasonForLessQuantity?: string = '';
  RequiresReason?: boolean = false;
  // ✅ NEW: StockLabelId for direct database relationship (used for barcode items stored in OutpassItemTable)
  StockLabelId?: number = 0;
}

export class OutpassStatusActionModel {
  OutpassId: number = 0;
  Status: string = '';
  Remark: string = '';
}

// New models for barcode functionality
export class OutpassBarcodeItemModel {
  OutpassItemId: number = 0;
  StockLabelId: number = 0;
  SerialNo: string = '';
  ShortCode: string = '';
  ProductName: string = '';
  Quantity: number = 0;
  PackagingUnit: string = '';
  Amount: number = 0;
  PricePerUnit: number = 0;
  Unit: string = '';
  ProductId: number = 0;
  StockProductId: number = 0;
  BatchNo: string = '';
  CurrentStoreId: number = 0;
  CurrentRackId: number = 0;
  StoreName: string = '';
  RackName: string = '';

  // Return information properties (matching OutpassItemModel structure)
  ReturnedQuantity?: number = 0;
  ReturnedStoreId?: number = 0;
  ReturnedRackId?: number = 0;
  ReturnedStoreName?: string = '';
  ReturnedRackName?: string = '';
  ReturnCompletedBy?: string = '';
  ReturnCompletedDate?: string = '';
  ReasonForLessQuantity?: string = '';
  RequiresReason?: boolean = false;
}

export class BarcodeValidationResult {
  HasBarcodeLabels: boolean = false;
  BarcodeCount: number = 0;
  Message: string = '';
  BarcodeLabels: StockLabelModel[] = [];
}

export class StockLabelModel {
  StockLabelId: number = 0;
  SerialNo: string = '';
  Quantity: number = 0;
  PackagingUnit: string = '';
  ProductName: string = '';
  BatchNo: string = '';
  ProductId: number = 0;
  StockProductId: number = 0;
  CurrentStoreId: number = 0;
  CurrentRackId: number = 0;
  StoreName: string = '';
  RackName: string = '';
  InspectionStatus: string = '';
  LabelStatus: string = '';
}

// New models for OutPass Print functionality with backend processing
export class OutpassPrintDataModel {
  OutpassDetails: OutPassModel = new OutPassModel();
  UnitGroups: PrintUnitGroupModel[] = [];
  Summary: PrintSummaryModel = new PrintSummaryModel();
}

export class PrintUnitGroupModel {
  UnitType: string = '';
  Products: PrintProductModel[] = [];
  TotalQuantity: number = 0;
  TotalAmount: number = 0;
  TotalLabels: number = 0;
  PackagingBreakdown: string = ''; // e.g., "3 Rolls, 2 Bags"
}

export class PrintProductModel {
  ProductId: number = 0;
  ProductName: string = '';
  Items: PrintItemModel[] = [];
  TotalQuantity: number = 0;
  TotalAmount: number = 0;
  LabelCount: number = 0;
  PackagingBreakdown: string = ''; // e.g., "2 Rolls, 1 Bag"
  HasBarcode: boolean = false;
  HasManual: boolean = false;
}

export class PrintItemModel {
  ProductId: number = 0;
  ProductName: string = '';
  BatchNo: string = '';
  Quantity: number = 0;
  Unit: string = '';
  Amount: number = 0;
  TotalAmount: number = 0;
  EntryType: string = ''; // "Manual" or "Barcode"
  StoreName: string = '';
  RackName: string = '';
  SerialNumbers: string[] = [];
  PackagingUnits: string[] = [];
  PackagingBreakdown: string = '';
  LabelCount: number = 0;
}

export class PrintSummaryModel {
  TotalProducts: number = 0;
  TotalManualItems: number = 0;
  TotalBarcodeItems: number = 0;
  TotalLabels: number = 0;
  GrandTotalQuantity: number = 0;
  GrandTotalAmount: number = 0;
  UnitsUsed: string[] = [];
  OverallPackagingBreakdown: string = '';
}