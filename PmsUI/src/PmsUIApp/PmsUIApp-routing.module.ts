import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ROUTES } from './Config/route-config';
import { AuthComponent } from './Authentication/auth/auth.component';
import { ExpenseGuard } from './Authentication/expense.guard';
import { LogoutComponent } from './Authentication/logout/logout.component';
import { GateInComponent } from './GateManagement/GateIn/GateIn.component';
import { GateOutComponent } from './GateManagement/GateOut/GateOut.component';
import { GatePassComponent } from './GateManagement/GatePass/GatePass.component';
import { HomeComponent } from './Home/Home.component';
import { AddStockComponent } from './InventoryManagement/AddStock/AddStock.component';
import { StockDetailsComponent } from './InventoryManagement/StockDetails/StockDetails.component';
import { StockInspectionComponent } from './InventoryManagement/StockInspection/StockInspection.component';
import { StockListComponent } from './InventoryManagement/StockList/StockList.component';
import { StockManageRejectedItemsComponent } from './InventoryManagement/StockManageRejectedItems/StockManageRejectedItems.component';
import { StockProductAllocationComponent } from './InventoryManagement/StockProductAllocation/StockProductAllocation.component';
import { ReturnStockComponent } from './InventoryManagement/ReturnStock/return-stock.component';
import { BranchComponent } from './Master/branch/branch.component';
import { DeliveryTermComponent } from './Master/delivery-term/delivery-term.component';
import { DepartmentComponent } from './Master/department/department.component';
import { PaymentTermComponent } from './Master/payment-term/payment-term.component';
import { ProductCategoryComponent } from './Product/product-category/product-category.component';
import { ProductFirstSubCategoryComponent } from './Product/product-first-sub-category/product-first-sub-category.component';
import { ProductSecSubCategoryComponent } from './Product/product-sec-sub-category/product-sec-sub-category.component';
import { RackComponent } from './Master/rack/rack.component';
import { StoreComponent } from './Master/store/store.component';
import { TagComponent } from './Master/tag/tag.component';
import { ProductListComponent } from './Product/ProductList/ProductList.component';
import { SupplierListComponent } from './SupplierManagement/Supplier/supplier-list/supplier-list.component';
import { TransportManagementComponent } from './transportManagement/transportManagement.component';
import { PurchaseorderComponent } from './PurchaseOrder/Add/purchaseorder.component';
import { POListComponent } from './PurchaseOrder/List/polist.component';
import { MeasurementConversionComponent } from './Master/measurement-conversion/measurement-conversion.component';
import { ColorComponent } from './Master/color/color.component';
import { ElementComponent } from './Master/element/element.component';
import { GrainComponent } from './Master/grain/grain.component';
import { CustomerListComponent } from './CustomerManagement/customer-list/customer-list.component';

import { AdminComponent } from './Admin/Users/<USER>';
import { IssueComponent } from './Issue/Add/Issue.component';
import { IssueListComponent } from './Issue/List/Issuelist.component';
import { CostingComponent } from './Costing/Add/Costing.component';
import { CostingListComponent } from './Costing/List/Costinglist.component';
import { MixingComponent } from './Mixing/Add/mixing.component';
import { MixingListComponent } from './Mixing/List/mixinglist.component';

import { GatepassprintComponent } from './GateManagement/GatePassPrint/gatepassprint.component';
import { POprintComponent } from './PurchaseOrder/Print/poprint.component';
import { POEmailComponent } from './PurchaseOrder/Email/poemail.component';
import { EmbossingListComponent } from './Production/Post Process/embossing-list/embossing-list.component';
import { VaccumListComponent } from './Production/Post Process/vaccum-list/vaccum-list.component';
import { TumblingListComponent } from './Production/Post Process/tumbling-list/tumbling-list.component';
import { LacquerListComponent } from './Production/Post Process/lacquer-list/lacquer-list.component';
import { PostprocessprintComponent } from './Production/Post Process/postprocessprint/postprocessprint.component';
import { MixingprintComponent } from './Production/mixingprint/mixingprint.component';
import { ProcessprintComponent } from './Production/processprint/processprint.component';

import { OpeningStockComponent } from './InventoryManagement/OpeningStock/OpeningStock.component';

import { StartscreenComponent } from './startscreen/startscreen.component';
// import { NewmixingComponent } from './Production/newmixing/newmixing.component';
import { ThicknessComponent } from './Master/thickness/thickness.component';
import { WidthComponent } from './Master/width/width.component';
import { ConsumptionListComponent } from './Consumption/List/consumptionlist.component';
import { ConsumptionComponent } from './Consumption/Add/consumption.component';
import { ResponsibilityMasterComponent } from './Admin/ResponsibilityMaster/ResponsibilityMaster.component';
import { RolesMasterComponent } from './Admin/RolesMaster/RolesMaster.component';
import { PostprocessComponent } from './Production/Post Process/PostProcess/postprocess.component';
import { FinalInspectionComponent } from './Production/FinalInspection/Add/FinalInspection.component';

import { OutPassPrintComponent } from './OutPass/outpassPrint/outpassprint.component';
import { OutPassPackingListPrintComponent } from './OutPass/outpassPackingListPrint/outpasspackinglistprint.component';
import { OutPassAddComponent } from './OutPass/Add/outpassAdd.component';
import { OutPassListComponent } from './OutPass/list/outpasslist.component';

import { StartProductionComponent } from './Production/StartProduction/start-production.component';

import { WorkplanListComponent } from './Production/workplan-list/workplan-list.component';
import { InspectionPrintComponent } from './Production/WorkPlan/inspectionprint/inspectionprint.component';
import { JumboListComponent } from './Production/WorkPlan/Jumbolist/jumbolist.componrnt';
import { JumboMasterComponent } from './Production/WorkPlan/JumboMaster/jumbomaster.component';
import { JumboPrintComponent } from './Production/WorkPlan/jumboprint/jumboprint.component';


import { FormulationCodeListComponent } from './Production/FormulationCode/List/formulation-list.component';
import { FormulationcodeComponent } from './Production/FormulationCode/Add/newformulationcode.component';
import { MeasurementUnitComponent } from './Master/measurement-unit/measurement-unit.component';
import { proformaAddComponent } from './SalesOrder/proformaInvoice/Add/proformaAdd.component';
import { proformaListComponent } from './SalesOrder/proformaInvoice/List/proformalist.component';
import { proformaPrintComponent } from './SalesOrder/proformaInvoice/Print/proformaPrint.component';
import { PresalesorderComponent } from './SalesOrder/SalesOrder/Add/presalesorder.component';
import { SaleslistComponent } from './SalesOrder/SalesOrder/List/saleslist.component';
import { BankDetailsComponent } from './Master/BankDetails/bankDetails.component';
import { UsersLoginHistoryComponent } from './Admin/UsersLoginHistory/UsersLoginHistory.component';
import { DemandListComponent } from './PurchaseOrder/DemandList/DemandList.component';
import { InvoiceListComponent } from './PurchaseOrder/InvoiceList/invoice-list.component';

import { InspectionAllPrintComponent } from './Production/WorkPlan/inspectionallprint/inspectionallprint.component';
import { PackagingComponent } from './Dispatch/Packaging/Add/Packaging.component';
import { PackagingListComponent } from './Dispatch/Packaging/List/PackagingList.component';
import { PackingListPrintComponent } from './Dispatch/Packaging/Print/PackingListPrint.component';
import { MBFormulationComponent } from './Production/PigmentMB/Add/MBFormulation.component';
import { ActivityLogComponent } from './Production/ActivityLog/ActivityLog.component';
import { FinalInspectionList } from './Production/FinalInspection/List/FinalInspectionList.component';
import { LinkSaleOrderComponent } from './Production/Post Process/LinkSaleOrder/LinkSaleOrder.component';
import { UnauthorizedComponent } from './pages/unauthorized/unauthorized.component';
import { GeneralConfigurationComponent } from './Admin/GeneralConfiguration/GeneralConfiguration.component';
import { CostingListPrintComponent } from './Costing/CostingListPrint/CostingListPrint.component';
import { ConsumptionPendingListComponent } from './Consumption/PendingList/pendinglist.component';
import { InspectionDetailsPrintComponent } from './Production/WorkPlan/InspectionDetailsPrint/InspectionDetailsPrint.component';
import { PackagingListEmailComponent } from './Dispatch/Packaging/Email/PackagingListEmail.component';

import { SingleOrderCostingPrint } from './Costing/SingleOrderCostingPrint/SingleOrderCostingPrint.component';
import { IssueSlipPrintComponent } from './Issue/IssueSlipPrint/issueslipprint.component';
import { FormulationPrintComponent } from './Production/FormulationCode/Print/formulationprint.component';
import { MeasurementConversionNewComponent } from './QuickTools/measurement-conversion-new/measurement-conversion-new.component';
import { DesignationComponent } from './Master/designation/designation.component';
import { FactoryWorkersComponent } from './Master/factoryworkers/factoryworkers.component';
import { OutPassPurposeComponent } from './Master/OutPassPurpose/outpasspurpose.component';
import { DigitalWeightComponent } from './IoTDevices/DigitalWeight/digital-weight/digital-weight.component';
import { KnittingDivisionStockListComponent } from './InventoryManagement/KnittingDivisionStockList/KnittingDivisionStockList.component';
import { NotificationComponent } from './Notification/ProductionStagesList/notification/notification.component';
import { NotificationTypeConfigurationComponent } from './Notification/NotificationTypeConfiguration/notification-type-configuration.component';
import { NotificationGroupComponent } from './Notification/NotificationGroup/notification-group/notification-group.component';
import { WhatsAppTemplateListComponent } from './Notification/whats-app-template-list/whats-app-template-list.component';
import { WhatsappConfigComponent } from './Notification/whatsapp-config/whatsapp-config.component';
import { EmailGroupMappingComponent } from './Notification/email-group-mapping/email-group-mapping.component';
import { CostEstimationComponent } from './Costing/Estimation/Add/costEstimationList.component';
import { CostEstimationListComponent } from './Costing/Estimation/List/costEstimationList.component';
import { CostEstimationListPrintComponent } from './Costing/Estimation/PrintList/costEstimationListPrint.component';
import { CostEstimationPrintComponent } from './Costing/Estimation/Print/costEstimationPrint.component';

import { OverheadCostComponent } from './Costing/Overhead/OverheadCost.component';

import { BarcodeLabelPrintComponent } from './Features/BarcodeLabelManagement/components/barcodelabelprint/barcodelabelprint.component';
import { StockQualityInspectionComponent } from './InventoryManagement/StockQualityInspection/StockQualityInspection.component';
import { PackagingTypeComponent } from './Master/packagingtype/packagingtype.component';
import { StockLabelListComponent } from './InventoryManagement/StockLabel/List/stocklabel.component';
import { ProductionDowntimeReasonComponent } from './ProductionMasters/production-downtime-reason/production-downtime-reason.component';
import { ProductionDowntimeListComponent } from './Production/production-downtime/production-downtime-list.component';
import { ProductTransferComponent } from './Product/producttransfer/producttransfer.component';
import { GateDashboardComponent } from './Dashboard/GateDashboard/GateDashboard.component';
const routes: Routes = [
  // { path: '', pathMatch: 'full', redirectTo: '/start' },
  { path: '', pathMatch: 'full', redirectTo: '/welcome' },
  {
    path: 'home',
    component: HomeComponent,
    children: [
      {
        path: 'stocklist',
        component: StockListComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stockinspectionlist',
        component: StockListComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock Inspection | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stockallocationlist',
        component: StockListComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock Allocation | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stocklabel/list',
        component: StockLabelListComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock Label | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stockqualityinspectionlist',
        component: StockListComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock Quality Inspection | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stockmanagerejectedlist',
        component: StockListComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock Rejected Items | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stockqualityinspection/:id',
        component: StockQualityInspectionComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock Quality Inspection | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stockinspection/:id',
        component: StockInspectionComponent,
        canActivate: [ExpenseGuard],
        title: 'Stock Inspection | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stockproductallocation/:id',
        component: StockProductAllocationComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock Allocation | List | Manage Products | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stockmanagerejecteditems/:id',
        component: StockManageRejectedItemsComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock Manage Rejected Items | List | Manage Products | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'stockdetails/:id',
        component: StockDetailsComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Stock Details | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'updatestock/:id',
        component: OpeningStockComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Opening Stock Edit | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'addstock',
        component: AddStockComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Add Stock | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'returnstock',
        component: ReturnStockComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Return Stock | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'knittingdivisionstocklist',
        component: KnittingDivisionStockListComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Knitting Division Stock | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'openingstock',
        component: OpeningStockComponent,
        canActivate: [ExpenseGuard],
        title: 'Inventory | Opening Stock Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'gatein',
        component: GateInComponent,
        canActivate: [ExpenseGuard],
        title: 'Gate Management | Gate In | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'gateout',
        component: GateOutComponent,
        canActivate: [ExpenseGuard],
        title: 'Gate Management | Gate Out | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'gatepass',
        component: GatePassComponent,
        canActivate: [ExpenseGuard],
        title: 'Gate Management | Issue Gate Pass | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'outpass/add',
        component: OutPassAddComponent,
        canActivate: [ExpenseGuard],
        title: 'Out Pass Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'outpass/list',
        component: OutPassListComponent,
        canActivate: [ExpenseGuard],
        title: 'Out Pass | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'outpasspurpose',
        component: OutPassPurposeComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Out Pass Purpose | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'productionmaster/downtimereason',
        component: ProductionDowntimeReasonComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Production | Downtime Reason | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'productlist',
        component: ProductListComponent,
        canActivate: [ExpenseGuard],
        title: 'Product | Product List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'product/producttransfer',
        component: ProductTransferComponent,
        canActivate: [ExpenseGuard],
        title: 'Product | Product Transfer | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'supplierlist',
        component: SupplierListComponent,
        canActivate: [ExpenseGuard],
        title: 'Supplier | Supplier List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'transportlist',
        component: TransportManagementComponent,
        canActivate: [ExpenseGuard],
        title: 'Transport | Transport List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'branchlist',
        component: BranchComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Branch | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'BankDetailslist',
        component: BankDetailsComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Bank Details | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'thicknesslist',
        component: ThicknessComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Thickness | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'widthlist',
        component: WidthComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Width | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'departmentlist',
        component: DepartmentComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Department | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'designation',
        component: DesignationComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Production | Employee Designation | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'factoryworkers',
        component: FactoryWorkersComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Production | Factory Employee | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'colorlist',
        component: ColorComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Production | Color | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'elementlist',
        component: ElementComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Production | Element | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'grainlist',
        component: GrainComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Production | Grain | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'taglist',
        component: TagComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Tag | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'storelist',
        component: StoreComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Stores | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'racklist',
        component: RackComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Rack | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'productcategorylist',
        component: ProductCategoryComponent,
        canActivate: [ExpenseGuard],
        title: 'Product | Category | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'productfirstsubcategorylist',
        component: ProductFirstSubCategoryComponent,
        canActivate: [ExpenseGuard],
        title: 'Product | Sub Category | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'productsecsubcategorylist',
        component: ProductSecSubCategoryComponent,
        canActivate: [ExpenseGuard],
        title: 'Product | Second Sub Category | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'paymenttermlist',
        component: PaymentTermComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Payment Term | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'deliverytermlist',
        component: DeliveryTermComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Delivery Term | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'po/add',
        component: PurchaseorderComponent,
        canActivate: [ExpenseGuard],
        title: 'Purchase Order | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'po/:type/:id',
        component: PurchaseorderComponent,
        canActivate: [ExpenseGuard],
        title: 'Purchase Order | Edit | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'po/list',
        component: POListComponent,
        canActivate: [ExpenseGuard],
        title: 'Purchase Order | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'po/demandlist',
        component: DemandListComponent,
        canActivate: [ExpenseGuard],
        title: 'Demand | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'po/invoicelist',
        component: InvoiceListComponent,
        canActivate: [ExpenseGuard],
        title: 'Invoice | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'salesorder/proformaInvoice/add',
        component: proformaAddComponent,
        canActivate: [ExpenseGuard],
        title: 'Sales Order | Proforma Invoice | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'costing/estimation/add',
        component: CostEstimationComponent,
        canActivate: [ExpenseGuard],
        title: 'Costing | Estimation | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'costing/estimation/add/:id',
        component: CostEstimationComponent,
        canActivate: [ExpenseGuard],
        title: 'Costing | Estimation | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'costing/estimation/list',
        component: CostEstimationListComponent,
        canActivate: [ExpenseGuard],
        title: 'Costing | Estimation | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'salesorder/proformaInvoice/:type/:id',
        component: proformaAddComponent,
        canActivate: [ExpenseGuard],
        title: 'Sales | Sales Order | Proforma Invoice | Edit | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'salesorder/proformaInvoice/list',
        component: proformaListComponent,
        canActivate: [ExpenseGuard],
        title: 'Sales | Sales Order | Proforma Invoice | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'quicktools/measurementconversion-new',
        component: MeasurementConversionNewComponent,
        canActivate: [ExpenseGuard],
        title: 'Quick Tools | Measurement Conversion | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'measurementconversion',
        component: MeasurementConversionComponent,
        canActivate: [ExpenseGuard],
      },
      {
        path: 'measurementunit',
        component: MeasurementUnitComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | General | Measurement Unit | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'packagingtype',
        component: PackagingTypeComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Production | Packaging Type | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'customerlist',
        component: CustomerListComponent,
        canActivate: [ExpenseGuard],
        title: 'Customer | Customer List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'salesorder/add',
        component: PresalesorderComponent,
        canActivate: [ExpenseGuard],
        title: 'Sales | Sales Order | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'salesorder/list',
        component: SaleslistComponent,
        canActivate: [ExpenseGuard],
        title: 'Sales | Sales Order | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      // {
      //   path: 'salesorder/edit/:id',
      //   component: PresalesorderComponent,
      //   canActivate: [ExpenseGuard],
      // },
      {
        path: 'salesorder/:type/:id',
        component: PresalesorderComponent,
        canActivate: [ExpenseGuard],
        title: 'Sales | Sales Order | Edit | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/formulationcode/add',
        component: FormulationcodeComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Formulation Code | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/formulationcode/edit/:id',
        component: FormulationcodeComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Formulation Code | Edit | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/formulationcode/list',
        component: FormulationCodeListComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Formulation Code | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {

        path: 'production/start',
        component: StartProductionComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Start Production | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/postprocess',
        component: PostprocessComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Post Process | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/postprocess/lamination',
        component: LinkSaleOrderComponent,
        canActivate: [ExpenseGuard],
      },
      {
        path: 'dispatch/packaging/add',
        component: PackagingComponent,
        canActivate: [ExpenseGuard],
        title: 'Dispatch | Packaging | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'dispatch/packaging/:type/:id',
        component: PackagingComponent,
        canActivate: [ExpenseGuard],
        title: 'Dispatch | Packaging | Edit | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'dispatch/packaging/list',
        component: PackagingListComponent,
        canActivate: [ExpenseGuard],
        title: 'Dispatch | Packaging | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/finalInspection/add',
        component: FinalInspectionComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Final Inspection | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/finalInspection/add/:JumboNo',
        component: FinalInspectionComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Final Inspection | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/finalInspection/list',
        component: FinalInspectionList,
        canActivate: [ExpenseGuard],
        title: 'Production | Final Inspection | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/jumbo/add',
        component: JumboMasterComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Jumbo | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/jumbo/list',
        component: JumboListComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Jumbo | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'production/workplan',
        component: WorkplanListComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Work Plan | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/ActivityLog',
        component: ActivityLogComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Activity Log | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/downtime',
        component: ProductionDowntimeListComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Downtime | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'jumbomaster',
        component: JumboMasterComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Jumbo | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      // {
      //   path: 'newmixing/add',
      //   component: NewmixingComponent,
      //   canActivate: [ExpenseGuard],
      // },

      {
        path: 'production/embossinglist',
        component: EmbossingListComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Post Process | Embossing | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/postprocessprint',
        component: PostprocessprintComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Post Process | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/vaccumlist',
        component: VaccumListComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Post Process | Vaccum | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/tumblinglist',
        component: TumblingListComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Post Process | Tumbling | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/lacquerlist',
        component: LacquerListComponent,
        canActivate: [ExpenseGuard],
        title: 'Master | Post Process | Lacquer | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'issue/add',
        component: IssueComponent,
        canActivate: [ExpenseGuard],
        title: 'Issue | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'issue/list',
        component: IssueListComponent,
        canActivate: [ExpenseGuard],
        title: 'Issue | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'costing/add',
        component: CostingComponent,
        canActivate: [ExpenseGuard],
        title: 'Costing | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'costing/list',
        component: CostingListComponent,
        canActivate: [ExpenseGuard],
        title: 'Costing List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'costing/overhead',
        component: OverheadCostComponent,
        canActivate: [ExpenseGuard],
        title: 'Costing | Overhead | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'admin/users',
        component: AdminComponent,
        canActivate: [ExpenseGuard],
        title: 'Admin | Manage Users | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'admin/operations',
        component: ResponsibilityMasterComponent,
        canActivate: [ExpenseGuard],
        title: 'Admin | Manage Operations | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'admin/rolemaster',
        component: RolesMasterComponent,
        canActivate: [ExpenseGuard],
        title: 'Admin | Manage Roles | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'admin/generalconfig',
        component: GeneralConfigurationComponent,
        canActivate: [ExpenseGuard],
        title: 'Admin | General Configuration | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'admin/usersloginhistory',
        component: UsersLoginHistoryComponent,
        canActivate: [ExpenseGuard],
        title: 'Admin | Users Login History | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/mixing/add',
        component: MixingComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Mixing | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/mixing/add/:id',
        component: MixingComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Mixing | Edit | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/mixing/list',
        component: MixingListComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Mixing | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'consumption/add',
        component: ConsumptionComponent,
        canActivate: [ExpenseGuard],
        title: 'Consumption | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'consumption/add/:workplanid/:saleorderid/:consumedorder',
        component: ConsumptionComponent,
        canActivate: [ExpenseGuard],
        title: 'Consumption | Add via Pending Order List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'consumption/list',
        component: ConsumptionListComponent,
        canActivate: [ExpenseGuard],
        title: 'Consumption | List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'consumption/pendingorders',
        component: ConsumptionPendingListComponent,
        canActivate: [ExpenseGuard],
        title: 'Consumption | Pending Order List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'production/pigmentmb/add',
        component: MBFormulationComponent,
        canActivate: [ExpenseGuard],
        title: 'Production | Pigment MB | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'notificationlist',
        component: NotificationComponent,
        canActivate: [ExpenseGuard],
        title: 'Notification | Production Stages List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'notificationTypeConfiguration',
        component: NotificationTypeConfigurationComponent,
        canActivate: [ExpenseGuard],
        title: 'Notification | Notification Type Configuration | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'notificationGrouplist',
        component: NotificationGroupComponent,
        canActivate: [ExpenseGuard],
        title: 'Notification | Notification Group List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'whatsAppTemplateListComponent',
        component: WhatsAppTemplateListComponent,
        canActivate: [ExpenseGuard],
        title: 'Notification | WhatsApp Template List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'WhatsappConfigList',
        component: WhatsappConfigComponent,
        canActivate: [ExpenseGuard],
        title: 'Notification | Whatsapp Config List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'EmailGroupMappingList',
        component: EmailGroupMappingComponent,
        canActivate: [ExpenseGuard],
        title: 'Notification | Email Group Mapping List | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      // {
      //   path: 'production/pigmentmb/add',
      //   component: MBFormulationComponent,
      //   canActivate: [ExpenseGuard],
      //   title: 'Production | Pigment MB | Add | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      // },
      {
        path: 'digitalWeightMachine',
        component: DigitalWeightComponent,
        canActivate: [ExpenseGuard],
        title: 'IoT Devices | Digital Weight Machine | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },

      {
        path: 'unauthorized',
        component: UnauthorizedComponent,
        canActivate: [ExpenseGuard],
        title: 'Unauthorized | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      // Direct lazy-loaded feature-wise report modules with meaningful chunk names
      // Production Reports
      {
        path: 'reports/production',
        loadChildren: () =>
          import(/* webpackChunkName: "production-reports" */ './Features/Reports/production-reports/production-reports.module').then((m) => m.ProductionReportsModule)
      },
      // Stock Reports
      {
        path: 'reports/stock',
        loadChildren: () =>
          import(/* webpackChunkName: "stock-reports" */ './Features/Reports/stock-reports/stock-reports.module').then((m) => m.StockReportsModule)
      },

      // Purchase Reports
      {
        path: 'reports/purchase',
        loadChildren: () =>
          import(/* webpackChunkName: "purchase-reports" */ './Features/Reports/purchase-reports/purchase-reports.module').then((m) => m.PurchaseReportsModule)
      },

      // Sales Reports
      {
        path: 'reports/sales',
        loadChildren: () =>
          import(/* webpackChunkName: "sales-reports" */ './Features/Reports/sales-reports/sales-reports.module').then((m) => m.SalesReportsModule)
      },

      {
        path: 'dashboard/reporting',
        component: GateDashboardComponent,
        canActivate: [ExpenseGuard],
        title: 'Dashboard | Reporting Dashboard | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
      {
        path: 'welcome', // ROUTES.DASHBOARD.WELCOME.replace('/home/', '')
        loadChildren: () =>
          import('./pages/welcome/welcome.module').then((m) => m.WelcomeModule),
        title: 'Welcome | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
      },
    ],
  },
  {
    path: 'login',
    component: AuthComponent,
    title: 'Login | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'logout',
    component: LogoutComponent,
    title: 'Logout | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'outpass/print/:id',
    component: OutPassPrintComponent,
    canActivate: [ExpenseGuard],
    title: 'Out Pass | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'outpass/packinglistprint/:id',
    component: OutPassPackingListPrintComponent,
    canActivate: [ExpenseGuard],
    title: 'Out Pass | Packing List Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'jumboprint/:id/:JumboId',
    component: JumboPrintComponent,
    title: 'Production | Jumbo | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'inspectionprint/:id/:JumboId/:InspectionId',
    component: InspectionPrintComponent,
    title: 'Production | Inspection | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'inspectionallprint/:id/:JumboId',
    component: InspectionAllPrintComponent,
    title: 'Production | Inspection | All Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'production/formulationprint/:id',
    component: FormulationPrintComponent,
    title: 'Production | Formulation | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'production/mixingprint/:id',
    component: MixingprintComponent,
    title: 'Production | Mixing | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'inspectiondetailsprint/:id',
    component: InspectionDetailsPrintComponent,
    title: 'Production | Inspection | Details | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'production/processprint/:id',
    component: ProcessprintComponent,
    title: 'Production | Process | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },

  {
    path: 'gatepassprint/:id',
    component: GatepassprintComponent,
    title: 'Gate Pass | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'poprint/:id',
    component: POprintComponent,
    title: 'Purchase Order | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'poprint/:id/:type',
    component: POprintComponent,
    title: 'Purchase Order | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'poemail/:id',
    component: POEmailComponent,
    title: 'Purchase Order | Email | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'dispatchpackagingemail/:id',
    component: PackagingListEmailComponent,
    title: 'Dispatch | Packaging | Email | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'proformaPrint/:id',
    component: proformaPrintComponent,
    title: 'Sales Order | Proforma | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'packinglistprint/:id',
    component: PackingListPrintComponent,
    canActivate: [ExpenseGuard],
    title: 'Dispatch | Packing List | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'costinglistprint',
    component: CostingListPrintComponent,
    canActivate: [ExpenseGuard],
    title: 'Costing | Costing List | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'costestimationlistprint',
    component: CostEstimationListPrintComponent,
    canActivate: [ExpenseGuard],
    title: 'Costing | Cost Estimation List | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'costestimationprint/:id',
    component: CostEstimationPrintComponent,
    canActivate: [ExpenseGuard],
    title: 'Costing | Cost Estimation | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'ordercostingprint/:id/:type',
    component: SingleOrderCostingPrint,
    canActivate: [ExpenseGuard],
    title: 'Costing | Order Costing | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'welcome',
    component: StartscreenComponent,
    title: 'Welcome | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'unauthorized',
    component: UnauthorizedComponent,
    title: 'Unauthorized | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'issueslipprint/:id',
    component: IssueSlipPrintComponent,
    title: 'Issue | Issue Slip | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'barcodelabel/:StockProductId/:StockId/:ProductId/:NumberOfLabels/:action',
    component: BarcodeLabelPrintComponent,
    title: 'Barcode | Barcode Label | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },
  {
    path: 'barcodelabel/:action',
    component: BarcodeLabelPrintComponent,
    title: 'Barcode | Barcode Label | Print | Rexine | Zaibunco | KanzenFlow by Misay Innovations'
  },

];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    initialNavigation: 'enabledNonBlocking',
  })],
  exports: [RouterModule],
})
export class PmsUIAppRoutingModule { }
