import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { AlertMessageService } from '../../Services/AlertMessageService';
import { BarcodeScannerService } from '../../Features/BarcodeLabelManagement/services/BarcodeScannerService';
import { ScannerMode } from '../../Models/Enums';
import { OutpassBarcodeItemModel, BarcodeValidationResult, StockLabelModel } from '../../Models/OutPassModel';
import { StockLabelResponseModel } from '../../Models/BarcodeLabelModel';

@Injectable({
    providedIn: 'root'
})
export class OutPassBarcodeService {

    private outPassBarcodeItemsSubject = new BehaviorSubject<OutpassBarcodeItemModel[]>([]);
    public outPassBarcodeItems$ = this.outPassBarcodeItemsSubject.asObservable();

    private isInitializedSubject = new BehaviorSubject<boolean>(false);
    public isInitialized$ = this.isInitializedSubject.asObservable();

    constructor(
        private http: HttpClient,
        private alertService: AlertMessageService,
        private barcodeService: BarcodeScannerService
    ) {
        this.initializeSubscriptions();
    }

    /**
     * Initialize subscriptions to barcode scanner service
     */
    private initializeSubscriptions(): void {
        // Subscribe to scanned labels from barcode service
        this.barcodeService.scannedLabels$.subscribe(labels => {
            if (this.barcodeService.getCurrentAction() === 'OutPassScan') {
                this.processScannedLabelsForOutPass(labels);
            }
        });

        // Subscribe to barcode values for direct scanning (camera/external device)
        this.barcodeService.barcodeValue$.subscribe(barcodeValue => {
            if (barcodeValue && this.barcodeService.getCurrentAction() === 'OutPassScan') {
                this.processBarcodeValueForOutPass(barcodeValue);
            }
        });
    }

    /**
     * Initialize barcode scanning for Out Pass
     */
    initializeBarcodeScanning(action: string = 'OutPassScan'): void {
        this.barcodeService.setScannerMode(ScannerMode.OverlayScanner, action);
        this.isInitializedSubject.next(true);
        this.alertService.info('Barcode scanner initialized for Out Pass. You can now scan, manually enter, or use external device.');
    }

    /**
     * Set scanner mode for different input methods
     */
    setScannerMode(mode: ScannerMode): void {
        this.barcodeService.setScannerMode(mode, 'OutPassScan');
    }

    /**
     * Validate if a batch has barcode labels
     */
    async validateBatchForBarcodeLabels(stockProductId: number): Promise<BarcodeValidationResult> {
        // Input validation
        if (!stockProductId || stockProductId <= 0) {
            const errorMessage = 'Invalid stock product ID provided for barcode validation';
            console.error(errorMessage, { stockProductId });
            this.alertService.error(errorMessage);
            return { HasBarcodeLabels: false, BarcodeCount: 0, Message: errorMessage, BarcodeLabels: [] };
        }

        try {
            const url = `${environment.Api_Url}outpass/validatebarcodeforbatch?stockproductid=${stockProductId}`;

            // Add timeout and retry logic
            const timeoutPromise = new Promise<never>((_, reject) => {
                setTimeout(() => reject(new Error('Request timeout')), 10000);
            });

            const result = await Promise.race([
                this.http.get<BarcodeValidationResult>(url).toPromise(),
                timeoutPromise
            ]);

            // Validate response structure
            if (!result) {
                throw new Error('Empty response from server');
            }

            // Ensure all required properties exist
            const validatedResult: BarcodeValidationResult = {
                HasBarcodeLabels: result.HasBarcodeLabels || false,
                BarcodeCount: result.BarcodeCount || 0,
                Message: result.Message || 'Validation completed',
                BarcodeLabels: result.BarcodeLabels || []
            };

            console.log('Batch validation result:', validatedResult);
            return validatedResult;

        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            console.error('Error validating barcode labels for batch:', error);

            if (error.message === 'Request timeout') {
                this.alertService.error('Request timed out. Please check your connection and try again.');
            } else if (error.status === 404) {
                this.alertService.error('Validation service not found. Please contact support.');
            } else if (error.status >= 500) {
                this.alertService.error('Server error occurred. Please try again later.');
            } else {
                this.alertService.error(`Failed to validate barcode labels: ${errorMessage}`);
            }

            return {
                HasBarcodeLabels: false,
                BarcodeCount: 0,
                Message: `Validation failed: ${errorMessage}`,
                BarcodeLabels: []
            };
        }
    }

    /**
     * Process scanned labels and convert to Out Pass barcode items
     */
    private processScannedLabelsForOutPass(labels: StockLabelResponseModel[]): void {
        const currentItems = this.outPassBarcodeItemsSubject.getValue();

        labels.forEach(label => {
            // Check if label is already added
            if (!currentItems.some(item => item.StockLabelId === label.StockLabelId)) {
                const barcodeItem: OutpassBarcodeItemModel = {
                    OutpassItemId: 0,
                    StockLabelId: label.StockLabelId,
                    SerialNo: label.SerialNo,
                    ShortCode: label.ShortCode,
                    ProductName: label.ProductName,
                    Quantity: label.Quantity,
                    PackagingUnit: label.PackagingUnit || '',
                    Amount: 0, // To be set by user
                    PricePerUnit: 0,
                    Unit: label.Unit || '',
                    ProductId: label.ProductId,
                    StockProductId: label.StockProductId,
                    BatchNo: label.BatchNo || '',
                    CurrentStoreId: label.CurrentStoreId,
                    CurrentRackId: label.CurrentRackId,
                    StoreName: label.StoreName || '',
                    RackName: label.RackName || ''
                };

                currentItems.push(barcodeItem);
            }
        });

        this.outPassBarcodeItemsSubject.next([...currentItems]);
        // this.alertService.success(`${labels.length} barcode label(s) added to Out Pass`);
    }

    /**
     * Process barcode value from scanner (camera/external device)
     */
    private async processBarcodeValueForOutPass(barcodeValue: string): Promise<void> {
        try {
            console.log('Processing barcode value for OutPass:', barcodeValue);

            // Use the same logic as manual entry but determine if it's a short code or serial number
            const isShortCode = this.isShortCodeFormat(barcodeValue);
            await this.addBarcodeItemManually(barcodeValue, isShortCode);

            // Reset the barcode value after processing to prevent duplicate processing
            this.barcodeService.setBarcodeValue(null);
        } catch (error) {
            console.error('Error processing barcode value for OutPass:', error);
            this.alertService.error('Failed to process scanned barcode. Please try again.');
        }
    }

    /**
     * Determine if a barcode value is in short code format
     */
    private isShortCodeFormat(value: string): boolean {
        // Implement logic to determine if the value is a short code
        // This could be based on length, pattern, or other criteria
        // For now, assume values with 6 or fewer characters are short codes
        return value && value.length <= 6;
    }

    /**
     * Add barcode item manually (for manual entry or external device)
     */
    async addBarcodeItemManually(serialNo: string, isShortCode: boolean = false): Promise<void> {
        // Input validation
        if (!serialNo || serialNo.trim().length === 0) {
            this.alertService.error('Please enter a valid serial number or short code');
            return;
        }

        const trimmedSerialNo = serialNo.trim();

        // Validate serial number format
        if (!this.validateSerialNumberFormat(trimmedSerialNo, isShortCode)) {
            return;
        }

        // Check for duplicates
        if (this.isDuplicateSerialNumber(trimmedSerialNo)) {
            this.alertService.warning(`Serial number "${trimmedSerialNo}" is already added to this Out Pass`);
            return;
        }

        try {
            // Fetch label details using the same API pattern as barcode scanner
            const request = {
                SerialNo: isShortCode ? '' : trimmedSerialNo,
                ShortCode: isShortCode ? trimmedSerialNo : '',
                IsSerialNo: !isShortCode,
                IsShortCode: isShortCode
            };

            const url = `${environment.Api_Url}stock/getstocklabelbyserialno`;

            // Add timeout for manual entry requests
            const timeoutPromise = new Promise<never>((_, reject) => {
                setTimeout(() => reject(new Error('Request timeout')), 8000);
            });

            const labelResponse = await Promise.race([
                this.http.post<StockLabelResponseModel>(url, request).toPromise(),
                timeoutPromise
            ]);

            // Validate response
            if (!labelResponse) {
                throw new Error('Empty response from server');
            }

            if (labelResponse.StockLabelId > 0) {
                // Additional validation for the label
                if (!this.validateLabelForOutPass(labelResponse)) {
                    return;
                }

                this.processScannedLabelsForOutPass([labelResponse]);
                this.alertService.success(`Barcode item "${trimmedSerialNo}" added successfully`);
            } else {
                this.alertService.error(`Barcode label "${trimmedSerialNo}" not found or is not available for Out Pass`);
            }
        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            console.error('Error adding barcode item manually:', error);

            if (error.message === 'Request timeout') {
                this.alertService.error('Request timed out. Please check your connection and try again.');
            } else if (error.status === 404) {
                this.alertService.error(`Barcode label "${trimmedSerialNo}" not found in the system`);
            } else if (error.status === 400) {
                this.alertService.error(`Invalid barcode format: "${trimmedSerialNo}"`);
            } else {
                this.alertService.error(`Failed to add barcode item: ${errorMessage}`);
            }
        }
    }

    /**
     * Remove barcode item from list
     */
    removeBarcodeItem(stockLabelId: number): void {
        const currentItems = this.outPassBarcodeItemsSubject.getValue();
        const updatedItems = currentItems.filter(item => item.StockLabelId !== stockLabelId);
        this.outPassBarcodeItemsSubject.next(updatedItems);
        this.alertService.info('Barcode item removed from Out Pass');
    }

    /**
     * Update barcode item amount
     */
    updateBarcodeItemAmount(stockLabelId: number, amount: number): void {
        const currentItems = this.outPassBarcodeItemsSubject.getValue();
        const item = currentItems.find(item => item.StockLabelId === stockLabelId);
        if (item) {
            item.Amount = amount;
            this.outPassBarcodeItemsSubject.next([...currentItems]);
        }
    }

    /**
     * Get current barcode items
     */
    getBarcodeItems(): OutpassBarcodeItemModel[] {
        return this.outPassBarcodeItemsSubject.getValue();
    }

    /**
     * Clear all barcode items
     */
    clearBarcodeItems(): void {
        this.outPassBarcodeItemsSubject.next([]);
        this.barcodeService.clearScannedLabels();
        this.isInitializedSubject.next(false);
    }

    /**
     * Reset service state
     */
    resetService(): void {
        this.clearBarcodeItems();
        this.barcodeService.resetValues();
    }

    /**
     * Close scanner interface without clearing data
     * Used when switching modes to preserve existing items
     */
    closeScannerOnly(): void {
        // Only close the scanner interface, don't clear data
        this.barcodeService.setScannerMode(ScannerMode.Closed);
        this.barcodeService.resetValues();
        // Don't call clearBarcodeItems() to preserve existing barcode items
    }

    /**
     * Check if barcode scanning is active
     */
    isScanningActive(): boolean {
        return this.barcodeService.getCurrentMode() !== ScannerMode.Closed;
    }

    /**
     * Close barcode scanner
     */
    closeScanner(): void {
        this.barcodeService.setScannerMode(ScannerMode.Closed);
    }

    /**
     * Get total quantity of barcode items
     */
    getTotalBarcodeQuantity(): number {
        const items = this.outPassBarcodeItemsSubject.getValue();
        return items.reduce((total, item) => total + item.Quantity, 0);
    }

    /**
     * Get total amount of barcode items
     */
    getTotalBarcodeAmount(): number {
        const items = this.outPassBarcodeItemsSubject.getValue();
        return items.reduce((total, item) => total + (item.Amount * item.Quantity), 0);
    }

    /**
     * Validate barcode items before saving
     */
    validateBarcodeItems(): { isValid: boolean; message: string } {
        const items = this.outPassBarcodeItemsSubject.getValue();

        if (items.length === 0) {
            return { isValid: false, message: 'No barcode items added' };
        }

        // Amount validation removed - amount is now optional for barcode items
        // const itemsWithoutAmount = items.filter(item => item.Amount <= 0);
        // if (itemsWithoutAmount.length > 0) {
        //     return {
        //         isValid: false,
        //         message: `Please set amount for ${itemsWithoutAmount.length} barcode item(s)`
        //     };
        // }

        return { isValid: true, message: 'All barcode items are valid' };
    }

    /**
     * Helper method to extract error message from error object
     */
    private getErrorMessage(error: any): string {
        if (error?.error?.message) {
            return error.error.message;
        }
        if (error?.message) {
            return error.message;
        }
        if (typeof error === 'string') {
            return error;
        }
        return 'Unknown error occurred';
    }

    /**
     * Validate serial number format
     */
    private validateSerialNumberFormat(serialNo: string, isShortCode: boolean): boolean {
        if (isShortCode) {
            // Short code validation (typically 6-12 characters)
            if (serialNo.length < 3 || serialNo.length > 20) {
                this.alertService.error('Short code must be between 3 and 20 characters');
                return false;
            }
            // Allow alphanumeric characters and some special characters
            if (!/^[A-Za-z0-9\-_]+$/.test(serialNo)) {
                this.alertService.error('Short code can only contain letters, numbers, hyphens, and underscores');
                return false;
            }
        } else {
            // Serial number validation (typically longer and more specific format)
            if (serialNo.length < 5 || serialNo.length > 50) {
                this.alertService.error('Serial number must be between 5 and 50 characters');
                return false;
            }
            // More flexible format for serial numbers
            if (!/^[A-Za-z0-9\-_\/\\]+$/.test(serialNo)) {
                this.alertService.error('Serial number contains invalid characters');
                return false;
            }
        }
        return true;
    }

    /**
     * Check if serial number is already added
     */
    private isDuplicateSerialNumber(serialNo: string): boolean {
        const currentItems = this.outPassBarcodeItemsSubject.getValue();
        return currentItems.some(item =>
            item.SerialNo.toLowerCase() === serialNo.toLowerCase()
        );
    }

    /**
     * Validate label for Out Pass eligibility
     */
    private validateLabelForOutPass(label: StockLabelResponseModel): boolean {
        // Check if label is active
        if (!label.StockLabelId || label.StockLabelId <= 0) {
            this.alertService.error('Invalid barcode label ID');
            return false;
        }
        if (!label.IsActive) {
            this.alertService.error('This barcode label is not active and cannot be used');
            return false;
        }

        // Check if product information is available
        if (!label.ProductName || label.ProductName.trim().length === 0) {
            this.alertService.error('Product information not available for this barcode');
            return false;
        }

        // Check if quantity is valid
        if (!label.Quantity || label.Quantity <= 0) {
            this.alertService.error('Invalid quantity for this barcode label');
            return false;
        }

        // Check if label is already in use (if status is provided)
        if (label.LabelStatus && label.LabelStatus.toLowerCase() === 'consumed') {
            this.alertService.error('This barcode label has already been consumed');
            return false;
        }

        if (label.LabelStatus && label.LabelStatus.toLowerCase() === 'blocked') {
            this.alertService.error('This barcode label is blocked and cannot be used');
            return false;
        }

        return true;
    }

    /**
     * Enhanced validation for barcode items with detailed checks
     */
    validateBarcodeItemsDetailed(): { isValid: boolean; errors: string[]; warnings: string[] } {
        const items = this.outPassBarcodeItemsSubject.getValue();
        const errors: string[] = [];
        const warnings: string[] = [];

        if (items.length === 0) {
            errors.push('No barcode items added');
            return { isValid: false, errors, warnings };
        }

        // Check each item
        items.forEach((item, index) => {
            const itemNumber = index + 1;

            // Amount validation removed - amount is now optional
            // if (item.Amount <= 0) {
            //     errors.push(`Item ${itemNumber} (${item.SerialNo}): Amount must be greater than zero`);
            // }

            // Quantity validation
            if (item.Quantity <= 0) {
                errors.push(`Item ${itemNumber} (${item.SerialNo}): Invalid quantity`);
            }

            // Product validation
            if (!item.ProductName || item.ProductName.trim().length === 0) {
                errors.push(`Item ${itemNumber} (${item.SerialNo}): Missing product information`);
            }

            // Location validation
            if (!item.CurrentStoreId || item.CurrentStoreId <= 0) {
                warnings.push(`Item ${itemNumber} (${item.SerialNo}): Store information missing`);
            }

            // Amount vs quantity ratio check (warning for unusual ratios) - only if amount is provided
            if (item.Amount > 0 && item.Quantity > 0) {
                const ratio = item.Amount / item.Quantity;
                if (ratio > 10000) { // Very high amount per unit
                    warnings.push(`Item ${itemNumber} (${item.SerialNo}): Unusually high amount per unit (₹${ratio.toFixed(2)})`);
                }
            }
        });

        // Check for duplicate serial numbers
        const serialNumbers = items.map(item => item.SerialNo.toLowerCase());
        const duplicates = serialNumbers.filter((serial, index) => serialNumbers.indexOf(serial) !== index);
        if (duplicates.length > 0) {
            errors.push(`Duplicate serial numbers found: ${duplicates.join(', ')}`);
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}
