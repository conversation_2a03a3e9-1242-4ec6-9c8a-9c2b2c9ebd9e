<app-OutpassTimeline></app-OutpassTimeline>
<div class="card card-flush h-xl-100">
  <div class="card-body pt-5">
    <nz-page-header [nzGhost]="false">
      <nz-page-header-title>Out Pass List</nz-page-header-title>
      <nz-page-header-subtitle>Manage your Out Pass here</nz-page-header-subtitle>
      <nz-page-header-extra>
        <button nz-button nzType="primary" routerLink="/home/<USER>/add">
          Add New
        </button>
      </nz-page-header-extra>
    </nz-page-header>
    <nz-collapse>
      <nz-collapse-panel nzHeader="Show Filters" [nzActive]="false" (nzActiveChange)="onFilterPanelOpen($event)">
        <div nz-col [nzSpan]="24">
          <div nz-row [nzGutter]="24">
            <div nz-col [nzSpan]="4">
              <nz-form-item>
                <nz-form-control [nzSpan]="24" nzErrorTip="Enter Amount">
                  <label> Order Date (From)</label>
                  <nz-date-picker nzPlaceHolder="From Date" name="orderDateFrom" class="form-control"
                    [(ngModel)]="request.fromDate" nzAllowClear nzShowTime></nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="4">
              <nz-form-item>
                <nz-form-control [nzSpan]="24" nzErrorTip="Enter Amount">
                  <label> Order Date (To)</label>
                  <nz-date-picker nzPlaceHolder="To Date" name="orderDateTo" class="form-control"
                    [(ngModel)]="request.toDate" nzAllowClear nzShowTime></nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="4">
              <nz-form-item>
                <nz-form-control [nzSpan]="24" nzErrorTip="Select product Name">
                  <label>OutPass To</label>
                  <nz-select class="form-select mb-2" nzShowSearch name="CustomerId" nzSize="default" nzAllowClear
                    [(ngModel)]="request.outpassToCustomerId" nzPlaceHolder="Choose">
                    <nz-option *ngFor="let s of this.CustomerList;" [nzValue]="s.CustomerId"
                      [nzLabel]="s.CustomerName"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="4">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <label>Outpass Type</label>
                  <nz-select class="form-select mb-2" nzSize="default" [(ngModel)]="request.outpassType"
                    nzPlaceHolder="Choose" nzShowSearch nzAllowClear>
                    <nz-option nzValue="Returnable" nzLabel="Returnable"></nz-option>
                    <nz-option nzValue="Non-Returnable" nzLabel="Non-Returnable"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="3">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <label>Returnable Status</label>
                  <nz-select class="form-select mb-2" nzSize="default" [(ngModel)]="request.isOutpassIn"
                    nzPlaceHolder="Choose" nzShowSearch nzAllowClear>
                    <nz-option nzValue=true nzLabel="Received"></nz-option>
                    <nz-option nzValue=false nzLabel="Pending"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="4">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <label>Purpose</label>
                  <nz-select class="form-select mb-2" nzSize="default" [(ngModel)]="request.purposeId"
                    nzPlaceHolder="Choose" nzShowSearch nzAllowClear>
                    <nz-option *ngFor="let s of this.PurposeList;" [nzValue]="s.PurposeId"
                      [nzLabel]="s.PurposeName"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <!-- ✅ NEW: Product Filter Mode Toggle -->
          <div nz-row [nzGutter]="24" style="margin-bottom: 16px;">
            <div nz-col [nzSpan]="24">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <div style="display: flex; align-items: center; gap: 12px;">
                    <label style="margin: 0; font-weight: 600; color: #1890ff;">Product Filter Mode:</label>
                    <nz-switch [(ngModel)]="isLegacyMode" (ngModelChange)="onProductFilterModeChange()"
                      [nzCheckedChildren]="legacyModeTemplate" [nzUnCheckedChildren]="productMasterModeTemplate"
                      nzSize="default">
                    </nz-switch>
                    <ng-template #legacyModeTemplate>
                      <span style="font-size: 12px;">📝 Legacy</span>
                    </ng-template>
                    <ng-template #productMasterModeTemplate>
                      <span style="font-size: 12px;">🏷️ Catalog</span>
                    </ng-template>
                    <span style="font-size: 12px; color: #666; margin-left: 8px;">
                      {{ isLegacyMode ? 'Search custom product names' : 'Use structured product catalog' }}
                    </span>
                  </div>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <!-- ✅ Product Master Mode (Default) -->
          <div nz-row [nzGutter]="24" *ngIf="!isLegacyMode">
            <div nz-col [nzSpan]="3">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <label>Product Type</label>
                  <nz-select class="form-select mb-2" nzSize="default" [(ngModel)]="SelectedProductType"
                    (ngModelChange)="onSelectedProductTypeChange()" nzPlaceHolder="Choose" nzAllowClear>
                    <nz-option nzValue="Raw" nzLabel="Raw"></nz-option>
                    <nz-option nzValue="Finished" nzLabel="Finished"></nz-option>
                    <nz-option nzValue="Internal-Use" nzLabel="Internal-Use"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="4">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <label>Category</label>
                  <nz-select class="form-select" nzShowSearch nzSize="default" [(ngModel)]="CategoryID" name="Category"
                    nzAllowClear nzPlaceHolder="Category" (ngModelChange)="GetAllFirstCategory($event)">
                    <nz-option *ngFor="let s of this.ProductCategoryList;" [nzValue]="s.ProductCategoryId"
                      [nzLabel]="s.ProductCategory"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="4">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <label>SubCategory</label>
                  <nz-select class="form-select" nzShowSearch nzSize="default" [(ngModel)]="FirstCategoryID"
                    name="FirstCategoryID" nzAllowClear nzPlaceHolder="SubCategory"
                    (ngModelChange)="GetAllSecondCategory($event)">
                    <nz-option *ngFor="let s of this.ProductFirstSubCategoryList;"
                      [nzValue]="s.ProductFirstSubCategoryId" [nzLabel]="s.ProductFirstSubCategory"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="4">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <label>2nd SubCategory</label>
                  <nz-select class="form-select" nzShowSearch nzSize="default" [(ngModel)]="SecondCategoryID"
                    name="SecondCategoryID" nzAllowClear nzPlaceHolder="2nd SubCategory"
                    (ngModelChange)="GetSecondCategoryFilteredProduct()">
                    <nz-option *ngFor="let s of this.ProductSecSubCategoryList;" [nzValue]="s.ProductSecSubCategoryId"
                      [nzLabel]="s.ProductSecSubCategory"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="7">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <label>Product Name</label>
                  <nz-select nzShowSearch class="form-select" nzSize="default" [(ngModel)]="selectedProductFromCatalog"
                    name="ProductCatalog" (ngModelChange)="onSelectedProductFromCatalogChange($event)" nzAllowClear
                    nzPlaceHolder="Choose Product">
                    <nz-option *ngFor="let s of this.FilteredProductList;" [nzValue]="s"
                      [nzLabel]="s.ProductName"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <!-- ✅ Legacy Custom Names Mode -->
          <div nz-row [nzGutter]="24" *ngIf="isLegacyMode">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-control [nzSpan]="24">
                  <label>Search Product Name</label>
                  <input nz-input [(ngModel)]="request.outpassProductName" name="LegacyProductName"
                    placeholder="Enter product name to search..." nzSize="default" style="width: 100%;" />
                  <div style="font-size: 12px; color: #666; margin-top: 4px;">
                    💡 Search for historical product names from existing OutPass records
                  </div>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <div style="padding: 16px; background-color: #f6ffed; border: 1px solid #b7eb8f; border-radius: 6px;">
                <div style="font-size: 13px; color: #389e0d; font-weight: 500; margin-bottom: 4px;">
                  📝 Legacy Mode Active
                </div>
                <div style="font-size: 12px; color: #52c41a;">
                  This mode searches through custom product names from historical OutPass records.
                  Use this to find OutPasses with manually entered product names.
                </div>
              </div>
            </div>
          </div>

          <div style="float: right; margin-bottom: 5%; margin-right: 10px">
            <div style="display: flex">
              <button nz-button nzSize="small" nzType="default" style="margin-right: 8px" (click)="resetAllFilters()">
                Reset
              </button>
              <button nz-button nzSize="small" nzType="primary" style="margin-right: 8px" (click)="GetAllOutPass()">
                Apply Filter
              </button>
              <!-- <button nz-button nzSize="small" (click)="onReset()">Reset</button> -->
            </div>
          </div>
        </div>
      </nz-collapse-panel>
    </nz-collapse>
    <nz-divider></nz-divider>
    <div style="padding: 8px;float:right">
      <nz-date-picker [(ngModel)]="myDateValue" nzPlaceHolder="Start Date"
        style="width: 150px;margin-right: 8px;"></nz-date-picker>
      <nz-date-picker [(ngModel)]="toDate" nzPlaceHolder="End Date"
        style="width: 150px;margin-right: 8px;"></nz-date-picker>
      <input type="text" style="width: 250px;margin-right: 8px;" nz-input placeholder="Search  "
        [(ngModel)]="searchValue" (keydown)="onKeydown($event)" (ngModelChange)="ValidateText()" />
      <button nz-button nzSize="small" nzType="primary" (click)="search()" style="margin-right: 8px;">Search</button>
      <button nz-button nzSize="small" (click)="reset()">Reset</button>
      <button nz-button nzSize="small" (click)="export()"><i nz-icon nzType="export" nzTheme="outline"></i>
        Export</button>
    </div>
    <div style="font-weight: bolder;">
      <p>* Yellow color cells are to show Non-Store items in Outpass. Admin should review them regularly.</p>
    </div>
    <nz-table nzSize="small" [nzScroll]="{ x: '1400px', y: '515px' }" style="width: 100%" #basicTable
      [nzData]="this.OutPassList" [nzLoading]="isTableLoading" nzBordered [nzFrontPagination]="false"
      [nzTotal]="totalItemsCount" [nzPageIndex]="pageNumber" [nzPageSize]="pageSize"
      (nzPageIndexChange)="onPageIndexChange($event)" (nzPageSizeChange)="onPageSizeChange($event)"
      nzShowPagination="true" nzShowSizeChanger [nzPageSizeOptions]="pageSizeOptions" [nzShowTotal]="totalTemplate"
      nzShowQuickJumper="true" nzResponsive=true>
      <thead>
        <tr>
          <th nzWidth="60px">S.No.</th>
          <th nzWidth="130px">Out Pass No.</th>
          <th nzWidth="190px">Out Pass To</th>
          <th nzWidth="150px">Out Pass Type</th>
          <th nzWidth="150px">Product Name</th>
          <th nzWidth="140px">Out Pass Date</th>
          <th nzWidth="150px">Purpose</th>
          <th nzWidth="150px">Status</th>
          <th nzWidth="150px">Transport</th>
          <th nzWidth="150px">Vehicle</th>
          <th nzWidth="200px">Remark </th>
          <th nzWidth="120px">Expected Return</th>
          <th nzWidth="120px">Added Date </th>
          <th nzWidth="150px">Added By </th>
          <th nzWidth="140px" style="text-align: center" nzRight>Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data">
          <td>{{data.SerialNo}}</td>
          <td [ngStyle]="{'background-color': GetCellColorforNonStoreItems(data)}">{{ data.OutpassNumber }}</td>
          <td>{{ data.OutpassTo }}</td>
          <td>{{ data.OutpassType }}</td>
          <td [ngStyle]="{'background-color': GetCellColorforNonStoreItems(data)}"
            (click)="OpenProductListWithDetails(data)" innerHTML="{{ GetProductName(data)}}"> </td>
          <td>{{ data.OutpassDate | DatetimeConverter}}</td>
          <td>{{ data.Purpose }}</td>
          <td><nz-tag [nzColor]="'blue'" (click)="OpenTimeline(data.OutpassId)"> <a>{{ data.Status }}</a></nz-tag></td>
          <td>{{ data.TransportName }}</td>
          <td>{{ data.VehicleNumber }}</td>
          <td>{{ data.Remark }}</td>
          <td>{{ data.ExpectedReturnDate | DatetimeConverter }}</td>
          <td>{{ data.AddedDate | DatetimeConverter }}</td>
          <td>{{ data.AddedBy }}</td>
          <td nzRight style="text-align: center">
            <!-- <button *ngIf="data.OutpassType=='Returnable' && data.IsOutpassIn!=true" class="btn btn-sm btn-light-primary"
              (click)="OpenInoutPass(data)" style="margin-right: 10px; ">In-OutPass</button>
            <button class="btn btn-sm btn-light-primary" (click)="OpenProductList(data.OutpassItems)">Show
              Products</button>
            <button class="btn btn-sm btn-light-primary" (click)="print(data)">Print</button> -->
            <button nz-button nz-dropdown nzTrigger="click" [nzDropdownMenu]="menu" class="btn btn-sm btn-light-primary"
              style="line-height:0">
              Action
              <span nz-icon nzType="down"></span>
            </button>
            <nz-dropdown-menu #menu="nzDropdownMenu">
              <ul nz-menu nzSelectable>
                <li *ngIf="data.OutpassType=='Returnable' && data.IsOutpassIn!=true && data.Status == 'Approved'"
                  style="width: 120px;" nz-menu-item (click)="OpenInoutPass(data)">Accept Return OutPass</li>
                <li
                  *ngIf="data.OutpassType=='Returnable' && data.IsOutpassIn!=true && (data.Status == 'Approved' || data.Status == 'Approval Pending')"
                  style="width: 120px;" nz-menu-item (click)="ShowOutpassReturnExtendModel(data)">Extend Return Date
                </li>
                <li style="width: 120px;" nz-menu-item (click)="OpenProductListWithDetails(data)">
                  View Details</li>
                <li style="width: 120px;" nz-menu-item (click)="OpenTimeline(data.OutpassId)">Show
                  Timeline</li>
                <li style="width: 120px;" nz-menu-item (click)="print(data)" *ngIf="EnablePrintButton(data)">Print
                  OutPass</li>
                <li style="width: 120px;" nz-menu-item (click)="printPackingList(data)"
                  *ngIf="EnablePackingListPrintButton(data)">Print
                  Packing List</li>
                <li style="width: 120px;" nz-menu-item
                  *ngIf="this.permission.AddTransport && !data.IsGateIn && (data.Status == 'Approved' || data.Status == 'Approval Pending')"
                  (click)="showTransportModal(data)">Add Transport</li>

                <!-- ✅ NEW: Status Change Submenu -->
                <li nz-submenu nzTitle="Status Change" nzIcon="edit"
                  *ngIf="EnableApproveButton(data) || EnableOnHoldButton(data) || EnableCancelButton(data)">
                  <ul>
                    <li style="width: 120px;" nz-menu-item (click)="ChangeOutpassStatus(data.OutpassId, 'Approved')"
                      *ngIf="EnableApproveButton(data)">
                      <i nz-icon nzType="check-circle" class="me-1"></i>
                      Approve
                    </li>
                    <li style="width: 120px;" nz-menu-item (click)="StatusChangeConfirmation(data.OutpassId, 'On Hold')"
                      *ngIf="EnableOnHoldButton(data)">
                      <i nz-icon nzType="pause-circle" class="me-1"></i>
                      On Hold
                    </li>
                    <li style="width: 120px;" nz-menu-item
                      (click)="StatusChangeConfirmation(data.OutpassId, 'Rejected')" *ngIf="EnableApproveButton(data)">
                      <i nz-icon nzType="close-circle" class="me-1"></i>
                      Reject
                    </li>
                    <li style="width: 120px;" nz-menu-item
                      (click)="StatusChangeConfirmationEnhanced(data.OutpassId, 'Cancelled')"
                      *ngIf="EnableCancelButton(data)">
                      <i nz-icon nzType="stop" class="me-1"></i>
                      Cancel
                    </li>
                  </ul>
                </li>

              </ul>
            </nz-dropdown-menu>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
</div>




<nz-modal [nzWidth]="1000" [nzStyle]="{ top: '20px' }" [(nzVisible)]="isVisible" [nzTitle]="modalTitle"
  [nzContent]="modalContent" [nzFooter]="modalFooter" (nzOnCancel)="handleCancel()">
  <ng-template #modalTitle>In-Out pass</ng-template>

  <ng-template #modalContent>
    <!-- ✅ NEW: Loading indicator for return data -->
    <div *ngIf="isLoadingReturnData" style="text-align: center; padding: 20px;">
      <nz-spin nzSize="large" nzTip="Loading OutPass details for return processing..."></nz-spin>
    </div>

    <div *ngIf="!isLoadingReturnData">
      <nz-table nzSize="small" [nzPageSize]="100" style="width: 100%;" [nzData]="['']" #basicTable1 nzBordered>
        <thead>
          <tr>
            <th>S.No</th>
            <th nzWidth="100px">Created Mode</th>
            <th>Location</th>
            <th>Product Details</th>
            <th>Quantity</th>
            <th>Return Location</th>
            <th>Returned Quantity</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of this.OutPass?.OutpassItems || [];let i=index">
            <td>{{i+1}}</td>
            <td>
              <nz-tag [nzColor]="data.EntryType === 'Manual' ? 'blue' : 'green'">
                {{ data.EntryType }}
              </nz-tag>
            </td>
            <td>
              <div style="line-height: 1.2;">
                <div style="font-weight: 500;">{{ data.StoreName }}</div>
                <div style="font-size: 12px; color: #666;">{{ data.RackName }}</div>
              </div>
            </td>
            <td>
              <div style="line-height: 1.2;">
                <div style="font-weight: 500;">{{ data.ProductName }}</div>
                <div style="font-size: 12px; color: #666;">
                  <span *ngIf="data.BatchNo">Batch: {{ data.BatchNo }}</span>
                </div>
                <div style="font-size: 12px; color: #666;">
                  <span *ngIf="data.SerialNo">Serial: {{ data.SerialNo }}</span>
                </div>
                <div style="font-size: 12px; color: #666;">
                  <span *ngIf="data.ShortCode">Short Code: {{ data.ShortCode }}</span>
                </div>
              </div>
            </td>
            <td>{{ data.Quantity }}</td>
            <td>
              <div style="display: flex; flex-direction: column; gap: 8px;">
                <nz-select class="form-select" nzShowSearch name="StoreId" nzSize="default" nzPlaceHolder="Select Store"
                  [(ngModel)]="data.ReturnedStoreId" (ngModelChange)="GetStorewiseStock(data)" style="width: 100%;">
                  <nz-option *ngFor="let s of this.AdminStoreList;" [nzValue]="s.StoreId"
                    [nzLabel]="s.StoreName"></nz-option>
                </nz-select>
                <nz-select class="form-select" nzShowSearch name="RackId" nzSize="default" nzPlaceHolder="Select Rack"
                  [(ngModel)]="data.ReturnedRackId" (ngModelChange)="onReturnRackChange($event, data)"
                  style="width: 100%;">
                  <nz-option *ngFor="let s of this.RackList;" [nzValue]="s.RackId" [nzLabel]="s.RackName"></nz-option>
                </nz-select>
              </div>
            </td>
            <td>
              <div style="display: flex; flex-direction: column; gap: 8px;">
                <input nz-input type="number" name="ReturnedQuantity" [(ngModel)]="data.ReturnedQuantity" (min)="1"
                  (change)="ValidateReturnQuantity(data)" style="width: 100px;" />

                <!-- Reason for Less Quantity field - shown conditionally -->
                <div *ngIf="data.RequiresReason" style="width: 200px;">
                  <nz-form-item style="margin-bottom: 0;">
                    <nz-form-control nzErrorTip="Reason is required when returning less quantity">
                      <textarea nz-input [(ngModel)]="data.ReasonForLessQuantity"
                        placeholder="Reason for less quantity..." rows="2" maxlength="100"
                        style="width: 100%; font-size: 12px;"
                        [class.ant-input-status-error]="data.RequiresReason && !data.ReasonForLessQuantity">
                    </textarea>
                      <div style="font-size: 10px; color: #999; text-align: right;">
                        {{ (data.ReasonForLessQuantity || '').length }}/100
                      </div>
                    </nz-form-control>
                  </nz-form-item>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div> <!-- ✅ Close the !isLoadingReturnData div -->

  </ng-template>
  <ng-template #modalFooter>
    <div class="text-center">
      <a nz-button nzType="primary" (click)="Save()">Save</a>
    </div>
  </ng-template>

</nz-modal>

<nz-modal [nzWidth]="1200" [nzStyle]="{ top: '20px' }" [(nzVisible)]="IsProductsVisible" [nzTitle]="modalTitle1"
  [nzContent]="modalContent1" [nzFooter]="modalFooter1" (nzOnCancel)="handleProductsViewCancel()">
  <ng-template #modalTitle1>Products Details - {{ selectedOutpassForView?.CreateMode || 'Manual' }} Mode</ng-template>

  <ng-template #modalContent1>

    <!-- ✅ FIX: Enhanced view for different OutPass modes -->
    <div *ngIf="selectedOutpassForView?.CreateMode === 'Mixed'" style="margin-bottom: 20px;">
      <nz-alert nzType="info" nzMessage="This OutPass contains both manual and barcode items" nzShowIcon></nz-alert>
    </div>
    <div *ngIf="selectedOutpassForView?.CreateMode === 'Barcode'" style="margin-bottom: 20px;">
      <nz-alert nzType="success" nzMessage="This OutPass contains only barcode scanned items" nzShowIcon></nz-alert>
    </div>
    <div *ngIf="!selectedOutpassForView?.CreateMode || selectedOutpassForView?.CreateMode === 'Manual'"
      style="margin-bottom: 20px;">
      <nz-alert nzType="info" nzMessage="This OutPass contains only manual entry items" nzShowIcon></nz-alert>
    </div>

    <!-- Manual Items Section -->
    <div *ngIf="OutPassProducts && OutPassProducts.length > 0">
      <h4 style="margin-bottom: 10px; color: #1890ff;">
        <i nz-icon nzType="edit" style="margin-right: 8px;"></i>
        Manual Entry Items ({{ OutPassProducts.length }})
      </h4>
      <nz-table nzSize="small" [nzPageSize]="100" style="width: 100%;" [nzData]="['']" #manualTable nzBordered>
        <thead>
          <tr>
            <th>S.No</th>
            <th>Product</th>
            <th>Quantity</th>
            <th>From Store</th>
            <th>From Rack</th>
            <th>Batch</th>
            <th>Amount</th>
            <th>Unit</th>
            <th>Total Amount</th>
            <th *ngIf="PopUpOutPassType == 'Returnable'">Return Store</th>
            <th *ngIf="PopUpOutPassType == 'Returnable'">Return Rack</th>
            <th *ngIf="PopUpOutPassType == 'Returnable'">Return Completed By</th>
            <th *ngIf="PopUpOutPassType == 'Returnable'">Return Completed Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of this.OutPassProducts;let i=index">
            <td>{{i+1}}</td>
            <td>{{ data.ProductName }}</td>
            <td>{{ data.Quantity }}</td>
            <td>{{ data.StoreName }}</td>
            <td>{{ data.RackName }}</td>
            <td>{{ data.BatchNo }}</td>
            <td>{{ data.Amount }}</td>
            <td>{{ data.Unit }}</td>
            <td>{{ (data.Quantity * data.Amount).toFixed(2) }}</td>
            <td *ngIf="PopUpOutPassType == 'Returnable' && data.ReturnCompletedBy?.Name != null">{{
              data.ReturnedStoreName
              }}
            </td>
            <td *ngIf="PopUpOutPassType == 'Returnable' && data.ReturnCompletedBy?.Name != null">{{
              data.ReturnedRackName}}</td>
            <td *ngIf="PopUpOutPassType == 'Returnable' && data.ReturnCompletedBy?.Name != null">{{
              data.ReturnCompletedBy?.Name }}
            </td>
            <td *ngIf="PopUpOutPassType == 'Returnable' && data.ReturnCompletedBy?.Name != null">{{
              data.ReturnCompletedDate |
              DatetimeConverter}}</td>
            <td *ngIf="PopUpOutPassType == 'Returnable' && data.ReturnCompletedBy?.Name == null">Pending</td>
            <td *ngIf="PopUpOutPassType == 'Returnable' && data.ReturnCompletedBy?.Name == null">Pending</td>
            <td *ngIf="PopUpOutPassType == 'Returnable' && data.ReturnCompletedBy?.Name == null">Pending</td>
            <td *ngIf="PopUpOutPassType == 'Returnable' && data.ReturnCompletedBy?.Name == null">Pending</td>
          </tr>
        </tbody>
      </nz-table>
    </div>

    <!-- Barcode Items Section -->
    <div *ngIf="selectedOutpassForView?.BarcodeItems && selectedOutpassForView.BarcodeItems.length > 0"
      style="margin-top: 20px;">
      <h4 style="margin-bottom: 10px; color: #52c41a;">
        <i nz-icon nzType="barcode" style="margin-right: 8px;"></i>
        Barcode Scanned Items ({{ selectedOutpassForView.BarcodeItems.length }})
      </h4>
      <nz-table nzSize="small" [nzPageSize]="100" style="width: 100%;" [nzData]="['']" #barcodeTable nzBordered>
        <thead>
          <tr>
            <th>S.No</th>
            <th>Serial Number</th>
            <th>Product</th>
            <th>Quantity</th>
            <th>Amount</th>
            <th>Total Amount</th>
            <th>Packaging Unit</th>
            <th>Unit</th>
            <th *ngIf="PopUpOutPassType == 'Returnable'">Returned Quantity</th>
            <th *ngIf="PopUpOutPassType == 'Returnable'">Return Location</th>
            <th *ngIf="PopUpOutPassType == 'Returnable'">Return Completed By/Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let barcodeItem of selectedOutpassForView.BarcodeItems; let j=index">
            <td>{{j+1}}</td>
            <td style="font-family: monospace; font-weight: bold;">{{ barcodeItem.SerialNo }}</td>
            <td>{{ barcodeItem.ProductName }}</td>
            <td>{{ barcodeItem.Quantity | number:'1.2-2' }}</td>
            <td>
              <div> {{ barcodeItem.Amount | currency:'INR':'symbol':'1.2-2' }}</div>
              <div style="font-size: 8px; color: #888;">Price Per Unit As Per Inventory: <b>{{ barcodeItem.PricePerUnit
                  | currency:'INR':'symbol':'1.2-2' }}</b></div>
            </td>
            <td><strong>{{ (barcodeItem.Quantity * barcodeItem.Amount) | currency:'INR':'symbol':'1.2-2' }}</strong>
            </td>
            <td>{{ barcodeItem.PackagingUnit }}</td>
            <td>{{ barcodeItem.Unit }}</td>

            <!-- Return Information Columns for Returnable OutPasses -->
            <td *ngIf="PopUpOutPassType == 'Returnable'">
              <span *ngIf="barcodeItem.ReturnCompletedBy != null">
                {{ barcodeItem.ReturnedQuantity | number:'1.2-2' }}
              </span>
              <span *ngIf="barcodeItem.ReturnCompletedBy == null" style="color: #999; font-style: italic;">
                Pending
              </span>
            </td>
            <td *ngIf="PopUpOutPassType == 'Returnable'">
              <span *ngIf="barcodeItem.ReturnCompletedBy != null">
                <div> {{ barcodeItem.ReturnedStoreName }}</div>
                <div style="color: #888;">Rack: {{
                  barcodeItem.ReturnedRackName}}</div>
              </span>
              <span *ngIf="barcodeItem.ReturnCompletedBy == null" style="color: #999; font-style: italic;">
                Pending
              </span>
            </td>
            <td *ngIf="PopUpOutPassType == 'Returnable'">
              <span *ngIf="barcodeItem.ReturnCompletedBy != null">
                <div>{{ barcodeItem.ReturnCompletedBy }}</div>
                <div>{{ barcodeItem.ReturnCompletedDate | DatetimeConverter}}</div>
              </span>
              <span *ngIf="barcodeItem.ReturnCompletedBy == null" style="color: #999; font-style: italic;">
                Pending
              </span>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoadingOutPassDetails"
      style="margin-top: 20px; padding: 40px; text-align: center; background-color: #f5f5f5; border-radius: 6px;">
      <nz-spin nzSize="large" nzTip="Loading OutPass details...">
        <div style="height: 100px;"></div>
      </nz-spin>
    </div>

    <!-- No Items Message - Only show after loading completes -->
    <div
      *ngIf="!isLoadingOutPassDetails && (!OutPassProducts || OutPassProducts.length === 0) && (!selectedOutpassForView?.BarcodeItems || selectedOutpassForView.BarcodeItems.length === 0)"
      style="margin-top: 20px; padding: 20px; text-align: center; background-color: #f5f5f5; border-radius: 6px;">
      <nz-alert nzType="warning" nzMessage="No items found in this OutPass" nzShowIcon></nz-alert>
    </div>

    <!-- Summary Section for Mixed Mode -->
    <div *ngIf="selectedOutpassForView?.CreateMode === 'Mixed'"
      style="margin-top: 20px; padding: 15px; background-color: #f0f2f5; border-radius: 6px;">
      <h4 style="margin-bottom: 10px; color: #333;">OutPass Summary</h4>
      <div style="display: flex; justify-content: space-between; flex-wrap: wrap;">
        <div style="margin-right: 20px;">
          <strong>Manual Items:</strong> {{ OutPassProducts?.length || 0 }} items<br>
          <strong>Barcode Items:</strong> {{ selectedOutpassForView?.BarcodeItems?.length || 0 }} items<br>
          <strong>Total Items:</strong> {{ (OutPassProducts?.length || 0) +
          (selectedOutpassForView?.BarcodeItems?.length || 0) }}
        </div>
        <div>
          <strong>Manual Total:</strong> ₹{{ getManualItemsTotal() | number:'1.2-2' }}<br>
          <strong>Barcode Total:</strong> ₹{{ getBarcodeItemsTotal() | number:'1.2-2' }}<br>
          <strong>Grand Total:</strong> <span style="color: #1890ff; font-size: 16px;">₹{{ getGrandTotal() |
            number:'1.2-2' }}</span>
        </div>
      </div>
    </div>

    <!-- Summary Section for Barcode-Only Mode -->
    <div *ngIf="selectedOutpassForView?.CreateMode === 'Barcode' && selectedOutpassForView?.BarcodeItems?.length > 0"
      style="margin-top: 20px; padding: 15px; background-color: #f6ffed; border-radius: 6px;">
      <h4 style="margin-bottom: 10px; color: #333;">Barcode OutPass Summary</h4>
      <div style="display: flex; justify-content: space-between; flex-wrap: wrap;">
        <div style="margin-right: 20px;">
          <strong>Total Items:</strong> {{ selectedOutpassForView?.BarcodeItems?.length || 0 }} barcode items
        </div>
        <div>
          <strong>Total Amount:</strong> <span style="color: #52c41a; font-size: 16px;">₹{{ getBarcodeItemsTotal() |
            number:'1.2-2' }}</span>
        </div>
      </div>
    </div>

    <!-- Summary Section for Manual-Only Mode -->
    <div
      *ngIf="(!selectedOutpassForView?.CreateMode || selectedOutpassForView?.CreateMode === 'Manual') && OutPassProducts?.length > 0"
      style="margin-top: 20px; padding: 15px; background-color: #e6f7ff; border-radius: 6px;">
      <h4 style="margin-bottom: 10px; color: #333;">Manual OutPass Summary</h4>
      <div style="display: flex; justify-content: space-between; flex-wrap: wrap;">
        <div style="margin-right: 20px;">
          <strong>Total Items:</strong> {{ OutPassProducts?.length || 0 }} manual items
        </div>
        <div>
          <strong>Total Amount:</strong> <span style="color: #1890ff; font-size: 16px;">₹{{ getManualItemsTotal() |
            number:'1.2-2' }}</span>
        </div>
      </div>
    </div>

  </ng-template>
  <ng-template #modalFooter1>
    <!-- ✅ NEW: Enhanced footer with action buttons -->
    <div class="modal-footer-actions">
      <!-- Status Change Actions -->
      <div class="action-buttons" *ngIf="selectedOutpassForView">
        <button nz-button nzType="primary" nzGhost
          (click)="ChangeOutpassStatus(selectedOutpassForView.OutpassId, 'Approved')"
          *ngIf="EnableApproveButton(selectedOutpassForView)">
          <i nz-icon nzType="check-circle"></i>
          Approve
        </button>

        <button nz-button nzType="default"
          (click)="StatusChangeConfirmation(selectedOutpassForView.OutpassId, 'On Hold')"
          *ngIf="EnableOnHoldButton(selectedOutpassForView)">
          <i nz-icon nzType="pause-circle"></i>
          On Hold
        </button>

        <button nz-button nzType="primary" nzDanger nzGhost
          (click)="StatusChangeConfirmation(selectedOutpassForView.OutpassId, 'Rejected')"
          *ngIf="EnableApproveButton(selectedOutpassForView)">
          <i nz-icon nzType="close-circle"></i>
          Reject
        </button>

        <button nz-button nzType="primary" nzDanger
          (click)="StatusChangeConfirmationEnhanced(selectedOutpassForView.OutpassId, 'Cancelled')"
          *ngIf="EnableCancelButton(selectedOutpassForView)">
          <i nz-icon nzType="stop"></i>
          Cancel
        </button>
      </div>

      <!-- Close Button -->
      <div class="close-button">
        <button nz-button nzType="primary" (click)="handleProductsViewCancel()">Close</button>
      </div>
    </div>
  </ng-template>
</nz-modal>

<nz-modal [(nzVisible)]="IsActivitiesActionPopVisible" [nzStyle]="{ top: '20px' }" [nzWidth]="400"
  [nzTitle]="modalTitleDispatch" [nzContent]="modalContentDispatch" [nzFooter]="modalFooterDispatch"
  (nzOnCancel)="handleActivitiesActionCancel()">
  <ng-template #modalTitleDispatch>Purchase Order Action</ng-template>

  <ng-template #modalContentDispatch>
    <label><b>Status Change for Outpass No.: {{this.selectedOutpass?.OutpassNumber}}</b></label>
    <p></p>
    <div nz-col [nzSpan]="12">
      <nz-form-item>
        <nz-form-control [nzSpan]="24" nzErrorTip="Enter Amount">
          <label>Status</label>
          <nz-select class="form-select " nzShowSearch nzSize="default" [(ngModel)]="OutpassStatusAction.Status"
            name="Category" nzAllowClear nzPlaceHolder="Select Status">
            <nz-option *ngFor="let s of this.outpassStatusList;" [nzValue]="s" [nzLabel]="s"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>
    <label style="color: red;"><b>* Remark is
        required</b></label>
    <div>
      <nz-form-item>
        <nz-form-control nzErrorTip="Only 180 characters allowed.">
          <nz-textarea-count [nzMaxCharacterCount]="180">
            <textarea nz-input class="form-control mb-2" name="remark" [(ngModel)]="OutpassStatusAction.Remark"
              rows="3"></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>
    </div>
  </ng-template>

  <ng-template #modalFooterDispatch>
    <button nz-button nzType="primary" (click)="SaveStatusChange()" [nzLoading]="isLoading">Submit</button>
    <button nz-button nzType="default" (click)="handleActivitiesActionCancel()" [nzLoading]="isLoading">Cancel</button>
  </ng-template>
</nz-modal>

<nz-modal [(nzVisible)]="IsTransportVisible" [nzStyle]="{ top: '50px'}" [nzWidth]="750" [nzTitle]="modalTitle2"
  [nzContent]="modalContent2" [nzFooter]="modalFooter2" (nzOnCancel)="handletransportcancel()">
  <ng-template #modalTitle2>Add Transport for Outpass No: {{this.OutpassModify?.OutpassNumber }}</ng-template>

  <ng-template #modalContent2>
    <nz-descriptions nzBordered nzLayout="vertical" nzSize="small" [nzColumn]="2">

      <nz-descriptions-item nzTitle="Transport">
        <nz-select nzShowSearch class="form-select mb-2" (ngModelChange)="onSelectedTransportChange($event)"
          [(ngModel)]="OutpassModify.TransportId" nzSize="default" nzAllowClear nzPlaceHolder="Choose">
          <nz-option *ngFor="let s of this.TransportList;" [nzValue]="s.TransportId"
            [nzLabel]="s.TransportCompanyName"></nz-option>
        </nz-select>
      </nz-descriptions-item>
      <nz-descriptions-item nzTitle="Vehicle">
        <nz-select class="form-select" nzShowSearch name="Rack" nzSize="default" [(ngModel)]="OutpassModify.VehicleId"
          (ngModelChange)="onSelectedVehicleChange($event)" nzAllowClear nzPlaceHolder="Choose">
          <nz-option *ngFor="let s of this.TransportVehicleList;" [nzValue]="s.VehicleId"
            [nzLabel]="s.VehicleNumber"></nz-option>
          <nz-option nzValue="0" nzLabel="Others"></nz-option>
        </nz-select>

      </nz-descriptions-item>
    </nz-descriptions>
    <span style="color: crimson;"><b>{{this.VehicleStatus}}</b></span>
  </ng-template>

  <ng-template #modalFooter2>

    <button nz-button nzType="default" (click)="handletransportcancel()">Cancel</button>
    <button nz-button nzType="primary" (click)="SaveModifyOutpass()" [nzLoading]="isLoading">Save</button>
  </ng-template>
</nz-modal>

<nz-modal [(nzVisible)]="IsExtendExpectedReturnVisible" [nzStyle]="{ top: '50px'}" [nzWidth]="450"
  [nzTitle]="modalTitle3" [nzContent]="modalContent3" [nzFooter]="modalFooter3"
  (nzOnCancel)="handleExtendExpectedReturnCancel()">
  <ng-template #modalTitle3>Extend Expected Return</ng-template>

  <ng-template #modalContent3>

    <label><b>New Expected Return Date for Outpass No: {{this.selectedOutpass?.OutpassNumber }}</b></label>
    <p></p>
    <div nz-col [nzSpan]="12">
      <nz-form-item>
        <nz-form-control [nzSpan]="24">
          <label>Expected Return Date</label>
          <nz-date-picker [nzDisabledDate]="disabledPastDate" nzPlaceHolder="Return Date" class="form-control"
            [(ngModel)]="OutpassModify.ExpectedReturnDate" nzShowTime></nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </div>
    <label style="color: red;"><b>* Remark is
        required</b></label>
    <div>
      <nz-form-item>
        <nz-form-control nzErrorTip="Only 180 characters allowed.">
          <nz-textarea-count [nzMaxCharacterCount]="150">
            <textarea nz-input class="form-control mb-2" name="remark" [(ngModel)]="OutpassModify.Remark"
              rows="3"></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>
    </div>
  </ng-template>

  <ng-template #modalFooter3>

    <button nz-button nzType="default" (click)="handleExtendExpectedReturnCancel()">Cancel</button>
    <button nz-button nzType="primary" (click)="SaveModifyOutpass()" [nzLoading]="isLoading">Save</button>
  </ng-template>
</nz-modal>

<ng-template #totalTemplate let-total>
  Showing {{ (pageNumber - 1) * pageSize + 1 }} to {{ Math.min(pageNumber * pageSize, totalItemsCount) }} of {{
  totalItemsCount }} items
</ng-template>