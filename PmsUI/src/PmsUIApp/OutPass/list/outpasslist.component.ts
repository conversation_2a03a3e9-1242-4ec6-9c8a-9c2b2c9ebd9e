import { HttpClient } from "@angular/common/http";
import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { AngularCsv } from "angular-csv-ext/dist/Angular-csv";
import { NzModalService } from "ng-zorro-antd/modal";
import { environment } from "../../../environments/environment";
import { UserInfo } from "../../Authentication/UserInfo";
import { Modules, Responsibility, OutpassStatus } from "../../Models/Enums";
import { AdminStoreModel, OutPassPurposeModel, ProductCategoryModel, ProductFirstSubCategoryModel, ProductSecSubCategoryModel, RackModel, StoreModel } from "../../Models/MasterModel";
import { OutpassItemModel, OutPassModel, OutpassStatusActionModel, OutpassFilterVm, PaginatedResult, OutpassBarcodeItemModel } from "../../Models/OutPassModel";
import { AlertMessageService } from "../../Services/AlertMessageService";
import { AuthService } from "../../Services/auth.service";
import moment from 'moment';
import { LoadingService } from "src/PmsUIApp/Services/loadingService";
import { ProductModel } from "src/PmsUIApp/Models/ProductModel";
import { CustomerModel } from "src/PmsUIApp/Models/SupplierModel";
import { GateInModel } from "src/PmsUIApp/Models/GateInModel";
import { TransportModel, TransportVehicleModel } from "src/PmsUIApp/Models/TransportModel";
import { OutpassTimelineService } from "src/PmsUIApp/Services/OutpassTimelineService";
import { OutPassApprovalService } from "../services/OutPassApprovalService";
@Component({
  selector: "app-outpasslist",
  templateUrl: "./outpasslist.component.html",
  styleUrls: ["./outpasslist.component.css"],
})
export class OutPassListComponent implements OnInit {
  ApiUrl = environment.Api_Url;

  // Make Math available to the template
  Math = Math;

  isVisible = false;
  IsProductsVisible = false;
  PopUpOutPassType: string = 'Non-Returnable';
  isLoading: boolean = false;
  isTableLoading: boolean = false;
  isLoadingOutPassDetails: boolean = false;
  isLoadingReturnData: boolean = false; // ✅ NEW: Loading state for return processing
  typeList: any[] = [];
  searchValue = "";
  myDateValue: Date | undefined;
  toDate: Date | undefined;
  isValidDate: any

  visible = false;
  error: any = { isError: false, errorMessage: "" };
  OutPassList: OutPassModel[] = [];
  outPassOriginal: OutPassModel[] = [];
  OutPass: OutPassModel = new OutPassModel();
  OutPassProducts: OutpassItemModel[] = [];
  selectedOutpassForView: OutPassModel | null = null;

  // ✅ NEW: Store original arrays for proper backend submission
  originalOutpassItems: OutpassItemModel[] = [];
  originalBarcodeItems: OutpassBarcodeItemModel[] = [];

  AdminStoreList: AdminStoreModel[] = [];
  RackListOriginal: RackModel[] = [];
  RackList: RackModel[] = [];
  fields: OutPassModel = new OutPassModel();
  exportoptions = {
    headers: [
      "OutpassTo",
      "OutpassType",
      "OutpassDate",
      "OutpassNumber",
      "Purpose",
      "Remark"

    ],
  };
  exportfields: OutPassModel[] = [];

  permission = {
    View: false,
    Add: false,
    Cancel: false,
    Approval: false,
    Manage: false,
    AddTransport: false
  }
  count: number;
  totalItemsCount: number = 0;
  totalPages: number = 1;
  pageSize: number = 20;
  pageNumber: number = 1;
  pageSizeOptions = [10, 20, 50, 100];

  // Updated request with pagination properties
  request: OutpassFilterVm = {
    outpassToCustomerId: 0,
    outpassTo: "",
    outpassNumber: "",
    outpassType: "",
    purposeId: 0,
    fromDate: new Date(new Date(new Date().setHours(0, 0, 1)).setDate(new Date().getDate() - 7)),
    toDate: new Date(new Date().setHours(23, 59, 59)),
    isOutpassIn: null,
    outpassProductName: "",
    pageNumber: 1,
    pageSize: 20,
    includeItems: true
  }
  FilteredProductList: ProductModel[];
  ProductList: ProductModel[];
  SelectedProductType: string;
  ProductCategoryList: ProductCategoryModel[];
  CategoryID: number;
  ProductFirstSubCategoryList: ProductFirstSubCategoryModel[];
  FirstCategoryID: number;
  ProductSecSubCategoryList: ProductSecSubCategoryModel[];
  SecondCategoryID: number;
  CustomerList: CustomerModel[];

  // ✅ NEW: Toggle-based product filtering properties
  isLegacyMode: boolean = false; // Default to Product Master mode
  selectedProductFromCatalog: ProductModel = null; // Separate variable for catalog selection
  IsActivitiesActionPopVisible: boolean = false;
  OutpassStatusAction: OutpassStatusActionModel = new OutpassStatusActionModel();
  outpassStatusList = Object.values(OutpassStatus);
  selectedOutpass: OutPassModel = new OutPassModel();
  IsTransportVisible: boolean = false;
  IsExtendExpectedReturnVisible: boolean = false;
  OutpassModify: OutPassModel = new OutPassModel();
  TransportList: TransportModel[];
  TransportVehicleList: TransportVehicleModel[] = [];
  VehicleStatus: string = null;
  PurposeList: OutPassPurposeModel[];
  constructor(public http: HttpClient,
    private alertService: AlertMessageService,
    private modalService: NzModalService,
    private route: ActivatedRoute,
    private auth: AuthService,
    private router: Router,
    private loader: LoadingService,
    private OutpassTimeline: OutpassTimelineService,
    private outPassApprovalService: OutPassApprovalService) { }

  ngOnInit() {
    this.permission.View = this.auth.CheckResponsibility(Modules.OutPass, Responsibility.View);
    this.permission.Add = this.auth.CheckResponsibility(Modules.OutPass, Responsibility.Add);
    this.permission.Cancel = this.auth.CheckResponsibility(Modules.OutPass, Responsibility.Cancel);
    this.permission.Approval = this.auth.CheckResponsibility(Modules.OutPass, Responsibility.Approval);
    this.permission.Manage = this.auth.CheckResponsibility(Modules.OutPass, Responsibility.Manage);
    this.permission.AddTransport = this.auth.CheckResponsibility(Modules.Transport, Responsibility.Add);

    if (this.permission.View != true) {
      this.router.navigate(['/home/<USER>']);
    }

    // ✅ NEW: Load user's preferred filter mode from localStorage
    this.loadProductFilterModePreference();

    this.GetAllOutPass();
    this.GetAllStore();
    this.GetAllRack();
  }
  GetAllStore() {
    let url = this.ApiUrl + 'store/getallstores';
    this.http.get<StoreModel[]>(url).subscribe(
      (res) => {


        res.forEach((x) => {
          var str = new AdminStoreModel();
          str.StoreId = x.StoreId;
          str.StoreName = x.StoreName;
          this.AdminStoreList.push(str);
        });
        this.GetUserStores(UserInfo.EmailID);
      },
      (res) => {
        this.count++;
        if (this.count < 2) {
          this.GetAllStore();
        }
      }
    );
  }
  GetAllRack() {
    this.isTableLoading = true;
    let url = this.ApiUrl + "rack/getallracks";
    this.http.get<RackModel[]>(url).subscribe(res => {
      this.RackList = res;
      this.RackListOriginal = res;
      this.isTableLoading = false;
    }, res => {
      this.count++;
      if (this.count < 2) { this.GetAllRack(); }
    });
  }
  GetUserStores(UserName: string) {
    this.isTableLoading = true;
    this.AdminStoreList.forEach((x) => (x.IsChecked = false));
    let url = this.ApiUrl + 'user/getuserstores/' + UserName;
    this.http.get<number[]>(url).subscribe(
      (res) => {
        console.log(`res is what`, res);
        this.AdminStoreList.forEach((s) => {
          if (res.includes(s.StoreId)) {
            s.IsChecked = true;
          }
        });
        this.AdminStoreList = this.AdminStoreList.filter((x) => x.IsChecked);

        this.isTableLoading = false;
      },
      (res) => { }
    );
  }
  GetStorewiseStock(data: OutpassItemModel) {
    this.RackList = this.RackListOriginal.filter(x => x.StoreId == data.ReturnedStoreId);
  }

  // ✅ NEW: Handle rack selection and auto-update store ID
  onReturnRackChange(selectedRackId: number, data: OutpassItemModel) {
    // Update the rack ID
    data.ReturnedRackId = selectedRackId;

    // Auto-update the store ID based on selected rack
    const selectedRack = this.RackListOriginal.find(r => r.RackId === selectedRackId);
    if (selectedRack) {
      data.ReturnedStoreId = selectedRack.StoreId;
    }
  }
  GetAllOutPass() {
    this.isTableLoading = true;

    // Update pagination parameters in the request
    this.request.pageNumber = this.pageNumber;
    this.request.pageSize = this.pageSize;

    let url = this.ApiUrl + 'outpass/getalloutpasswithfilters';
    this.http.post<PaginatedResult<OutPassModel>>(url, this.request).subscribe({
      next: (res) => {
        // Update pagination information
        this.OutPassList = res.Items || [];
        this.outPassOriginal = [...this.OutPassList];
        this.totalItemsCount = res.TotalCount;
        this.totalPages = res.TotalPages;

        // Process the data
        this.exportfields = [];
        let count = 0;
        this.OutPassList.forEach((x) => {
          count++;
          x.SerialNo = ((this.pageNumber - 1) * this.pageSize) + count; // Calculate correct serial number

          this.fields = new OutPassModel();
          this.fields.OutpassTo = x.OutpassTo;
          this.fields.OutpassType = x.OutpassType;
          this.fields.OutpassDate = x.OutpassDate;
          this.fields.OutpassNumber = x.OutpassNumber;
          this.fields.Purpose = x.Purpose;
          this.fields.Remark = x.Remark;

          this.exportfields.push(this.fields);
        });

        this.isTableLoading = false;
      },
      error: (error) => {
        console.error('Error fetching outpass data:', error);
        this.isTableLoading = false;
        this.count++;
        if (this.count < 2) {
          this.GetAllOutPass();
        }
      }
    });
  }
  handleCancel(): void {
    this.isVisible = false;
  }
  reset(): void {
    this.searchValue = "";
    this.pageNumber = 1;
    this.pageSize = 20;
    this.myDateValue = undefined;
    this.toDate = undefined;
    this.GetAllOutPass();
  }

  // Pagination methods
  onPageIndexChange(pageIndex: number): void {
    this.pageNumber = pageIndex;
    this.GetAllOutPass();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageNumber = 1; // Reset to first page when changing page size
    this.GetAllOutPass();
  }

  nextPage(): void {
    if (this.pageNumber < this.totalPages) {
      this.pageNumber++;
      this.GetAllOutPass();
    }
  }

  previousPage(): void {
    if (this.pageNumber > 1) {
      this.pageNumber--;
      this.GetAllOutPass();
    }
  }
  export() {
    var exportdate = moment(new Date()).format("-DDMMYYYY-hhmmss");
    if (this.exportfields.length > 0)
      new AngularCsv(
        this.exportfields,
        'Out-Pass-export' + exportdate,
        this.exportoptions
      );
  }
  onKeydown(event: any) {
    if (event.target.selectionStart === 0 && event.code === "Space" || event.key === "Enter" && event.keyCode === 13) {

      event.preventDefault();
      event = this.search()
    }
  }
  ValidateText() {
    this.searchValue.trim()
    this.searchValue = this.searchValue.trim();

  }
  reverseAndTimeStamp(dateString: any) {
    const reverse = new Date(dateString.split('-').reverse().join('-'));
    return reverse.getTime();
  }
  validateDates(sDate: string, eDate: string) {
    this.isValidDate = true;
    if ((sDate == null || eDate == null)) {
      this.alertService.error("Start date and end date are required.");
      this.isValidDate = false;
    }
    const isReversed = moment(sDate.split('-').reverse().join('-')
    ).isAfter(eDate.split('-').reverse().join('-')
    )
    if ((sDate != null && eDate != null) && isReversed) {
      this.alertService.error("End date should be grater then start date.");
      this.isValidDate = false;
    }
    return this.isValidDate;
  }

  search() {
    // Update request with search parameters
    if (this.myDateValue) {
      this.request.fromDate = this.myDateValue;
    }

    if (this.toDate) {
      this.request.toDate = this.toDate;
    }

    if (this.searchValue) {
      // Set the appropriate search field based on the search value
      // You can modify this to search in specific fields as needed
      if (this.searchValue.match(/^[A-Za-z\s]+$/)) {
        // If search value contains only letters and spaces, search in text fields
        this.request.outpassTo = this.searchValue;
        this.request.outpassType = this.searchValue;
        this.request.outpassProductName = this.searchValue;
      } else if (this.searchValue.match(/^[0-9]+$/)) {
        // If search value contains only numbers, search in number fields
        this.request.outpassNumber = this.searchValue;
      } else {
        // For mixed content, search in all fields
        this.request.outpassNumber = this.searchValue;
        this.request.outpassTo = this.searchValue;
      }
    } else {
      // Clear search fields if search value is empty
      this.request.outpassNumber = "";
      this.request.outpassTo = "";
      this.request.outpassType = "";
      this.request.outpassProductName = "";
    }

    // Reset to first page when searching
    this.pageNumber = 1;
    this.request.pageNumber = 1;

    // Call API with updated request
    this.GetAllOutPass();
  }

  print(data: OutPassModel) {
    window.open(`${window.location.origin}/outpass/print/` + data.OutpassId);
  }

  // ✅ NEW: Print packing list for barcode/mixed mode OutPasses
  printPackingList(data: OutPassModel) {
    window.open(`${window.location.origin}/outpass/packinglistprint/` + data.OutpassId);
  }
  cancel() { }
  OpenInoutPass(data: OutPassModel) {
    // ✅ ENHANCED: Defensive programming and proper initialization
    if (!data || !data.OutpassId) {
      this.alertService.error('Invalid OutPass data');
      return;
    }

    // Initialize state
    this.isVisible = true;
    this.isLoadingReturnData = true;
    this.OutPass = new OutPassModel(); // Initialize to prevent template errors
    this.originalOutpassItems = [];
    this.originalBarcodeItems = [];

    // Fetch complete OutPass details including barcode items for return processing
    this.GetOutPassByIdForReturn(data.OutpassId);
  }

  // ✅ ENHANCED: Method to fetch complete OutPass details for return processing
  GetOutPassByIdForReturn(outpassId: number) {
    if (!outpassId || outpassId <= 0) {
      this.alertService.error('Invalid OutPass ID');
      return;
    }

    // Start loading
    this.isLoadingReturnData = true;

    let url = this.ApiUrl + 'Outpass/getoutpassbyid?outpassid=' + outpassId;
    this.http.get<OutPassModel>(url).subscribe({
      next: (res) => {
        try {
          // Defensive programming - ensure we have a valid response
          if (!res) {
            this.alertService.error('No OutPass data received');
            return;
          }

          // Initialize the OutPass object with proper defaults
          this.OutPass = {
            ...new OutPassModel(),
            ...res,
            OutpassItems: res.OutpassItems || [],
            BarcodeItems: res.BarcodeItems || []
          };

          // Prepare items for return processing with clean separation
          this.PrepareReturnItems();
        } catch (error) {
          console.error('Error processing OutPass data:', error);
          this.alertService.error('Error processing OutPass data');
          this.OutPass = new OutPassModel();
          this.originalOutpassItems = [];
          this.originalBarcodeItems = [];
        } finally {
          this.isLoadingReturnData = false;
        }
      },
      error: (error) => {
        this.alertService.error('Failed to load OutPass details');
        console.error('Error loading OutPass details:', error);

        // Initialize empty OutPass to prevent template errors
        this.OutPass = new OutPassModel();
        this.originalOutpassItems = [];
        this.originalBarcodeItems = [];
        this.isLoadingReturnData = false;
      }
    });
  }

  // ✅ OPTIMIZED: Clean separation method for return processing
  PrepareReturnItems() {
    // Defensive programming - ensure OutPass is initialized
    if (!this.OutPass) {
      console.error('OutPass object is not initialized');
      this.OutPass = new OutPassModel();
      return;
    }

    // Initialize arrays if they don't exist
    this.OutPass.OutpassItems = this.OutPass.OutpassItems || [];
    this.OutPass.BarcodeItems = this.OutPass.BarcodeItems || [];

    // Store original arrays for backend submission (deep copy to avoid reference issues)
    this.originalOutpassItems = JSON.parse(JSON.stringify(this.OutPass.OutpassItems));
    this.originalBarcodeItems = JSON.parse(JSON.stringify(this.OutPass.BarcodeItems));

    // Create a combined array ONLY for UI display purposes
    let combinedItemsForDisplay: any[] = [];

    // Add manual items with entry type for display
    this.OutPass.OutpassItems.forEach(item => {
      combinedItemsForDisplay.push({
        ...item,
        EntryType: 'Manual',
        SerialNo: item.SerialNo || '', // Manual items typically don't have serial numbers
        ShortCode: '', // Manual items don't have short codes
        IsManualItem: true,
        StockLabelId: item.StockLabelId || null, // Manual items should have null StockLabelId
        // Ensure all required fields are present
        ReturnedQuantity: item.ReturnedQuantity || 0,
        ReturnedStoreId: item.ReturnedStoreId || 0,
        ReturnedRackId: item.ReturnedRackId || 0,
        ReasonForLessQuantity: item.ReasonForLessQuantity || '',
        RequiresReason: item.RequiresReason || false
      });
    });

    // Add barcode items with entry type for display
    this.OutPass.BarcodeItems.forEach(item => {
      combinedItemsForDisplay.push({
        // Map barcode item properties to match OutpassItemModel structure for display
        OutpassItemId: item.OutpassItemId || 0,
        OutpassId: this.OutPass.OutpassId,
        StockProductId: item.StockProductId,
        ProductId: item.ProductId,
        ProductName: item.ProductName,
        RackName: item.RackName || '',
        StoreName: item.StoreName || '',
        BatchNo: item.BatchNo || '',
        Quantity: item.Quantity,
        Amount: item.Amount,
        Total: (item.Amount || 0) * (item.Quantity || 0),
        RackId: item.CurrentRackId,
        Unit: item.Unit || '',
        ReturnedQuantity: item.ReturnedQuantity || 0,
        ReturnedStoreId: item.ReturnedStoreId || 0,
        ReturnedRackId: item.ReturnedRackId || 0,
        ReturnCompletedBy: item.ReturnCompletedBy || null,
        ReturnCompletedDate: item.ReturnCompletedDate || '',
        OutpassType: this.OutPass.OutpassType,
        ReturnedRackName: item.ReturnedRackName || '',
        ReturnedStoreName: item.ReturnedStoreName || '',
        ReasonForLessQuantity: item.ReasonForLessQuantity || '',
        RequiresReason: item.RequiresReason || false,
        // Enhanced properties for return functionality
        EntryType: 'Barcode',
        SerialNo: item.SerialNo || '',
        ShortCode: item.ShortCode || '',
        IsManualItem: false,
        StockLabelId: item.StockLabelId // ✅ CRITICAL: Preserve StockLabelId for backend relationship
      });
    });

    // Use combined items ONLY for display - original arrays preserved for backend
    this.OutPass.OutpassItems = combinedItemsForDisplay;
  }

  // ✅ ENHANCED: Restore original arrays and update with return data from UI
  RestoreAndUpdateOriginalArrays() {
    // Defensive programming - ensure arrays exist
    if (!this.OutPass || !this.OutPass.OutpassItems) {
      console.error('OutPass or OutpassItems is not initialized');
      return;
    }

    // Ensure original arrays exist
    this.originalOutpassItems = this.originalOutpassItems || [];
    this.originalBarcodeItems = this.originalBarcodeItems || [];

    // Update manual items with return data from combined display array
    const manualItemsFromDisplay = this.OutPass.OutpassItems.filter(item => item.EntryType === 'Manual');
    this.originalOutpassItems.forEach(originalItem => {
      const displayItem = manualItemsFromDisplay.find(item => {
        // Try multiple matching strategies for robustness
        return item.OutpassItemId === originalItem.OutpassItemId ||
          (item.StockProductId === originalItem.StockProductId &&
            item.ProductId === originalItem.ProductId &&
            item.RackId === originalItem.RackId);
      });

      if (displayItem) {
        // Update return data from UI
        originalItem.ReturnedQuantity = displayItem.ReturnedQuantity || 0;
        originalItem.ReturnedRackId = displayItem.ReturnedRackId || 0;
        originalItem.ReturnedStoreId = displayItem.ReturnedStoreId || 0;
        originalItem.ReasonForLessQuantity = displayItem.ReasonForLessQuantity || '';
        originalItem.RequiresReason = displayItem.RequiresReason || false;
      }
    });

    // Update barcode items with return data from combined display array
    const barcodeItemsFromDisplay = this.OutPass.OutpassItems.filter(item => item.EntryType === 'Barcode');

    this.originalBarcodeItems.forEach(originalItem => {
      const displayItem = barcodeItemsFromDisplay.find(item =>
        item.StockLabelId === originalItem.StockLabelId && item.StockLabelId > 0
      );

      if (displayItem) {
        // Update return data from UI
        originalItem.ReturnedQuantity = displayItem.ReturnedQuantity || 0;
        originalItem.ReturnedRackId = displayItem.ReturnedRackId || 0;
        originalItem.ReturnedStoreId = displayItem.ReturnedStoreId || 0;
        originalItem.ReasonForLessQuantity = displayItem.ReasonForLessQuantity || '';
        originalItem.RequiresReason = displayItem.RequiresReason || false;
      }
    });

    // Restore original arrays for backend submission
    this.OutPass.OutpassItems = [...this.originalOutpassItems];
    this.OutPass.BarcodeItems = [...this.originalBarcodeItems];
  }
  Save() {
    // ✅ CRITICAL FIX: Restore original arrays and update them with return data
    this.RestoreAndUpdateOriginalArrays();

    var StoreItemsCount = this.OutPass.OutpassItems.filter(x => x.RackId > 0).length;
    var ReturnStoreItemsCount = this.OutPass.OutpassItems.filter(x => x.ReturnedRackId > 0).length;
    if (StoreItemsCount > ReturnStoreItemsCount) {
      this.alertService.error('Please select return rack for all store products');
      return;
    }

    // Validate reason for less quantity for manual items
    const itemsRequiringReason = this.OutPass.OutpassItems.filter(x => x.RequiresReason);
    const itemsWithoutReason = itemsRequiringReason.filter(x => !x.ReasonForLessQuantity || x.ReasonForLessQuantity.trim() === '');

    // Validate reason for less quantity for barcode items
    const barcodeItemsRequiringReason = this.OutPass.BarcodeItems.filter(x => x.RequiresReason);
    const barcodeItemsWithoutReason = barcodeItemsRequiringReason.filter(x => !x.ReasonForLessQuantity || x.ReasonForLessQuantity.trim() === '');

    if (itemsWithoutReason.length > 0 || barcodeItemsWithoutReason.length > 0) {
      const allItemsWithoutReason = [...itemsWithoutReason, ...barcodeItemsWithoutReason];
      const productNames = allItemsWithoutReason.map(x => x.ProductName).join(', ');
      this.alertService.error(`Please provide reason for less quantity for: ${productNames}`);
      return;
    }

    this.OutPass.AddedBy = UserInfo.EmailID;
    let url = this.ApiUrl + 'outpass/inOutpass';
    this.http.post<any>(url, this.OutPass).subscribe({
      next: (res) => {
        this.alertService.success('Outpass Saved Successfully');
        this.isLoading = false;
        this.handleCancel();
        this.GetAllOutPass();
      },
      error: (res) => {
        this.alertService.error(res.error);
        this.isLoading = false;
      },
    });
  }
  OpenProductList(data: OutpassItemModel[], OutpassType: string) {
    this.OutPassProducts = data
    this.PopUpOutPassType = OutpassType;
    this.IsProductsVisible = true;
  }

  // ✅ FIX: Enhanced method to open product list with full OutPass details
  OpenProductListWithDetails(outpass: OutPassModel) {
    // Fetch complete OutPass details including barcode items
    this.GetOutPassByIdForView(outpass.OutpassId);
    this.PopUpOutPassType = outpass.OutpassType;
    this.IsProductsVisible = true;
  }

  // ✅ FIX: Method to fetch complete OutPass details for view
  GetOutPassByIdForView(outpassId: number) {
    // ✅ START LOADING: Show loading indicator
    this.isLoadingOutPassDetails = true;

    let url = this.ApiUrl + 'outpass/getoutpassbyid?outpassid=' + outpassId;
    this.http.get<OutPassModel>(url).subscribe(
      (res) => {
        this.selectedOutpassForView = res;
        // ✅ BACKEND HANDLES SEPARATION: OutpassItems now contains only manual items
        // BarcodeItems contains only barcode-scanned items - no frontend filtering needed
        this.OutPassProducts = res.OutpassItems || [];

        // Calculate totals for each manual item
        this.OutPassProducts.forEach(item => {
          item.Total = (item.Quantity || 0) * (item.Amount || 0);
        });

        // Calculate totals for each barcode item if they exist
        if (res.BarcodeItems && res.BarcodeItems.length > 0) {
          res.BarcodeItems.forEach(barcodeItem => {
            (barcodeItem as any).Total = (barcodeItem.Amount || 0) * (barcodeItem.Quantity || 1);
          });
        }

        // ✅ STOP LOADING: Hide loading indicator on success
        this.isLoadingOutPassDetails = false;
      },
      (error) => {
        console.error('Error fetching OutPass details:', error);
        this.alertService.error('Failed to load OutPass details');
        // ✅ STOP LOADING: Hide loading indicator on error
        this.isLoadingOutPassDetails = false;
      }
    );
  }
  handleProductsViewCancel(): void {
    this.IsProductsVisible = false;
    this.OutPassProducts = [];
    this.selectedOutpassForView = null;
    // ✅ RESET LOADING: Clear loading state when modal is closed
    this.isLoadingOutPassDetails = false;
  }

  // ✅ FIX: Calculation methods for view details summary
  getManualItemsTotal(): number {
    if (!this.OutPassProducts) return 0;
    return this.OutPassProducts.reduce((total, item) => total + ((item.Amount || 0) * (item.Quantity || 0)), 0);
  }

  getBarcodeItemsTotal(): number {
    if (!this.selectedOutpassForView?.BarcodeItems) return 0;
    return this.selectedOutpassForView.BarcodeItems.reduce((total, item) => total + ((item.Amount || 0) * (item.Quantity || 1)), 0);
  }

  getGrandTotal(): number {
    return this.getManualItemsTotal() + this.getBarcodeItemsTotal();
  }
  GetProductName(data: OutPassModel) {
    // ✅ FIX: Consider both manual and barcode items for product display
    const manualItemsCount = data.OutpassItems?.length || 0;
    const barcodeItemsCount = data.BarcodeItems?.length || 0;
    const totalItemsCount = manualItemsCount + barcodeItemsCount;

    if (totalItemsCount == 0) {
      return 'NA'
    }
    else if (totalItemsCount == 1) {
      // Return the single item name (either manual or barcode)
      if (manualItemsCount > 0) {
        return data.OutpassItems[0].ProductName;
      } else {
        return data.BarcodeItems[0].ProductName;
      }
    }
    else {
      // Show first item name and count of additional items
      let firstProductName = '';
      if (manualItemsCount > 0) {
        firstProductName = data.OutpassItems[0].ProductName;
      } else {
        firstProductName = data.BarcodeItems[0].ProductName;
      }

      const modeIndicator = data.CreateMode === 'Mixed' ? ' (Mixed)' :
        data.CreateMode === 'Barcode' ? ' (Barcode)' : '';

      return firstProductName + '<span class="spn"> & <a class="btn-link"> ' + (totalItemsCount - 1) + ' more </a></span>' + modeIndicator;
    }
  }
  GetCellColorforNonStoreItems(data: OutPassModel) {
    if (data.OutpassItems.filter(x => x.StockProductId < 1).length > 0) {
      return 'yellow';
    }
    else {
      return '';
    }
  }
  onFilterPanelOpen(data: any) {
    if (data == true) {
      this.loader.show();
      this.GetAllProducts();
      this.GetAllCustomer();
      this.getAllPurpose();
    }
  }
  onSelectedProductTypeChange() {
    this.FilteredProductList = this.ProductList.filter(
      (x) => x.ProductType == this.SelectedProductType
    );
    this.GetAllProductCategory();
  }
  GetAllProducts() {
    let url = this.ApiUrl + 'product/getallproducts';
    this.http.get<ProductModel[]>(url).subscribe(
      (res) => {
        this.ProductList = res;
      },
      (res) => {
        this.count++;
        if (this.count < 2) {
          this.GetAllProducts();
        }
      }
    );
  }
  GetAllProductCategory() {
    let url = this.ApiUrl + 'productcategory/getallproductcategoriesforlisting';
    this.http.get<ProductCategoryModel[]>(url).subscribe(
      (res) => {
        const filteredProductCategoryIds = this.FilteredProductList.map(x => x.ProductCategoryId);
        this.ProductCategoryList = res.filter(x => filteredProductCategoryIds.includes(x.ProductCategoryId));
      },
      (res) => { }
    );
  }

  GetAllFirstCategory(data: any, id: number = 0) {
    this.FilteredProductList = this.ProductList.filter(
      (x) =>
        x.ProductType == this.SelectedProductType &&
        x.ProductCategoryId == this.CategoryID
    );
    let url = this.ApiUrl + 'productcategory/getallproductfirstsubcategories';
    this.http.get<ProductFirstSubCategoryModel[]>(url).subscribe(
      (res) => {
        this.ProductFirstSubCategoryList = res.filter(
          (x) => x.ProductCategoryId == data
        );

      },
      (res) => { }
    );
  }
  GetAllSecondCategory(data: any, id: number = 0) {
    this.FilteredProductList = this.ProductList.filter(
      (x) =>
        x.ProductType == this.SelectedProductType &&
        x.ProductFirstSubCategoryId == this.FirstCategoryID
    );
    let url = this.ApiUrl + 'productcategory/getallproductsecsubcategories';
    this.http.get<ProductSecSubCategoryModel[]>(url).subscribe(
      (res) => {
        this.ProductSecSubCategoryList = res.filter(
          (x) => x.ProductFirstSubCategoryId == data
        );
      },
      (res) => { }
    );
  }
  GetSecondCategoryFilteredProduct() {
    this.FilteredProductList = this.ProductList.filter(
      (x) =>
        x.ProductType == this.SelectedProductType &&
        x.ProductSecSubCategoryId == this.SecondCategoryID
    );
  }
  onSelectedProductChange(data: any) {
    var fp = this.FilteredProductList.filter(
      (x) => x.ProductId == data
    )[0];
    this.CategoryID = fp.ProductCategoryId;
    let url = this.ApiUrl + 'productcategory/getallproductfirstsubcategories';
    this.http.get<ProductFirstSubCategoryModel[]>(url).subscribe(
      (res) => {
        this.ProductFirstSubCategoryList = res.filter(
          (x) => x.ProductCategoryId == this.CategoryID
        );
        this.FirstCategoryID = fp.ProductFirstSubCategoryId;
        let url = this.ApiUrl + 'productcategory/getallproductsecsubcategories';
        this.http.get<ProductSecSubCategoryModel[]>(url).subscribe(
          (res) => {
            this.ProductSecSubCategoryList = res.filter(
              (x) => x.ProductFirstSubCategoryId == this.FirstCategoryID
            );
            this.SecondCategoryID = fp.ProductSecSubCategoryId;
          },
          (res) => { }
        );
      },
      (res) => { }
    );
  }
  GetAllCustomer() {
    this.loader.show();
    let url = this.ApiUrl + "customer/getallcustomers";
    this.http.get<CustomerModel[]>(url).subscribe({
      next: res => {
        this.CustomerList = res;
        this.loader.hide();
      },
      error: res => {
        this.loader.hide();
        this.count++;
        if (this.count < 2) { this.GetAllCustomer(); }
      }
    });
  }
  handleActivitiesActionCancel() {
    this.IsActivitiesActionPopVisible = false;
  }
  ShowOutpassStatusChange(data: OutPassModel) {
    this.OutpassStatusAction = new OutpassStatusActionModel();
    this.selectedOutpass = data
    this.IsActivitiesActionPopVisible = true;
  }
  SaveStatusChange() {
    this.OutpassStatusAction.OutpassId = this.selectedOutpass.OutpassId;
    let url = this.ApiUrl + 'outpass/outpassstatusactions';
    this.http.post<any>(url, this.OutpassStatusAction).subscribe({
      next: (res) => {
        this.alertService.success('Outpass status updated successfully');
        this.IsActivitiesActionPopVisible = false;
        this.GetAllOutPass(); // Refresh the list
      },
      error: (res) => {
        this.alertService.error(res.error);
      },
    });
  }
  StatusChangeConfirmation(OutpassId: number, Status: string) {
    const modal1 = this.modalService.confirm({
      nzTitle: 'Confirmation',
      nzContent: 'Are you sure to proceed?',
      nzOkText: 'Yes',
      nzCancelText: 'No',
      nzOnOk: () => this.ChangeOutpassStatus(OutpassId, Status),
    });
    setTimeout(() => modal1.destroy(), 30000);
  }
  async ChangeOutpassStatus(OutpassId: number, Status: string) {
    this.OutpassStatusAction = new OutpassStatusActionModel();
    this.OutpassStatusAction.OutpassId = OutpassId;
    this.OutpassStatusAction.Status = Status;

    // Special handling for approval status
    if (Status === 'Approved') {
      await this.handleApprovalWithStockRemoval(OutpassId);
    } else {
      // Handle other status changes normally
      await this.updateStatusNormally();
    }
  }

  /**
   * Handle approval with barcode stock removal
   */
  private async handleApprovalWithStockRemoval(outpassId: number): Promise<void> {
    try {
      // Show loading indicator
      this.isLoading = true;

      // Validate stock removal eligibility
      const validation = await this.outPassApprovalService.validateStockRemovalEligibility(outpassId);

      if (!validation.isEligible) {
        this.alertService.error(validation.message);
        return;
      }

      // Show warnings if any
      if (validation.warnings.length > 0) {
        const warningMessage = `Warnings found:\n${validation.warnings.join('\n')}\n\nDo you want to proceed?`;

        const confirmed = await this.showConfirmationDialog(
          'Approval Warnings',
          warningMessage
        );

        if (!confirmed) {
          return;
        }
      }

      // Show confirmation for approval with stock removal
      const confirmMessage = validation.message.includes('barcode labels')
        ? `${validation.message}\n\nThis will remove stock from inventory. Are you sure you want to approve?`
        : 'Are you sure you want to approve this Out Pass?';

      const confirmed = await this.showConfirmationDialog(
        'Confirm Approval',
        confirmMessage
      );

      if (!confirmed) {
        return;
      }

      // Proceed with approval and stock removal
      const success = await this.outPassApprovalService.approveOutPassWithStockRemoval(
        outpassId,
        this.OutpassStatusAction
      );

      if (success) {
        this.IsActivitiesActionPopVisible = false;
        this.GetAllOutPass(); // Refresh the list
      }

    } catch (error) {
      console.error('Error in approval with stock removal:', error);
      this.alertService.error(`Approval failed: ${error.message}`);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Update status normally (non-approval statuses)
   */
  private async updateStatusNormally(): Promise<void> {
    const url = this.ApiUrl + 'outpass/outpassstatusactions';

    try {
      const res = await this.http.post<any>(url, this.OutpassStatusAction).toPromise();
      this.alertService.success('Outpass status updated successfully');
      this.IsActivitiesActionPopVisible = false;
      this.GetAllOutPass(); // Refresh the list
    } catch (error) {
      this.alertService.error(error.error || 'Failed to update status');
    }
  }

  /**
   * Show confirmation dialog
   */
  private showConfirmationDialog(title: string, content: string): Promise<boolean> {
    return new Promise((resolve) => {
      const modal = this.modalService.confirm({
        nzTitle: title,
        nzContent: content,
        nzOkText: 'Yes',
        nzCancelText: 'No',
        nzOnOk: () => resolve(true),
        nzOnCancel: () => resolve(false)
      });

      // Auto-close after 60 seconds
      setTimeout(() => {
        modal.destroy();
        resolve(false);
      }, 60000);
    });
  }

  /**
   * Handle cancellation with enhanced confirmation for barcode Out Passes
   * Backend automatically handles stock rollback when status changes to 'Cancelled'
   */
  async handleCancellationWithRollback(outpassId: number): Promise<void> {
    try {
      // Check if this is an approved Out Pass with barcode items
      const validation = await this.outPassApprovalService.validateStockRemovalEligibility(outpassId);

      if (validation.isEligible && validation.message.includes('barcode labels')) {
        const confirmed = await this.showConfirmationDialog(
          'Confirm Cancellation',
          'This Out Pass has barcode items. Cancelling will automatically rollback the stock removal. Are you sure?'
        );

        if (!confirmed) {
          return;
        }
      }

      // Proceed with cancellation - backend handles stock rollback automatically
      this.OutpassStatusAction.OutpassId = outpassId;
      this.OutpassStatusAction.Status = 'Cancelled';
      await this.updateStatusNormally();

    } catch (error) {
      console.error('Error in cancellation:', error);
      this.alertService.error(`Cancellation failed: ${error.message}`);
    }
  }

  /**
   * Enhanced status change confirmation with special handling
   */
  StatusChangeConfirmationEnhanced(OutpassId: number, Status: string) {
    if (Status === 'Cancelled') {
      // Use enhanced cancellation for approved Out Passes
      this.handleCancellationWithRollback(OutpassId);
    } else {
      // Use normal confirmation for other statuses
      this.StatusChangeConfirmation(OutpassId, Status);
    }
  }

  EnableApproveButton(data: OutPassModel) {
    return (data.Status == 'Approval Pending' || data.Status == 'On Hold') && this.permission.Approval
  }
  EnableOnHoldButton(data: OutPassModel) {
    return data.Status == 'Approval Pending' && (this.permission.Cancel || this.permission.Manage)
  }
  EnableCancelButton(data: OutPassModel) {
    return data.Status == 'Approval Pending' && (this.permission.Add || this.permission.Manage)
  }
  EnablePrintButton(data: OutPassModel) {
    return ((data.Status == 'Approved' || data.Status == 'Approval Pending') && this.permission.View)
  }

  // ✅ NEW: Enable packing list print only for Barcode or Mixed mode OutPasses
  EnablePackingListPrintButton(data: OutPassModel) {
    return ((data.Status == 'Approved' || data.Status == 'Approval Pending') &&
      this.permission.View &&
      (data.CreateMode === 'Barcode' || data.CreateMode === 'Mixed'))
  }
  handletransportcancel() {
    this.IsTransportVisible = false;
    this.OutpassModify = new OutPassModel();
    this.TransportList = [];
  }
  showTransportModal(data: OutPassModel): void {
    this.GetAllTransport(data);

  }
  ShowOutpassReturnExtendModel(data: OutPassModel) {
    this.OutpassModify = new OutPassModel();
    this.selectedOutpass = data;
    this.OutpassModify.ExpectedReturnDate = this.selectedOutpass.ExpectedReturnDate;
    this.OutpassModify.OutpassId = this.selectedOutpass.OutpassId;
    this.IsExtendExpectedReturnVisible = true;
  }
  handleExtendExpectedReturnCancel() {
    this.IsExtendExpectedReturnVisible = false;
    this.OutpassModify = new OutPassModel();
  }
  disabledPastDate = (DateValue: Date): boolean => {
    if (!DateValue || !this.OutpassModify.ExpectedReturnDate) {
      return false;
    }
    return DateValue.getTime() <= new Date(this.OutpassModify.ExpectedReturnDate).getTime();
  };
  GetAllTransport(data: OutPassModel) {
    this.loader.show()
    let url = this.ApiUrl + "transport/getalltransport";
    this.http.get<TransportModel[]>(url).subscribe({
      next: res => {
        this.TransportList = res
        this.OutpassModify = data;
        if (this.OutpassModify.TransportId > 0) {
          this.onSelectedTransportChange(this.OutpassModify.TransportId);
        }
        this.loader.hide();
        this.IsTransportVisible = true;
      },
      error: res => {
        this.loader.hide();
        this.alertService.error("An error has been occured. Please try again");
        this.count++;
        if (this.count < 2) {
          this.GetAllTransport(data);
        }
      },
    });
  }
  onSelectedTransportChange($event: number) {
    var selectedTransport = this.TransportList.filter(x => x.TransportId == $event)[0];
    this.TransportVehicleList = selectedTransport.TransportVehicle;

  }

  onSelectedVehicleChange($event: number) {
    let url = this.ApiUrl + "gate/getvehiclestatus?vehicleid=" + $event;
    this.http.get<GateInModel>(url).subscribe({
      next: res => {
        this.VehicleStatus = null;
        if (res != null) {
          this.VehicleStatus = "Selected Vehicle is already Gate-in";
          if (res.GatePassIssue == true) {
            this.VehicleStatus = this.VehicleStatus + " and Gate pass issued";
          }
        }
      },
      error: res => {
        this.alertService.error("An error has been occured. Please try again");
      },
    });
  }
  SaveModifyOutpass() {
    if (this.OutpassModify.VehicleId == 0 && this.OutpassModify.TransportId == 0 && this.OutpassModify.Remark == '') {
      this.alertService.error('Remark is required');
      return;
    }
    let url = this.ApiUrl + 'outpass/modifyoutpass';
    this.http.post<any>(url, this.OutpassModify).subscribe({
      next: (res) => {
        this.alertService.success('Outpass updated successfully');
        if (this.OutpassModify.VehicleId > 0) {
          this.handletransportcancel();
        }
        else {
          this.handleExtendExpectedReturnCancel();
        }

        this.GetAllOutPass(); // Refresh the list
      },
      error: (res) => {
        this.alertService.error(res.error);
      },
    });
  }
  OpenTimeline(OutpassId: number) {
    this.OutpassTimeline.OutpassId = OutpassId;
    this.OutpassTimeline.show();
  }
  ValidateReturnQuantity(data: OutpassItemModel) {
    if (data.ReturnedQuantity > data.Quantity) {
      this.alertService.error('Returned quantity should be less than or equal to sent quantity');
      data.ReturnedQuantity = data.Quantity;
      return;
    }

    // Check if reason is required for less quantity
    if (data.ReturnedQuantity < data.Quantity) {
      data.RequiresReason = true;
    } else {
      data.RequiresReason = false;
      data.ReasonForLessQuantity = ''; // Clear reason if not needed
    }
  }

  // ✅ NEW: Toggle-based product filtering methods

  /**
   * Load user's preferred product filter mode from localStorage
   */
  loadProductFilterModePreference(): void {
    const savedMode = localStorage.getItem('outpass-product-filter-mode');
    this.isLegacyMode = savedMode === 'legacy';
  }

  /**
   * Save user's preferred product filter mode to localStorage
   */
  saveProductFilterModePreference(): void {
    const mode = this.isLegacyMode ? 'legacy' : 'catalog';
    localStorage.setItem('outpass-product-filter-mode', mode);
  }

  /**
   * Handle product filter mode toggle change
   */
  onProductFilterModeChange(): void {
    // Save user preference
    this.saveProductFilterModePreference();

    // Clear existing product filter values when switching modes
    this.clearProductFilters();

    // Show user feedback
    const mode = this.isLegacyMode ? 'Legacy Custom Names' : 'Product Master Catalog';
    // this.alertService.success(`Switched to ${mode} mode`);
  }

  /**
   * Clear all product-related filter values
   */
  clearProductFilters(): void {
    // Clear the main filter field
    this.request.outpassProductName = '';
    this.request.productId = 0;

    // Clear catalog-specific selections
    this.selectedProductFromCatalog = null;
    this.SelectedProductType = '';
    this.CategoryID = null;
    this.FirstCategoryID = null;
    this.SecondCategoryID = null;

    // Clear filtered product list
    this.FilteredProductList = [];
  }
  resetAllFilters(): void {
    this.request.outpassProductName = '';
    this.request.productId = 0;
    this.selectedProductFromCatalog = null;
    this.SelectedProductType = '';
    this.CategoryID = null;
    this.FirstCategoryID = null;
    this.SecondCategoryID = null;
    this.FilteredProductList = [];
    this.request.fromDate = new Date(new Date(new Date().setHours(0, 0, 1)).setDate(new Date().getDate() - 7));
    this.request.toDate = new Date(new Date().setHours(23, 59, 59));
    this.request.outpassToCustomerId = 0;
    this.request.outpassTo = '';
    this.request.outpassNumber = '';
    this.request.outpassType = '';
    this.request.purposeId = 0;
    this.request.isOutpassIn = null;
    this.request.pageNumber = 1;
    this.request.pageSize = 20;
    this.request.includeItems = true;
    this.GetAllOutPass();
  }

  /**
   * Handle product selection from catalog mode
   */
  onSelectedProductFromCatalogChange(selectedProductName: ProductModel): void {
    // Set the selected product name to the main filter field
    this.request.outpassProductName = selectedProductName.ProductName;
    this.request.productId = selectedProductName.ProductId;

    // Provide user feedback
    if (selectedProductName) {
      console.log('Selected product from catalog:', selectedProductName);
    }
  }
  getAllPurpose() {
    let url = this.ApiUrl + "outpass/getalloutpasspurpose";
    this.http.get<OutPassPurposeModel[]>(url).subscribe({
      next: res => {
        this.PurposeList = res;
      },
      error: res => {
        this.alertService.error("An error has been occured. Please try again");
        this.count++;
        if (this.count < 2) {
          this.getAllPurpose();
        }
      },
    });
  }


}
