import { Injectable } from '@angular/core';
import { ROUTES, MENU_STRUCTURE, RouteConfig, MenuStructure } from '../Config/route-config';
import { EXTENDED_MENU_STRUCTURE } from '../Config/route-config-extended';
import { FINAL_MENU_STRUCTURE } from '../Config/route-config-final';
import { BreadcrumbItem } from './breadcrumb.service';

@Injectable({
  providedIn: 'root'
})
export class RouteConfigService {
  private allMenuStructures: MenuStructure[];
  private routeToBreadcrumbMap: Map<string, BreadcrumbItem[]> = new Map();

  constructor() {
    // Combine all menu structures
    this.allMenuStructures = [
      ...MENU_STRUCTURE,
      ...EXTENDED_MENU_STRUCTURE,
      ...FINAL_MENU_STRUCTURE
    ];
    
    // Build breadcrumb mapping
    this.buildBreadcrumbMapping();
  }

  /**
   * Get all menu structures
   */
  getAllMenuStructures(): MenuStructure[] {
    return this.allMenuStructures;
  }

  /**
   * Get menu structure by ID
   */
  getMenuStructureById(id: string): MenuStructure | undefined {
    return this.allMenuStructures.find(menu => menu.id === id);
  }

  /**
   * Get route constants
   */
  getRoutes() {
    return ROUTES;
  }

  /**
   * Get breadcrumb items for a given route
   */
  getBreadcrumbsForRoute(route: string): BreadcrumbItem[] {
    // Remove query parameters and fragments
    const cleanRoute = route.split('?')[0].split('#')[0];
    
    // Check for exact match
    if (this.routeToBreadcrumbMap.has(cleanRoute)) {
      return this.routeToBreadcrumbMap.get(cleanRoute)!;
    }

    // Check for dynamic routes (with parameters)
    for (const [routePattern, breadcrumbs] of this.routeToBreadcrumbMap.entries()) {
      if (this.isRouteMatch(routePattern, cleanRoute)) {
        return breadcrumbs;
      }
    }

    return [];
  }

  /**
   * Build breadcrumb mapping from menu structures
   */
  private buildBreadcrumbMapping(): void {
    this.allMenuStructures.forEach(menu => {
      this.processMenuForBreadcrumbs(menu, []);
    });
  }

  /**
   * Recursively process menu structure to build breadcrumbs
   */
  private processMenuForBreadcrumbs(
    menu: MenuStructure | RouteConfig, 
    parentBreadcrumbs: BreadcrumbItem[]
  ): void {
    const currentBreadcrumb: BreadcrumbItem = {
      label: menu.label,
      icon: menu.icon
    };

    const currentBreadcrumbs = [...parentBreadcrumbs, currentBreadcrumb];

    // If this menu item has a path, add it to the mapping
    if ('path' in menu && menu.path && menu.path !== '') {
      this.routeToBreadcrumbMap.set(menu.path, currentBreadcrumbs);
    }

    // Process children recursively
    if (menu.children && menu.children.length > 0) {
      menu.children.forEach(child => {
        this.processMenuForBreadcrumbs(child, currentBreadcrumbs);
      });
    }
  }

  /**
   * Check if a route pattern matches an actual URL
   */
  private isRouteMatch(routePattern: string, actualUrl: string): boolean {
    const patternParts = routePattern.split('/');
    const urlParts = actualUrl.split('/');

    if (patternParts.length !== urlParts.length) {
      return false;
    }

    for (let i = 0; i < patternParts.length; i++) {
      if (patternParts[i].startsWith(':')) {
        // This is a parameter, skip comparison
        continue;
      }
      if (patternParts[i] !== urlParts[i]) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get menu item by route path
   */
  getMenuItemByRoute(route: string): RouteConfig | undefined {
    const cleanRoute = route.split('?')[0].split('#')[0];
    
    for (const menu of this.allMenuStructures) {
      const found = this.findRouteInMenu(menu, cleanRoute);
      if (found) return found;
    }
    
    return undefined;
  }

  /**
   * Recursively find route in menu structure
   */
  private findRouteInMenu(menu: MenuStructure | RouteConfig, route: string): RouteConfig | undefined {
    if ('path' in menu && menu.path === route) {
      return menu;
    }

    if (menu.children) {
      for (const child of menu.children) {
        const found = this.findRouteInMenu(child, route);
        if (found) return found;
      }
    }

    return undefined;
  }

  /**
   * Get parent menu for a given route
   */
  getParentMenuForRoute(route: string): MenuStructure | undefined {
    const cleanRoute = route.split('?')[0].split('#')[0];
    
    for (const menu of this.allMenuStructures) {
      if (this.routeExistsInMenu(menu, cleanRoute)) {
        return menu;
      }
    }
    
    return undefined;
  }

  /**
   * Check if route exists in menu structure
   */
  private routeExistsInMenu(menu: MenuStructure | RouteConfig, route: string): boolean {
    if ('path' in menu && menu.path === route) {
      return true;
    }

    if (menu.children) {
      return menu.children.some(child => this.routeExistsInMenu(child, route));
    }

    return false;
  }

  /**
   * Get all routes as flat array
   */
  getAllRoutes(): string[] {
    const routes: string[] = [];
    
    this.allMenuStructures.forEach(menu => {
      this.collectRoutesFromMenu(menu, routes);
    });
    
    return routes;
  }

  /**
   * Recursively collect routes from menu structure
   */
  private collectRoutesFromMenu(menu: MenuStructure | RouteConfig, routes: string[]): void {
    if ('path' in menu && menu.path && menu.path !== '') {
      routes.push(menu.path);
    }

    if (menu.children) {
      menu.children.forEach(child => {
        this.collectRoutesFromMenu(child, routes);
      });
    }
  }

  /**
   * Validate route exists in configuration
   */
  isValidRoute(route: string): boolean {
    const cleanRoute = route.split('?')[0].split('#')[0];
    return this.routeToBreadcrumbMap.has(cleanRoute) || this.getAllRoutes().includes(cleanRoute);
  }

  /**
   * Get menu structure for navigation rendering
   */
  getNavigationStructure(): MenuStructure[] {
    return this.allMenuStructures;
  }
}
