<div class="container-fluid">
  <nz-page-header [nzGhost]="false">
    <nz-page-header-title>Return Stock Management</nz-page-header-title>
    <nz-page-header-extra>
      <button nz-button nzType="primary" (click)="showNewReturnForm()" *ngIf="!showReturnForm">
        <i nz-icon nzType="plus"></i>
        Create Return Stock
      </button>
    </nz-page-header-extra>
  </nz-page-header>

  <!-- Return Stock Creation Form -->
  <nz-card *ngIf="showReturnForm" [nzTitle]="'Create Return Stock'" style="margin-bottom: 16px;">
    <!-- Step Indicator -->
    <nz-steps [nzCurrent]="currentStep - 1" style="margin-bottom: 24px;">
      <nz-step nzTitle="Search Order"></nz-step>
      <nz-step nzTitle="Select Items"></nz-step>
      <nz-step nzTitle="Return Details"></nz-step>
      <nz-step nzTitle="Confirmation"></nz-step>
    </nz-steps>

    <!-- Step 1: Search Sale Order -->
    <div *ngIf="currentStep === 1">
      <h4>Search Dispatched Sale Order</h4>
      <div class="row">
        <div class="col-md-6">
          <label class="form-label">Sale Order Number</label>
          <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
            <input type="text" nz-input placeholder="Enter Sale Order Number" [(ngModel)]="saleOrderNumber" />
          </nz-input-group>
          <ng-template #suffixIconButton>
            <button nz-button nzType="primary" nzSearch (click)="searchSaleOrder()" [nzLoading]="isLoading">
              Search
            </button>
          </ng-template>
        </div>
      </div>
    </div>

    <!-- Step 2: Select Items for Return -->
    <div *ngIf="currentStep === 2">
      <h4>Select Items for Return</h4>
      <p><strong>Customer:</strong> {{ dispatchedItems.CustomerName }}</p>
      <p><strong>Sale Order:</strong> {{ dispatchedItems.SaleOrderNumber }}</p>
      <p><strong>Dispatch Date:</strong> {{ dispatchedItems.DispatchDate | date }}</p>

      <nz-table #itemsTable [nzData]="dispatchedItems.DispatchedItems" nzSize="small">
        <thead>
          <tr>
            <th>Product Name</th>
            <th>Dispatched Qty</th>
            <th>Previously Returned</th>
            <th>Available for Return</th>
            <th>Unit</th>
            <th>Grade</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of itemsTable.data">
            <td>{{ item.ProductName }}</td>
            <td>{{ item.DispatchedQuantity }}</td>
            <td>{{ item.PreviouslyReturnedQuantity }}</td>
            <td>{{ item.AvailableForReturn }}</td>
            <td>{{ item.Unit }}</td>
            <td>{{ item.Grade }}</td>
            <td>
              <button nz-button nzType="primary" nzSize="small" 
                      (click)="selectItemForReturn(item)"
                      [disabled]="item.AvailableForReturn <= 0">
                Select for Return
              </button>
            </td>
          </tr>
        </tbody>
      </nz-table>

      <!-- Selected Items for Return -->
      <div *ngIf="returnStockModel.ReturnedItems.length > 0" style="margin-top: 24px;">
        <h5>Selected Items for Return</h5>
        <nz-table #selectedTable [nzData]="returnStockModel.ReturnedItems" nzSize="small">
          <thead>
            <tr>
              <th>Product Name</th>
              <th>Return Quantity</th>
              <th>Unit</th>
              <th>Condition</th>
              <th>Notes</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of selectedTable.data; let i = index">
              <td>{{ item.ProductName }}</td>
              <td>
                <nz-input-number [(ngModel)]="item.ReturnedQuantity" 
                                [nzMin]="0.01" 
                                [nzMax]="item.OriginalDispatchedQuantity"
                                [nzStep]="0.01">
                </nz-input-number>
              </td>
              <td>{{ item.Unit }}</td>
              <td>
                <nz-select [(ngModel)]="item.ReturnCondition" style="width: 120px;">
                  <nz-option *ngFor="let condition of returnConditions" 
                            [nzValue]="condition.value" 
                            [nzLabel]="condition.label">
                  </nz-option>
                </nz-select>
              </td>
              <td>
                <input nz-input [(ngModel)]="item.ReturnNotes" placeholder="Optional notes" />
              </td>
              <td>
                <button nz-button nzType="danger" nzSize="small" (click)="removeReturnItem(i)">
                  Remove
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>

        <div style="margin-top: 16px;">
          <button nz-button nzType="primary" (click)="proceedToReturnDetails()">
            Proceed to Return Details
          </button>
        </div>
      </div>
    </div>

    <!-- Step 3: Return Details -->
    <div *ngIf="currentStep === 3">
      <h4>Return Details</h4>
      <div class="row">
        <div class="col-md-6">
          <label class="form-label required">Return Reason</label>
          <nz-select [(ngModel)]="returnStockModel.ReturnReason" style="width: 100%;" 
                     nzPlaceHolder="Select return reason">
            <nz-option nzValue="Defective Product" nzLabel="Defective Product"></nz-option>
            <nz-option nzValue="Wrong Product Delivered" nzLabel="Wrong Product Delivered"></nz-option>
            <nz-option nzValue="Customer Dissatisfaction" nzLabel="Customer Dissatisfaction"></nz-option>
            <nz-option nzValue="Damaged in Transit" nzLabel="Damaged in Transit"></nz-option>
            <nz-option nzValue="Quality Issues" nzLabel="Quality Issues"></nz-option>
            <nz-option nzValue="Other" nzLabel="Other"></nz-option>
          </nz-select>
        </div>
        <div class="col-md-6">
          <label class="form-label required">Returned By</label>
          <input nz-input [(ngModel)]="returnStockModel.ReturnedBy" placeholder="Enter person name" />
        </div>
      </div>
      <div class="row" style="margin-top: 16px;">
        <div class="col-md-6">
          <label class="form-label">Return Date</label>
          <nz-date-picker [(ngModel)]="returnStockModel.ReturnDate" style="width: 100%;"></nz-date-picker>
        </div>
      </div>

      <div style="margin-top: 24px;">
        <button nz-button (click)="currentStep = 2" style="margin-right: 8px;">
          Back
        </button>
        <button nz-button nzType="primary" (click)="submitReturnStock()" [nzLoading]="isLoading">
          Create Return Stock
        </button>
        <button nz-button (click)="cancelReturn()" style="margin-left: 8px;">
          Cancel
        </button>
      </div>
    </div>
  </nz-card>

  <!-- Return Stock List -->
  <nz-card [nzTitle]="'Return Stock List'" *ngIf="!showReturnForm">
    <!-- Filters -->
    <nz-collapse>
      <nz-collapse-panel nzHeader="Filters" [nzActive]="false">
        <div class="row">
          <div class="col-md-3">
            <label class="form-label">From Return Date</label>
            <nz-date-picker [(ngModel)]="filterModel.FromReturnDate" style="width: 100%;"></nz-date-picker>
          </div>
          <div class="col-md-3">
            <label class="form-label">To Return Date</label>
            <nz-date-picker [(ngModel)]="filterModel.ToReturnDate" style="width: 100%;"></nz-date-picker>
          </div>
          <div class="col-md-3">
            <label class="form-label">Customer</label>
            <nz-select [(ngModel)]="filterModel.CustomerId" style="width: 100%;" nzAllowClear>
              <nz-option *ngFor="let customer of customerList" 
                        [nzValue]="customer.CustomerId" 
                        [nzLabel]="customer.CustomerName">
              </nz-option>
            </nz-select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Return Reason</label>
            <input nz-input [(ngModel)]="filterModel.ReturnReason" placeholder="Enter return reason" />
          </div>
        </div>
        <div class="row" style="margin-top: 16px;">
          <div class="col-md-12">
            <button nz-button nzType="primary" (click)="applyFilter()">Apply Filter</button>
            <button nz-button (click)="clearFilter()" style="margin-left: 8px;">Clear</button>
          </div>
        </div>
      </nz-collapse-panel>
    </nz-collapse>

    <!-- Return Stock Table -->
    <nz-table #returnStockTable [nzData]="returnStockList" [nzLoading]="isTableLoading" 
              nzSize="small" style="margin-top: 16px;">
      <thead>
        <tr>
          <th>Return Date</th>
          <th>Invoice Number</th>
          <th>Original Sale Order</th>
          <th>Customer</th>
          <th>Return Reason</th>
          <th>Total Items</th>
          <th>Total Quantity</th>
          <th>Inspection</th>
          <th>Allocation</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of returnStockTable.data">
          <td>{{ item.ReturnDate | date }}</td>
          <td>{{ item.InvoiceNumber }}</td>
          <td>{{ item.OriginalSaleOrderNumber }}</td>
          <td>{{ item.CustomerName }}</td>
          <td>{{ item.ReturnReason }}</td>
          <td>{{ item.TotalItems }}</td>
          <td>{{ item.TotalQuantity }}</td>
          <td>
            <nz-tag [nzColor]="item.InspectionCompleted ? 'green' : 'orange'">
              {{ item.InspectionCompleted ? 'Completed' : 'Pending' }}
            </nz-tag>
          </td>
          <td>
            <nz-tag [nzColor]="item.AllocationCompleted ? 'green' : 'orange'">
              {{ item.AllocationCompleted ? 'Completed' : 'Pending' }}
            </nz-tag>
          </td>
          <td>
            <button nz-button nzType="link" nzSize="small" (click)="viewReturnStock(item.StockId)">
              View
            </button>
            <button nz-button nzType="link" nzSize="small" (click)="editReturnStock(item.StockId)"
                    *ngIf="!item.InspectionCompleted">
              Inspect
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-card>
</div>
