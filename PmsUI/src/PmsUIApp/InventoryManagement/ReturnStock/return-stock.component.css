.container-fluid {
  padding: 20px;
}

.form-label {
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.form-label.required::after {
  content: ' *';
  color: #ff4d4f;
}

.row {
  margin-bottom: 16px;
}

.col-md-3, .col-md-6, .col-md-12 {
  padding: 0 8px;
}

nz-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

nz-table {
  margin-top: 16px;
}

.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px;
}

nz-steps {
  max-width: 600px;
  margin: 0 auto;
}

.step-content {
  margin-top: 24px;
  padding: 24px;
  background: #fafafa;
  border-radius: 6px;
}

.return-summary {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
}

.return-summary h5 {
  color: #52c41a;
  margin-bottom: 8px;
}

.action-buttons {
  margin-top: 24px;
  text-align: right;
}

.action-buttons button {
  margin-left: 8px;
}

.filter-section {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.status-tag {
  font-size: 12px;
}

.table-actions {
  white-space: nowrap;
}

.table-actions button {
  margin-right: 8px;
}

.search-section {
  background: #f0f2f5;
  padding: 24px;
  border-radius: 6px;
  text-align: center;
}

.search-section h4 {
  margin-bottom: 16px;
  color: #1890ff;
}

.selected-items-section {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.selected-items-section h5 {
  color: #fa8c16;
  margin-bottom: 16px;
}

.return-details-section {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 24px;
}

.return-details-section h4 {
  color: #52c41a;
  margin-bottom: 24px;
}

.customer-info {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.customer-info p {
  margin-bottom: 8px;
  font-weight: 500;
}

.customer-info p:last-child {
  margin-bottom: 0;
}

.quantity-input {
  width: 100px;
}

.condition-select {
  width: 120px;
}

.notes-input {
  width: 150px;
}

@media (max-width: 768px) {
  .container-fluid {
    padding: 10px;
  }
  
  .col-md-3, .col-md-6, .col-md-12 {
    padding: 0 4px;
    margin-bottom: 12px;
  }
  
  .action-buttons {
    text-align: center;
  }
  
  .action-buttons button {
    margin: 4px;
    width: 100px;
  }
  
  .table-actions button {
    display: block;
    margin: 2px 0;
    width: 100%;
  }
}
