# PMS Centralized Route Configuration System

## Overview

This document describes the centralized route configuration system implemented for the PMS (Production Management System) application. The system eliminates code duplication and provides a single source of truth for all route definitions, menu structures, permissions, and hierarchical relationships.

## Architecture

### Core Components

1. **Route Configuration (`route-config.ts`)** - Main configuration file with type-safe route constants
2. **Extended Configuration (`route-config-extended.ts`)** - Additional menu structures 
3. **Final Configuration (`route-config-final.ts`)** - Master, Admin, and Notification menus
4. **Route Config Service (`route-config.service.ts`)** - Service layer for route management
5. **Route Helpers (`route-helpers.ts`)** - Utility functions for route manipulation
6. **Breadcrumb Service (`breadcrumb.service.ts`)** - Automated breadcrumb generation

### Key Features

- **Type-Safe Routes**: All route paths are defined as constants with TypeScript type safety
- **Hierarchical Menu Structure**: Complete menu hierarchy with parent-child relationships
- **Permission Integration**: Built-in permission checking for modules and responsibilities
- **Automatic Breadcrumbs**: Dynamic breadcrumb generation based on route hierarchy
- **Centralized Management**: Single location for all route-related configuration
- **Backward Compatibility**: Maintains compatibility with existing routing and navigation

## File Structure

```
PmsUI/src/PmsUIApp/
├── Config/
│   ├── route-config.ts              # Main route constants and menu structure
│   ├── route-config-extended.ts     # Extended menu structures
│   ├── route-config-final.ts        # Final menu structures
│   └── README.md                    # This documentation
├── Services/
│   ├── route-config.service.ts      # Route configuration service
│   └── breadcrumb.service.ts        # Updated breadcrumb service
└── Utils/
    └── route-helpers.ts             # Route utility functions
```

## Usage Examples

### 1. Using Type-Safe Route Constants

```typescript
import { ROUTES } from '../Config/route-config';

// Navigate to a specific route
this.router.navigate([ROUTES.SALES.ORDER.LIST]);

// Build route with parameters
const routeWithId = RouteHelper.buildRoute(ROUTES.REPORTS.SALES.SALES_ORDER_TRAILS + '/:id', { id: '123' });

// Use TypeSafe route helpers
const salesOrderList = TypeSafeRoutes.Sales.Order.list();
const stockAvailabilityWithProduct = TypeSafeRoutes.Reports.Stock.stockAvailability('product123');
```

### 2. Accessing Menu Structure

```typescript
import { RouteConfigService } from '../Services/route-config.service';

constructor(private routeConfigService: RouteConfigService) {}

// Get all menu structures
const allMenus = this.routeConfigService.getAllMenuStructures();

// Get specific menu by ID
const salesMenu = this.routeConfigService.getMenuStructureById('sales');

// Get breadcrumbs for current route
const breadcrumbs = this.routeConfigService.getBreadcrumbsForRoute('/home/<USER>/order/list');
```

### 3. Route Validation and Helpers

```typescript
import { RouteHelper, NavigationHelper } from '../Utils/route-helpers';

// Build complete route with parameters and query string
const completeRoute = RouteHelper.buildCompleteRoute(
  '/home/<USER>/sales/salesordertrails/:id',
  { id: '123' },
  { status: 'active', page: 1 }
);

// Check if route matches pattern
const matches = RouteHelper.matchesPattern('/home/<USER>/:type/:action', '/home/<USER>/order/list');

// Check if route is active
const isActive = NavigationHelper.isRouteActive(currentRoute, targetRoute);
```

## Menu Structure Configuration

Each menu item follows this structure:

```typescript
interface MenuStructure {
  id: string;                    // Unique identifier
  label: string;                 // Display name
  icon: string;                  // Ant Design icon name
  permission: {                  // Permission requirements
    module: string;
  };
  openMapKey: string;           // Key for menu expansion state
  dataLabel: string;            // Label for collapsed menu
  children: RouteConfig[];      // Child menu items
}
```

### Route Configuration Structure

```typescript
interface RouteConfig {
  path: string;                 // Route path
  label: string;                // Display label
  icon?: string;                // Optional icon
  permission?: {                // Optional permission check
    module?: string;
    responsibility?: string;
    action?: string;
  };
  children?: RouteConfig[];     // Child routes
  isLazyLoaded?: boolean;       // Lazy loading flag
  component?: string;           // Component name
  title?: string;               // Page title
}
```

## Breadcrumb System

The breadcrumb system automatically generates navigation breadcrumbs based on the menu hierarchy:

### Features
- **Automatic Generation**: Breadcrumbs are automatically created from menu structure
- **Icon Support**: Displays appropriate icons for menu levels
- **Dynamic Updates**: Updates automatically on route changes
- **Hierarchical Display**: Shows complete navigation path

### Breadcrumb Item Structure
```typescript
interface BreadcrumbItem {
  label: string;    // Display text
  url?: string;     // Optional navigation URL
  icon?: string;    // Optional icon
}
```

## Supported Menus

The system covers all PMS application menus:

### Main Menus
1. **Dashboard** - Welcome, Reporting Dashboard
2. **Reports** - Sales, Stock, Purchase, Production reports
3. **Sales** - Sales Orders, Proforma Invoices
4. **Demand** - Demand management
5. **Purchase Order** - Purchase orders and invoices
6. **Production** - Manufacturing processes
7. **Dispatch** - Packaging and dispatch
8. **Issue** - Issue management
9. **Consumption** - Material consumption
10. **Costing** - Cost estimation and overhead
11. **Inventory** - Stock management
12. **Gate In/Out** - Gate operations
13. **Issue Gate Pass** - Gate pass management
14. **Out Pass** - Out pass operations
15. **Quick Tools** - Utility tools
16. **Master** - Master data management
17. **Admin** - System administration
18. **Notification** - Notification management

## Migration Benefits

### Before (Problems Solved)
- Route URLs scattered across multiple files
- Inconsistent route definitions
- Manual breadcrumb maintenance
- Risk of typos in route paths
- Difficult to track route usage
- Hard to maintain menu structures

### After (Improvements)
- Single source of truth for all routes
- Type-safe route constants
- Automatic breadcrumb generation
- Centralized menu structure management
- Easy route validation and helpers
- Consistent navigation patterns
- Improved maintainability

## Best Practices

### 1. Route Definition
- Always use route constants instead of hardcoded strings
- Follow consistent naming conventions
- Include proper TypeScript types

### 2. Menu Structure
- Maintain hierarchical relationships
- Include proper permission checks
- Use consistent icon naming

### 3. Navigation
- Use TypeSafe route helpers for complex routes
- Validate routes before navigation
- Handle route parameters properly

### 4. Maintenance
- Update route configuration when adding new features
- Test breadcrumb generation for new routes
- Maintain backward compatibility

## Future Enhancements

1. **Route Guards Integration** - Integrate permission-based route guards
2. **Dynamic Menu Loading** - Support for dynamic menu structures
3. **Route Analytics** - Track route usage and performance
4. **Internationalization** - Multi-language support for menu labels
5. **Route Caching** - Optimize route resolution performance

## Troubleshooting

### Common Issues

1. **Missing Breadcrumbs**
   - Ensure route is defined in menu structure
   - Check route path matches exactly
   - Verify menu hierarchy is correct

2. **Type Errors**
   - Import correct route constants
   - Use proper TypeScript types
   - Check route parameter types

3. **Permission Issues**
   - Verify permission module names
   - Check responsibility mappings
   - Ensure user has required permissions

### Debug Tools

```typescript
// Check if route exists in configuration
const isValid = this.routeConfigService.isValidRoute('/home/<USER>/order/list');

// Get all available routes
const allRoutes = this.routeConfigService.getAllRoutes();

// Find menu item by route
const menuItem = this.routeConfigService.getMenuItemByRoute('/home/<USER>/order/list');
```

## Support

For questions or issues with the route configuration system:
1. Check this documentation first
2. Review the TypeScript interfaces for proper usage
3. Test with the debug tools provided
4. Ensure all imports are correct

The centralized route configuration system provides a robust foundation for navigation and menu management in the PMS application, improving maintainability and developer experience.
