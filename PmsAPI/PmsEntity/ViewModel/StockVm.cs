
using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class StockVm
    {
        public long StockId { get; set; }
        public string Products { get; set; }
        public DateTime StockDate { get; set; }
        public string Batch { get; set; }
        public long InvoiceId { get; set; }
        public bool? InspectionCompleted { get; set; }
        public bool? AllocationCompleted { get; set; }
        public bool? ManageRejectedItemsCompleted { get; set; }
        public bool? IsOpeningStock { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public InvoiceMasterVm Invoice { get; set; }
        public List<StockProductTableVm> StockProduct { get; set; }
        public long? SaleOrderId { get; set; }
        public string InspectionCompletedBy { get; set; }
        public DateTime? InspectionCompletedDate { get; set; }
        public string ProductQuality { get; set; }
        public bool? IsTransferedStock { get; set; }
        public bool? IsQualityInspectionCompleted { get; set; }
        public UserMasterBasicVm QualityInspectionCompletedBy { get; set; }
        public DateTime? QualityInspectionCompletedDate { get; set; }
    }
    public class StockRequestVm
    {
        public bool? IsOpeningStock { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string DateType { get; set; }
        public string ProductType { get; set; }
        public long? ProductId { get; set; }
        public long? ProductCategoryId { get; set; }
        public long? ProductFirstSubCategoryId { get; set; }
        public long? ProductSecSubCategoryId { get; set; }
        public bool? IsStockTransferIncluded { get; set; }
        public string StockType { get; set; }
        public string PONumber { get; set; }
        public string InvoiceNumber { get; set; }
        public long? SupplierId { get; set; }
        public bool? IsInspectionCompleted { get; set; }
        public bool? IsAllocationCompleted { get; set; }
        public bool? IsQualityInspectionCompleted { get; set; }
        public string BatchNo { get; set; }
    }
    public class ProductAvailableStock : ProductMasterVm
    {
        public decimal Quantity { get; set; }
    }
    public class ProductAvailableStockRequestVm
    {
        public List<long> ProductIds { get; set; }
        public string ProductQuality { get; set; }
    }
    public class ProductStockTransferRequestVm
    {
        public long FromProductId { get; set; }
        public long ToProductId { get; set; }
        public string RequestReason { get; set; }
        public string ActionRemark { get; set; }
        public string Status { get; set; }
        public long TransferId { get; set; }
    }
}