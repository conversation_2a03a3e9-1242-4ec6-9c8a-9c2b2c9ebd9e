﻿
using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class StockProductTableVm
    {
        public long StockProductId { get; set; }
        public long StockId { get; set; }
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductType { get; set; }
        public string Sku { get; set; }
        public string Barcode { get; set; }
        public decimal? Quantity { get; set; }
        public DateTime? ManufacturedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string Unit { get; set; }
        public decimal? PricePerUnit { get; set; }
        public string Grade { get; set; }
        public decimal? AcceptedQuantity { get; set; }
        public decimal? RejectedQuantity { get; set; }
        public decimal? ReceivedQuantity { get; set; }
        public string Comments { get; set; }
        public string AddedBy { get; set; }
        public long? ProductCategoryId { get; set; }
        public string ProductCategory { get; set; }
        public long? ProductFirstSubCategoryId { get; set; }
        public string ProductFirstSubCategory { get; set; }
        public long? ProductSecSubCategoryId { get; set; }
        public string ProductSecSubCategory { get; set; }
        public long? ThicknessId { get; set; }
        public long? GrainId { get; set; }
        public long? WidthId { get; set; }
        public long? ColorId { get; set; }
        public string PostProcess { get; set; }
        public decimal? FreightPerUnit { get; set; }
        public decimal? MiscPerUnit { get; set; }
        public decimal? InvoicePricePerUnit { get; set; }
        public decimal? ShippingHandlingPerUnit { get; set; }
        public bool IsBarcodeLabelGenerated { get; set; }
        public int? InspectionPendingLabelCount { get; set; }
        public List<StockProductAllocationVm> StockProductAllocation { get; set; }
        public List<StockProductManageRejectedVm> StockProductManageRejected { get; set; }
        public List<FileUploadTableVm> FileUploads { get; set; }

        // Product Supplier Mapping Properties
        public string SupplierProductName { get; set; }
        public long? ProductSupplierMappingId { get; set; }
    }

    public class StockProductTableMasterVm : StockProductTableVm
    {
        public long RackId { get; set; }
        public long SupplierId { get; set; }
        public DateTime? StockDate { get; set; }
        public bool isProductionStock { get; set; }
        public long? SaleOrderId { get; set; }

    }
}
