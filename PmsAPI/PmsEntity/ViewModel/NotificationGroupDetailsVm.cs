using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    /// <summary>
    /// Comprehensive view model for notification group details
    /// Used for detailed drawer view with all related information
    /// </summary>
    public class NotificationGroupDetailsVm
    {
        // Basic notification group information
        public long NotificationGroupUserId { get; set; }
        public string NotificationType { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string UserType { get; set; }
        public long? UserMasterId { get; set; }
        public string ReportName { get; set; }
        public List<string> TriggerType { get; set; } = new List<string>();
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public DateTime? LastTriggeredDate { get; set; }
        public string LastTriggeredBy { get; set; }

        // WhatsApp configuration details
        public bool? IsWhatsAppNotificationEnabled { get; set; }
        public long? WhatsAppTemplateMasterId { get; set; }
        public string MobileNumber { get; set; }
        public WhatsAppTemplateDetailsVm WhatsAppTemplate { get; set; }
        public WhatsAppConfigDetailsVm WhatsAppConfig { get; set; }

        // Email configuration details
        public bool? EnableToEmail { get; set; }
        public bool? EnableCcemail { get; set; }
        public bool? EnableBccemail { get; set; }
        public EmailConfigDetailsVm EmailConfig { get; set; }

        // Related mappings and schedules
        public List<EmailGroupMappingDetailsVm> EmailGroupMappings { get; set; } = new List<EmailGroupMappingDetailsVm>();
        public List<NotificationReportScheduleMappingDetailsVm> NotificationReportScheduleMappings { get; set; } = new List<NotificationReportScheduleMappingDetailsVm>();

        // Status information
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
    }

    /// <summary>
    /// WhatsApp template details for comprehensive view
    /// </summary>
    public class WhatsAppTemplateDetailsVm
    {
        public long WhatsAppTemplateMasterId { get; set; }
        public string ProviderName { get; set; }
        public string ProviderTemplateId { get; set; }
        public string ProviderTemplateName { get; set; }
        public string ProviderTemplateDescription { get; set; }
        public List<NotificationTemplateParameterTableVm> Parameters { get; set; } = new List<NotificationTemplateParameterTableVm>();
    }

    /// <summary>
    /// WhatsApp configuration details for comprehensive view
    /// </summary>
    public class WhatsAppConfigDetailsVm
    {
        public long WhatsAppConfigId { get; set; }
        public string ConfigName { get; set; }
        public string ProviderName { get; set; }
        public string RegisteredSenderNumber { get; set; }
        public string ApiEndpoint { get; set; }
        public int? MaxDailyMessages { get; set; }
        public int? MaxMonthlyMessages { get; set; }
    }

    /// <summary>
    /// Email configuration details for comprehensive view
    /// </summary>
    public class EmailConfigDetailsVm
    {
        public long EmailConfigId { get; set; }
        public string EmailConfigName { get; set; }
        public string EmailConfigSmtp { get; set; }
        public string EmailConfigFromEmailId { get; set; }
        public string EmailConfigFromEmailDisplayName { get; set; }
        public int? EmailConfigPort { get; set; }
    }

    /// <summary>
    /// Email group mapping details with stage information
    /// </summary>
    public class EmailGroupMappingDetailsVm
    {
        public long EmailGroupMappingId { get; set; }
        public long? NotificationGroupUserId { get; set; }
        public long? StageId { get; set; }
        public string StageName { get; set; }
        public bool? Enabled { get; set; }
        public bool? OnlyInternal { get; set; }
        public bool? OnlyCustomer { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
    }

    /// <summary>
    /// Notification report schedule mapping details with template information
    /// </summary>
    public class NotificationReportScheduleMappingDetailsVm
    {
        public long ReportId { get; set; }
        public string ReportType { get; set; }
        public string ReportName { get; set; }
        public long? NotificationGroupUserId { get; set; }
        public long? TemplateMasterId { get; set; }
        public string TemplateName { get; set; }
        public string CronExpression { get; set; }
        public DateTime? LastRunTime { get; set; }
        public DateTime? NextRunTime { get; set; }
        public bool IsActive { get; set; }
        public string TimeZone { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool Disabled { get; set; }
        public long? DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
    }
}
