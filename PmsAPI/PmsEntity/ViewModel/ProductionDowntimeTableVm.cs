﻿using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public partial class ProductionDowntimeTableVm
    {
        public long ProductionDowntimeId { get; set; }
        public long? ProductionDowntimeReasonId { get; set; }
        public string ReasonName { get; set; }
        public string ReasonCode { get; set; }
        public string ProductionLineType { get; set; }
        public int? ProductionLineNo { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public decimal? StandardDurationMinutes { get; set; }
        public string StandardDurationFormatted { get; set; }
        public decimal? ActualDurationMinutes { get; set; }
        public string ActualDurationFormatted { get; set; }
        public decimal? ExcessDurationMinutes { get; set; }
        public string ExcessDurationFormatted { get; set; }
        public string Comments { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public bool? IsDeleted { get; set; }
        public ProductionDowntimeReasonMasterVm ProductionDowntimeReason { get; set; }
    }
    public class ProductionDowntimeFilterVm
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string ProductionLineType { get; set; }
        public int? ProductionLineNo { get; set; }
        public long? ProductionDowntimeReasonId { get; set; }
        public bool? IsExceededDuration { get; set; }
        public int PageNo { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
    }
}
