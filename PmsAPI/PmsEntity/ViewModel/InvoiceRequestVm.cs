using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class InvoiceRequestVm
    {
        public long? SupplierId { get; set; }
        public long? POId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string SearchText { get; set; }
        public string InvoiceNumber { get; set; }
        public bool? Active { get; set; }
        public long ProductId { get; set; }

        // New filter options
        public bool ShowInvoicesWithoutPOMapping { get; set; } = false;
        public bool ShowInvoicesPendingStockReceipt { get; set; } = false;

        // Pagination parameters
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalCount { get; set; } = 0;
    }

    public class PaginatedInvoiceResult
    {
        public List<InvoiceMasterVm> Items { get; set; } = new List<InvoiceMasterVm>();
        public int TotalCount { get; set; } = 0;
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);
    }
}
