﻿
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class UserMasterVm
    {
        public string ADid { get; set; }
        public long UserId { get; set; }
        public string Name { get; set; }
        public string Contact { get; set; }
        public string Email { get; set; } //this is used as username
        public string EmailAddress { get; set; } //this is used as email
        public string Address { get; set; }
        public string Password { get; set; }
        public string NewPassword { get; set; }
        public List<UserRoleMasterVm> UserRole { get; set; }
    }
    public class UserMasterBasicVm
    {
        public string Name { get; set; }
        public string Contact { get; set; }
        public string Email { get; set; }
        public string EmailAddress { get; set; }
    }
}
