using System;

namespace PmsEntity.ViewModel
{
    public class NotificationTemplateConfigurationVm
    {
        public long ConfigurationId { get; set; }
        public string CategoryType { get; set; }        // Level 1: Business Domain (Inventory, Production, Costing)
        public string NotificationType { get; set; }    // Level 2: Business Function (Low Stock, Order Status, Reports)
        public string SubType { get; set; }             // Level 3: Specific Implementation (LowStockReport, YieldSummaryReport)
        public string DisplayName { get; set; }
        public string Description { get; set; }
        public bool? InternalAllowed { get; set; }
        public bool ExternalAllowed { get; set; }
        public bool CustomerAllowed { get; set; }
        public bool? EventAllowed { get; set; }
        public bool SchedulingAllowed { get; set; }
        public bool? OnDemandAllowed { get; set; }
        public bool SchedulingRequired { get; set; }
        public long? WhatsAppTemplateMasterId { get; set; }
        public long? EmailTemplateMasterId { get; set; }
        public int? SortOrder { get; set; }
        public bool IsDefault { get; set; }
        public long? AddedById { get; set; }
        public DateTime? AddedDate { get; set; }
        public long? UpdatedById { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool Disabled { get; set; }
        public long? DisabledById { get; set; }
        public DateTime? DisabledDate { get; set; }

        // Additional properties for display
        public string AddedBy { get; set; }
        public string UpdatedBy { get; set; }
        public string DisabledBy { get; set; }
        public string WhatsAppTemplateName { get; set; }
        public int SerialNumber { get; set; }
    }

    public class NotificationTypeListVm
    {
        public long ConfigurationId { get; set; }
        public string CategoryType { get; set; }
        public string NotificationType { get; set; }
        public string SubType { get; set; }
        public string DisplayName { get; set; }
        public bool? InternalAllowed { get; set; }
        public bool ExternalAllowed { get; set; }
        public bool CustomerAllowed { get; set; }
        public bool? EventAllowed { get; set; }
        public bool SchedulingAllowed { get; set; }
        public bool? OnDemandAllowed { get; set; }
        public bool SchedulingRequired { get; set; }
        public long? WhatsAppTemplateMasterId { get; set; }
        public long? EmailTemplateMasterId { get; set; }
        public int? SortOrder { get; set; }
        public bool IsDefault { get; set; }
    }
}
