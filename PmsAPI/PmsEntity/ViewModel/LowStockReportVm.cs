using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class LowStockReportVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductType { get; set; }
        public string ProductCategory { get; set; }
        public string ProductFirstSubCategory { get; set; }
        public string ProductSecSubCategory { get; set; }
        public decimal MinimumLevel { get; set; }
        public decimal TotalAvailableQty { get; set; }
        public decimal DomesticQty { get; set; }
        public decimal ImportedQty { get; set; }
        public string Unit { get; set; }
    }

    public class LowStockReportRequestVm
    {
        public DateTime? GeneratedDate { get; set; }
        public bool IncludeWIPStore { get; set; } = false;
    }

    public class LowStockReportSummaryVm
    {
        public List<LowStockReportVm> LowStockItems { get; set; } = new List<LowStockReportVm>();
        public int TotalItemsCount { get; set; }
        public DateTime GeneratedDate { get; set; }
    }
}
