﻿using PmsCommon;
using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public partial class SaleOrderTableVm
    {
        public long SaleOrderId { get; set; }
        public string SaleOrderType { get; set; }
        public long? CustomerId { get; set; }
        public string CustomerName { get; set; }
        public long? CategoryId { get; set; }
        public string Category { get; set; }
        public bool? IsRawMaterialIssued { get; set; }
        public string SaleOrderNumber { get; set; }
        public DateTime? SaleOrderDate { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public string Remarks { get; set; }
        public bool? CostingAdded { get; set; }
        public string SaleOrderStatus { get; set; }
        public string SaleFormulationCode { get; set; }
        public long? ProformaInvoiceId { get; set; }
        public string FormulationFabricProductName { get; set; }
        public decimal? FormulationFabricProductPrice { get; set; }
        public string FormulationFabricProductUnit { get; set; }
        public long? FormulationFabricProductId { get; set; }
        public decimal? FormulationFabricProductQty { get; set; }
        public decimal? FormulationFabricProducWidthInMeter { get; set; }
        public List<LinkedSaleOrderTableVm> LinkedSaleOrder { get; set; }
        public List<LinkedSaleOrderTableVm> ParentSaleOrder { get; set; }
        public List<SaleOrderPostProcessOrderTableVm> SaleOrderPostProcessOrder { get; set; }
        public SaleFormulationCodeMasterVm FormulationCode { get; set; }
        public SaleOrderCostingTableVm SaleOrderCosting { get; set; }
        public SaleOrderProductionCompleteTableVm SaleOrderProductionComplete { get; set; }
        public SaleOrderProductionTableVm SaleOrderProduction { get; set; }
        public SaleOrderProductionPostProcessVm SaleOrderProductionPostProcess { get; set; }
        public bool? WorkPlanStatus { get; set; }
        public string WorkPlanNumber { get; set; }
        public ESalesOrderStatus Status { get; set; }
        public long? SaleFormulationCodeId { get; set; }
        public bool SendToConsumption { get; set; }
        public long ConsumptionStoreId { get; set; }
        public string ProductionCompletionRemarks { get; set; }
        public bool? IsJumboRequired { get; set; }
        public bool? IsPrintRequired { get; set; }
        public bool? IsTumblingRequired { get; set; }
        public bool? IsVacuumRequired { get; set; }
        public bool? IsLacquerRequired { get; set; }
        public bool? IsEmbossingRequired { get; set; }
        public string BORNumber { get; set; }
        public string SaleOrderCode { get; set; }
        public string FinishCode { get; set; }
        public string PreInspectionCompletedBy { get; set; }
        public DateTime? PreInspectionCompletedOn { get; set; }
        public string OrderStatusActionBy { get; set; }
        public DateTime? OrderStatusActionDate { get; set; }
        public long? OrderIdForLinking { get; set; }
        public long? ShiftSupervisorWorkerId { get; set; }

    }
    public partial class SaleOrderCostingViewTableVm
    {
        public long SaleOrderId { get; set; }
        public string SaleOrderType { get; set; }
        public long? CustomerId { get; set; }
        public string CustomerName { get; set; }
        public long? CategoryId { get; set; }
        public string Category { get; set; }
        public string SaleOrderNumber { get; set; }
        public DateTime? SaleOrderDate { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public string Remarks { get; set; }
        public bool? CostingAdded { get; set; }
        public string SaleOrderStatus { get; set; }
        public string SaleFormulationCode { get; set; }
        public long? ProformaInvoiceId { get; set; }
        public bool IsLiningOrder { get; set; }
        public bool IsUpperOrder { get; set; }
        public SaleOrderCostingTableVm SaleOrderCosting { get; set; }
        public SaleOrderCostingTableVm LinkedSaleOrderCosting { get; set; }
        public SaleOrderProductionCompleteTableVm SaleOrderProductionComplete { get; set; }
        public SaleOrderProductionCostingTableVm SaleOrderProduction { get; set; }
        public bool? WorkPlanStatus { get; set; }
        public string WorkPlanNumber { get; set; }
        public ESalesOrderStatus Status { get; set; }
        public long? SaleFormulationCodeId { get; set; }
        public string BORNumber { get; set; }
        public string SaleOrderCode { get; set; }
        public string FinishCode { get; set; }
    }
    public partial class SaleOrderShortVm
    {
        public long SaleOrderId { get; set; }
        public string SaleOrderType { get; set; }
        public string SaleOrderNumber { get; set; }
        public string SaleOrderCode { get; set; }
        public DateTime? SaleOrderDate { get; set; }
        public string SaleFormulationCode { get; set; }
        public ESalesOrderStatus Status { get; set; }
    }
    public partial class SaleOrderRequestFilter
    {
        public string SaleOrderNumber { get; set; }
        public string DateType { get; set; }
        public DateTime? FromAddedDate { get; set; }
        public DateTime? ToAddedDate { get; set; }
        public DateTime? FromSaleOrderDate { get; set; }
        public DateTime? ToSaleOrderDate { get; set; }
        public DateTime? FromDeliveryDate { get; set; }
        public DateTime? ToDeliveryDate { get; set; }
        public ESalesOrderStatus? Status { get; set; }
        public string AddedBy { get; set; }
        public long? SaleFormulationCodeId { get; set; }
        public string ArticleName { get; set; }
        public long? CustomerId { get; set; }
        public long? ColorId { get; set; }
        public long? GrainId { get; set; }
        public string ProductType { get; set; }
        public string OrderType { get; set; }
        public string SaleOrderStatus { get; set; }
        public string WorkPlanNo { get; set; }
        public decimal? Thick { get; set; }
        public int NumberOfRecords { get; set; }
    }
    public partial class SaleOrderCompleteProductionVm
    {
        public long WorkPlanJumboMasterId { get; set; }
        public long SaleOrderId { get; set; }
        public string ProductionCompletionRemarks { get; set; }
        public decimal? PreSkinGsm { get; set; }
        public decimal? PreSkinScGsm { get; set; }
        public decimal? PreSkinRemainingPasteQty { get; set; }
        public decimal? PreSkinActualPasteQty { get; set; }
        public decimal? SkinGsm { get; set; }
        public decimal? SkinScGsm { get; set; }
        public decimal? SkinRemainingPasteQty { get; set; }
        public decimal? SkinActualPasteQty { get; set; }
        public decimal? FoamGsm { get; set; }
        public decimal? FoamScGsm { get; set; }
        public decimal? FoamRemainingPasteQty { get; set; }
        public decimal? FoamActualPasteQty { get; set; }
        public decimal? AdhesiveGsm { get; set; }
        public decimal? AdhesiveScGsm { get; set; }
        public decimal? AdhesiveRemainingPasteQty { get; set; }
        public decimal? AdhesiveActualPasteQty { get; set; }
        public SaleOrderProductionTableVm SaleOrderProduction { get; set; }
    }
    public partial class SaleOrderJumboRemoveVm
    {
        public long WorkPlanJumboMasterId { get; set; }
        public decimal ManufacturingQuantity { get; set; }
    }
    public partial class ResultSaleOrder
    {
        public ApiResult Result { get; set; }
    }
    public partial class KeyValue
    {
        public int? Key { get; set; }
        public string Value { get; set; }
    }
}
