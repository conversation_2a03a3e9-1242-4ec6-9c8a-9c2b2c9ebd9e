using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PmsEntity.ViewModel
{
    public class ReturnStockVm
    {
        [Required]
        public long OriginalSaleOrderId { get; set; }
        
        public long? OriginalDispatchId { get; set; }
        
        [Required]
        public long CustomerId { get; set; }
        
        public string CustomerName { get; set; }
        
        [Required]
        public string ReturnReason { get; set; }
        
        public DateTime? ReturnDate { get; set; }
        
        public string ReturnedBy { get; set; }
        
        public long? OriginalDispatchInvoiceId { get; set; }
        
        public List<ReturnStockItemVm> ReturnedItems { get; set; } = new List<ReturnStockItemVm>();
        
        // Original order details for reference
        public string SaleOrderNumber { get; set; }
        public DateTime? OriginalDispatchDate { get; set; }
        public string OriginalInvoiceNumber { get; set; }
    }

    public class ReturnStockItemVm
    {
        [Required]
        public long ProductId { get; set; }
        
        public string ProductName { get; set; }
        
        [Required]
        public decimal ReturnedQuantity { get; set; }
        
        public string Unit { get; set; }
        
        public decimal OriginalDispatchedQuantity { get; set; }
        
        public DateTime? OriginalManufacturedDate { get; set; }
        
        // Product specifications
        public long? ThicknessId { get; set; }
        public long? GrainId { get; set; }
        public long? WidthId { get; set; }
        public long? ColorId { get; set; }
        public string PostProcess { get; set; }
        
        // Return specific details
        public string ReturnCondition { get; set; } // Good, Damaged, Defective
        public string ReturnNotes { get; set; }
    }

    public class DispatchedItemsForReturnVm
    {
        public long SaleOrderId { get; set; }
        public string SaleOrderNumber { get; set; }
        public long CustomerId { get; set; }
        public string CustomerName { get; set; }
        public DateTime? DispatchDate { get; set; }
        public string InvoiceNumber { get; set; }
        public long? InvoiceId { get; set; }
        
        public List<DispatchedItemDetailVm> DispatchedItems { get; set; } = new List<DispatchedItemDetailVm>();
    }

    public class DispatchedItemDetailVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal DispatchedQuantity { get; set; }
        public string Unit { get; set; }
        public DateTime? ManufacturedDate { get; set; }
        public string Grade { get; set; }
        
        // Product specifications
        public long? ThicknessId { get; set; }
        public long? GrainId { get; set; }
        public long? WidthId { get; set; }
        public long? ColorId { get; set; }
        public string PostProcess { get; set; }
        
        // For tracking returns
        public decimal? PreviouslyReturnedQuantity { get; set; }
        public decimal AvailableForReturn { get; set; }
    }

    public class ReturnStockListVm
    {
        public long StockId { get; set; }
        public DateTime StockDate { get; set; }
        public string InvoiceNumber { get; set; }
        public long OriginalSaleOrderId { get; set; }
        public string OriginalSaleOrderNumber { get; set; }
        public long CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string ReturnReason { get; set; }
        public DateTime? ReturnDate { get; set; }
        public string ReturnedBy { get; set; }
        public bool InspectionCompleted { get; set; }
        public bool AllocationCompleted { get; set; }
        public string Batch { get; set; }
        public int TotalItems { get; set; }
        public decimal TotalQuantity { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
    }

    public class ReturnStockFilterVm
    {
        public DateTime? FromReturnDate { get; set; }
        public DateTime? ToReturnDate { get; set; }
        public long? CustomerId { get; set; }
        public long? OriginalSaleOrderId { get; set; }
        public string ReturnReason { get; set; }
        public bool? InspectionCompleted { get; set; }
        public bool? AllocationCompleted { get; set; }
        public string AddedBy { get; set; }
    }
}
