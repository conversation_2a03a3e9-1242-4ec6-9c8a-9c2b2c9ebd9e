﻿using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public partial class ProductTransferTableVm
    {
        public long TransferId { get; set; }
        public long? FromProductId { get; set; }
        public string FromProductName { get; set; }
        public long? ToProductId { get; set; }
        public string ToProductName { get; set; }
        public decimal? Quantity { get; set; }
        public string Status { get; set; }
        public string RequestReason { get; set; }
        public UserMasterVm AddedByDetails { get; set; }
        public DateTime? AddedDate { get; set; }
        public UserMasterVm ActionByDetails { get; set; }
        public DateTime? ActionDate { get; set; }
        public string ActionRemark { get; set; }
    }
}
