﻿using System;
using PmsCommon;

namespace PmsEntity.ViewModel
{
    public partial class JumboInspectionTableVm
    {
        public long JumboInspectionId { get; set; }
        public long? WorkPlanJumboMasterId { get; set; }
        public long? SaleOrderId { get; set; }
        public string SaleOrderNumber { get; set; }
        public string SaleOrderCode { get; set; }
        public string Grade { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? Weight { get; set; }
        public string Code { get; set; }
        public string Unit { get; set; }
        public string ThicknessNumber { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? DispatchedQuantity { get; set; }
        public string DispatchStatus { get; set; }
        public string InspectedBy { get; set; }
        public long? JumboDispatchId { get; set; }
        public string RollType { get; set; }
        public bool? IsSampleJumbo { get; set; }
        public long? StockId { get; set; }
        public long? StoreId { get; set; }
        public string StoreName { get; set; }
        public long RackId { get; set; }
        public string RackName { get; set; }
        public ESalesOrderStatus SaleOrderStatus { get; set; }
    }

    public partial class JumboInspectionFilterVm
    {
        public long? SaleOrderId { get; set; }
        public long? CustomerId { get; set; }
        public long? SaleFormulationCodeId { get; set; }
        public long? GrainId { get; set; }
        public long? ColorId { get; set; }
        public long? FabricColorId { get; set; }
        public decimal? Thick { get; set; }
        public decimal? Width { get; set; }
        public DateTime? OrderDateFrom { get; set; }
        public DateTime? OrderDateTo { get; set; }
        public long? EmbossingMasterId { get; set; }
        public long? PrintMasterId { get; set; }
        public long? TumblingMasterId { get; set; }
        public long? VacuumMasterId { get; set; }
        public long? LacquerMasterId { get; set; }
        public string Grade { get; set; }
        public long? FabricProductId { get; set; }
        public string RollType { get; set; }
    }
}
