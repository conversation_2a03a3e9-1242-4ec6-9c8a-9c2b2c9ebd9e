﻿using System;
using System.Collections.Generic;
using PmsCommon;

namespace PmsData.Models
{
    public partial class WorkPlanOrderTrackingTableVm
    {
        public long WorkPlanTrackingId { get; set; }
        public long SaleOrderId { get; set; }
        public int? OrderStatus { get; set; }
        public long CurrentWorkPlanId { get; set; }
        public long? NewWorkPlanId { get; set; }
        public string ChangedBy { get; set; }
        public DateTime? ChangedDate { get; set; }
    }

    public partial class WorkPlanOrderTrackingTableDetailVm
    {
        public long WorkPlanTrackingId { get; set; }
        public long SaleOrderId { get; set; }
        public ESalesOrderStatus OrderStatus { get; set; }
        public long CurrentWorkPlanId { get; set; }
        public long? NewWorkPlanId { get; set; }
        public string ChangedBy { get; set; }
        public DateTime? ChangedDate { get; set; }
    }
}

