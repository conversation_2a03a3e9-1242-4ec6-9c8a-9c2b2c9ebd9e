﻿using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public partial class ProductionDowntimeScheduledVm
    {
        public long ScheduledDowntimeId { get; set; }
        public long ProductionDowntimeReasonId { get; set; }
        public string ReasonName { get; set; }
        public string ReasonCode { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string RecurrencePattern { get; set; }
        public string ApplicableDays { get; set; }
        public int? DayOfMonth { get; set; }
        public bool IsRecurring { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public int ProductionLineNo { get; set; }
        public bool? IsActive { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public bool IsDeleted { get; set; }
    }
    public class ScheduledDowntimeVm
    {
        public DateTime StartDateTime { get; set; }
        public DateTime EndDateTime { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string RecurrencePattern { get; set; }
        public string ApplicableDays { get; set; }
        public int? DayOfMonth { get; set; }
        public ProductionDowntimeReasonMasterVm Reason { get; set; }
        public string ReasonCode { get; set; }
        public string DowntimeType { get; set; }
        public int ProductionLineNo { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
    }
}
