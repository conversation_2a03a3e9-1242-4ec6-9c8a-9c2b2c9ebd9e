﻿using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public partial class OutpassMasterVm
    {
        public long OutpassId { get; set; }
        public string OutpassTo { get; set; }
        public string OutpassNumber { get; set; }
        public DateTime? OutpassDate { get; set; }
        public string OutpassType { get; set; }
        public string Purpose { get; set; }
        public string Remark { get; set; }
        public bool? IsOutpassIn { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public List<OutpassItemTableVm> OutpassItems { get; set; }
        public long? OutpassToCustomerId { get; set; }
        public long? PurposeId { get; set; }
        public DateTime? ExpectedReturnDate { get; set; }
        public string Status { get; set; }
        public long? TransportId { get; set; }
        public string TransportName { get; set; }
        public long? VehicleId { get; set; }
        public string VehicleNumber { get; set; }
        public bool? IsGateIn { get; set; }
        public string ApprovedByName { get; set; }
        public string CreateMode { get; set; }
        public List<OutpassBarcodeItemVm> BarcodeItems { get; set; }
        public List<StockLabelTableVm> BarcodeDetails { get; set; }
    }
    public partial class OutpassFilterVm
    {
        public long? OutpassToCustomerId { get; set; }
        public string OutpassTo { get; set; }
        public string OutpassNumber { get; set; }
        public string OutpassType { get; set; }
        public long? PurposeId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? IsOutpassIn { get; set; }
        public string OutpassProductName { get; set; }
        public long? ProductId { get; set; } // Added ProductId filter
        // New pagination properties
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
    public class PaginatedResult<T>
    {
        public List<T> Items { get; set; }
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);
    }
    public partial class OutpassStatusActionVm
    {
        public long OutpassId { get; set; }
        public string Status { get; set; }
        public string Remark { get; set; }
    }

    // New ViewModels for Pending Returnable OutPass Notifications
    public class PendingReturnableOutPassVm
    {
        public long OutpassId { get; set; }
        public string OutpassNumber { get; set; }
        public string OutpassTo { get; set; }
        public DateTime? OutpassDate { get; set; }
        public DateTime? ExpectedReturnDate { get; set; }
        public string Purpose { get; set; }
        public string Remark { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public List<OutpassItemTableVm> OutpassItems { get; set; } = new List<OutpassItemTableVm>();
        public int DaysOverdue { get; set; }
        public bool IsOverdue { get; set; }
        public string Status { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class PendingReturnableOutPassReportVm
    {
        public List<PendingReturnableOutPassVm> PendingOutPasses { get; set; } = new List<PendingReturnableOutPassVm>();
        public int TotalPendingCount { get; set; }
        public int OverdueCount { get; set; }
        public DateTime GeneratedDate { get; set; }
        public decimal TotalPendingAmount { get; set; }
    }

    public class PendingReturnableOutPassRequestVm
    {
        public DateTime? GeneratedDate { get; set; }
        public bool IncludeOverdueOnly { get; set; } = false;
        public int? DaysOverdueThreshold { get; set; } = 0;
    }

    // New ViewModels for Barcode functionality
    public class OutpassBarcodeItemVm
    {
        public long OutpassItemId { get; set; }
        public long StockLabelId { get; set; }
        public string SerialNo { get; set; }
        public string ShortCode { get; set; }
        public string ProductName { get; set; }
        public decimal Quantity { get; set; }
        public string PackagingUnit { get; set; }
        public decimal Amount { get; set; }
        public string Unit { get; set; }
        public long ProductId { get; set; }
        public long StockProductId { get; set; }
        public string BatchNo { get; set; }
        public long CurrentStoreId { get; set; }
        public long CurrentRackId { get; set; }
        public string StoreName { get; set; }
        public string RackName { get; set; }
        public decimal PricePerUnit { get; set; }

        // NEW PROPERTIES FOR CONSUMPTION TRACKING
        public decimal? ReturnedQuantity { get; set; }
        public long? ReturnedStoreId { get; set; }
        public long? ReturnedRackId { get; set; }
        public string ReasonForLessQuantity { get; set; }
        public bool RequiresReason { get; set; }
        public string ReturnedStoreName { get; set; }
        public string ReturnedRackName { get; set; }
        public string ReturnCompletedBy { get; set; }
        public DateTime? ReturnCompletedDate { get; set; }
    }

    public class BarcodeValidationResult
    {
        public bool HasBarcodeLabels { get; set; }
        public int BarcodeCount { get; set; }
        public string Message { get; set; }
        public List<StockLabelTableVm> BarcodeLabels { get; set; }
    }

    // New ViewModels for OutPass Packing List Print functionality
    public class OutpassPackingListDataVm
    {
        public OutpassMasterVm OutpassDetails { get; set; }
        public List<PackingListUnitGroupVm> UnitGroups { get; set; } = new List<PackingListUnitGroupVm>();
        public PackingListSummaryVm Summary { get; set; }
    }

    public class PackingListUnitGroupVm
    {
        public string UnitType { get; set; }
        public List<PackingListProductVm> Products { get; set; } = new List<PackingListProductVm>();
        public decimal TotalQuantity { get; set; }
        public decimal TotalAmount { get; set; }
        public int TotalLabels { get; set; }
        public string PackagingBreakdown { get; set; } // e.g., "3 Rolls, 2 Bags"
    }

    public class PackingListProductVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string BatchNo { get; set; }
        public decimal TotalQuantity { get; set; }
        public string Unit { get; set; }
        public decimal TotalAmount { get; set; }
        public int LabelCount { get; set; }
        public string PackagingBreakdown { get; set; } // e.g., "2 Rolls, 1 Bag"
        public List<PackingListLabelVm> Labels { get; set; } = new List<PackingListLabelVm>();
    }

    public class PackingListLabelVm
    {
        public long StockLabelId { get; set; }
        public string SerialNo { get; set; }
        public string ShortCode { get; set; }
        public decimal Quantity { get; set; }
        public string PackagingUnit { get; set; }
        public decimal Amount { get; set; }
        public string StoreName { get; set; }
        public string RackName { get; set; }
    }

    public class PackingListSummaryVm
    {
        public int TotalProducts { get; set; }
        public int TotalLabels { get; set; }
        public decimal GrandTotalQuantity { get; set; }
        public decimal GrandTotalAmount { get; set; }
        public string OverallPackagingBreakdown { get; set; } // e.g., "15 Rolls, 8 Bags, 3 Sheets"
        public List<string> UnitsUsed { get; set; } = new List<string>(); // e.g., ["Meters", "KGs"]
    }

    // New ViewModels for OutPass Print functionality with backend processing
    public class OutpassPrintDataVm
    {
        public OutpassMasterVm OutpassDetails { get; set; }
        public List<PrintUnitGroupVm> UnitGroups { get; set; } = new List<PrintUnitGroupVm>();
        public PrintSummaryVm Summary { get; set; }
    }

    public class PrintUnitGroupVm
    {
        public string UnitType { get; set; }
        public List<PrintProductVm> Products { get; set; } = new List<PrintProductVm>();
        public decimal TotalQuantity { get; set; }
        public decimal TotalAmount { get; set; }
        public int TotalLabels { get; set; }
        public string PackagingBreakdown { get; set; } // e.g., "3 Rolls, 2 Bags"
    }

    public class PrintProductVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public List<PrintItemVm> Items { get; set; } = new List<PrintItemVm>();
        public decimal TotalQuantity { get; set; }
        public decimal TotalAmount { get; set; }
        public int LabelCount { get; set; }
        public string PackagingBreakdown { get; set; } // e.g., "2 Rolls, 1 Bag"
        public bool HasBarcode { get; set; }
        public bool HasManual { get; set; }
    }

    public class PrintItemVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string BatchNo { get; set; }
        public decimal Quantity { get; set; }
        public string Unit { get; set; }
        public decimal Amount { get; set; }
        public decimal TotalAmount { get; set; }
        public string EntryType { get; set; } // "Manual" or "Barcode"
        public string StoreName { get; set; }
        public string RackName { get; set; }
        public List<string> SerialNumbers { get; set; } = new List<string>();
        public List<string> PackagingUnits { get; set; } = new List<string>();
        public string PackagingBreakdown { get; set; }
        public int LabelCount { get; set; }
    }

    public class PrintSummaryVm
    {
        public int TotalProducts { get; set; }
        public int TotalManualItems { get; set; }
        public int TotalBarcodeItems { get; set; }
        public int TotalLabels { get; set; }
        public decimal GrandTotalQuantity { get; set; }
        public decimal GrandTotalAmount { get; set; }
        public List<string> UnitsUsed { get; set; } = new List<string>();
        public string OverallPackagingBreakdown { get; set; }
    }


}
