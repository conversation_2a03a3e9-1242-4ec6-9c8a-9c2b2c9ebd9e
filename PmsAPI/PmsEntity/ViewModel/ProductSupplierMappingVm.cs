using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class ProductSupplierMappingVm
    {
        public long ProductSupplierMappingId { get; set; }
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public long SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string SupplierProductName { get; set; }
        public int UsageCount { get; set; }
        public DateTime LastUsedDate { get; set; }
        public long LastUsedById { get; set; }
        public string LastUsedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public long CreatedById { get; set; }
        public string CreatedBy { get; set; }
    }

    public class ProductWithSupplierNamesVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string ProductType { get; set; }
        public string Unit { get; set; }
        public List<SupplierProductNameVm> SupplierNames { get; set; } = new List<SupplierProductNameVm>();
    }

    public class SupplierProductNameVm
    {
        public long ProductSupplierMappingId { get; set; }
        public long SupplierId { get; set; }
        public long ProductId { get; set; }
        public string SupplierName { get; set; }
        public string SupplierProductName { get; set; }
        public int UsageCount { get; set; }
        public DateTime LastUsedDate { get; set; }
        public bool IsFrequentlyUsed => UsageCount >= 3;
    }

    public class CreateProductSupplierMappingVm
    {
        public long ProductId { get; set; }
        public long SupplierId { get; set; }
        public string SupplierProductName { get; set; }
        public long UserId { get; set; }
    }

    public class UpdateSupplierProductNameVm
    {
        public long ProductSupplierMappingId { get; set; }
        public string SupplierProductName { get; set; }
        public long UserId { get; set; }
    }

    public class InvoiceProductDetailsVm
    {
        public long InvoiceId { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public string SupplierName { get; set; }
        public List<InvoiceProductItemVm> Products { get; set; } = new List<InvoiceProductItemVm>();
    }

    public class InvoiceProductItemVm
    {
        public long ProductId { get; set; }
        public string InternalProductName { get; set; }
        public string SupplierProductName { get; set; }
        public decimal? Quantity { get; set; }
        public string Unit { get; set; }
        public decimal? PricePerUnit { get; set; }
        public decimal? TotalAmount { get; set; }
        public string Grade { get; set; }
        public long? ProductSupplierMappingId { get; set; }
    }

    // New ViewModels for Supplier Product Mapping Report
    public class SupplierProductMappingReportRequestVm
    {
        public long? ProductId { get; set; }
        public long? SupplierId { get; set; }
        public bool? FrequentlyUsed { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    public class SupplierProductMappingReportResponseVm
    {
        public List<SupplierProductMappingReportItemVm> Items { get; set; } = new List<SupplierProductMappingReportItemVm>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    }

    public class SupplierProductMappingReportItemVm
    {
        public long ProductSupplierMappingId { get; set; }
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string ProductType { get; set; }
        public string ProductCategory { get; set; }
        public string ProductFirstSubCategory { get; set; }
        public string ProductSecSubCategory { get; set; }
        public long SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string SupplierProductName { get; set; }
        public int UsageCount { get; set; }
        public DateTime LastUsedDate { get; set; }
        public string LastUsedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public bool IsFrequentlyUsed => UsageCount >= 3;
    }
}
