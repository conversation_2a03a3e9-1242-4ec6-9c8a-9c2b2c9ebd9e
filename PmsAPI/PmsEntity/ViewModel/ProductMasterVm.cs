﻿
using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class ProductMasterVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string Unit { get; set; }
        public string ProductType { get; set; }
        public string ProductCode { get; set; }
        public long? ProductCategoryId { get; set; }
        public string ProductCategory { get; set; }
        public long? ProductFirstSubCategoryId { get; set; }
        public string ProductFirstSubCategory { get; set; }
        public long? ProductSecSubCategoryId { get; set; }
        public string ProductSecSubCategory { get; set; }
        public decimal? MinimumQuantity { get; set; }
        public decimal? AvgGsm { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public ProductMasterExtensionVm ProductExtension { get; set; }
        public bool Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public decimal? WidthInMeter { get; set; }
        public string ProductDescription { get; set; }

    }

    public class ProductStockReportVm
    {
        public long StockId { get; set; }
        public long StockProductId { get; set; }
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductType { get; set; }
        public string ProductCode { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? TotalValue { get; set; }
        public decimal? RecievedQuantity { get; set; }
        public decimal? AcceptedQuantity { get; set; }
        public decimal? RejectedQuantity { get; set; }
        public long? ProductCategoryId { get; set; }
        public string ProductCategory { get; set; }
        public long? ProductFirstSubCategoryId { get; set; }
        public string ProductFirstSubCategory { get; set; }
        public long? ProductSecSubCategoryId { get; set; }
        public string ProductSecSubCategory { get; set; }
        public string Barcode { get; set; }
        public DateTime? ManufacturedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public decimal? PricePerUnit { get; set; }
        public decimal? FreightPerUnit { get; set; }
        public string ProductQuality { get; set; }
        public string Unit { get; set; }
        public string Batch { get; set; }
        public DateTime? ReceivedDate { get; set; }
        public string ReceivedBy { get; set; }
        public string SupplierName { get; set; }
        public long? SupplierId { get; set; }
        public bool? IsStockReissued { get; set; }
        public List<ConsumeStockProductMasterVm> ConsumeStockProduct { get; set; }
        public List<IssueProductTableVm> IssueProduct { get; set; }
        public List<StockProductAllocationVm> StockProductAllocation { get; set; }
        public StockTransactions StockTransactions { get; set; }
        public long? StoreId { get; set; }
        public string StoreCode { get; set; }
        public string StoreName { get; set; }
        public long RackId { get; set; }
        public string RackCode { get; set; }
        public string RackName { get; set; }
        public decimal? MinimumQuantity { get; set; }
        public bool IsBarcodeLabelExist { get; set; }
    }
    public class StockAvailabilityReportRequestVm
    {
        public string ProductType { get; set; }
        public string Unit { get; set; }
        public long? ProductCategoryId { get; set; }
        public long? ProductFirstSubCategoryId { get; set; }
        public long? ProductSecSubCategoryId { get; set; }
        public long? ProductId { get; set; }
        public string ProductQuality { get; set; }
        public string BatchNo { get; set; }
        public long? SupplierId { get; set; }
        public long? StoreId { get; set; }
        public long? RackId { get; set; }
        public string ReceivedBy { get; set; }
        public bool isMinumQuantityCheck { get; set; }
        public bool isOutOfStockCheck { get; set; } // CRITICAL FIX: Add new parameter using successful pattern
        public bool? IncludeWIPStore { get; set; }
        // CRITICAL FIX: Add date parameters for dashboard drill-down consistency
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
    }
    public class SearchParamsProductStockReportVm
    {
        public long ProductId { get; set; }
        public long SupplierId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }

    }

    public class SearchParamsProductCategoryReportVm
    {
        public string ProductType { get; set; }
        public long? ProductCategoryId { get; set; }
        public long? ProductFirstSubCategoryId { get; set; }
        public long? ProductSecSubCategoryId { get; set; }
        public long? SupplierId { get; set; }
        public string Unit { get; set; }
        public bool? IncludeWIPStore { get; set; }
        // CRITICAL FIX: Add date parameters for dashboard drill-down consistency
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
    }
    public class SearchParamsProductCategoryByNameVm
    {
        public string ProductCategoryName { get; set; }
        public string ProductFirstSubCategoryName { get; set; }
        public string ProductSecSubCategoryName { get; set; }
    }

    public class ProductStockStoreReportVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductType { get; set; }
        public string ProductCode { get; set; }
        public long? StoreId { get; set; }
        public string StoreName { get; set; }
        public string StoreCode { get; set; }
        public decimal? Quantity { get; set; }
        public string Unit { get; set; }
    }

    public class ProductStockStoreRackReportVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductType { get; set; }
        public string ProductCode { get; set; }
        public long? ProductCategoryId { get; set; }
        public string ProductCategory { get; set; }
        public long? ProductFirstSubCategoryId { get; set; }
        public string ProductFirstSubCategory { get; set; }
        public long? ProductSecSubCategoryId { get; set; }
        public string ProductSecSubCategory { get; set; }
        public long? StoreId { get; set; }
        public string StoreName { get; set; }
        public string StoreCode { get; set; }
        public long? RackId { get; set; }
        public string RackName { get; set; }
        public string RackCode { get; set; }
        public decimal? Quantity { get; set; }
        public string Unit { get; set; }
        public decimal? MinimumQty { get; set; }
        public decimal? ProdQuantity { get; set; }
    }

    public class IssueSaleOrderProductsStockVm
    {
        public long ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductType { get; set; }
        public string ProductCode { get; set; }
        public string SKU { get; set; }
        public string BarCode { get; set; }
        public string Unit { get; set; }
        public decimal? PerUnitPrice { get; set; }
        public DateTime? ManufacturedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public long? StockId { get; set; }
        public long? StockProductId { get; set; }
        public long? InvoiceId { get; set; }
        public string InvoiceNumber { get; set; }
        public long? SupplierId { get; set; }
        public string SupplierName { get; set; }
        public long? StoreId { get; set; }
        public string StoreName { get; set; }
        public string StoreCode { get; set; }
        public long? RackId { get; set; }
        public string RackName { get; set; }
        public string RackCode { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? SaleOrderQuantity { get; set; }
        public DateTime StockDate { get; set; }
        public string Batch { get; set; }
        public decimal? ActualQuantity { get; set; }
        public string MixingName { get; set; }
        public decimal? ConsumedQty { get; set; }
        public decimal? ConsumedScQty { get; set; }
        public Boolean? IsDamaged { get; set; }
        public string ConsumedBy { get; set; }
        public DateTime? ConsumedDate { get; set; }
        public string ProductQuality { get; set; }
        public long? StockLabelId { get; set; }
        public string ShortCode { get; set; }
    }

    public class StockProductPriceVm
    {
        public long? StockId { get; set; }
        public long? StockProductId { get; set; }
        public decimal? PricePerUnit { get; set; }
        public decimal? ShippingHandlingPerUnit { get; set; }
        public decimal? FreightPerUnit { get; set; }
        public decimal? InvoicePricePerUnit { get; set; }
        public decimal? MiscPerUnit { get; set; }
    }


    public class StockTransaction
    {
        public DateTime? Date { get; set; }
        public decimal? OpeningStock { get; set; }
        public decimal? TransactionQuantity { get; set; }
        public decimal? ClosingStock { get; set; }
    }


    public class StockTransactions
    {

        public decimal? OpeningStock { get; set; }
        public decimal? ClosingStock { get; set; }

    }

    public class StockProductTrackingPriceVm
    {
        public long? ProductId { get; set; }
        public long? StockProductId { get; set; }
        public string ProductName { get; set; }
        public string Unit { get; set; }
        public string Batch { get; set; }
        public decimal? PricePerUnit { get; set; }
        public decimal? NewPricePerUnit { get; set; }
        public decimal? ShippingHandlingPerUnit { get; set; }
        public decimal? NewShippingHandlingPerUnit { get; set; }
        public decimal? FreightPerUnit { get; set; }
        public decimal? NewFreightPerUnit { get; set; }
        public decimal? InvoicePricePerUnit { get; set; }
        public decimal? NewInvoicePricePerUnit { get; set; }
        public decimal? MiscPerUnit { get; set; }
        public decimal? NewMiscPerUnit { get; set; }
        public long? SaleOrderId { get; set; }
        public string SaleOrderNumber { get; set; }

    }
}
