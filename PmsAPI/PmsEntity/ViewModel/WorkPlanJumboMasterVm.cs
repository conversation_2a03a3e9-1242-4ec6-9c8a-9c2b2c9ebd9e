﻿using PmsCommon;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PmsEntity.ViewModel
{
    public partial class WorkPlanJumboMasterVm
    {
        public long WorkPlanJumboMasterId { get; set; }
        public long? WorkPlanOrdersId { get; set; }
        public long? WorkPlanId { get; set; }
        public string WorkPlanNo { get; set; }
        public long? OrderId { get; set; }
        public DateTime? JumboRollDate { get; set; }
        public DateTime? JumboRollStartTime { get; set; }
        public DateTime? JumboRollEndTime { get; set; }
        public decimal? JumboRollPrdSpeed { get; set; }
        public string JumboNo { get; set; }
        public string SaleOrderNumber { get; set; }
        public long? SaleOrderId { get; set; }
        public ESalesOrderStatus SaleOrderStatus { get; set; }
        public long? CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal? Rate { get; set; }
        public decimal? Amount { get; set; }
        public decimal? JumboRolQty { get; set; }
        public decimal? JumboCount { get; set; }
        public decimal? ActualQuantity { get; set; }
        public decimal? QuantityFromJumboInspection { get; set; }
        public decimal? Weight { get; set; }
        public long? RackId { get; set; }
        public string RackCode { get; set; }
        public string RackName { get; set; }
        public long? StoreId { get; set; }
        public string StoreCode { get; set; }
        public string StoreName { get; set; }
        public string Remark { get; set; }
        public decimal? Yield { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? WastagePrint { get; set; }
        public decimal? WastageEmbossing { get; set; }
        public decimal? WastageLacquer { get; set; }
        public decimal? WastageVacuum { get; set; }
        public decimal? WastageTumbling { get; set; }
        public bool? IsInspectionCompleted { get; set; }
        public bool? IsSampleJumbo { get; set; }
        public decimal? FirstGrade { get; set; }
        public decimal? FirstGradeCount { get; set; }
        public decimal? AGrade { get; set; }
        public decimal? AGradeCount { get; set; }
        public decimal? CUTPCGrade { get; set; }
        public decimal? CUTPCGradeCount { get; set; }
        public decimal? FILMGrade { get; set; }
        public decimal? FILMGradeCount { get; set; }
        public string FabricName { get; set; }
        public decimal? LOTGrade { get; set; }
        public decimal? LOTGradeCount { get; set; }
        public decimal? NSGrade { get; set; }
        public decimal? NSGradeCount { get; set; }
        public decimal? WASTEGrade { get; set; }
        public decimal? WASTEGradeCount { get; set; }
        public decimal? SampleQuantity { get; set; }
        public decimal? SampleCount { get; set; }
        public List<JumboInspectionTableVm> JumboInspection { get; set; }

        public string Grade { get; set; }
        public string SaleOrderCode { get; set; }
        public string SaleOrderManufacturingProductName { get; set; }
        public decimal? ManufacturingQuantity { get; set; }
        public decimal? SaleOrderQuantity { get; set; }
        public bool IsLiningOrder { get; set; }
        public string SaleFormulationCodeName { get; set; }
        public long? ShiftSupervisorWorkerId { get; set; }
        public string ShiftSupervisorWorkerName { get; set; }
        public long? ProductionLineNo { get; set; }

        // Added for flexible allocation strategy
        public long? BatchRackId { get; set; }
    }
    public partial class WorkPlanJumboSingleObjResponseVm
    {
        public long WorkPlanJumboMasterId { get; set; }
        public string JumboNo { get; set; }
        public DateTime? JumboRollDate { get; set; }
        public DateTime? JumboRollStartTime { get; set; }
        public DateTime? JumboRollEndTime { get; set; }
        public decimal? Rate { get; set; }
        public decimal? Amount { get; set; }
        public decimal? Weight { get; set; }
        public decimal? JumboRolQty { get; set; }
        public long? RackId { get; set; }
    }
    public partial class WorkPlanJumboReOpenInspectionVm
    {
        public long WorkPlanJumboMasterId { get; set; }
        public long? SaleOrderId { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
    }

    public partial class ResultWorkPlanJumboMaster
    {
        public ApiResult Result { get; set; }
        public WorkPlanJumboSingleObjResponseVm Data { get; set; }

    }
    public partial class ResultWorkPlanJumboMasterList
    {
        public ApiResult Result { get; set; }
        public List<WorkPlanJumboMasterVm> Data { get; set; }

    }

    public class JumboActualList
    {
        public long WorkPlanJumboMasterId { get; set; }
        public decimal? ActualQuantity { get; set; }


    }

    public partial class YieldReportVm
    {
        public string SaleOrderNumber { get; set; }
        public long? SaleOrderId { get; set; }
        public string CustomerName { get; set; }
        public decimal? JumboCount { get; set; }
        public decimal? Yield { get; set; }
        public decimal? FirstGrade { get; set; }
        public decimal? FirstGradeCount { get; set; }
        public decimal? AGrade { get; set; }
        public decimal? AGradeCount { get; set; }
        public decimal? CUTPCGrade { get; set; }
        public decimal? CUTPCGradeCount { get; set; }
        public decimal? FILMGrade { get; set; }
        public decimal? FILMGradeCount { get; set; }
        public string FabricName { get; set; }
        public decimal? LOTGrade { get; set; }
        public decimal? LOTGradeCount { get; set; }
        public decimal? NSGrade { get; set; }
        public decimal? NSGradeCount { get; set; }
        public decimal? WASTEGrade { get; set; }
        public decimal? WASTEGradeCount { get; set; }
        public decimal? SampleQuantity { get; set; }
        public decimal? SampleCount { get; set; }
        public string SaleOrderCode { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? Amount { get; set; }
        public decimal? ActualQuantity { get; set; }
        public decimal? JumboRolQty { get; set; }
        public decimal? WastageEmbossing { get; set; }
        public decimal? WastageLacquer { get; set; }
        public decimal? WastagePrint { get; set; }
        public decimal? WastageTumbling { get; set; }
        public decimal? WastageVacuum { get; set; }
        public decimal? ManufacturingQuantity { get; set; }
        public decimal? SaleOrderQuantity { get; set; }
    }

    public partial class WastageReportVm
    {
        public string WorkPlanNo { get; set; }
        public string JumboNo { get; set; }
        public string SaleOrderNumber { get; set; }
        public long? SaleOrderId { get; set; }
        public string SaleOrderCode { get; set; }
        public decimal? ActualQuantity { get; set; }
        public decimal? JumboRolQty { get; set; }
        public decimal? WastageEmbossing { get; set; }
        public decimal? WastageLacquer { get; set; }
        public decimal? WastagePrint { get; set; }
        public decimal? WastageTumbling { get; set; }
        public decimal? WastageVacuum { get; set; }
    }

    public class JumboFinalInspectionFilter
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? IsInspectionCompleted { get; set; }
        public string SaleOrderNumber { get; set; }
        public long? GrainId { get; set; }
        public long? ColorId { get; set; }
        public long? CustomerId { get; set; }
        public long? SaleFormulationCodeId { get; set; }
        public string JumboNumber { get; set; }
    }
    public class JumboPrintDataVm
    {
        public long WorkPlanJumboMasterId { get; set; }
        public string JumboNo { get; set; }
        public string SaleOrderNumber { get; set; }
        public long SaleOrderId { get; set; }
        public string ParentSaleOrderNumber { get; set; }
        public string SaleFormulationCode { get; set; }
        public string CustomerName { get; set; }
        public string ArticleName { get; set; }
        public string GrainCode { get; set; }
        public string GrainName { get; set; }
        public long? ColorId { get; set; }
        public string ColorCode { get; set; }
        public string ColorName { get; set; }
        public string FinishCode { get; set; }
        public string UpperFinishCode { get; set; }
        public string Thickness { get; set; }
        public string WidthNumber { get; set; }
        public decimal? Length { get; set; }
        public decimal? Weight { get; set; }
        public DateTime? JumboRollDate { get; set; }
        public string SupervisorName { get; set; }
        public long? ProductionLineNo { get; set; }
    }
    public partial class ResultJumboPrintData
    {
        public ApiResult Result { get; set; }
        public JumboPrintDataVm Data { get; set; }

    }
}
