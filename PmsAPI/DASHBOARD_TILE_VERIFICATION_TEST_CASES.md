# 🧪 Dashboard Tile Data Verification Test Cases

## 📋 Overview

Comprehensive test cases to verify that each dashboard tile displays correct data when the project starts. These tests cover both automated and manual verification procedures.

## 🚀 Pre-Test Setup

### **🔧 Required Services:**
```bash
# Terminal 1: Start PmsAPI
cd pms-backend-api\PmsAPI\PmsAPI
func start --port 7071

# Terminal 2: Start PmsReportingAPI
cd pms-backend-api\PmsAPI\PmsReportingAPI
func start --port 7072

# Terminal 3: Start Frontend
cd pms-frontend\PmsUI
ng serve
```

### **🌐 Access URLs:**
- **Frontend**: http://localhost:4200
- **Gate Dashboard**: http://localhost:4200/home/<USER>/gate
- **PmsAPI**: http://localhost:7071/api/
- **PmsReportingAPI**: http://localhost:7072/api/

## 🧪 Test Case 1: Initial Dashboard Load

### **📋 Test Objective:**
Verify all tiles display correct data when dashboard loads for the first time.

### **🔧 Test Steps:**
1. **Navigate to Gate Dashboard**: http://localhost:4200/home/<USER>/gate
2. **Wait for data to load** (loading spinner should disappear)
3. **Verify each tile displays a value** (not zero unless database is empty)

### **✅ Expected Results:**

| Tile | Expected Behavior | Acceptance Criteria |
|------|------------------|-------------------|
| **Pending Gate-Out** | Shows count > 0 or test value (5) | Number displayed, not "0" or "undefined" |
| **Pending Gate Pass** | Shows count > 0 or test value (8) | Number displayed, not "0" or "undefined" |
| **Invoices Without PO** | Shows count ≥ 0 or test value (3) | Number displayed, not "undefined" |
| **Active Purchase Orders** | Shows count > 0 or test value (8) | Number displayed, not "0" or "undefined" |
| **Revised Purchase Orders** | Shows count ≥ 0 or test value (4) | Number displayed, not "undefined" |
| **Delayed Delivery POs** | Shows count ≥ 0 or test value (4) | Number displayed, not "undefined" |
| **Delayed Payment POs** | Shows count ≥ 0 or test value (7) | Number displayed, not "undefined" |

### **🔍 Verification Method:**
```javascript
// Open browser console and run:
console.log('=== TILE VERIFICATION ===');
document.querySelectorAll('.dashboard-tile').forEach((tile, index) => {
  const title = tile.querySelector('.tile-title')?.textContent;
  const value = tile.querySelector('.tile-value')?.textContent;
  console.log(`Tile ${index + 1}: ${title} = ${value}`);
});
```

## 🧪 Test Case 2: Force Button Functionality

### **📋 Test Objective:**
Verify force button triggers fresh data load and updates all tiles.

### **🔧 Test Steps:**
1. **Open browser developer tools** (F12)
2. **Go to Network tab**
3. **Click Force Refresh button** (⟳ icon)
4. **Monitor network requests and tile updates**

### **✅ Expected Results:**
- **Network Request**: POST to `http://localhost:7072/api/report/gatedashboard`
- **Request Body**: Contains date filter information
- **Response**: JSON with all tile data
- **UI Update**: All tiles refresh with new values
- **Console Log**: "Forcing fresh data reload..."

### **🔍 Verification Method:**
```javascript
// Before clicking force button:
const beforeValues = {};
document.querySelectorAll('.dashboard-tile').forEach((tile, index) => {
  const title = tile.querySelector('.tile-title')?.textContent;
  const value = tile.querySelector('.tile-value')?.textContent;
  beforeValues[title] = value;
});

// After clicking force button (wait for completion):
const afterValues = {};
document.querySelectorAll('.dashboard-tile').forEach((tile, index) => {
  const title = tile.querySelector('.tile-title')?.textContent;
  const value = tile.querySelector('.tile-value')?.textContent;
  afterValues[title] = value;
});

console.log('Before:', beforeValues);
console.log('After:', afterValues);
console.log('Data refreshed:', JSON.stringify(beforeValues) !== JSON.stringify(afterValues));
```

## 🧪 Test Case 3: Date Filter Impact

### **📋 Test Objective:**
Verify date filters affect Purchase Order tile values but not Gate Operation tiles.

### **🔧 Test Steps:**
1. **Set date filter to "All"** and note tile values
2. **Change to "Today"** and verify changes
3. **Change to "Last 7 Days"** and verify changes
4. **Change to "Last 30 Days"** and verify changes

### **✅ Expected Results:**

| Date Filter | Gate Operations | Purchase Orders |
|-------------|----------------|-----------------|
| **All** | Shows all-time data | Shows all-time data |
| **Today** | Shows all-time data | Shows POs added today |
| **Last 7 Days** | Shows all-time data | Shows POs added in last 7 days |
| **Last 30 Days** | Shows all-time data | Shows POs added in last 30 days |

### **🔍 Verification Method:**
```javascript
// Function to capture current tile values
function captureTileValues(filterName) {
  const values = {};
  document.querySelectorAll('.dashboard-tile').forEach(tile => {
    const title = tile.querySelector('.tile-title')?.textContent;
    const value = tile.querySelector('.tile-value')?.textContent;
    values[title] = parseInt(value) || 0;
  });
  console.log(`${filterName} Filter:`, values);
  return values;
}

// Test different filters
const allFilter = captureTileValues('All');
// Change to Today filter, wait for load
const todayFilter = captureTileValues('Today');
// Change to Last 7 Days filter, wait for load
const last7Filter = captureTileValues('Last 7 Days');

// Verify PO tiles changed but Gate tiles remained same
console.log('Gate tiles consistent:', 
  allFilter['Pending Gate-Out'] === todayFilter['Pending Gate-Out'] &&
  allFilter['Pending Gate Pass'] === todayFilter['Pending Gate Pass']
);
```

## 🧪 Test Case 4: API Response Validation

### **📋 Test Objective:**
Verify backend API returns correct data structure and values.

### **🔧 Test Steps:**
1. **Open browser developer tools**
2. **Go to Network tab**
3. **Refresh dashboard or click force button**
4. **Find the API request** to `report/gatedashboard`
5. **Examine response data**

### **✅ Expected Response Structure:**
```json
{
  "PendingGateOutCount": 5,
  "PendingGatePassCount": 8,
  "InvoicesWithoutPOCount": 3,
  "ActivePOCount": 8,
  "RevisedPOCount": 4,
  "DelayedDeliveryPOCount": 4,
  "DelayedPaymentPOCount": 7
}
```

### **🔍 Verification Method:**
```bash
# Direct API test
curl -X POST http://localhost:7072/api/report/gatedashboard \
  -H "Content-Type: application/json" \
  -d '{"DateFilterType":"all"}' | jq .
```

## 🧪 Test Case 5: Error Handling

### **📋 Test Objective:**
Verify dashboard handles errors gracefully and shows appropriate messages.

### **🔧 Test Steps:**
1. **Stop PmsReportingAPI service**
2. **Try to refresh dashboard**
3. **Verify error handling**
4. **Restart service and verify recovery**

### **✅ Expected Results:**
- **Error Message**: "Error loading dashboard data. Please try again later."
- **Loading State**: Stops loading
- **Tiles**: Remain with previous values or show zeros
- **Recovery**: Works when service is restarted

## 🧪 Test Case 6: Navigation Functionality

### **📋 Test Objective:**
Verify clicking tiles navigates to correct filtered pages.

### **🔧 Test Steps:**
1. **Click Active Purchase Orders tile**
2. **Verify navigation** to `/home/<USER>/list?status=Active`
3. **Click Revised Purchase Orders tile**
4. **Verify navigation** to `/home/<USER>/list?status=Revised`

### **✅ Expected Results:**
- **URL Changes**: Correct route with query parameters
- **Page Loads**: PO list page opens
- **Filter Applied**: Status filter is applied automatically

## 🧪 Test Case 7: Cache Behavior

### **📋 Test Objective:**
Verify caching works correctly and force button bypasses cache.

### **🔧 Test Steps:**
1. **Load dashboard** (first time - should hit API)
2. **Refresh page** (should use cache if available)
3. **Click force button** (should bypass cache and hit API)

### **✅ Expected Results:**
- **First Load**: Network request visible
- **Page Refresh**: May use cache (faster load)
- **Force Button**: Always makes network request

## 🧪 Test Case 8: Local Development Test Data

### **📋 Test Objective:**
Verify test data appears only in local development environment.

### **🔧 Test Steps:**
1. **Ensure empty database** or no real data
2. **Load dashboard**
3. **Verify test data values appear**

### **✅ Expected Test Data Values:**
```javascript
const expectedTestData = {
  'Pending Gate-Out': 5,
  'Pending Gate Pass': 8,
  'Invoices Without PO': 3,
  'Active Purchase Orders': 8,
  'Revised Purchase Orders': 4,
  'Delayed Delivery POs': 4,
  'Delayed Payment POs': 7
};
```

## 🔧 Automated Test Execution

### **🚀 Run Unit Tests:**
```bash
cd pms-frontend\PmsUI
ng test --watch=false --browsers=ChromeHeadless
```

### **🚀 Run Specific Test Suite:**
```bash
ng test --include="**/GateDashboard.component.spec.ts" --watch=false
```

## 📊 Test Results Template

### **✅ Test Execution Checklist:**

| Test Case | Status | Notes |
|-----------|--------|-------|
| **Initial Dashboard Load** | ⬜ Pass / ⬜ Fail | |
| **Force Button Functionality** | ⬜ Pass / ⬜ Fail | |
| **Date Filter Impact** | ⬜ Pass / ⬜ Fail | |
| **API Response Validation** | ⬜ Pass / ⬜ Fail | |
| **Error Handling** | ⬜ Pass / ⬜ Fail | |
| **Navigation Functionality** | ⬜ Pass / ⬜ Fail | |
| **Cache Behavior** | ⬜ Pass / ⬜ Fail | |
| **Test Data Verification** | ⬜ Pass / ⬜ Fail | |

### **🔍 Issue Tracking:**

| Issue | Description | Severity | Status |
|-------|-------------|----------|--------|
| | | High/Medium/Low | Open/Fixed |

## 🎯 Success Criteria

### **✅ All Tests Pass When:**
1. **All tiles display numeric values** (not undefined/null)
2. **Force button triggers API calls** and updates tiles
3. **Date filters affect PO tiles** appropriately
4. **API returns correct data structure**
5. **Error handling works** gracefully
6. **Navigation works** from tiles
7. **Cache behavior** is correct
8. **Test data appears** in local development

### **🚀 Ready for Production When:**
- All test cases pass consistently
- No console errors during normal operation
- All tiles show real data (not test data) in production
- Performance is acceptable (< 3 seconds load time)
- Error handling covers all edge cases

**These test cases ensure the dashboard works correctly and reliably displays accurate data for all tiles!** 🎉
