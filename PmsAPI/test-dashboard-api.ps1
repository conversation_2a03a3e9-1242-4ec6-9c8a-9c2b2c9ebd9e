# Dashboard API Test Script
# Run this PowerShell script to verify backend API functionality

Write-Host "🧪 DASHBOARD API VERIFICATION SCRIPT" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Configuration
$PmsApiUrl = "http://localhost:7071/api/"
$ReportingApiUrl = "http://localhost:7072/api/"
$DashboardEndpoint = "report/gatedashboard"

# Test data payloads
$AllDataPayload = @{
    DateFilterType = "all"
} | ConvertTo-Json

$TodayPayload = @{
    DateFrom = (Get-Date).ToString("yyyy-MM-ddT00:00:00.000Z")
    DateTo = (Get-Date).ToString("yyyy-MM-ddT23:59:59.999Z")
    DateFilterType = "fullday"
} | ConvertTo-Json

$Last7DaysPayload = @{
    DateFrom = (Get-Date).AddDays(-6).ToString("yyyy-MM-ddT00:00:00.000Z")
    DateTo = (Get-Date).ToString("yyyy-MM-ddT23:59:59.999Z")
    DateFilterType = "fullday"
} | ConvertTo-Json

function Test-ServiceAvailability {
    param($Url, $ServiceName)

    Write-Host "🔍 Testing $ServiceName availability..." -ForegroundColor Yellow

    try {
        $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $ServiceName is running on $Url" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ $ServiceName is not available on $Url" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-DashboardAPI {
    param($Payload, $TestName)

    Write-Host "🧪 Testing Dashboard API: $TestName" -ForegroundColor Yellow

    $fullUrl = $ReportingApiUrl + $DashboardEndpoint

    try {
        $response = Invoke-RestMethod -Uri $fullUrl -Method POST -Body $Payload -ContentType "application/json" -TimeoutSec 30

        Write-Host "✅ API Response received for $TestName" -ForegroundColor Green

        # Verify response structure
        $expectedFields = @(
            "PendingGateOutCount",
            "PendingGatePassCount",
            "InvoicesWithoutPOCount",
            "ActivePOCount",
            "RevisedPOCount",
            "DelayedDeliveryPOCount",
            "DelayedPaymentPOCount",
            "TotalProductsCount",
            "LowStockProductsCount",
            "OutOfStockProductsCount",
            "DelayedDemandsCount",
            "ProductsBelowMinQuantityCount",
            "PendingIssueRequestsCount"
        )

        $missingFields = @()
        foreach ($field in $expectedFields) {
            if (-not $response.PSObject.Properties.Name -contains $field) {
                $missingFields += $field
            }
        }

        if ($missingFields.Count -eq 0) {
            Write-Host "✅ Response structure is valid" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Missing fields in response: $($missingFields -join ', ')" -ForegroundColor Yellow
        }

        # Display tile values
        Write-Host "📊 Tile Values for $TestName" -ForegroundColor Cyan
        Write-Host "   Gate Operations:" -ForegroundColor White
        Write-Host "     Pending Gate-Out: $($response.PendingGateOutCount)" -ForegroundColor White
        Write-Host "     Pending Gate Pass: $($response.PendingGatePassCount)" -ForegroundColor White
        Write-Host "     Invoices Without PO: $($response.InvoicesWithoutPOCount)" -ForegroundColor White
        Write-Host "   Purchase Orders:" -ForegroundColor White
        Write-Host "     Active POs: $($response.ActivePOCount)" -ForegroundColor White
        Write-Host "     Revised POs: $($response.RevisedPOCount)" -ForegroundColor White
        Write-Host "     Delayed Delivery POs: $($response.DelayedDeliveryPOCount)" -ForegroundColor White
        Write-Host "     Delayed Payment POs: $($response.DelayedPaymentPOCount)" -ForegroundColor White
        Write-Host "   Products:" -ForegroundColor White
        Write-Host "     Total Products: $($response.TotalProductsCount)" -ForegroundColor White
        Write-Host "     Low Stock Products: $($response.LowStockProductsCount)" -ForegroundColor White
        Write-Host "     Out of Stock Products: $($response.OutOfStockProductsCount)" -ForegroundColor White
        Write-Host "     Delayed Demands: $($response.DelayedDemandsCount)" -ForegroundColor White
        Write-Host "     Products Below Min Quantity: $($response.ProductsBelowMinQuantityCount)" -ForegroundColor White
        Write-Host "     Pending Issue Requests: $($response.PendingIssueRequestsCount)" -ForegroundColor White

        # Check for test data (local development)
        $isTestData = ($response.PendingGateOutCount -eq 5 -and
                      $response.PendingGatePassCount -eq 8 -and
                      $response.InvoicesWithoutPOCount -eq 3 -and
                      $response.ActivePOCount -eq 8 -and
                      $response.RevisedPOCount -eq 4 -and
                      $response.DelayedDeliveryPOCount -eq 4 -and
                      $response.DelayedPaymentPOCount -eq 7 -and
                      $response.TotalProductsCount -eq 150 -and
                      $response.LowStockProductsCount -eq 12 -and
                      $response.OutOfStockProductsCount -eq 6 -and
                      $response.DelayedDemandsCount -eq 8 -and
                      $response.ProductsBelowMinQuantityCount -eq 15 -and
                      $response.PendingIssueRequestsCount -eq 9)

        if ($isTestData) {
            Write-Host "🧪 Test data detected (local development environment)" -ForegroundColor Magenta
        } else {
            Write-Host "📊 Real data detected" -ForegroundColor Green
        }

        # Check for all zeros (potential issue)
        $allZeros = ($response.PendingGateOutCount -eq 0 -and
                    $response.PendingGatePassCount -eq 0 -and
                    $response.InvoicesWithoutPOCount -eq 0 -and
                    $response.ActivePOCount -eq 0 -and
                    $response.RevisedPOCount -eq 0 -and
                    $response.DelayedDeliveryPOCount -eq 0 -and
                    $response.DelayedPaymentPOCount -eq 0 -and
                    $response.TotalProductsCount -eq 0 -and
                    $response.LowStockProductsCount -eq 0 -and
                    $response.OutOfStockProductsCount -eq 0 -and
                    $response.DelayedDemandsCount -eq 0 -and
                    $response.ProductsBelowMinQuantityCount -eq 0 -and
                    $response.PendingIssueRequestsCount -eq 0)

        if ($allZeros) {
            Write-Host "⚠️  All tile values are zero - check database or date filters" -ForegroundColor Yellow
        }

        return $response
    }
    catch {
        Write-Host "❌ API test failed for $TestName" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Compare-Responses {
    param($Response1, $Response2, $TestName1, $TestName2)

    Write-Host "🔍 Comparing responses: $TestName1 vs $TestName2" -ForegroundColor Yellow

    if ($Response1 -and $Response2) {
        $fields = @("PendingGateOutCount", "PendingGatePassCount", "InvoicesWithoutPOCount",
                   "ActivePOCount", "RevisedPOCount", "DelayedDeliveryPOCount", "DelayedPaymentPOCount",
                   "TotalProductsCount", "LowStockProductsCount", "OutOfStockProductsCount",
                   "DelayedDemandsCount", "ProductsBelowMinQuantityCount", "PendingIssueRequestsCount")

        $differences = @()
        foreach ($field in $fields) {
            $val1 = $Response1.$field
            $val2 = $Response2.$field
            if ($val1 -ne $val2) {
                $differences += "$field: $val1 -> $val2"
            }
        }

        if ($differences.Count -eq 0) {
            Write-Host "✅ Responses are identical" -ForegroundColor Green
        } else {
            Write-Host "📊 Differences found:" -ForegroundColor Cyan
            foreach ($diff in $differences) {
                Write-Host "   $diff" -ForegroundColor White
            }
        }
    }
}

# Main test execution
Write-Host ""
Write-Host "🚀 Starting API verification tests..." -ForegroundColor Cyan
Write-Host ""

# Test 1: Service availability
Write-Host "📋 Test 1: Service Availability" -ForegroundColor Cyan
Write-Host "------------------------------" -ForegroundColor Cyan
$pmsApiAvailable = Test-ServiceAvailability -Url $PmsApiUrl -ServiceName "PmsAPI"
$reportingApiAvailable = Test-ServiceAvailability -Url $ReportingApiUrl -ServiceName "PmsReportingAPI"

if (-not $reportingApiAvailable) {
    Write-Host ""
    Write-Host "❌ CRITICAL: PmsReportingAPI is not running!" -ForegroundColor Red
    Write-Host "💡 Start it with: cd pms-backend-api\PmsAPI\PmsReportingAPI && func start --port 7072" -ForegroundColor Yellow
    Write-Host ""
    exit 1
}

Write-Host ""

# Test 2: Dashboard API with different filters
Write-Host "📋 Test 2: Dashboard API Functionality" -ForegroundColor Cyan
Write-Host "-------------------------------------" -ForegroundColor Cyan

$allDataResponse = Test-DashboardAPI -Payload $AllDataPayload -TestName "All Data"
Write-Host ""

$todayResponse = Test-DashboardAPI -Payload $TodayPayload -TestName "Today Filter"
Write-Host ""

$last7DaysResponse = Test-DashboardAPI -Payload $Last7DaysPayload -TestName "Last 7 Days Filter"
Write-Host ""

# Test 3: Compare responses
Write-Host "📋 Test 3: Date Filter Impact Analysis" -ForegroundColor Cyan
Write-Host "-------------------------------------" -ForegroundColor Cyan

Compare-Responses -Response1 $allDataResponse -Response2 $todayResponse -TestName1 "All Data" -TestName2 "Today"
Write-Host ""

Compare-Responses -Response1 $allDataResponse -Response2 $last7DaysResponse -TestName1 "All Data" -TestName2 "Last 7 Days"
Write-Host ""

# Test 4: Performance test
Write-Host "📋 Test 4: Performance Test" -ForegroundColor Cyan
Write-Host "--------------------------" -ForegroundColor Cyan

$stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
$perfResponse = Test-DashboardAPI -Payload $AllDataPayload -TestName "Performance Test"
$stopwatch.Stop()

$responseTime = $stopwatch.ElapsedMilliseconds
Write-Host "⚡ Response time: $responseTime ms" -ForegroundColor $(if ($responseTime -lt 1000) { "Green" } elseif ($responseTime -lt 3000) { "Yellow" } else { "Red" })

if ($responseTime -lt 1000) {
    Write-Host "✅ Performance: Excellent" -ForegroundColor Green
} elseif ($responseTime -lt 3000) {
    Write-Host "⚠️  Performance: Acceptable" -ForegroundColor Yellow
} else {
    Write-Host "❌ Performance: Poor - Consider optimization" -ForegroundColor Red
}

Write-Host ""

# Summary
Write-Host "📊 TEST SUMMARY" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan

$testsRun = 0
$testsPassed = 0

# Service availability
$testsRun += 2
if ($pmsApiAvailable) { $testsPassed++ }
if ($reportingApiAvailable) { $testsPassed++ }

# API functionality
$testsRun += 3
if ($allDataResponse) { $testsPassed++ }
if ($todayResponse) { $testsPassed++ }
if ($last7DaysResponse) { $testsPassed++ }

# Performance
$testsRun++
if ($responseTime -lt 3000) { $testsPassed++ }

$successRate = [math]::Round(($testsPassed / $testsRun) * 100, 1)

Write-Host "Tests Run: $testsRun" -ForegroundColor White
Write-Host "Tests Passed: $testsPassed" -ForegroundColor White
Write-Host "Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

if ($successRate -ge 80) {
    Write-Host ""
    Write-Host "🎉 DASHBOARD API VERIFICATION PASSED!" -ForegroundColor Green
    Write-Host "✅ All systems are working correctly" -ForegroundColor Green
} elseif ($successRate -ge 60) {
    Write-Host ""
    Write-Host "⚠️  DASHBOARD API VERIFICATION PARTIAL" -ForegroundColor Yellow
    Write-Host "⚠️  Some issues found - review test results" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "❌ DASHBOARD API VERIFICATION FAILED" -ForegroundColor Red
    Write-Host "❌ Major issues found - fix required" -ForegroundColor Red
}

Write-Host ""
Write-Host "💡 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Start frontend: cd pms-frontend\PmsUI && ng serve" -ForegroundColor White
Write-Host "   2. Open dashboard: http://localhost:4200/home/<USER>/gate" -ForegroundColor White
Write-Host "   3. Verify tiles display the values shown above" -ForegroundColor White
Write-Host ""
