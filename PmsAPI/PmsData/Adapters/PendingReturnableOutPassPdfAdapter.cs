using PmsCore.PDFGeneration.Models;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PmsData.Adapters
{
    /// <summary>
    /// PDF adapter for single pending returnable outpass (reminder notification)
    /// </summary>
    public class PendingReturnableOutPassPdfAdapter : IPendingReturnableOutPassPdfData, IPdfDocumentData
    {
        private readonly PendingReturnableOutPassVm _outpass;

        public string DocumentType => "PendingReturnableOutPass";
        public DateTime GeneratedDate { get; set; }

        public long OutpassId => _outpass.OutpassId;
        public string OutpassNumber => _outpass.OutpassNumber;
        public string OutpassTo => _outpass.OutpassTo;
        public DateTime? OutpassDate => _outpass.OutpassDate;
        public DateTime? ExpectedReturnDate => _outpass.ExpectedReturnDate;
        public string Purpose => _outpass.Purpose;
        public string Remark => _outpass.Remark;
        public string AddedBy => _outpass.AddedBy;
        public DateTime? AddedDate => _outpass.AddedDate;
        public int DaysOverdue => _outpass.DaysOverdue;
        public bool IsOverdue => _outpass.IsOverdue;
        public string Status => _outpass.Status;
        public decimal TotalAmount => _outpass.TotalAmount;

        public IEnumerable<IPendingReturnableOutPassItemData> OutpassItems =>
            _outpass.OutpassItems?.Select(x => new PendingReturnableOutPassItemAdapter(x)) ?? new List<PendingReturnableOutPassItemAdapter>();

        public PendingReturnableOutPassPdfAdapter(PendingReturnableOutPassVm outpass, DateTime generatedDate)
        {
            _outpass = outpass ?? throw new ArgumentNullException(nameof(outpass));
            GeneratedDate = generatedDate;
        }
    }

    /// <summary>
    /// Adapter for individual outpass item data
    /// </summary>
    internal class PendingReturnableOutPassItemAdapter : IPendingReturnableOutPassItemData
    {
        private readonly OutpassItemTableVm _item;

        public PendingReturnableOutPassItemAdapter(OutpassItemTableVm item)
        {
            _item = item ?? throw new ArgumentNullException(nameof(item));
        }

        public long OutpassItemId => _item.OutpassItemId;
        public long? ProductId => _item.ProductId;
        public string ProductName => _item.ProductName;
        public decimal? Quantity => _item.Quantity;
        public decimal? Amount => _item.Amount;
        public string Unit => _item.Unit;
        public string RackName => _item.RackName;
        public string StoreName => _item.StoreName;
        public string BatchNo => _item.BatchNo;
        public decimal? ReturnedQuantity => _item.ReturnedQuantity;
    }

    /// <summary>
    /// PDF adapter for pending returnable outpass report (comprehensive report)
    /// </summary>
    public class PendingReturnableOutPassReportPdfAdapter : IPendingReturnableOutPassReportPdfData, IPdfDocumentData
    {
        private readonly PendingReturnableOutPassReportVm _report;

        public string DocumentType => "PendingReturnableOutPassReport";
        public DateTime GeneratedDate => _report.GeneratedDate;

        public IEnumerable<IPendingReturnableOutPassReportItemData> PendingOutPasses =>
            _report.PendingOutPasses?.Select(x => new PendingReturnableOutPassReportItemAdapter(x)) ?? new List<PendingReturnableOutPassReportItemAdapter>();

        public int TotalPendingCount => _report.TotalPendingCount;
        public int OverdueCount => _report.OverdueCount;
        public decimal TotalPendingAmount => _report.TotalPendingAmount;

        public PendingReturnableOutPassReportPdfAdapter(PendingReturnableOutPassReportVm report)
        {
            _report = report ?? throw new ArgumentNullException(nameof(report));
        }
    }

    /// <summary>
    /// Adapter for individual report item data
    /// </summary>
    internal class PendingReturnableOutPassReportItemAdapter : IPendingReturnableOutPassReportItemData
    {
        private readonly PendingReturnableOutPassVm _outpass;

        public PendingReturnableOutPassReportItemAdapter(PendingReturnableOutPassVm outpass)
        {
            _outpass = outpass ?? throw new ArgumentNullException(nameof(outpass));
        }

        public long OutpassId => _outpass.OutpassId;
        public string OutpassNumber => _outpass.OutpassNumber;
        public string OutpassTo => _outpass.OutpassTo;
        public DateTime? OutpassDate => _outpass.OutpassDate;
        public DateTime? ExpectedReturnDate => _outpass.ExpectedReturnDate;
        public string Purpose => _outpass.Purpose;
        public string AddedBy => _outpass.AddedBy;
        public int DaysOverdue => _outpass.DaysOverdue;
        public bool IsOverdue => _outpass.IsOverdue;
        public string Status => _outpass.Status;
        public decimal TotalAmount => _outpass.TotalAmount;
        public int ItemCount => _outpass.OutpassItems?.Count ?? 0;
    }
}
