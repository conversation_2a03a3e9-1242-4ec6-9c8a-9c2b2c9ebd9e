using PmsCore.DataAccessRepository.Models;
using PmsCore.PDFGeneration.Models;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PmsData.Adapters
{
    public class YieldSummaryReportPdfAdapter : IYieldReportSummeryModel, IPdfDocumentData
    {
        private readonly List<YieldReportDto> _allReports;
        public string DocumentType => "YieldReport";
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }

        public YieldSummaryReportPdfAdapter(List<YieldReportVm> reports, DateTime fromDate, DateTime toDate)
        {
            _allReports = reports.Select(r => new YieldReportDto
            {
                SaleOrderNumber = r.SaleOrderNumber,
                CustomerName = r.CustomerName,
                FirstGrade = r.FirstGrade,
                AGrade = r.AGrade,
                FabricName = r.FabricName,
                AddedDate = r.AddedDate,
                SaleOrderId = r.SaleOrderId,
                JumboCount = r.Jumbo<PERSON>ount,
                Yield = r.Yield,
                FirstGradeCount = r.FirstGradeCount,
                AGradeCount = r.AGradeCount,
                CUTPCGrade = r.CUTPCGrade,
                CUTPCGradeCount = r.CUTPCGradeCount,
                FILMGrade = r.FILMGrade,
                FILMGradeCount = r.FILMGradeCount,
                LOTGrade = r.LOTGrade,
                LOTGradeCount = r.LOTGradeCount,
                NSGrade = r.NSGrade,
                NSGradeCount = r.NSGradeCount,
                WASTEGrade = r.WASTEGrade,
                WASTEGradeCount = r.WASTEGradeCount,
                SampleQuantity = r.SampleQuantity,
                SampleCount = r.SampleCount,
                SaleOrderCode = r.SaleOrderCode,
                Amount = r.Amount,
                ActualQuantity = r.ActualQuantity,
                JumboRolQty = r.JumboRolQty,
                WastageEmbossing = r.WastageEmbossing,
                WastageLacquer = r.WastageLacquer,
                WastagePrint = r.WastagePrint,
                WastageTumbling = r.WastageTumbling,
                WastageVacuum = r.WastageVacuum,
                ManufacturingQuantity = r.ManufacturingQuantity,
                SaleOrderQuantity = r.SaleOrderQuantity,
            }).ToList();
            FromDate = fromDate;
            ToDate = toDate;
        }
        public IReadOnlyList<YieldReportDto> AllRecords => _allReports.AsReadOnly();
    }
}