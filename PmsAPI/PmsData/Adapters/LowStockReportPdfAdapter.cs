using PmsCore.PDFGeneration.Models;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PmsData.Adapters
{
    public class LowStockReportPdfAdapter : ILowStockReportPdfData, IPdfDocumentData
    {
        private readonly List<LowStockReportVm> _lowStockItems;
        
        public string DocumentType => "LowStockReport";
        public DateTime GeneratedDate { get; set; }
        public IEnumerable<ILowStockReportItemData> LowStockItems => _lowStockItems.Select(x => new LowStockReportItemAdapter(x));
        public int TotalItemsCount => _lowStockItems?.Count ?? 0;

        public LowStockReportPdfAdapter(List<LowStockReportVm> lowStockItems, DateTime generatedDate)
        {
            _lowStockItems = lowStockItems ?? new List<LowStockReportVm>();
            GeneratedDate = generatedDate;
        }
    }

    public class LowStockReportItemAdapter : ILowStockReportItemData
    {
        private readonly LowStockReportVm _item;

        public LowStockReportItemAdapter(LowStockReportVm item)
        {
            _item = item;
        }

        public string ProductName => _item.ProductName ?? string.Empty;
        public string ProductType => _item.ProductType ?? string.Empty;
        public string ProductCategory => _item.ProductCategory ?? string.Empty;
        public string ProductFirstSubCategory => _item.ProductFirstSubCategory ?? string.Empty;
        public string ProductSecSubCategory => _item.ProductSecSubCategory ?? string.Empty;
        public decimal MinimumLevel => _item.MinimumLevel;
        public decimal TotalAvailableQty => _item.TotalAvailableQty;
        public decimal DomesticQty => _item.DomesticQty;
        public decimal ImportedQty => _item.ImportedQty;
        public string Unit => _item.Unit ?? string.Empty;
    }
}
