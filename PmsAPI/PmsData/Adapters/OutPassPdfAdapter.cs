using System;
using System.Collections.Generic;
using System.Linq;
using PmsCore.PDFGeneration.Models;
using PmsEntity.ViewModel;

namespace PmsData.Adapters
{
    /// <summary>
    /// Adapter to convert OutpassMasterVm to IOutPassPdfData
    /// </summary>
    public class OutPassPdfAdapter : IOutPassPdfData
    {
        private readonly OutpassMasterVm _outpass;

        public OutPassPdfAdapter(OutpassMasterVm outpass)
        {
            _outpass = outpass ?? throw new ArgumentNullException(nameof(outpass));
        }

        public string DocumentType => "OutPass";
        public long OutpassId => _outpass.OutpassId;
        public string OutpassNumber => _outpass.OutpassNumber ?? string.Empty;
        public string OutpassTo => _outpass.OutpassTo ?? string.Empty;
        public DateTime? OutpassDate => _outpass.OutpassDate;
        public string OutpassType => _outpass.OutpassType ?? string.Empty;
        public string Purpose => _outpass.Purpose ?? string.Empty;
        public string AddedBy => _outpass.AddedBy ?? string.Empty;
        public DateTime? AddedDate => _outpass.AddedDate;
        public string Status => _outpass.Status ?? string.Empty;
        public string Remark => _outpass.Remark ?? string.Empty;

        public IEnumerable<IOutPassItemData> OutpassItems =>
            _outpass.OutpassItems?.Select(item => new OutPassItemDataAdapter(item)) ?? Enumerable.Empty<IOutPassItemData>();
    }

    /// <summary>
    /// Adapter to convert OutpassItemTableVm to IOutPassItemData
    /// </summary>
    public class OutPassItemDataAdapter : IOutPassItemData
    {
        private readonly OutpassItemTableVm _item;

        public OutPassItemDataAdapter(OutpassItemTableVm item)
        {
            _item = item ?? throw new ArgumentNullException(nameof(item));
        }

        public long OutpassItemId => _item.OutpassItemId;
        public long? ProductId => _item.ProductId;
        public string ProductName => _item.ProductName ?? string.Empty;
        public decimal? Quantity => _item.Quantity;
        public string Unit => _item.Unit ?? string.Empty;
        public decimal? Amount => _item.Amount;
        public decimal? Total => (_item.Quantity ?? 0) * (_item.Amount ?? 0);
        public string BatchNo => _item.BatchNo ?? string.Empty;
    }
}
