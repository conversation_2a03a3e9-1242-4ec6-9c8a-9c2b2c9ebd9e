using PmsCore.PDFGeneration.Models;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PmsData.Adapters.PdfAdapters
{
    public class PurchaseOrderPdfAdapter : IPurchaseOrderPdfData
    {
        private readonly PurchaseOrderVm _po;
        private readonly string _amountInWords;
        public PurchaseOrderPdfAdapter(PurchaseOrderVm po, string amountInWords)
        {
            _po = po;
            _amountInWords = amountInWords;
        }

        public string DocumentType => "purchaseorder";
        public string Ponumber => _po.Ponumber;
        public DateTime? PocreationDate => _po.PocreationDate;
        public DateTime? DeliveryDate => _po.DeliveryDate;
        public string PaymentTerm => _po.PaymentTerm;
        public string Status => _po.Status;
        public decimal PototalAmount => decimal.Parse(_po.PototalAmount);
        public decimal PototalTax => _po.PototalTax.Value;
        public decimal Pograndtotal => _po.Pograndtotal.Value;
        public string Remarks => _po.Remarks;
        public string SupplierName => _po.SupplierName;
        public string SupplierAddress => _po.Supplier.Address;
        public string SupplierEmail => _po.Supplier.Email;
        public string SupplierContactNumber => _po.Supplier.SupplierContactNumber;
        public string SupplierGSTIN => _po.Supplier.Gst;
        public string DepartmentName => _po.DepartmentName;
        public string TransportName => _po.TransportCompanyName;
        public string DeliveryTerm => _po.DeliveryTerm;
        public string Reference => _po.Reference;
        public string TotalInWords => _po.TotalInWords;
        public string ContactPersonName => _po.UserDetails.Name;
        public string ContactPersonNumber => _po.UserDetails.Contact;
        public string ApprovedByName => _po.ApprovedBy.Name;
        public string AddedByName => _po.AddedBy.Name;
        public string AmountInWords => _amountInWords;
        public IEnumerable<IPurchaseOrderProductData> PurchaseOrderProduct => 
            _po.PurchaseOrderProduct.Select(p => new PurchaseOrderProductAdapter(p));
    }

    internal class PurchaseOrderProductAdapter : IPurchaseOrderProductData
    {
        private readonly PurchaseOrderProductVm _product;

        public PurchaseOrderProductAdapter(PurchaseOrderProductVm product)
        {
            _product = product;
        }

        public string ProductName => _product.ProductName;
        public decimal? Quantity => _product.Quantity;
        public string Unit => _product.Unit;
        public decimal? Rate => _product.Rate;
        public decimal? Amount => _product.Amount;
        public string Igst => _product.Igst;
        public string Currency => _product.Currency;
    }
}