﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class StockMaster
    {
        public StockMaster()
        {
            StockLabelTables = new HashSet<StockLabelTable>();
        }

        public long StockId { get; set; }
        public DateTime StockDate { get; set; }
        public long InvoiceId { get; set; }
        public bool? InspectionCompleted { get; set; }
        public string Batch { get; set; }
        public bool? AllocationCompleted { get; set; }
        public bool? ManageRejectedItemsCompleted { get; set; }
        public bool? IsOpeningStock { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public long? SaleOrderId { get; set; }
        public string InspectionCompletedBy { get; set; }
        public DateTime? InspectionCompletedDate { get; set; }
        public string ProductQuality { get; set; }
        public bool? IsQualityInspectionCompleted { get; set; }
        public long? QualityInspectionCompletedBy { get; set; }
        public DateTime? QualityInspectionCompletedDate { get; set; }

        // Return Stock fields
        public bool? IsReturnStock { get; set; }
        public long? OriginalSaleOrderId { get; set; }
        public long? OriginalDispatchId { get; set; }
        public string ReturnReason { get; set; }
        public DateTime? ReturnDate { get; set; }
        public string ReturnedBy { get; set; }
        public long? CustomerId { get; set; }

        public virtual UserMaster QualityInspectionCompletedByNavigation { get; set; }
        public virtual ICollection<StockLabelTable> StockLabelTables { get; set; }
    }
}
