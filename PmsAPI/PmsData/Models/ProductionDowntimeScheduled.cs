﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class ProductionDowntimeScheduled
    {
        public long ScheduledDowntimeId { get; set; }
        public long ProductionDowntimeReasonId { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string RecurrencePattern { get; set; }
        public string ApplicableDays { get; set; }
        public int? DayOfMonth { get; set; }
        public bool IsRecurring { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public int ProductionLineNo { get; set; }
        public bool? IsActive { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public string ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public bool IsDeleted { get; set; }

        public virtual ProductionDowntimeReasonMaster ProductionDowntimeReason { get; set; }
    }
}
