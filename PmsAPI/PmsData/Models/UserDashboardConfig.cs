﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class UserDashboardConfig
    {
        public long ConfigId { get; set; }
        public string UserId { get; set; }
        public string DashboardType { get; set; }
        public string ConfigJson { get; set; }
        public string ConfigName { get; set; }
        public string Description { get; set; }
        public bool? IsDefault { get; set; }
        public bool? Disabled { get; set; }
        public int? Version { get; set; }
        public string Tags { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public string ModifiedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }
}
