﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class StockProductTable
    {
        public StockProductTable()
        {
            StockLabelTables = new HashSet<StockLabelTable>();
        }

        public long StockProductId { get; set; }
        public long StockId { get; set; }
        public long ProductId { get; set; }
        public string Sku { get; set; }
        public string Barcode { get; set; }
        public decimal? Quantity { get; set; }
        public DateTime? ManufacturedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string Unit { get; set; }
        public decimal? PricePerUnit { get; set; }
        public string Grade { get; set; }
        public decimal? AcceptedQuantity { get; set; }
        public decimal? RejectedQuantity { get; set; }
        public string Comments { get; set; }
        public decimal? ReceivedQuantity { get; set; }
        public long? ThicknessId { get; set; }
        public long? GrainId { get; set; }
        public long? WidthId { get; set; }
        public long? ColorId { get; set; }
        public string PostProcess { get; set; }
        public decimal? FreightPerUnit { get; set; }
        public decimal? MiscPerUnit { get; set; }
        public decimal? InvoicePricePerUnit { get; set; }
        public decimal? ShippingHandlingPerUnit { get; set; }
        public string SupplierProductName { get; set; }
        public long? ProductSupplierMappingId { get; set; }

        public virtual ProductSupplierMapping ProductSupplierMapping { get; set; }
        public virtual ICollection<StockLabelTable> StockLabelTables { get; set; }
    }
}
