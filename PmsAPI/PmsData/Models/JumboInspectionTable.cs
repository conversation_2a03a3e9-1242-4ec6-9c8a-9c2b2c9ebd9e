﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class JumboInspectionTable
    {
        public long JumboInspectionId { get; set; }
        public long? WorkPlanJumboMasterId { get; set; }
        public string Grade { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? Weight { get; set; }
        public string Code { get; set; }
        public string Unit { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? DispatchedQuantity { get; set; }
        public string RollType { get; set; }
        public string DispatchStatus { get; set; }
        public string InspectedBy { get; set; }
        public long? JumboDispatchId { get; set; }
        public long? StockId { get; set; }
    }
}
