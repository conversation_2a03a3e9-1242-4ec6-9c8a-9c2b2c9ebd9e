﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace PmsData.Models
{
    public partial class pmsdbContext : DbContext
    {
        public pmsdbContext()
        {
        }

        public pmsdbContext(DbContextOptions<pmsdbContext> options)
            : base(options)
        {
        }

        public virtual DbSet<AuditTable> AuditTables { get; set; }
        public virtual DbSet<BankMaster> BankMasters { get; set; }
        public virtual DbSet<BranchMaster> BranchMasters { get; set; }
        public virtual DbSet<ColorMaster> ColorMasters { get; set; }
        public virtual DbSet<ConfigTable> ConfigTables { get; set; }
        public virtual DbSet<ConsumeStockProductMaster> ConsumeStockProductMasters { get; set; }
        public virtual DbSet<CustomerMaster> CustomerMasters { get; set; }
        public virtual DbSet<DeliveryTermMaster> DeliveryTermMasters { get; set; }
        public virtual DbSet<DemandTable> DemandTables { get; set; }
        public virtual DbSet<DeptMaster> DeptMasters { get; set; }
        public virtual DbSet<ElementMaster> ElementMasters { get; set; }
        public virtual DbSet<EmailConfigTable> EmailConfigTables { get; set; }
        public virtual DbSet<EmailGroupMappingTable> EmailGroupMappingTables { get; set; }
        public virtual DbSet<EmailGroupTable> EmailGroupTables { get; set; }
        public virtual DbSet<EmailSubscriptionCustomerTable> EmailSubscriptionCustomerTables { get; set; }
        public virtual DbSet<EmailTrackingTable> EmailTrackingTables { get; set; }
        public virtual DbSet<EmbossingMaster> EmbossingMasters { get; set; }
        public virtual DbSet<EstimationCodeMixingRawMaterialTable> EstimationCodeMixingRawMaterialTables { get; set; }
        public virtual DbSet<EstimationFabricProductDetail> EstimationFabricProductDetails { get; set; }
        public virtual DbSet<EstimationFinishingTable> EstimationFinishingTables { get; set; }
        public virtual DbSet<EstimationMixingTable> EstimationMixingTables { get; set; }
        public virtual DbSet<EstimationOrderStatus> EstimationOrderStatuses { get; set; }
        public virtual DbSet<EstimationOrderTable> EstimationOrderTables { get; set; }
        public virtual DbSet<FactoryWorkersMaster> FactoryWorkersMasters { get; set; }
        public virtual DbSet<FileUploadTable> FileUploadTables { get; set; }
        public virtual DbSet<FormulationCodeMixingRawMaterialTable> FormulationCodeMixingRawMaterialTables { get; set; }
        public virtual DbSet<FormulationCodeMixingTable> FormulationCodeMixingTables { get; set; }
        public virtual DbSet<FormulationCodePrefixMaster> FormulationCodePrefixMasters { get; set; }
        public virtual DbSet<GateInInvoiceMappingTable> GateInInvoiceMappingTables { get; set; }
        public virtual DbSet<GateInTable> GateInTables { get; set; }
        public virtual DbSet<GrainMaster> GrainMasters { get; set; }
        public virtual DbSet<Grnmaster> Grnmasters { get; set; }
        public virtual DbSet<IndexUsageStat> IndexUsageStats { get; set; }
        public virtual DbSet<InspectionCancellationTrackingTable> InspectionCancellationTrackingTables { get; set; }
        public virtual DbSet<InspectionFormulationCodeMixingRawMaterialTable> InspectionFormulationCodeMixingRawMaterialTables { get; set; }
        public virtual DbSet<InspectionFormulationCodeMixingTable> InspectionFormulationCodeMixingTables { get; set; }
        public virtual DbSet<InspectionSaleFormulationCodeMaster> InspectionSaleFormulationCodeMasters { get; set; }
        public virtual DbSet<InvoiceMaster> InvoiceMasters { get; set; }
        public virtual DbSet<IssueProductTable> IssueProductTables { get; set; }
        public virtual DbSet<IssueSlipTable> IssueSlipTables { get; set; }
        public virtual DbSet<JumboDispatchTable> JumboDispatchTables { get; set; }
        public virtual DbSet<JumboInspectionTable> JumboInspectionTables { get; set; }
        public virtual DbSet<KnittingFabricWeightInputTable> KnittingFabricWeightInputTables { get; set; }
        public virtual DbSet<LacquerMaster> LacquerMasters { get; set; }
        public virtual DbSet<LacquerRawMaterialTable> LacquerRawMaterialTables { get; set; }
        public virtual DbSet<LinkedSaleOrderTable> LinkedSaleOrderTables { get; set; }
        public virtual DbSet<MbformulationMaster> MbformulationMasters { get; set; }
        public virtual DbSet<MbformulationProductTable> MbformulationProductTables { get; set; }
        public virtual DbSet<MbformulationRawMaterialMaster> MbformulationRawMaterialMasters { get; set; }
        public virtual DbSet<MbformulationRawMaterialProductTable> MbformulationRawMaterialProductTables { get; set; }
        public virtual DbSet<MeasureUnitMaster> MeasureUnitMasters { get; set; }
        public virtual DbSet<MeasurementConversionMaster> MeasurementConversionMasters { get; set; }
        public virtual DbSet<MixingMaster> MixingMasters { get; set; }
        public virtual DbSet<MixingRawMaterialTable> MixingRawMaterialTables { get; set; }
        public virtual DbSet<NotificationGroupsTable> NotificationGroupsTables { get; set; }
        public virtual DbSet<NotificationRateLimitTable> NotificationRateLimitTables { get; set; }
        public virtual DbSet<NotificationReportScheduleMappingTable> NotificationReportScheduleMappingTables { get; set; }
        public virtual DbSet<NotificationSaleOrderStagesTable> NotificationSaleOrderStagesTables { get; set; }
        public virtual DbSet<NotificationTemplateConfigurationTable> NotificationTemplateConfigurationTables { get; set; }
        public virtual DbSet<NotificationTemplateParameterTable> NotificationTemplateParameterTables { get; set; }
        public virtual DbSet<NotificationTrackingTable> NotificationTrackingTables { get; set; }
        public virtual DbSet<OutPassPurposeMaster> OutPassPurposeMasters { get; set; }
        public virtual DbSet<OutpassItemTable> OutpassItemTables { get; set; }
        public virtual DbSet<OutpassMaster> OutpassMasters { get; set; }
        public virtual DbSet<OutpassStatusHistory> OutpassStatusHistories { get; set; }
        public virtual DbSet<OverheadColumnMaster> OverheadColumnMasters { get; set; }
        public virtual DbSet<OverheadCostMappingTable> OverheadCostMappingTables { get; set; }
        public virtual DbSet<OverheadCostMonthlyMappingTable> OverheadCostMonthlyMappingTables { get; set; }
        public virtual DbSet<OverheadCostTable> OverheadCostTables { get; set; }
        public virtual DbSet<OverheadCostingTable> OverheadCostingTables { get; set; }
        public virtual DbSet<PackagingTypeMaster> PackagingTypeMasters { get; set; }
        public virtual DbSet<PaymentTermMaster> PaymentTermMasters { get; set; }
        public virtual DbSet<PostProcessCostingMaster> PostProcessCostingMasters { get; set; }
        public virtual DbSet<PrintMaster> PrintMasters { get; set; }
        public virtual DbSet<ProductCategoryMaster> ProductCategoryMasters { get; set; }
        public virtual DbSet<ProductFirstSubCategoryMaster> ProductFirstSubCategoryMasters { get; set; }
        public virtual DbSet<ProductMaster> ProductMasters { get; set; }
        public virtual DbSet<ProductMasterExtension> ProductMasterExtensions { get; set; }
        public virtual DbSet<ProductSecSubCategoryMaster> ProductSecSubCategoryMasters { get; set; }
        public virtual DbSet<ProductSupplierMapping> ProductSupplierMappings { get; set; }
        public virtual DbSet<ProductTransferTable> ProductTransferTables { get; set; }
        public virtual DbSet<ProductionDowntimeReasonMaster> ProductionDowntimeReasonMasters { get; set; }
        public virtual DbSet<ProductionDowntimeScheduled> ProductionDowntimeScheduleds { get; set; }
        public virtual DbSet<ProductionDowntimeTable> ProductionDowntimeTables { get; set; }
        public virtual DbSet<ProductionElementTable> ProductionElementTables { get; set; }
        public virtual DbSet<ProformaInvoiceItemTable> ProformaInvoiceItemTables { get; set; }
        public virtual DbSet<ProformaInvoiceTable> ProformaInvoiceTables { get; set; }
        public virtual DbSet<PurchaseOrderProductTable> PurchaseOrderProductTables { get; set; }
        public virtual DbSet<PurchaseOrderTable> PurchaseOrderTables { get; set; }
        public virtual DbSet<PurchaseOrderTimelineTable> PurchaseOrderTimelineTables { get; set; }
        public virtual DbSet<RackMaster> RackMasters { get; set; }
        public virtual DbSet<ResponsibilityMaster> ResponsibilityMasters { get; set; }
        public virtual DbSet<SaleFormulationCodeMaster> SaleFormulationCodeMasters { get; set; }
        public virtual DbSet<SaleOrderCostingTable> SaleOrderCostingTables { get; set; }
        public virtual DbSet<SaleOrderDispatchItemsTable> SaleOrderDispatchItemsTables { get; set; }
        public virtual DbSet<SaleOrderPostProcessEmbossingTable> SaleOrderPostProcessEmbossingTables { get; set; }
        public virtual DbSet<SaleOrderPostProcessLacquerTable> SaleOrderPostProcessLacquerTables { get; set; }
        public virtual DbSet<SaleOrderPostProcessOrderTable> SaleOrderPostProcessOrderTables { get; set; }
        public virtual DbSet<SaleOrderPostProcessPrintTable> SaleOrderPostProcessPrintTables { get; set; }
        public virtual DbSet<SaleOrderPostProcessTumblingTable> SaleOrderPostProcessTumblingTables { get; set; }
        public virtual DbSet<SaleOrderPostProcessVacuumTable> SaleOrderPostProcessVacuumTables { get; set; }
        public virtual DbSet<SaleOrderProductionCompleteTable> SaleOrderProductionCompleteTables { get; set; }
        public virtual DbSet<SaleOrderProductionElementTable> SaleOrderProductionElementTables { get; set; }
        public virtual DbSet<SaleOrderProductionEmbossingMaster> SaleOrderProductionEmbossingMasters { get; set; }
        public virtual DbSet<SaleOrderProductionLacquerRawMaterialTable> SaleOrderProductionLacquerRawMaterialTables { get; set; }
        public virtual DbSet<SaleOrderProductionMiscellaneousRawMaterialTable> SaleOrderProductionMiscellaneousRawMaterialTables { get; set; }
        public virtual DbSet<SaleOrderProductionMixingRawMaterialTable> SaleOrderProductionMixingRawMaterialTables { get; set; }
        public virtual DbSet<SaleOrderProductionMixingTable> SaleOrderProductionMixingTables { get; set; }
        public virtual DbSet<SaleOrderProductionPrintMaster> SaleOrderProductionPrintMasters { get; set; }
        public virtual DbSet<SaleOrderProductionRawMaterialTable> SaleOrderProductionRawMaterialTables { get; set; }
        public virtual DbSet<SaleOrderProductionTable> SaleOrderProductionTables { get; set; }
        public virtual DbSet<SaleOrderProductionTumblingMaster> SaleOrderProductionTumblingMasters { get; set; }
        public virtual DbSet<SaleOrderProductionVacuumMaster> SaleOrderProductionVacuumMasters { get; set; }
        public virtual DbSet<SaleOrderTable> SaleOrderTables { get; set; }
        public virtual DbSet<SaleOrderTimelineTable> SaleOrderTimelineTables { get; set; }
        public virtual DbSet<StockLabelMovementHistoryTable> StockLabelMovementHistoryTables { get; set; }
        public virtual DbSet<StockLabelTable> StockLabelTables { get; set; }
        public virtual DbSet<StockLabelTimeline> StockLabelTimelines { get; set; }
        public virtual DbSet<StockMaster> StockMasters { get; set; }
        public virtual DbSet<StockPriceTrackingTable> StockPriceTrackingTables { get; set; }
        public virtual DbSet<StockProductAllocationTable> StockProductAllocationTables { get; set; }
        public virtual DbSet<StockProductManageRejectedTable> StockProductManageRejectedTables { get; set; }
        public virtual DbSet<StockProductRejectedDispatchTable> StockProductRejectedDispatchTables { get; set; }
        public virtual DbSet<StockProductTable> StockProductTables { get; set; }
        public virtual DbSet<StoreMaster> StoreMasters { get; set; }
        public virtual DbSet<SupplierMaster> SupplierMasters { get; set; }
        public virtual DbSet<SystemDashboardDefault> SystemDashboardDefaults { get; set; }
        public virtual DbSet<TagMaster> TagMasters { get; set; }
        public virtual DbSet<ThicknessMaster> ThicknessMasters { get; set; }
        public virtual DbSet<TransportCompanyMaster> TransportCompanyMasters { get; set; }
        public virtual DbSet<TransportVehicleTable> TransportVehicleTables { get; set; }
        public virtual DbSet<TumblingMaster> TumblingMasters { get; set; }
        public virtual DbSet<UserDashboardConfig> UserDashboardConfigs { get; set; }
        public virtual DbSet<UserExceptionForceLogoutTable> UserExceptionForceLogoutTables { get; set; }
        public virtual DbSet<UserMaster> UserMasters { get; set; }
        public virtual DbSet<UserRoleMaster> UserRoleMasters { get; set; }
        public virtual DbSet<UserRoleResponsibilityMappingTable> UserRoleResponsibilityMappingTables { get; set; }
        public virtual DbSet<UserStoreMappingTable> UserStoreMappingTables { get; set; }
        public virtual DbSet<UsernameUserRoleMappingTable> UsernameUserRoleMappingTables { get; set; }
        public virtual DbSet<VacuumMaster> VacuumMasters { get; set; }
        public virtual DbSet<WhatsAppConfigTable> WhatsAppConfigTables { get; set; }
        public virtual DbSet<WhatsAppSubscriptionCustomerTable> WhatsAppSubscriptionCustomerTables { get; set; }
        public virtual DbSet<WhatsAppTemplateMaster> WhatsAppTemplateMasters { get; set; }
        public virtual DbSet<WidthMaster> WidthMasters { get; set; }
        public virtual DbSet<WorkPlanJumboMaster> WorkPlanJumboMasters { get; set; }
        public virtual DbSet<WorkPlanMaster> WorkPlanMasters { get; set; }
        public virtual DbSet<WorkPlanOrder> WorkPlanOrders { get; set; }
        public virtual DbSet<WorkPlanOrderTrackingTable> WorkPlanOrderTrackingTables { get; set; }
        public virtual DbSet<WorkerDesignationMaster> WorkerDesignationMasters { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // Check if running locally (development environment)
                bool isLocalDevelopment = IsLocalDevelopment();

                if (isLocalDevelopment)
                {
                    // Use connection string from configuration file for local development
                    var devConnectionString = PmsCommon.ConfigurationHelper.GetDevConnectionString();

                    if (!string.IsNullOrEmpty(devConnectionString))
                    {
                        optionsBuilder.UseSqlServer(devConnectionString);
                        Console.WriteLine("Using dev connection string from configuration file");
                    }
                    else
                    {
                        // Callback to hard coded connection string if config not found
                        optionsBuilder.UseSqlServer("Server=tcp:pms-mssqlserver-dev.database.windows.net,1433;Initial Catalog=pmsdb;Persist Security Info=False;User ID=pmssqluser;Password=*****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;");
                        Console.WriteLine("Using fallback hardcoded dev connection string");
                    }
                }
                else
                {
                    // Use KeyVault for production/deployed environments
                    var connection = PmsCommon.KeyVault.GetKeyValue("db-connectionstring");
                    optionsBuilder.UseSqlServer(connection);
                }
            }
        }

        /// <summary>
        /// Determines if the application is running in local development environment
        /// </summary>
        private bool IsLocalDevelopment()
        {
            try
            {
                // Check multiple indicators for local development

                // 1. Check environment variable using configuration helper
                if (PmsCommon.ConfigurationHelper.IsDevelopmentEnvironment())
                {
                    return true;
                }

                // 2. Check environment variables directly
                var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ??
                                Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT");
                if (!string.IsNullOrEmpty(environment) && environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine("Running in development environment");
                    return true;
                }
                if (!string.IsNullOrEmpty(environment) && environment.Equals("UAT", StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine("Running in UAT environment");
                    return false;
                }
                if (!string.IsNullOrEmpty(environment) && environment.Equals("Production", StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine("Running in production environment");
                    return false;
                }

                // 3. Check if running in Visual Studio or local development
                var isDebug = System.Diagnostics.Debugger.IsAttached;

                // 4. Check if Azure Functions runtime is not present (local development)
                var azureFunctionsVersion = Environment.GetEnvironmentVariable("FUNCTIONS_EXTENSION_VERSION");
                var isAzureFunctions = !string.IsNullOrEmpty(azureFunctionsVersion);

                // 5. Check machine name patterns (common local development indicators)
                var machineName = Environment.MachineName;
                var isLocalMachine = !machineName.StartsWith("RD", StringComparison.OrdinalIgnoreCase) &&
                                   !machineName.StartsWith("DW", StringComparison.OrdinalIgnoreCase) &&
                                   !machineName.Contains("AZURE", StringComparison.OrdinalIgnoreCase);

                // 6. Check if dev connection string is available in config (indicates local development)
                var hasDevConnectionString = !string.IsNullOrEmpty(PmsCommon.ConfigurationHelper.GetDevConnectionString());

                // 7. Check if KeyVault is accessible (if not, likely local development)
                bool keyVaultAccessible = false;
                try
                {
                    // Try to access KeyVault - if it fails, we're likely in local development
                    var testConnection = PmsCommon.KeyVault.GetKeyValue("db-connectionstring");
                    keyVaultAccessible = !string.IsNullOrEmpty(testConnection);
                }
                catch
                {
                    // KeyVault not accessible, likely local development
                    keyVaultAccessible = false;
                }

                // Return true if any local development indicator is present
                return isDebug || !isAzureFunctions || isLocalMachine || hasDevConnectionString || !keyVaultAccessible;
            }
            catch
            {
                // If any error occurs in detection, default to local development for safety
                return true;
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<AuditTable>(entity =>
            {
                entity.HasKey(e => new { e.RecId, e.TableName, e.EntityName, e.AddedBy, e.AddedDate });

                entity.ToTable("AuditTable");

                entity.Property(e => e.TableName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EntityName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<BankMaster>(entity =>
            {
                entity.HasKey(e => e.BankId);

                entity.ToTable("BankMaster");

                entity.Property(e => e.AccountHolderName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AccountNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AccountType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.BankName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Branch)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Ifsc)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("IFSC");

                entity.Property(e => e.Micr)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("MICR");

                entity.Property(e => e.SwiftCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<BranchMaster>(entity =>
            {
                entity.HasKey(e => e.BranchId);

                entity.ToTable("BranchMaster");

                entity.Property(e => e.BranchAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.BranchAddedDate).HasColumnType("date");

                entity.Property(e => e.BranchCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.BranchDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.BranchName)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ColorMaster>(entity =>
            {
                entity.HasKey(e => e.ColorId);

                entity.ToTable("ColorMaster");

                entity.Property(e => e.ColorAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ColorAddedDate).HasColumnType("date");

                entity.Property(e => e.ColorCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ColorDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ColorName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<ConfigTable>(entity =>
            {
                entity.HasKey(e => e.ConfigId);

                entity.ToTable("ConfigTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ConfigItem)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ConfigValue)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ConsumeStockProductMaster>(entity =>
            {
                entity.HasKey(e => e.ConsumeStockProductId);

                entity.ToTable("ConsumeStockProductMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ConsumedDate).HasColumnType("datetime");

                entity.Property(e => e.MaterialCategory)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Purpose)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Scquantity)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("SCQuantity");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<CustomerMaster>(entity =>
            {
                entity.HasKey(e => e.CustomerId);

                entity.ToTable("CustomerMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Address)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Country)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.CustomerCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CustomerContactNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CustomerName)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.CustomerShortName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Email)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Gstnumber)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("GSTNumber");

                entity.Property(e => e.State)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<DeliveryTermMaster>(entity =>
            {
                entity.HasKey(e => e.DeliveryTermId);

                entity.ToTable("DeliveryTermMaster");

                entity.Property(e => e.DeliveryTerm)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DeliveryTermAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DeliveryTermAddedDate).HasColumnType("date");

                entity.Property(e => e.DeliveryTermDesc)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<DemandTable>(entity =>
            {
                entity.HasKey(e => e.DemandId);

                entity.ToTable("DemandTable");

                entity.Property(e => e.ActionBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ActionDate).HasColumnType("datetime");

                entity.Property(e => e.ActionRemark)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Status)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<DeptMaster>(entity =>
            {
                entity.HasKey(e => e.DeptId);

                entity.ToTable("DeptMaster");

                entity.Property(e => e.DeptAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DeptAddedDate).HasColumnType("date");

                entity.Property(e => e.DeptCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DeptDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DeptName)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ElementMaster>(entity =>
            {
                entity.HasKey(e => e.ElementId);

                entity.ToTable("ElementMaster");

                entity.Property(e => e.ElementAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ElementAddedDate).HasColumnType("date");

                entity.Property(e => e.ElementCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ElementDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ElementName)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<EmailConfigTable>(entity =>
            {
                entity.HasKey(e => e.EmailConfigId);

                entity.ToTable("EmailConfigTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DisabledBy).HasMaxLength(50);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.EmailConfigAccountId)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmailConfigFromEmailDisplayName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmailConfigFromEmailId)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmailConfigName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmailConfigPassword)
                    .HasMaxLength(5000)
                    .IsUnicode(false);

                entity.Property(e => e.EmailConfigPort)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmailConfigSmtp)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EnableSsl)
                    .IsRequired()
                    .HasColumnName("EnableSSL")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.RetryCount).HasDefaultValueSql("((3))");
            });

            modelBuilder.Entity<EmailGroupMappingTable>(entity =>
            {
                entity.HasKey(e => e.EmailGroupMappingId)
                    .HasName("PK_EmailGroupMapping");

                entity.ToTable("EmailGroupMappingTable");

                entity.Property(e => e.AddedBy).HasMaxLength(50);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DisabledBy).HasMaxLength(50);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<EmailGroupTable>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("EmailGroupTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.EmailGroupName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmailId)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Enabled)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('true')");

                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<EmailSubscriptionCustomerTable>(entity =>
            {
                entity.HasKey(e => e.EmailCustomerSubscriptionId);

                entity.ToTable("EmailSubscriptionCustomerTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.LastUpdatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.LastUpdatedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<EmailTrackingTable>(entity =>
            {
                entity.HasKey(e => e.EmailTrackingId);

                entity.ToTable("EmailTrackingTable");

                entity.Property(e => e.BccemailList)
                    .HasMaxLength(1000)
                    .IsUnicode(false)
                    .HasColumnName("BCCEmailList");

                entity.Property(e => e.CcemailList)
                    .HasMaxLength(1000)
                    .IsUnicode(false)
                    .HasColumnName("CCEmailList");

                entity.Property(e => e.EmailInitiatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmailInitiatedDate).HasColumnType("datetime");

                entity.Property(e => e.ModuleName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SesmessageId)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("SESMessageId");

                entity.Property(e => e.Status)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ToEmailList)
                    .HasMaxLength(1000)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<EmbossingMaster>(entity =>
            {
                entity.ToTable("EmbossingMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Code)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.ImageName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Name)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<EstimationCodeMixingRawMaterialTable>(entity =>
            {
                entity.HasKey(e => e.EstimationCodeRawMaterialMixingId);

                entity.ToTable("EstimationCodeMixingRawMaterialTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<EstimationFabricProductDetail>(entity =>
            {
                entity.HasKey(e => e.EstimationFabricProductId);

                entity.ToTable("EstimationFabricProductDetail");

                entity.Property(e => e.AddedBy).IsUnicode(false);

                entity.Property(e => e.AddedDate)
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.FabricEstimationQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FabricGsm).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.FabricProductCostPerLm).HasColumnType("decimal(18, 4)");
            });

            modelBuilder.Entity<EstimationFinishingTable>(entity =>
            {
                entity.HasKey(e => e.EstimationFinishingId);

                entity.ToTable("EstimationFinishingTable");

                entity.Property(e => e.AddedBy).IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("date");

                entity.Property(e => e.EstimationMaterialType).IsUnicode(false);

                entity.Property(e => e.Finishing).IsUnicode(false);

                entity.Property(e => e.FinishingType).IsUnicode(false);

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<EstimationMixingTable>(entity =>
            {
                entity.HasKey(e => e.EstimationCodeMixingId);

                entity.ToTable("EstimationMixingTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.AdhesiveGsm).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.AvgGsm).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FoamGsm).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PreSkinGsm).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.SkinGsm).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<EstimationOrderStatus>(entity =>
            {
                entity.ToTable("EstimationOrderStatus");

                entity.Property(e => e.AddedBy).IsUnicode(false);

                entity.Property(e => e.AddedDate)
                    .HasColumnType("date")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.EstimationOrderStatus1)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("EstimationOrderStatus");

                entity.Property(e => e.Remark).IsUnicode(false);
            });

            modelBuilder.Entity<EstimationOrderTable>(entity =>
            {
                entity.HasKey(e => e.EstimationOrderId);

                entity.ToTable("EstimationOrderTable");

                entity.Property(e => e.AddedBy).IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("date");

                entity.Property(e => e.Disabled).HasDefaultValueSql("((0))");

                entity.Property(e => e.DisabledBy).IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("date");

                entity.Property(e => e.EstimationOrderType).IsUnicode(false);

                entity.Property(e => e.EstimationPrice).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.GrainPrice).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.LineSpeed).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.Lmconstant).HasColumnName("LMConstant");

                entity.Property(e => e.ManufacturingProductName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.OverheadCost).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.ProductionCostLm).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.Rejection).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.Remarks).IsUnicode(false);

                entity.Property(e => e.TotalCostPerLm).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.TotalFinishPrice).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.TotalProfitLoss).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<FactoryWorkersMaster>(entity =>
            {
                entity.HasKey(e => e.WorkerId)
                    .HasName("PK_FactorySupervisorsMaster");

                entity.ToTable("FactoryWorkersMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Disabled).HasDefaultValueSql("((0))");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Name)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ShortName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WorkShift)
                    .HasMaxLength(10)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<FileUploadTable>(entity =>
            {
                entity.HasKey(e => e.FileUploadId);

                entity.ToTable("FileUploadTable");

                entity.Property(e => e.ContainerName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EntityName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.FileName)
                    .IsRequired()
                    .HasMaxLength(150)
                    .IsUnicode(false);

                entity.Property(e => e.FilePath)
                    .IsRequired()
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.UploadedDate).HasColumnType("datetime");

                entity.HasOne(d => d.UploadedByNavigation)
                    .WithMany(p => p.FileUploadTables)
                    .HasForeignKey(d => d.UploadedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FileUploadTable_UserMaster");
            });

            modelBuilder.Entity<FormulationCodeMixingRawMaterialTable>(entity =>
            {
                entity.HasKey(e => e.FormulationCodeMixingRawMaterialId);

                entity.ToTable("FormulationCodeMixingRawMaterialTable");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Scquantity)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("SCQuantity");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<FormulationCodeMixingTable>(entity =>
            {
                entity.HasKey(e => e.FormulationCodeMixingId)
                    .HasName("PK_FormulationCodeMixingMaster");

                entity.ToTable("FormulationCodeMixingTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<FormulationCodePrefixMaster>(entity =>
            {
                entity.HasKey(e => e.FormulationCodePrefixId);

                entity.ToTable("FormulationCodePrefixMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.FormulationPrefix)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.FormulationType)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<GateInInvoiceMappingTable>(entity =>
            {
                entity.ToTable("GateInInvoiceMappingTable");
            });

            modelBuilder.Entity<GateInTable>(entity =>
            {
                entity.HasKey(e => e.GateInId);

                entity.ToTable("GateInTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.GateInDate).HasColumnType("datetime");

                entity.Property(e => e.GateInPerson)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.GateInPersonContact)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.GateOutAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.GateOutDate).HasColumnType("datetime");

                entity.Property(e => e.GatePassAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.GatePassIssueDate).HasColumnType("datetime");

                entity.Property(e => e.InWeight).HasColumnType("decimal(18, 3)");

                entity.Property(e => e.OutWeight).HasColumnType("decimal(18, 3)");

                entity.Property(e => e.Type)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<GrainMaster>(entity =>
            {
                entity.HasKey(e => e.GrainId);

                entity.ToTable("GrainMaster");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.GrainAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.GrainAddedDate).HasColumnType("date");

                entity.Property(e => e.GrainCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.GrainDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.GrainName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<Grnmaster>(entity =>
            {
                entity.HasKey(e => e.Grn)
                    .HasName("PK_GRNMaster_1");

                entity.ToTable("GRNMaster");

                entity.Property(e => e.Grn)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("GRN");

                entity.Property(e => e.AddedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Grnid)
                    .ValueGeneratedOnAdd()
                    .HasColumnName("GRNId");
            });

            modelBuilder.Entity<IndexUsageStat>(entity =>
            {
                entity.Property(e => e.CaptureDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.IndexName).HasMaxLength(128);

                entity.Property(e => e.LastUsed).HasColumnType("datetime");

                entity.Property(e => e.TableName).HasMaxLength(128);
            });

            modelBuilder.Entity<InspectionCancellationTrackingTable>(entity =>
            {
                entity.HasKey(e => e.InspectionCancellationTrackingId)
                    .HasName("PK_InspectionCancellationTracking");

                entity.ToTable("InspectionCancellationTrackingTable");

                entity.Property(e => e.CancelledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CancelledDate).HasColumnType("datetime");

                entity.Property(e => e.Reason)
                    .HasMaxLength(200)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<InspectionFormulationCodeMixingRawMaterialTable>(entity =>
            {
                entity.HasKey(e => e.FormulationCodeMixingRawMaterialId);

                entity.ToTable("InspectionFormulationCodeMixingRawMaterialTable");

                entity.Property(e => e.BaseQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Scquantity)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("SCQuantity");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<InspectionFormulationCodeMixingTable>(entity =>
            {
                entity.HasKey(e => e.FormulationCodeMixingId)
                    .HasName("PK_InspectionFormulationCodeMixingMaster");

                entity.ToTable("InspectionFormulationCodeMixingTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.CostGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("CostGSM");

                entity.Property(e => e.CostPerKg).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.CostPerLm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("CostPerLM");

                entity.Property(e => e.StdPasteRequirementQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.StdPasteRequirementScquantity)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("StdPasteRequirementSCQuantity");
            });

            modelBuilder.Entity<InspectionSaleFormulationCodeMaster>(entity =>
            {
                entity.HasKey(e => e.InspectionSaleFormulationCodeId);

                entity.ToTable("InspectionSaleFormulationCodeMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.AdhesiveGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("AdhesiveGSM");

                entity.Property(e => e.FabricGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FabricGSM");

                entity.Property(e => e.FabricProductQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FabricWidthInMeter).HasColumnType("decimal(18, 4)");

                entity.Property(e => e.FoamGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FoamGSM");

                entity.Property(e => e.InspectionSaleFormulationCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PreSkinGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("PreSkinGSM");

                entity.Property(e => e.SkinGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("SkinGSM");

                entity.Property(e => e.TotalGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("TotalGSM");
            });

            modelBuilder.Entity<InvoiceMaster>(entity =>
            {
                entity.HasKey(e => e.InvoiceId);

                entity.ToTable("InvoiceMaster");

                entity.Property(e => e.Active)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.EwayBill)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.EwayBillDate).HasColumnType("datetime");

                entity.Property(e => e.FreightInsurance).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Grn)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("GRN");

                entity.Property(e => e.InvoiceDate).HasColumnType("datetime");

                entity.Property(e => e.InvoiceFile)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.InvoiceNumber)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.InvoiceTotal).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.InvoiceTotalPrice).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.OtherCharges).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Poid).HasColumnName("POId");

                entity.Property(e => e.ShippingHandling).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");

                entity.HasOne(d => d.DisabledBy)
                    .WithMany(p => p.InvoiceMasterDisabledBies)
                    .HasForeignKey(d => d.DisabledById)
                    .HasConstraintName("FK_InvoiceMaster_UserMaster_DisabledBy");

                entity.HasOne(d => d.ProductSupplierMapping)
                    .WithMany(p => p.InvoiceMasters)
                    .HasForeignKey(d => d.ProductSupplierMappingId)
                    .HasConstraintName("FK_InvoiceMaster_ProductSupplierMapping");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.InvoiceMasterUpdatedBies)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("FK_InvoiceMaster_UserMaster_UpdatedBy");
            });

            modelBuilder.Entity<IssueProductTable>(entity =>
            {
                entity.HasKey(e => e.IssueId);

                entity.ToTable("IssueProductTable");

                entity.Property(e => e.ActionBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ActionDate).HasColumnType("datetime");

                entity.Property(e => e.ApprovalMode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.DemandQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.IssueNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Remark)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RequestType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<IssueSlipTable>(entity =>
            {
                entity.HasKey(e => e.IssueSlipId);

                entity.ToTable("IssueSlipTable");

                entity.Property(e => e.IssueSlipNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RequestedDate).HasColumnType("datetime");

                entity.Property(e => e.Requestedby)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<JumboDispatchTable>(entity =>
            {
                entity.HasKey(e => e.JumboDispatchId);

                entity.ToTable("JumboDispatchTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DispatchDate).HasColumnType("datetime");

                entity.Property(e => e.DispatchNumber)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.DispatchQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PackageId)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PackingNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Remark)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Weight)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<JumboInspectionTable>(entity =>
            {
                entity.HasKey(e => e.JumboInspectionId);

                entity.ToTable("JumboInspectionTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Code)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DispatchStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DispatchedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Grade)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.InspectedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.RollType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Weight).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<KnittingFabricWeightInputTable>(entity =>
            {
                entity.HasKey(e => e.KnittingFabricWeightInputId);

                entity.ToTable("KnittingFabricWeightInputTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.BambooRollWeightInKgs).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Poid).HasColumnName("POId");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Weight).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<LacquerMaster>(entity =>
            {
                entity.ToTable("LacquerMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Code)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Name)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<LacquerRawMaterialTable>(entity =>
            {
                entity.HasKey(e => e.LacquerRawMaterialId);

                entity.ToTable("LacquerRawMaterialTable");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<LinkedSaleOrderTable>(entity =>
            {
                entity.HasKey(e => e.LinkedId);

                entity.ToTable("LinkedSaleOrderTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<MbformulationMaster>(entity =>
            {
                entity.HasKey(e => e.MbformulationId);

                entity.ToTable("MBFormulationMaster");

                entity.Property(e => e.MbformulationId).HasColumnName("MBFormulationId");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.BatchSize).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.MbformulationName)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("MBFormulationName");
            });

            modelBuilder.Entity<MbformulationProductTable>(entity =>
            {
                entity.HasKey(e => e.MbformulationProductId);

                entity.ToTable("MBFormulationProductTable");

                entity.Property(e => e.MbformulationProductId).HasColumnName("MBFormulationProductId");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Batch)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.BatchSize).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.MbformulationId).HasColumnName("MBFormulationId");

                entity.Property(e => e.MbformulationProductName)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("MBFormulationProductName");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<MbformulationRawMaterialMaster>(entity =>
            {
                entity.HasKey(e => e.MbformulationRawMaterialId);

                entity.ToTable("MBFormulationRawMaterialMaster");

                entity.Property(e => e.MbformulationRawMaterialId).HasColumnName("MBFormulationRawMaterialId");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.MbformulationId).HasColumnName("MBFormulationId");

                entity.Property(e => e.QuantityBatchSizeInKg).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.QuantityInKg).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<MbformulationRawMaterialProductTable>(entity =>
            {
                entity.HasKey(e => e.MbformulationRawMaterialProductId);

                entity.ToTable("MBFormulationRawMaterialProductTable");

                entity.Property(e => e.MbformulationRawMaterialProductId).HasColumnName("MBFormulationRawMaterialProductId");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.MbformulationProductId).HasColumnName("MBFormulationProductId");

                entity.Property(e => e.QuantityBatchSizeInKg).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.QuantityInKg).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<MeasureUnitMaster>(entity =>
            {
                entity.ToTable("MeasureUnitMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Unit)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UnitName)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.UnitType)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<MeasurementConversionMaster>(entity =>
            {
                entity.HasKey(e => e.ConversionId);

                entity.ToTable("MeasurementConversionMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ConversionValue).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FromUnit)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ToUnit)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<MixingMaster>(entity =>
            {
                entity.HasKey(e => e.MixingId);

                entity.ToTable("MixingMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.MixingName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Total).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Wastage).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.WastageType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WeightGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("WeightGSM");
            });

            modelBuilder.Entity<MixingRawMaterialTable>(entity =>
            {
                entity.HasKey(e => e.MixingRawMaterialId);

                entity.ToTable("MixingRawMaterialTable");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Scquantity)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("SCQuantity");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<NotificationGroupsTable>(entity =>
            {
                entity.HasKey(e => e.NotificationGroupUserId)
                    .HasName("PK_NotificationGroups");

                entity.ToTable("NotificationGroupsTable");

                entity.HasIndex(e => e.ConfigurationId, "IX_NotificationGroups_ConfigurationId");

                entity.HasIndex(e => new { e.UserType, e.UserMasterId, e.ConfigurationId }, "IX_NotificationGroups_Customer_Config")
                    .HasFilter("([UserType]='Customer')");

                entity.HasIndex(e => new { e.UserType, e.UserMasterId, e.ConfigurationId }, "UQ_NotificationGroups_Customer_Config")
                    .IsUnique()
                    .HasFilter("([UserType]='Customer' AND [Disabled]=(0))");

                entity.Property(e => e.AddedBy).HasMaxLength(50);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.CronScheduleExpression).HasMaxLength(50);

                entity.Property(e => e.DisabledBy).HasMaxLength(50);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Email).HasMaxLength(100);

                entity.Property(e => e.EnableBccemail).HasColumnName("EnableBCCEmail");

                entity.Property(e => e.EnableCcemail).HasColumnName("EnableCCEmail");

                entity.Property(e => e.LastTriggeredBy).HasMaxLength(100);

                entity.Property(e => e.LastTriggeredDate).HasColumnType("datetime");

                entity.Property(e => e.MobileNumber)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.Name).HasMaxLength(50);

                entity.Property(e => e.NotificationFrequency)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.NotificationType).HasMaxLength(50);

                entity.Property(e => e.ReportName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TimeZone)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('UTC')");

                entity.Property(e => e.TriggerType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UseDefaultSchedule).HasDefaultValueSql("((1))");

                entity.Property(e => e.UserType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.Configuration)
                    .WithMany(p => p.NotificationGroupsTables)
                    .HasForeignKey(d => d.ConfigurationId)
                    .HasConstraintName("FK_NotificationGroupsTable_ConfigurationId_NotificationTemplateConfiguration");

                entity.HasOne(d => d.WhatsAppTemplateMaster)
                    .WithMany(p => p.NotificationGroupsTables)
                    .HasForeignKey(d => d.WhatsAppTemplateMasterId)
                    .HasConstraintName("FK_NotificationGroupsTable_WhatsAppTemplateMaster");
            });

            modelBuilder.Entity<NotificationRateLimitTable>(entity =>
            {
                entity.HasKey(e => e.RateLimitId);

                entity.ToTable("NotificationRateLimitTable");

                entity.HasIndex(e => e.RecipientId, "IX_NotificationRateLimit_RecipientId");

                entity.Property(e => e.LastMessageTime).HasColumnType("datetime");

                entity.Property(e => e.LastUpdated).HasColumnType("datetime");

                entity.Property(e => e.NotificationType)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<NotificationReportScheduleMappingTable>(entity =>
            {
                entity.HasKey(e => e.ReportId);

                entity.ToTable("NotificationReportScheduleMappingTable");

                entity.Property(e => e.AddedBy).HasMaxLength(50);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.CronExpression)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.LastRunTime).HasColumnType("datetime");

                entity.Property(e => e.NextRunTime).HasColumnType("datetime");

                entity.Property(e => e.ReportName).HasMaxLength(50);

                entity.Property(e => e.ReportType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TimeZone)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<NotificationSaleOrderStagesTable>(entity =>
            {
                entity.HasKey(e => e.StageId);

                entity.ToTable("NotificationSaleOrderStagesTable");

                entity.Property(e => e.AddedBy).HasMaxLength(50);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DisabledBy).HasMaxLength(50);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.SaleOrderStages).HasMaxLength(50);

                entity.HasOne(d => d.WhatsappTemplate)
                    .WithMany(p => p.NotificationSaleOrderStagesTables)
                    .HasForeignKey(d => d.WhatsappTemplateId)
                    .HasConstraintName("FK_NotificationSaleOrderStagesTable_WhatsAppTemplateMaster");
            });

            modelBuilder.Entity<NotificationTemplateConfigurationTable>(entity =>
            {
                entity.HasKey(e => e.ConfigurationId)
                    .HasName("PK_NotificationTemplateConfiguration");

                entity.ToTable("NotificationTemplateConfigurationTable");

                entity.HasIndex(e => e.AddedById, "IX_NotificationTemplateConfiguration_AddedById");

                entity.HasIndex(e => e.CategoryType, "IX_NotificationTemplateConfiguration_CategoryType");

                entity.HasIndex(e => e.DisabledById, "IX_NotificationTemplateConfiguration_DisabledById");

                entity.HasIndex(e => new { e.Disabled, e.SortOrder }, "IX_NotificationTemplateConfiguration_Disabled_SortOrder");

                entity.HasIndex(e => e.NotificationType, "IX_NotificationTemplateConfiguration_NotificationType");

                entity.HasIndex(e => e.UpdatedById, "IX_NotificationTemplateConfiguration_UpdatedById");

                entity.HasIndex(e => e.WhatsAppTemplateMasterId, "IX_NotificationTemplateConfiguration_WhatsAppTemplateMasterId");

                entity.HasIndex(e => new { e.CategoryType, e.NotificationType, e.SubType }, "UQ_NotificationTemplateConfiguration_Hierarchy")
                    .IsUnique();

                entity.Property(e => e.AddedDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.CategoryType)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Description).HasMaxLength(500);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.DisplayName)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.EventAllowed)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.InternalAllowed)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.NotificationType)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.OnDemandAllowed)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.SortOrder).HasDefaultValueSql("((0))");

                entity.Property(e => e.SubType).HasMaxLength(50);

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");

                entity.HasOne(d => d.AddedBy)
                    .WithMany(p => p.NotificationTemplateConfigurationTableAddedBies)
                    .HasForeignKey(d => d.AddedById)
                    .HasConstraintName("FK_NotificationTemplateConfiguration_AddedById_UserMaster");

                entity.HasOne(d => d.DisabledBy)
                    .WithMany(p => p.NotificationTemplateConfigurationTableDisabledBies)
                    .HasForeignKey(d => d.DisabledById)
                    .HasConstraintName("FK_NotificationTemplateConfiguration_DisabledById_UserMaster");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.NotificationTemplateConfigurationTableUpdatedBies)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("FK_NotificationTemplateConfiguration_UpdatedById_UserMaster");

                entity.HasOne(d => d.WhatsAppTemplateMaster)
                    .WithMany(p => p.NotificationTemplateConfigurationTables)
                    .HasForeignKey(d => d.WhatsAppTemplateMasterId)
                    .HasConstraintName("FK_NotificationTemplateConfiguration_WhatsAppTemplateMasterId_WhatsAppTemplateMaster");
            });

            modelBuilder.Entity<NotificationTemplateParameterTable>(entity =>
            {
                entity.HasKey(e => e.ParameterId);

                entity.ToTable("NotificationTemplateParameterTable");

                entity.Property(e => e.DefaultValue).HasMaxLength(500);

                entity.Property(e => e.IsRequired)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.ParameterName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ParameterType)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ValidationRegex)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.HasOne(d => d.TemplateMaster)
                    .WithMany(p => p.NotificationTemplateParameterTables)
                    .HasForeignKey(d => d.TemplateMasterId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_NotificationTemplateParameter_WhatsAppTemplateMaster");
            });

            modelBuilder.Entity<NotificationTrackingTable>(entity =>
            {
                entity.HasKey(e => e.NotificationTrackingId);

                entity.ToTable("NotificationTrackingTable");

                entity.HasIndex(e => e.NotificationMessageId, "IX_NotificationTrackingTable_NotificationMessageId");

                entity.HasIndex(e => e.RecipientEmail, "IX_NotificationTrackingTable_RecipientEmail");

                entity.HasIndex(e => e.RecipientMobileNumber, "IX_NotificationTrackingTable_RecipientMobileNumber");

                entity.HasIndex(e => e.RecipientId, "IX_NotificationTracking_RecipientId");

                entity.HasIndex(e => e.SentTime, "IX_NotificationTracking_SentTime");

                entity.Property(e => e.AddedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DeliveredTime).HasColumnType("datetime");

                entity.Property(e => e.ErrorMessage).HasMaxLength(500);

                entity.Property(e => e.MessageType)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.NotificationMessageId).HasMaxLength(100);

                entity.Property(e => e.NotificationType)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.ProviderMessageId).HasMaxLength(100);

                entity.Property(e => e.ReadTime).HasColumnType("datetime");

                entity.Property(e => e.RecipientEmail).HasMaxLength(255);

                entity.Property(e => e.RecipientMobileNumber).HasMaxLength(50);

                entity.Property(e => e.SentTime).HasColumnType("datetime");

                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<OutPassPurposeMaster>(entity =>
            {
                entity.HasKey(e => e.PurposeId);

                entity.ToTable("OutPassPurposeMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.PurposeCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PurposeName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<OutpassItemTable>(entity =>
            {
                entity.HasKey(e => e.OutpassItemId)
                    .HasName("PK_OutpostItemTable");

                entity.ToTable("OutpassItemTable");

                entity.HasIndex(e => e.StockLabelId, "IX_OutpassItemTable_StockLabelId");

                entity.Property(e => e.Amount).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ProductName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ReasonForLessQuantity)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ReturnCompletedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ReturnCompletedDate).HasColumnType("datetime");

                entity.Property(e => e.ReturnedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.StockLabel)
                    .WithMany(p => p.OutpassItemTables)
                    .HasForeignKey(d => d.StockLabelId)
                    .HasConstraintName("FK_OutpassItemTable_StockLabelTable");
            });

            modelBuilder.Entity<OutpassMaster>(entity =>
            {
                entity.HasKey(e => e.OutpassId)
                    .HasName("PK_OutpostMaster");

                entity.ToTable("OutpassMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.CreateMode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.ExpectedReturnDate).HasColumnType("datetime");

                entity.Property(e => e.OutpassDate).HasColumnType("datetime");

                entity.Property(e => e.OutpassNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.OutpassTo)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.OutpassType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Purpose)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Remark)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Status)
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<OutpassStatusHistory>(entity =>
            {
                entity.ToTable("OutpassStatusHistory");

                entity.Property(e => e.AddedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Remark)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.HasOne(d => d.Outpass)
                    .WithMany(p => p.OutpassStatusHistories)
                    .HasForeignKey(d => d.OutpassId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_OutpassStatusHistory_OutpassMaster");
            });

            modelBuilder.Entity<OverheadColumnMaster>(entity =>
            {
                entity.HasKey(e => e.OverheadColumnId);

                entity.ToTable("OverheadColumnMaster");

                entity.HasIndex(e => e.OverheadColumnName, "UQ__tmp_ms_x__9A16226AF95F2A85")
                    .IsUnique();

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.OverheadColumnName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.OverheadType)
                    .HasMaxLength(255)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<OverheadCostMappingTable>(entity =>
            {
                entity.HasKey(e => e.OverheadCostMappingId);

                entity.ToTable("OverheadCostMappingTable");

                entity.Property(e => e.AddedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ApplicableOn).HasColumnType("date");

                entity.Property(e => e.AvgLineSpeed).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.AvgOverheadValue).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.OverheadType)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<OverheadCostMonthlyMappingTable>(entity =>
            {
                entity.HasKey(e => e.OverheadCostMonthlyyMappingId);

                entity.ToTable("OverheadCostMonthlyMappingTable");

                entity.Property(e => e.AddedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ApplicableDate).HasColumnType("date");

                entity.Property(e => e.AvgLineSpeed).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.AvgOverheadValue).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.UpdatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<OverheadCostTable>(entity =>
            {
                entity.HasKey(e => e.OverHeadCostId)
                    .HasName("PK_OverHeadCostTable");

                entity.ToTable("OverheadCostTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DivisionName)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.OverheadValue).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.UpdatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<OverheadCostingTable>(entity =>
            {
                entity.HasKey(e => e.OverheadCostingId);

                entity.ToTable("OverheadCostingTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ApplicableOn).HasColumnType("date");

                entity.Property(e => e.OverheadCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.UpdatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<PackagingTypeMaster>(entity =>
            {
                entity.HasKey(e => e.PackagingTypeId);

                entity.ToTable("PackagingTypeMaster");

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.PackagingTypeCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PackagingTypeName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.AddedBy)
                    .WithMany(p => p.PackagingTypeMasterAddedBies)
                    .HasForeignKey(d => d.AddedById)
                    .HasConstraintName("FK_PackagingTypeMaster_UserMaster_AddedBy");

                entity.HasOne(d => d.DisabledBy)
                    .WithMany(p => p.PackagingTypeMasterDisabledBies)
                    .HasForeignKey(d => d.DisabledById)
                    .HasConstraintName("FK_PackagingTypeMaster_UserMaster_DisabledBy");
            });

            modelBuilder.Entity<PaymentTermMaster>(entity =>
            {
                entity.HasKey(e => e.PaymentTermId);

                entity.ToTable("PaymentTermMaster");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.PaymentTerm)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.PaymentTermAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PaymentTermAddedDate).HasColumnType("date");

                entity.Property(e => e.PaymentTermDesc)
                    .HasMaxLength(1000)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<PostProcessCostingMaster>(entity =>
            {
                entity.ToTable("PostProcessCostingMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Cost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PostProcessName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SaleOrderType)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<PrintMaster>(entity =>
            {
                entity.ToTable("PrintMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Code)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.ImageName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Name)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ProductCategoryMaster>(entity =>
            {
                entity.HasKey(e => e.ProductCategoryId);

                entity.ToTable("ProductCategoryMaster");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.ProductCategory)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductCategoryAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductCategoryAddedDate).HasColumnType("date");

                entity.Property(e => e.ProductCategoryDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ProductType)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ProductFirstSubCategoryMaster>(entity =>
            {
                entity.HasKey(e => e.ProductFirstSubCategoryId);

                entity.ToTable("ProductFirstSubCategoryMaster");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.ProductFirstSubCategory)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductFirstSubCategoryAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductFirstSubCategoryAddedDate).HasColumnType("date");

                entity.Property(e => e.ProductFirstSubCategoryDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ProductMaster>(entity =>
            {
                entity.HasKey(e => e.ProductId);

                entity.ToTable("ProductMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.AvgGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("AvgGSM");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.MinimumQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ProductCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductDescription)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.ProductName)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ProductType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WidthInMeter).HasColumnType("decimal(18, 4)");
            });

            modelBuilder.Entity<ProductMasterExtension>(entity =>
            {
                entity.HasKey(e => e.ProductExtId);

                entity.ToTable("ProductMasterExtension");

                entity.Property(e => e.Gender)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Gsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("GSM");

                entity.Property(e => e.Length).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.MarketFor)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductStyle)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Remark)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.Shape)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Width).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<ProductSecSubCategoryMaster>(entity =>
            {
                entity.HasKey(e => e.ProductSecSubCategoryId);

                entity.ToTable("ProductSecSubCategoryMaster");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.ProductSecSubCategory)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductSecSubCategoryAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductSecSubCategoryAddedDate).HasColumnType("date");

                entity.Property(e => e.ProductSecSubCategoryDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ProductSupplierMapping>(entity =>
            {
                entity.ToTable("ProductSupplierMapping");

                entity.HasIndex(e => new { e.SupplierId, e.ProductId }, "IX_ProductSupplierMapping_SupplierId_ProductId");

                entity.HasIndex(e => e.UsageCount, "IX_ProductSupplierMapping_UsageCount");

                entity.Property(e => e.CreatedDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.LastUsedDate)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.SupplierProductName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.UsageCount).HasDefaultValueSql("((1))");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.ProductSupplierMappingCreatedBies)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductSupplierMapping_CreatedById_UserMaster");

                entity.HasOne(d => d.LastUsedBy)
                    .WithMany(p => p.ProductSupplierMappingLastUsedBies)
                    .HasForeignKey(d => d.LastUsedById)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductSupplierMapping_LastUsedById_UserMaster");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductSupplierMappings)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductSupplierMapping_ProductId_ProductMaster");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.ProductSupplierMappings)
                    .HasForeignKey(d => d.SupplierId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductSupplierMapping_SupplierId_SupplierMaster");
            });

            modelBuilder.Entity<ProductTransferTable>(entity =>
            {
                entity.HasKey(e => e.TransferId);

                entity.ToTable("ProductTransferTable");

                entity.Property(e => e.ActionDate).HasColumnType("datetime");

                entity.Property(e => e.ActionRemark)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.RequestReason)
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.Property(e => e.Status)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.ActionBy)
                    .WithMany(p => p.ProductTransferTableActionBies)
                    .HasForeignKey(d => d.ActionById)
                    .HasConstraintName("FK_ProductTransferTable_UserMaster_ActionBy");

                entity.HasOne(d => d.AddedBy)
                    .WithMany(p => p.ProductTransferTableAddedBies)
                    .HasForeignKey(d => d.AddedById)
                    .HasConstraintName("FK_ProductTransferTable_UserMaster_AddedBy");

                entity.HasOne(d => d.FromProduct)
                    .WithMany(p => p.ProductTransferTableFromProducts)
                    .HasForeignKey(d => d.FromProductId)
                    .HasConstraintName("FK_ProductTransferTable_ProductMaster_FromProduct");

                entity.HasOne(d => d.ToProduct)
                    .WithMany(p => p.ProductTransferTableToProducts)
                    .HasForeignKey(d => d.ToProductId)
                    .HasConstraintName("FK_ProductTransferTable_ProductMaster_ToProduct");
            });

            modelBuilder.Entity<ProductionDowntimeReasonMaster>(entity =>
            {
                entity.HasKey(e => e.ProductionDowntimeReasonId)
                    .HasName("PK__Producti__394110618D859783");

                entity.ToTable("ProductionDowntimeReasonMaster");

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedOn)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Description)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DowntimeType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.IsActive).HasDefaultValueSql("((1))");

                entity.Property(e => e.IsDeleted).HasDefaultValueSql("((0))");

                entity.Property(e => e.ModifiedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ModifiedOn).HasColumnType("datetime");

                entity.Property(e => e.ProductionLineType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ReasonCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ReasonName)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.StandardDurationMinutes).HasColumnType("decimal(10, 2)");
            });

            modelBuilder.Entity<ProductionDowntimeScheduled>(entity =>
            {
                entity.HasKey(e => e.ScheduledDowntimeId);

                entity.ToTable("ProductionDowntimeScheduled");

                entity.HasIndex(e => e.ProductionDowntimeReasonId, "IX_ProductionDowntimeScheduled_ProductionDowntimeReasonId");

                entity.HasIndex(e => new { e.ProductionLineNo, e.IsActive }, "IX_ProductionDowntimeScheduled_ProductionLine_Active");

                entity.Property(e => e.ApplicableDays)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedOn)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.EffectiveFrom).HasColumnType("datetime");

                entity.Property(e => e.EffectiveTo).HasColumnType("datetime");

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.ModifiedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ModifiedOn).HasColumnType("datetime");

                entity.Property(e => e.RecurrencePattern)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.HasOne(d => d.ProductionDowntimeReason)
                    .WithMany(p => p.ProductionDowntimeScheduleds)
                    .HasForeignKey(d => d.ProductionDowntimeReasonId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ScheduledDowntime_ProductionDowntimeReasonMaster");
            });

            modelBuilder.Entity<ProductionDowntimeTable>(entity =>
            {
                entity.HasKey(e => e.ProductionDowntimeId)
                    .HasName("PK__Producti__ED25E0A9695CCC2B");

                entity.ToTable("ProductionDowntimeTable");

                entity.Property(e => e.ActualDurationMinutes)
                    .HasColumnType("decimal(10, 2)")
                    .HasComputedColumnSql("(CONVERT([decimal](10,2),datediff(minute,[StartTime],[EndTime])+(datediff(second,[StartTime],[EndTime])%(60))/(60.0)))", true);

                entity.Property(e => e.Comments)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.CreatedOn)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.EndTime).HasColumnType("datetime");

                entity.Property(e => e.ExcessDurationMinutes).HasColumnType("decimal(10, 2)");

                entity.Property(e => e.IsDeleted).HasDefaultValueSql("((0))");

                entity.Property(e => e.ModifiedBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ModifiedOn).HasColumnType("datetime");

                entity.Property(e => e.ProductionLineType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StartTime).HasColumnType("datetime");

                entity.HasOne(d => d.ProductionDowntimeReason)
                    .WithMany(p => p.ProductionDowntimeTables)
                    .HasForeignKey(d => d.ProductionDowntimeReasonId)
                    .HasConstraintName("FK__Productio__Produ__32616E72");
            });

            modelBuilder.Entity<ProductionElementTable>(entity =>
            {
                entity.HasKey(e => e.ProductionElementId);

                entity.ToTable("ProductionElementTable");

                entity.Property(e => e.PerUnitCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TotalCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Value)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ProformaInvoiceItemTable>(entity =>
            {
                entity.HasKey(e => e.ProformaInvoiceItemId);

                entity.ToTable("ProformaInvoiceItemTable");

                entity.Property(e => e.ArticleName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DescriptionOfGoods)
                    .HasMaxLength(150)
                    .IsUnicode(false);

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<ProformaInvoiceTable>(entity =>
            {
                entity.HasKey(e => e.ProformaInvoiceId);

                entity.ToTable("ProformaInvoiceTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.BankAccountNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.BankBranch)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.BankName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.BeneficiaryName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.BuyerReferenceNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ConsignorReference)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CountryOfDestinaton)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CountryOfOrigin)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Currency)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Discount).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FinalDestination)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Gst)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("GST");

                entity.Property(e => e.Gstn)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("GSTN");

                entity.Property(e => e.Hsncode)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("HSNcode");

                entity.Property(e => e.Ifsccode)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("IFSCCode");

                entity.Property(e => e.MaterialType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ModeOfTransport)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Moq)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("MOQ");

                entity.Property(e => e.Moqtotal)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("MOQTotal");

                entity.Property(e => e.PortOfDischarge)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PortOfLoading)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProformaInvoiceDate).HasColumnType("date");

                entity.Property(e => e.ProformaInvoiceNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ReferenceType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SwiftCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TermsCondition)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.TotalPrice).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<PurchaseOrderProductTable>(entity =>
            {
                entity.HasKey(e => e.PoproductId);

                entity.ToTable("PurchaseOrderProductTable");

                entity.Property(e => e.PoproductId).HasColumnName("POProductId");

                entity.Property(e => e.Amount).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Currency)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Grade)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Igst)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("IGST");

                entity.Property(e => e.ImportCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Poid).HasColumnName("POId");

                entity.Property(e => e.ProductQuality)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Rate).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<PurchaseOrderTable>(entity =>
            {
                entity.HasKey(e => e.Poid);

                entity.ToTable("PurchaseOrderTable");

                entity.Property(e => e.Poid).HasColumnName("POId");

                entity.Property(e => e.ActionBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ApprovedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ApprovedDate).HasColumnType("datetime");

                entity.Property(e => e.DeliveryDate).HasColumnType("datetime");

                entity.Property(e => e.Grn)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("GRN");

                entity.Property(e => e.IsPocomplete).HasColumnName("IsPOComplete");

                entity.Property(e => e.PocreationDate)
                    .HasColumnType("datetime")
                    .HasColumnName("POCreationDate");

                entity.Property(e => e.Ponumber)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("PONumber");

                entity.Property(e => e.PototalAmount)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("POTotalAmount");

                entity.Property(e => e.Potype)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("POType");

                entity.Property(e => e.Reference)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Remarks)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Status)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<PurchaseOrderTimelineTable>(entity =>
            {
                entity.HasKey(e => new { e.Poid, e.Status, e.AddedDate });

                entity.ToTable("PurchaseOrderTimelineTable");

                entity.Property(e => e.Status)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Remark)
                    .HasMaxLength(200)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<RackMaster>(entity =>
            {
                entity.HasKey(e => e.RackId);

                entity.ToTable("RackMaster");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.RackAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RackAddedDate).HasColumnType("date");

                entity.Property(e => e.RackCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RackDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.RackName)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ResponsibilityMaster>(entity =>
            {
                entity.HasKey(e => e.ResponsibilityId);

                entity.ToTable("ResponsibilityMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Module)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.ResponsibilityCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ResponsibilityDescripton)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.ResponsibilityName)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SaleFormulationCodeMaster>(entity =>
            {
                entity.HasKey(e => e.SaleFormulationCodeId);

                entity.ToTable("SaleFormulationCodeMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.AdhesiveGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("AdhesiveGSM");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.FabricGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FabricGSM");

                entity.Property(e => e.FabricProductQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FoamGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FoamGSM");

                entity.Property(e => e.MaxSpeed).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.MinSpeed).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PreSkinGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("PreSkinGSM");

                entity.Property(e => e.SaleFormulationCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SkinGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("SkinGSM");

                entity.Property(e => e.TotalGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("TotalGSM");
            });

            modelBuilder.Entity<SaleOrderCostingTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderId)
                    .HasName("PK_SaleOrderCosting");

                entity.ToTable("SaleOrderCostingTable");

                entity.Property(e => e.SaleOrderId).ValueGeneratedNever();

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.CoatingCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.EmbossingCostPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FabricCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FabricCostLm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FabricCost_LM");

                entity.Property(e => e.FinishingCostLm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FinishingCost_LM");

                entity.Property(e => e.GrainCostLm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("GrainCost_LM");

                entity.Property(e => e.InlineScraping).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.LacquerCostPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.MiscellaneousCostPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.OverheadCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PackagingCostPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PasteCostLm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("PasteCost_LM");

                entity.Property(e => e.PerLmconstant)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("PerLMConstant");

                entity.Property(e => e.PrintCostPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ProductionCostLm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("ProductionCost_LM");

                entity.Property(e => e.Rejection).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.RmcostLm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("RMCost_LM");

                entity.Property(e => e.SaleOrderMaterialType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TumblingCostPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.UpdatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");

                entity.Property(e => e.VacuumCostPerUnit).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<SaleOrderDispatchItemsTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderDispatchItemsId);

                entity.ToTable("SaleOrderDispatchItemsTable");

                entity.Property(e => e.PacketNumber).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PacketWeight).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<SaleOrderPostProcessEmbossingTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderPostProcessEmbossingId);

                entity.ToTable("SaleOrderPostProcessEmbossingTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.EmbossingCompletedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.EmbossingMeasurementUnit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmbossingStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmbossingWastageQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.EndDateTime).HasColumnType("datetime");

                entity.Property(e => e.PricePerUnit).HasColumnType("decimal(18, 3)");

                entity.Property(e => e.ReceivedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Remark)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.StartDateTime).HasColumnType("datetime");

                entity.HasOne(d => d.ShiftSupervisorWorker)
                    .WithMany(p => p.SaleOrderPostProcessEmbossingTables)
                    .HasForeignKey(d => d.ShiftSupervisorWorkerId)
                    .HasConstraintName("FK_SaleOrderPostProcessEmbossingTable_FactoryWorkersMaster");
            });

            modelBuilder.Entity<SaleOrderPostProcessLacquerTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderPostProcessLacquerId);

                entity.ToTable("SaleOrderPostProcessLacquerTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.EndDateTime).HasColumnType("datetime");

                entity.Property(e => e.LacquerCompletedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.LacquerMeasurementUnit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.LacquerStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.LacquerWastageQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PricePerUnit).HasColumnType("decimal(18, 3)");

                entity.Property(e => e.ReceivedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Remark)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.StartDateTime).HasColumnType("datetime");

                entity.HasOne(d => d.ShiftSupervisorWorker)
                    .WithMany(p => p.SaleOrderPostProcessLacquerTables)
                    .HasForeignKey(d => d.ShiftSupervisorWorkerId)
                    .HasConstraintName("FK_SaleOrderPostProcessLacquerTable_FactoryWorkersMaster");
            });

            modelBuilder.Entity<SaleOrderPostProcessOrderTable>(entity =>
            {
                entity.HasKey(e => new { e.SaleOrderId, e.Rank, e.PostProcessName });

                entity.ToTable("SaleOrderPostProcessOrderTable");

                entity.Property(e => e.PostProcessName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SaleOrderPostProcessPrintTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderPostProcessPrintId);

                entity.ToTable("SaleOrderPostProcessPrintTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.EndDateTime).HasColumnType("datetime");

                entity.Property(e => e.PricePerUnit).HasColumnType("decimal(18, 3)");

                entity.Property(e => e.PrintCompletedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PrintMeasurementUnit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PrintStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PrintWastageQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ReceivedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Remark)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.StartDateTime).HasColumnType("datetime");

                entity.HasOne(d => d.ShiftSupervisorWorker)
                    .WithMany(p => p.SaleOrderPostProcessPrintTables)
                    .HasForeignKey(d => d.ShiftSupervisorWorkerId)
                    .HasConstraintName("FK_SaleOrderPostProcessPrintTable_FactoryWorkersMaster");
            });

            modelBuilder.Entity<SaleOrderPostProcessTumblingTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderPostProcessTumblingId);

                entity.ToTable("SaleOrderPostProcessTumblingTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.EndDateTime).HasColumnType("datetime");

                entity.Property(e => e.PricePerUnit).HasColumnType("decimal(18, 3)");

                entity.Property(e => e.ReceivedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Remark)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.StartDateTime).HasColumnType("datetime");

                entity.Property(e => e.TumblingCompletedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TumblingMeasurementUnit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TumblingStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TumblingWastageQuantity).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<SaleOrderPostProcessVacuumTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderPostProcessVacuumId);

                entity.ToTable("SaleOrderPostProcessVacuumTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.EndDateTime).HasColumnType("datetime");

                entity.Property(e => e.PricePerUnit).HasColumnType("decimal(18, 3)");

                entity.Property(e => e.ReceivedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Remark)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.StartDateTime).HasColumnType("datetime");

                entity.Property(e => e.VacuumCompletedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.VacuumMeasurementUnit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.VacuumStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.VacuumWastageQuantity).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<SaleOrderProductionCompleteTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderProductionCompleteId);

                entity.ToTable("SaleOrderProductionCompleteTable");

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Addedby)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.AdhesiveActualPasteQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.AdhesiveGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("AdhesiveGSM");

                entity.Property(e => e.AdhesiveRemainingPasteQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.AdhesiveScGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("AdhesiveScGSM");

                entity.Property(e => e.FabricGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FabricGSM");

                entity.Property(e => e.FoamActualPasteQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FoamGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FoamGSM");

                entity.Property(e => e.FoamRemainingPasteQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FoamScGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FoamScGSM");

                entity.Property(e => e.ManufacturedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PreSkinActualPasteQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PreSkinGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("PreSkinGSM");

                entity.Property(e => e.PreSkinRemainingPasteQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PreSkinScGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("PreSkinScGSM");

                entity.Property(e => e.SkinActualPasteQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.SkinGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("SkinGSM");

                entity.Property(e => e.SkinRemainingPasteQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.SkinScGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("SkinScGSM");
            });

            modelBuilder.Entity<SaleOrderProductionElementTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderProductionElementId);

                entity.ToTable("SaleOrderProductionElementTable");

                entity.Property(e => e.PerUnitCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TotalCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Value)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SaleOrderProductionEmbossingMaster>(entity =>
            {
                entity.ToTable("SaleOrderProductionEmbossingMaster");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.RemovedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RemovedDate).HasColumnType("datetime");

                entity.Property(e => e.Total).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<SaleOrderProductionLacquerRawMaterialTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderProductionLacquerRawMaterialId);

                entity.ToTable("SaleOrderProductionLacquerRawMaterialTable");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.RemovedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RemovedDate).HasColumnType("datetime");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SaleOrderProductionMiscellaneousRawMaterialTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderProductionMiscellaneousRawMaterialId);

                entity.ToTable("SaleOrderProductionMiscellaneousRawMaterialTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.MaterialCategory)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PricePerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SaleOrderProductionMixingRawMaterialTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderProductionMixingRawMaterialId);

                entity.ToTable("SaleOrderProductionMixingRawMaterialTable");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SaleOrderProductionMixingTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderProductionMixingId)
                    .HasName("PK_SaleOrderProductionMixingMaster");

                entity.ToTable("SaleOrderProductionMixingTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ProductionMixingName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Total).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Wastage).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.WastageType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WeightGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("WeightGSM");
            });

            modelBuilder.Entity<SaleOrderProductionPrintMaster>(entity =>
            {
                entity.ToTable("SaleOrderProductionPrintMaster");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.RemovedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RemovedDate).HasColumnType("datetime");

                entity.Property(e => e.Total).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<SaleOrderProductionRawMaterialTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderProductionRawMaterialId);

                entity.ToTable("SaleOrderProductionRawMaterialTable");

                entity.Property(e => e.PerUnitCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TotalCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SaleOrderProductionTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderProductionId);

                entity.ToTable("SaleOrderProductionTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.AdhesiveGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("AdhesiveGSM");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Batch).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ColorPrice).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.CostingStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ExtraProduction).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.FabricGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FabricGSM");

                entity.Property(e => e.FoamGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("FoamGSM");

                entity.Property(e => e.GrainPrice).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Lmconstant)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("LMConstant");

                entity.Property(e => e.Lot).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ManufacturingProductCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ManufacturingProductName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ManufacturingQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.MixingFormulationCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.OrderQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PreSkinGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("PreSkinGSM");

                entity.Property(e => e.ProcessFormulationCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductionStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SalePrice).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.SkinGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("SkinGSM");

                entity.Property(e => e.SlippagePercent).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Thick).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ThickPrice).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TotalCost).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.TotalGsm)
                    .HasColumnType("decimal(18, 2)")
                    .HasColumnName("TotalGSM");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Width).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.WidthPrice).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<SaleOrderProductionTumblingMaster>(entity =>
            {
                entity.ToTable("SaleOrderProductionTumblingMaster");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.RemovedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RemovedDate).HasColumnType("datetime");

                entity.Property(e => e.Total).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<SaleOrderProductionVacuumMaster>(entity =>
            {
                entity.ToTable("SaleOrderProductionVacuumMaster");

                entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.RemovedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RemovedDate).HasColumnType("datetime");

                entity.Property(e => e.Total).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<SaleOrderTable>(entity =>
            {
                entity.HasKey(e => e.SaleOrderId);

                entity.ToTable("SaleOrderTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ApprovedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ApprovedDate).HasColumnType("datetime");

                entity.Property(e => e.Bornumber)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("BORNumber");

                entity.Property(e => e.DeliveryDate).HasColumnType("datetime");

                entity.Property(e => e.FinishCode)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.HoldBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.HoldDate).HasColumnType("datetime");

                entity.Property(e => e.ProductionCompletionRemarks)
                    .HasMaxLength(5000)
                    .IsUnicode(false);

                entity.Property(e => e.Remarks)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.SaleOrderCode)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.SaleOrderDate).HasColumnType("datetime");

                entity.Property(e => e.SaleOrderNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SaleOrderStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SaleOrderType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedBy)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<SaleOrderTimelineTable>(entity =>
            {
                entity.HasKey(e => new { e.SaleOrderId, e.Status });

                entity.ToTable("SaleOrderTimelineTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<StockLabelMovementHistoryTable>(entity =>
            {
                entity.HasKey(e => e.LabelMovementId);

                entity.ToTable("StockLabelMovementHistoryTable");

                entity.Property(e => e.MovedBy)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.MovementDate).HasColumnType("datetime");

                entity.Property(e => e.Reason)
                    .IsRequired()
                    .HasMaxLength(250)
                    .IsUnicode(false);

                entity.HasOne(d => d.Issue)
                    .WithMany(p => p.StockLabelMovementHistoryTables)
                    .HasForeignKey(d => d.IssueId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockLabelMovementHistoryTable_IssueProductTable");

                entity.HasOne(d => d.StockLabel)
                    .WithMany(p => p.StockLabelMovementHistoryTables)
                    .HasForeignKey(d => d.StockLabelId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockLabelMovementHistoryTable_StockLabelTable");
            });

            modelBuilder.Entity<StockLabelTable>(entity =>
            {
                entity.HasKey(e => e.StockLabelId);

                entity.ToTable("StockLabelTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ExpiryDate).HasColumnType("date");

                entity.Property(e => e.Grade)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.InspectionStatus)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.IsActive).HasDefaultValueSql("((0))");

                entity.Property(e => e.LabelStatus)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.MfgDate).HasColumnType("date");

                entity.Property(e => e.PackagingUnit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.SerialNo)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ShortCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");

                entity.HasOne(d => d.CurrentRack)
                    .WithMany(p => p.StockLabelTables)
                    .HasForeignKey(d => d.CurrentRackId)
                    .HasConstraintName("FK_StockLabelTable_RackMaster");

                entity.HasOne(d => d.CurrentStore)
                    .WithMany(p => p.StockLabelTables)
                    .HasForeignKey(d => d.CurrentStoreId)
                    .HasConstraintName("FK_StockLabelTable_StoreMaster");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.StockLabelTables)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockLabelTable_ProductMaster");

                entity.HasOne(d => d.Stock)
                    .WithMany(p => p.StockLabelTables)
                    .HasForeignKey(d => d.StockId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockLabelTable_StockMaster");

                entity.HasOne(d => d.StockProduct)
                    .WithMany(p => p.StockLabelTables)
                    .HasForeignKey(d => d.StockProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockLabelTable_StockProductTable");
            });

            modelBuilder.Entity<StockLabelTimeline>(entity =>
            {
                entity.HasKey(e => e.LabelTimelineId);

                entity.ToTable("StockLabelTimeline");

                entity.Property(e => e.ChangeDate).HasColumnType("datetime");

                entity.Property(e => e.ChangedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.NewStatus)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.OldStatus)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.RelatedEntityType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Remark)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.HasOne(d => d.StockLabel)
                    .WithMany(p => p.StockLabelTimelines)
                    .HasForeignKey(d => d.StockLabelId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockLabelTimeline_StockLabelTable");
            });

            modelBuilder.Entity<StockMaster>(entity =>
            {
                entity.HasKey(e => e.StockId);

                entity.ToTable("StockMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Batch)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.InspectionCompletedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.InspectionCompletedDate).HasColumnType("datetime");

                entity.Property(e => e.IsQualityInspectionCompleted).HasDefaultValueSql("((0))");

                entity.Property(e => e.ProductQuality)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.QualityInspectionCompletedDate).HasColumnType("datetime");

                entity.Property(e => e.StockDate).HasColumnType("datetime");

                entity.HasOne(d => d.QualityInspectionCompletedByNavigation)
                    .WithMany(p => p.StockMasters)
                    .HasForeignKey(d => d.QualityInspectionCompletedBy)
                    .HasConstraintName("FK_StockMaster_UserMaster");
            });

            modelBuilder.Entity<StockPriceTrackingTable>(entity =>
            {
                entity.HasKey(e => e.StockPriceTrackingId);

                entity.ToTable("StockPriceTrackingTable");

                entity.Property(e => e.FreightPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.InvoicePricePerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.MiscPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.NewFreightPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.NewInvoicePricePerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.NewMiscPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.NewPricePerUnit).HasColumnType("decimal(10, 2)");

                entity.Property(e => e.NewShippingHandlingPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PricePerUnit).HasColumnType("decimal(10, 2)");

                entity.Property(e => e.ShippingHandlingPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.UpdatedDate).HasColumnType("datetime");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.StockPriceTrackingTables)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("FK_StockPriceTrackingTable_UserMaster");
            });

            modelBuilder.Entity<StockProductAllocationTable>(entity =>
            {
                entity.HasKey(e => e.AllocationId)
                    .HasName("PK_StockProductAllocation");

                entity.ToTable("StockProductAllocationTable");

                entity.Property(e => e.InspectionType)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<StockProductManageRejectedTable>(entity =>
            {
                entity.ToTable("StockProductManageRejectedTable");

                entity.Property(e => e.ItemAction)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<StockProductRejectedDispatchTable>(entity =>
            {
                entity.ToTable("StockProductRejectedDispatchTable");

                entity.Property(e => e.DispatchDate).HasColumnType("datetime");

                entity.Property(e => e.DispatchId)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<StockProductTable>(entity =>
            {
                entity.HasKey(e => e.StockProductId);

                entity.ToTable("StockProductTable");

                entity.Property(e => e.AcceptedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Barcode)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Comments)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.ExpiryDate).HasColumnType("datetime");

                entity.Property(e => e.Grade)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.InvoicePricePerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ManufacturedDate).HasColumnType("datetime");

                entity.Property(e => e.MiscPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.PostProcess)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.PricePerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Quantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ReceivedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.RejectedQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.ShippingHandlingPerUnit).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Sku)
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("SKU");

                entity.Property(e => e.SupplierProductName).HasMaxLength(500);

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.ProductSupplierMapping)
                    .WithMany(p => p.StockProductTables)
                    .HasForeignKey(d => d.ProductSupplierMappingId)
                    .HasConstraintName("FK_StockProductTable_ProductSupplierMapping");
            });

            modelBuilder.Entity<StoreMaster>(entity =>
            {
                entity.HasKey(e => e.StoreId);

                entity.ToTable("StoreMaster");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.IsWorkInProgressStore).HasDefaultValueSql("((0))");

                entity.Property(e => e.StoreAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StoreAddedDate).HasColumnType("date");

                entity.Property(e => e.StoreCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StoreDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SupplierMaster>(entity =>
            {
                entity.HasKey(e => e.SupplierId);

                entity.ToTable("SupplierMaster");

                entity.Property(e => e.Address)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ContactPersonName)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ContactPersonNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Email)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Gst)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("GST");

                entity.Property(e => e.SupplierContactNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SupplierName)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<SystemDashboardDefault>(entity =>
            {
                entity.HasKey(e => e.DefaultId)
                    .HasName("PK__SystemDa__49F77F7B7B5FAE2A");

                entity.Property(e => e.ConfigJson).IsRequired();

                entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DashboardType)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Disabled).HasDefaultValueSql("((0))");

                entity.Property(e => e.Version).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<TagMaster>(entity =>
            {
                entity.HasKey(e => e.TagId);

                entity.ToTable("TagMaster");

                entity.Property(e => e.TagAddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TagAddedDate).HasColumnType("date");

                entity.Property(e => e.TagDesc)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.TagName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ThicknessMaster>(entity =>
            {
                entity.HasKey(e => e.ThicknessId);

                entity.ToTable("ThicknessMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Code)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.ThicknessNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TransportCompanyMaster>(entity =>
            {
                entity.HasKey(e => e.TransportId);

                entity.ToTable("TransportCompanyMaster");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Gst)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TransportCompanyAddress)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.TransportCompanyContact)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TransportCompanyEmail)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TransportCompanyName)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TransportVehicleTable>(entity =>
            {
                entity.HasKey(e => e.VehicleId);

                entity.ToTable("TransportVehicleTable");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.VehicleNumber)
                    .IsRequired()
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.VehicleType)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TumblingMaster>(entity =>
            {
                entity.ToTable("TumblingMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Code)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Name)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<UserDashboardConfig>(entity =>
            {
                entity.HasKey(e => e.ConfigId)
                    .HasName("PK__UserDash__C3BC335CAEC79AAF");

                entity.ToTable("UserDashboardConfig");

                entity.HasIndex(e => new { e.UserId, e.DashboardType, e.IsDefault }, "IX_UserDashboardConfig_Default")
                    .HasFilter("([IsDefault]=(1))");

                entity.HasIndex(e => new { e.UserId, e.DashboardType }, "IX_UserDashboardConfig_User_Type");

                entity.Property(e => e.AddedBy).HasMaxLength(100);

                entity.Property(e => e.AddedDate).HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ConfigJson).IsRequired();

                entity.Property(e => e.ConfigName).HasMaxLength(200);

                entity.Property(e => e.DashboardType)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Description).HasMaxLength(500);

                entity.Property(e => e.Disabled).HasDefaultValueSql("((0))");

                entity.Property(e => e.IsDefault).HasDefaultValueSql("((0))");

                entity.Property(e => e.ModifiedBy).HasMaxLength(100);

                entity.Property(e => e.ModifiedDate).HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Tags).HasMaxLength(200);

                entity.Property(e => e.UserId)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.Version).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<UserExceptionForceLogoutTable>(entity =>
            {
                entity.HasKey(e => e.UserExceptionId);

                entity.ToTable("UserExceptionForceLogoutTable");

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.HasOne(d => d.AddedBy)
                    .WithMany(p => p.UserExceptionForceLogoutTableAddedBies)
                    .HasForeignKey(d => d.AddedById)
                    .HasConstraintName("FK_UserExceptionForceLogoutTable_UserMaster_AddedBy");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserExceptionForceLogoutTableUsers)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_UserExceptionForceLogoutTable_UserMaster");
            });

            modelBuilder.Entity<UserMaster>(entity =>
            {
                entity.HasKey(e => e.UserId);

                entity.ToTable("UserMaster");

                entity.Property(e => e.Address)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.AdobjectId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("ADObjectId");

                entity.Property(e => e.Contact)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.EmailAddress)
                    .HasMaxLength(70)
                    .IsUnicode(false);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Status)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<UserRoleMaster>(entity =>
            {
                entity.HasKey(e => e.UserRoleId);

                entity.ToTable("UserRoleMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.UserRoleCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UserRoleDescripton)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.UserRoleName)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<UserRoleResponsibilityMappingTable>(entity =>
            {
                entity.HasKey(e => e.UserRoleResponsibilityMappingId)
                    .HasName("PK_UserRoleResponsibilityMapping");

                entity.ToTable("UserRoleResponsibilityMappingTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<UserStoreMappingTable>(entity =>
            {
                entity.HasKey(e => e.UserStoreId);

                entity.ToTable("UserStoreMappingTable");

                entity.Property(e => e.Username)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<UsernameUserRoleMappingTable>(entity =>
            {
                entity.HasKey(e => e.UsernameUserRoleMappingId)
                    .HasName("PK_UsernameUserRoleMapping");

                entity.ToTable("UsernameUserRoleMappingTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Username)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<VacuumMaster>(entity =>
            {
                entity.ToTable("VacuumMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Code)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Description)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Name)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<WhatsAppConfigTable>(entity =>
            {
                entity.HasKey(e => e.WhatsAppConfigId);

                entity.ToTable("WhatsAppConfigTable");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ApiEndpoint)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ConfigName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.ProviderKey)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ProviderName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RegisteredSenderNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WebhookUrl)
                    .HasMaxLength(500)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<WhatsAppSubscriptionCustomerTable>(entity =>
            {
                entity.HasKey(e => e.WhatsAppCustomerSubscriptionId);

                entity.ToTable("WhatsAppSubscriptionCustomerTable");

                entity.HasIndex(e => new { e.CustomerId, e.Enabled }, "IX_WhatsAppSubscriptionCustomerTables_Composite");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.LastUpdatedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.LastUpdatedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<WhatsAppTemplateMaster>(entity =>
            {
                entity.ToTable("WhatsAppTemplateMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.ApprovalStatus)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Category)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Language)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('en')");

                entity.Property(e => e.ProviderName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProviderTemplateDescription)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ProviderTemplateName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<WidthMaster>(entity =>
            {
                entity.HasKey(e => e.WidthId);

                entity.ToTable("WidthMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Description)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Unit)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WidthNumber)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<WorkPlanJumboMaster>(entity =>
            {
                entity.ToTable("WorkPlanJumboMaster");

                entity.Property(e => e.ActualQuantity).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.Amount).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.JumboNo)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.JumboRolQty).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.JumboRollDate).HasColumnType("datetime");

                entity.Property(e => e.JumboRollEndTime).HasColumnType("datetime");

                entity.Property(e => e.JumboRollStartTime).HasColumnType("datetime");

                entity.Property(e => e.RackCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RackName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Rate).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Remark)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.StoreCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StoreName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WastageEmbossing).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.WastageLacquer).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.WastagePrint).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.WastageTumbling).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.WastageVacuum).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Weight).HasColumnType("decimal(18, 2)");

                entity.Property(e => e.Yield).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.Entity<WorkPlanMaster>(entity =>
            {
                entity.HasKey(e => e.WorkPlanId);

                entity.ToTable("WorkPlanMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.BatchNo)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.LotNo)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ProductionDetails)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ReviewedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ReviewedDate).HasColumnType("datetime");

                entity.Property(e => e.WorkPlanDate).HasColumnType("datetime");

                entity.Property(e => e.WorkPlanNo)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WorkShift)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<WorkPlanOrder>(entity =>
            {
                entity.HasKey(e => e.WorkPlanOrdersId)
                    .HasName("PK_WorkPlanOrders");

                entity.ToTable("WorkPlanOrder");

            });

            modelBuilder.Entity<WorkPlanOrderTrackingTable>(entity =>
            {
                entity.HasKey(e => e.WorkPlanTrackingId);

                entity.ToTable("WorkPlanOrderTrackingTable");

                entity.Property(e => e.ChangedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ChangedDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<WorkerDesignationMaster>(entity =>
            {
                entity.HasKey(e => e.DesignationId);

                entity.ToTable("WorkerDesignationMaster");

                entity.Property(e => e.AddedBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AddedDate).HasColumnType("datetime");

                entity.Property(e => e.DisabledBy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DisabledDate).HasColumnType("datetime");

                entity.Property(e => e.Name)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ShortName)
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
