﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class EstimationOrderTable
    {
        public long EstimationOrderId { get; set; }
        public long? ProductCategoryId { get; set; }
        public string ManufacturingProductName { get; set; }
        public long? OrderQuantity { get; set; }
        public long? Lmconstant { get; set; }
        public long? ExtraProduction { get; set; }
        public long? ManufacturingQuantity { get; set; }
        public long? ColorId { get; set; }
        public long? GrainId { get; set; }
        public decimal? GrainPrice { get; set; }
        public long? Thick { get; set; }
        public long? Width { get; set; }
        public decimal? EstimationPrice { get; set; }
        public decimal? ProductionCostLm { get; set; }
        public decimal? TotalFinishPrice { get; set; }
        public decimal? OverheadCost { get; set; }
        public decimal? Rejection { get; set; }
        public decimal? TotalCostPerLm { get; set; }
        public decimal? LineSpeed { get; set; }
        public decimal? TotalProfitLoss { get; set; }
        public long? CustomerId { get; set; }
        public string Remarks { get; set; }
        public bool? Disabled { get; set; }
        public string AddedBy { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public DateTime? DisabledDate { get; set; }
        public string EstimationOrderType { get; set; }
        public long? EstimationFormulationCodeId { get; set; }
    }
}
