﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderProductionCompleteTable
    {
        public long SaleOrderProductionCompleteId { get; set; }
        public long SaleOrderId { get; set; }
        public decimal? ManufacturedQuantity { get; set; }
        public decimal? PreSkinGsm { get; set; }
        public decimal? SkinGsm { get; set; }
        public decimal? FoamGsm { get; set; }
        public decimal? AdhesiveGsm { get; set; }
        public decimal? FabricGsm { get; set; }
        public decimal? PreSkinScGsm { get; set; }
        public decimal? SkinScGsm { get; set; }
        public decimal? FoamScGsm { get; set; }
        public decimal? AdhesiveScGsm { get; set; }
        public decimal? PreSkinRemainingPasteQty { get; set; }
        public decimal? PreSkinActualPasteQty { get; set; }
        public decimal? SkinRemainingPasteQty { get; set; }
        public decimal? SkinActualPasteQty { get; set; }
        public decimal? FoamRemainingPasteQty { get; set; }
        public decimal? FoamActualPasteQty { get; set; }
        public decimal? AdhesiveRemainingPasteQty { get; set; }
        public decimal? AdhesiveActualPasteQty { get; set; }
        public string Addedby { get; set; }
        public DateTime? AddedDate { get; set; }
    }
}
