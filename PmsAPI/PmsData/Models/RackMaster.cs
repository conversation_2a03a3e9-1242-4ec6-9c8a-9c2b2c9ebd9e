﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class RackMaster
    {
        public RackMaster()
        {
            StockLabelTables = new HashSet<StockLabelTable>();
        }

        public long RackId { get; set; }
        public long? StoreId { get; set; }
        public string RackName { get; set; }
        public string RackCode { get; set; }
        public string RackDesc { get; set; }
        public DateTime? RackAddedDate { get; set; }
        public string RackAddedBy { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }

        public virtual ICollection<StockLabelTable> StockLabelTables { get; set; }
    }
}
