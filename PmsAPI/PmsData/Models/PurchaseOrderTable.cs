﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class PurchaseOrderTable
    {
        public long Poid { get; set; }
        public string Ponumber { get; set; }
        public long? SupplierId { get; set; }
        public long? DeliveryTermId { get; set; }
        public long? PaymentTermId { get; set; }
        public string Reference { get; set; }
        public long? TransportId { get; set; }
        public string PototalAmount { get; set; }
        public DateTime? PocreationDate { get; set; }
        public DateTime? AddedDate { get; set; }
        public string AddedBy { get; set; }
        public string Grn { get; set; }
        public bool? IsPocomplete { get; set; }
        public long? ContactPersonUserId { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string Status { get; set; }
        public string Remarks { get; set; }
        public string ActionBy { get; set; }
        public string ApprovedBy { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public long? DepartmentId { get; set; }
        public string Potype { get; set; }
    }
}
