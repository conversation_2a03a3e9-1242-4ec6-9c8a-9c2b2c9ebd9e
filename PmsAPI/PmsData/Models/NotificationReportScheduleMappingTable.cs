﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class NotificationReportScheduleMappingTable
    {
        public long ReportId { get; set; }
        public string ReportType { get; set; }
        public string ReportName { get; set; }
        public long? NotificationGroupUserId { get; set; }
        public long? TemplateMasterId { get; set; }
        public string CronExpression { get; set; }
        public DateTime? LastRunTime { get; set; }
        public DateTime? NextRunTime { get; set; }
        public bool IsActive { get; set; }
        public string TimeZone { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool Disabled { get; set; }
        public long? DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
    }
}
