﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class OverheadCostMappingTable
    {
        public long OverheadCostMappingId { get; set; }
        public long OverheadCostId { get; set; }
        public long OverheadColumnId { get; set; }
        public string OverheadType { get; set; }
        public decimal AvgLineSpeed { get; set; }
        public decimal AvgOverheadValue { get; set; }
        public DateTime ApplicableOn { get; set; }
        public string AddedBy { get; set; }
        public DateTime AddedDate { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
