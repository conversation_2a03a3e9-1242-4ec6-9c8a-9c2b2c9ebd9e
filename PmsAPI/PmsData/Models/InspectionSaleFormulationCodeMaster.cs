﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class InspectionSaleFormulationCodeMaster
    {
        public long InspectionSaleFormulationCodeId { get; set; }
        public string InspectionSaleFormulationCode { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public long? CategoryId { get; set; }
        public long? ThicknessId { get; set; }
        public decimal? PreSkinGsm { get; set; }
        public decimal? SkinGsm { get; set; }
        public decimal? FoamGsm { get; set; }
        public decimal? AdhesiveGsm { get; set; }
        public decimal? FabricGsm { get; set; }
        public decimal? TotalGsm { get; set; }
        public long? FabricProductId { get; set; }
        public decimal? FabricProductQty { get; set; }
        public decimal? FabricWidthInMeter { get; set; }
        public long? ShiftSupervisorWorkerId { get; set; }
    }
}
