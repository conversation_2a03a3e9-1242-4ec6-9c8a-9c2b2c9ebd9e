﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class ProductSupplierMapping
    {
        public ProductSupplierMapping()
        {
            InvoiceMasters = new HashSet<InvoiceMaster>();
            StockProductTables = new HashSet<StockProductTable>();
        }

        public long ProductSupplierMappingId { get; set; }
        public long ProductId { get; set; }
        public long SupplierId { get; set; }
        public string SupplierProductName { get; set; }
        public int UsageCount { get; set; }
        public DateTime LastUsedDate { get; set; }
        public long LastUsedById { get; set; }
        public DateTime CreatedDate { get; set; }
        public long CreatedById { get; set; }

        public virtual UserMaster CreatedBy { get; set; }
        public virtual UserMaster LastUsedBy { get; set; }
        public virtual ProductMaster Product { get; set; }
        public virtual SupplierMaster Supplier { get; set; }
        public virtual ICollection<InvoiceMaster> InvoiceMasters { get; set; }
        public virtual ICollection<StockProductTable> StockProductTables { get; set; }
    }
}
