﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class NotificationTrackingTable
    {
        public long NotificationTrackingId { get; set; }
        public string NotificationType { get; set; }
        public string MessageType { get; set; }
        public long RecipientId { get; set; }
        public string MessageContent { get; set; }
        public long? MasterTemplateId { get; set; }
        public string Status { get; set; }
        public string ErrorMessage { get; set; }
        public string ProviderMessageId { get; set; }
        public DateTime SentTime { get; set; }
        public DateTime? DeliveredTime { get; set; }
        public DateTime? ReadTime { get; set; }
        public string AddedBy { get; set; }
        public DateTime AddedDate { get; set; }
        public string NotificationMessageId { get; set; }
        public string RecipientMobileNumber { get; set; }
        public string RecipientEmail { get; set; }
        public long? NotificationGroupUserId { get; set; }
    }
}
