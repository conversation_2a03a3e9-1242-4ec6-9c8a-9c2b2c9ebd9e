﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class NotificationGroupsTable
    {
        public long NotificationGroupUserId { get; set; }
        public string NotificationType { get; set; }
        public string TriggerType { get; set; }
        public string ReportName { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public bool? EnableToEmail { get; set; }
        public bool? EnableCcemail { get; set; }
        public bool? EnableBccemail { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public string UserType { get; set; }
        public long? UserMasterId { get; set; }
        public bool? IsWhatsAppNotificationEnabled { get; set; }
        public string MobileNumber { get; set; }
        public TimeSpan? PreferredNotificationTime { get; set; }
        public string TimeZone { get; set; }
        public string NotificationFrequency { get; set; }
        public long? WhatsAppTemplateMasterId { get; set; }
        public string LastTriggeredBy { get; set; }
        public DateTime? LastTriggeredDate { get; set; }
        public long? ConfigurationId { get; set; }
        public string CronScheduleExpression { get; set; }
        public bool? UseDefaultSchedule { get; set; }

        public virtual NotificationTemplateConfigurationTable Configuration { get; set; }
        public virtual WhatsAppTemplateMaster WhatsAppTemplateMaster { get; set; }
    }
}
