﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class NotificationSaleOrderStagesTable
    {
        public long StageId { get; set; }
        public string SaleOrderStages { get; set; }
        public bool? OnlyInternal { get; set; }
        public bool? OnlyCustomer { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public long? WhatsappTemplateId { get; set; }

        public virtual WhatsAppTemplateMaster WhatsappTemplate { get; set; }
    }
}
