﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class FactoryWorkersMaster
    {
        public FactoryWorkersMaster()
        {
            SaleOrderPostProcessEmbossingTables = new HashSet<SaleOrderPostProcessEmbossingTable>();
            SaleOrderPostProcessLacquerTables = new HashSet<SaleOrderPostProcessLacquerTable>();
            SaleOrderPostProcessPrintTables = new HashSet<SaleOrderPostProcessPrintTable>();
        }

        public long WorkerId { get; set; }
        public string Name { get; set; }
        public string ShortName { get; set; }
        public string WorkShift { get; set; }
        public long? DesignationId { get; set; }
        public long? DepartmentId { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }

        public virtual ICollection<SaleOrderPostProcessEmbossingTable> SaleOrderPostProcessEmbossingTables { get; set; }
        public virtual ICollection<SaleOrderPostProcessLacquerTable> SaleOrderPostProcessLacquerTables { get; set; }
        public virtual ICollection<SaleOrderPostProcessPrintTable> SaleOrderPostProcessPrintTables { get; set; }
    }
}
