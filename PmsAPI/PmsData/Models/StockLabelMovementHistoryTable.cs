﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class StockLabelMovementHistoryTable
    {
        public long LabelMovementId { get; set; }
        public long StockLabelId { get; set; }
        public long FromStoreId { get; set; }
        public long FromRackId { get; set; }
        public long ToStoreId { get; set; }
        public long ToRackId { get; set; }
        public DateTime MovementDate { get; set; }
        public string MovedBy { get; set; }
        public string Reason { get; set; }
        public long IssueId { get; set; }

        public virtual IssueProductTable Issue { get; set; }
        public virtual StockLabelTable StockLabel { get; set; }
    }
}
