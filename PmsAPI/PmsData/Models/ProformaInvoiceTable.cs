﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class ProformaInvoiceTable
    {
        public long ProformaInvoiceId { get; set; }
        public long? CustomerId { get; set; }
        public string Gstn { get; set; }
        public string ProformaInvoiceNumber { get; set; }
        public DateTime? ProformaInvoiceDate { get; set; }
        public string ConsignorReference { get; set; }
        public string ReferenceType { get; set; }
        public string BuyerReferenceNumber { get; set; }
        public string CountryOfOrigin { get; set; }
        public string CountryOfDestinaton { get; set; }
        public string MaterialType { get; set; }
        public string Hsncode { get; set; }
        public string BankName { get; set; }
        public string BankBranch { get; set; }
        public string BankAccountNumber { get; set; }
        public string Ifsccode { get; set; }
        public string SwiftCode { get; set; }
        public string BeneficiaryName { get; set; }
        public string PortOfLoading { get; set; }
        public string PortOfDischarge { get; set; }
        public string FinalDestination { get; set; }
        public string ModeOfTransport { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? Moq { get; set; }
        public decimal? Moqtotal { get; set; }
        public decimal? Gst { get; set; }
        public decimal? TotalPrice { get; set; }
        public string TermsCondition { get; set; }
        public string Currency { get; set; }
        public decimal? Discount { get; set; }
        public long? BankId { get; set; }
    }
}
