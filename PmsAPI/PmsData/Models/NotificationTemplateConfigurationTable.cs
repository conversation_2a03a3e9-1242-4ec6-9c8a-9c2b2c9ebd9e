﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class NotificationTemplateConfigurationTable
    {
        public NotificationTemplateConfigurationTable()
        {
            NotificationGroupsTables = new HashSet<NotificationGroupsTable>();
        }

        public long ConfigurationId { get; set; }
        public string CategoryType { get; set; }
        public string NotificationType { get; set; }
        public string SubType { get; set; }
        public string DisplayName { get; set; }
        public string Description { get; set; }
        public bool? InternalAllowed { get; set; }
        public bool ExternalAllowed { get; set; }
        public bool CustomerAllowed { get; set; }
        public bool? EventAllowed { get; set; }
        public bool SchedulingAllowed { get; set; }
        public bool? OnDemandAllowed { get; set; }
        public bool SchedulingRequired { get; set; }
        public long? WhatsAppTemplateMasterId { get; set; }
        public long? EmailTemplateMasterId { get; set; }
        public int? SortOrder { get; set; }
        public bool IsDefault { get; set; }
        public long? AddedById { get; set; }
        public DateTime? AddedDate { get; set; }
        public long? UpdatedById { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool Disabled { get; set; }
        public long? DisabledById { get; set; }
        public DateTime? DisabledDate { get; set; }

        public virtual UserMaster AddedBy { get; set; }
        public virtual UserMaster DisabledBy { get; set; }
        public virtual UserMaster UpdatedBy { get; set; }
        public virtual WhatsAppTemplateMaster WhatsAppTemplateMaster { get; set; }
        public virtual ICollection<NotificationGroupsTable> NotificationGroupsTables { get; set; }
    }
}
