﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class IssueProductTable
    {
        public IssueProductTable()
        {
            StockLabelMovementHistoryTables = new HashSet<StockLabelMovementHistoryTable>();
        }

        public long IssueId { get; set; }
        public long? SaleOrderId { get; set; }
        public long FromStore { get; set; }
        public long? FromRack { get; set; }
        public long ToStore { get; set; }
        public long ProductId { get; set; }
        public decimal Quantity { get; set; }
        public string Remark { get; set; }
        public string Status { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string ActionBy { get; set; }
        public DateTime? ActionDate { get; set; }
        public long? StockProductId { get; set; }
        public long? StockId { get; set; }
        public long? ToRack { get; set; }
        public long? ToNewStockId { get; set; }
        public long? ToNewStockProductId { get; set; }
        public long? IssueSlipId { get; set; }
        public string IssueNumber { get; set; }
        public decimal? DemandQuantity { get; set; }
        public string RequestType { get; set; }
        public string ApprovalMode { get; set; }
        public long? ParentIssueId { get; set; }

        public virtual ICollection<StockLabelMovementHistoryTable> StockLabelMovementHistoryTables { get; set; }
    }
}
