﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderPostProcessPrintTable
    {
        public long SaleOrderPostProcessPrintId { get; set; }
        public long? SaleOrderId { get; set; }
        public long? PrintRack { get; set; }
        public decimal? PrintCompletedQuantity { get; set; }
        public decimal? PrintWastageQuantity { get; set; }
        public string PrintMeasurementUnit { get; set; }
        public string PrintStatus { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? ReceivedQuantity { get; set; }
        public string Remark { get; set; }
        public int? Rank { get; set; }
        public DateTime? StartDateTime { get; set; }
        public DateTime? EndDateTime { get; set; }
        public int? LineNo { get; set; }
        public long? ShiftSupervisorWorkerId { get; set; }
        public decimal? PricePerUnit { get; set; }

        public virtual FactoryWorkersMaster ShiftSupervisorWorker { get; set; }
    }
}
