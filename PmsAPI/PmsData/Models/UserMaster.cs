﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class UserMaster
    {
        public UserMaster()
        {
            FileUploadTables = new HashSet<FileUploadTable>();
            InvoiceMasterDisabledBies = new HashSet<InvoiceMaster>();
            InvoiceMasterUpdatedBies = new HashSet<InvoiceMaster>();
            NotificationTemplateConfigurationTableAddedBies = new HashSet<NotificationTemplateConfigurationTable>();
            NotificationTemplateConfigurationTableDisabledBies = new HashSet<NotificationTemplateConfigurationTable>();
            NotificationTemplateConfigurationTableUpdatedBies = new HashSet<NotificationTemplateConfigurationTable>();
            PackagingTypeMasterAddedBies = new HashSet<PackagingTypeMaster>();
            PackagingTypeMasterDisabledBies = new HashSet<PackagingTypeMaster>();
            ProductSupplierMappingCreatedBies = new HashSet<ProductSupplierMapping>();
            ProductSupplierMappingLastUsedBies = new HashSet<ProductSupplierMapping>();
            ProductTransferTableActionBies = new HashSet<ProductTransferTable>();
            ProductTransferTableAddedBies = new HashSet<ProductTransferTable>();
            StockMasters = new HashSet<StockMaster>();
            StockPriceTrackingTables = new HashSet<StockPriceTrackingTable>();
            UserExceptionForceLogoutTableAddedBies = new HashSet<UserExceptionForceLogoutTable>();
            UserExceptionForceLogoutTableUsers = new HashSet<UserExceptionForceLogoutTable>();
        }

        public long UserId { get; set; }
        public string Name { get; set; }
        public string Contact { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string Status { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public string AdobjectId { get; set; }
        public string EmailAddress { get; set; }

        public virtual ICollection<FileUploadTable> FileUploadTables { get; set; }
        public virtual ICollection<InvoiceMaster> InvoiceMasterDisabledBies { get; set; }
        public virtual ICollection<InvoiceMaster> InvoiceMasterUpdatedBies { get; set; }
        public virtual ICollection<NotificationTemplateConfigurationTable> NotificationTemplateConfigurationTableAddedBies { get; set; }
        public virtual ICollection<NotificationTemplateConfigurationTable> NotificationTemplateConfigurationTableDisabledBies { get; set; }
        public virtual ICollection<NotificationTemplateConfigurationTable> NotificationTemplateConfigurationTableUpdatedBies { get; set; }
        public virtual ICollection<PackagingTypeMaster> PackagingTypeMasterAddedBies { get; set; }
        public virtual ICollection<PackagingTypeMaster> PackagingTypeMasterDisabledBies { get; set; }
        public virtual ICollection<ProductSupplierMapping> ProductSupplierMappingCreatedBies { get; set; }
        public virtual ICollection<ProductSupplierMapping> ProductSupplierMappingLastUsedBies { get; set; }
        public virtual ICollection<ProductTransferTable> ProductTransferTableActionBies { get; set; }
        public virtual ICollection<ProductTransferTable> ProductTransferTableAddedBies { get; set; }
        public virtual ICollection<StockMaster> StockMasters { get; set; }
        public virtual ICollection<StockPriceTrackingTable> StockPriceTrackingTables { get; set; }
        public virtual ICollection<UserExceptionForceLogoutTable> UserExceptionForceLogoutTableAddedBies { get; set; }
        public virtual ICollection<UserExceptionForceLogoutTable> UserExceptionForceLogoutTableUsers { get; set; }
    }
}
