﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class PackagingTypeMaster
    {
        public long PackagingTypeId { get; set; }
        public string PackagingTypeName { get; set; }
        public string PackagingTypeCode { get; set; }
        public long? AddedById { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public long? DisabledById { get; set; }
        public DateTime? DisabledDate { get; set; }

        public virtual UserMaster AddedBy { get; set; }
        public virtual UserMaster DisabledBy { get; set; }
    }
}
