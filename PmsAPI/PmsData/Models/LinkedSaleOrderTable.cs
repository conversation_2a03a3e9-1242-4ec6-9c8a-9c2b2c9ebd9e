﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class LinkedSaleOrderTable
    {
        public long LinkedId { get; set; }
        public long? ParentSaleOrder { get; set; }
        public long? LinkedSaleOrder { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? IsLinkingComplete { get; set; }
    }
}
