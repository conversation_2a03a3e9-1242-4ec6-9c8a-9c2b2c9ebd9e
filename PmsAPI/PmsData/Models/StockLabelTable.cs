﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class StockLabelTable
    {
        public StockLabelTable()
        {
            OutpassItemTables = new HashSet<OutpassItemTable>();
            StockLabelMovementHistoryTables = new HashSet<StockLabelMovementHistoryTable>();
            StockLabelTimelines = new HashSet<StockLabelTimeline>();
        }

        public long StockLabelId { get; set; }
        public string SerialNo { get; set; }
        public long StockId { get; set; }
        public long StockProductId { get; set; }
        public long? OriginalLabelId { get; set; }
        public long ProductId { get; set; }
        public decimal? Quantity { get; set; }
        public string PackagingUnit { get; set; }
        public DateTime? MfgDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public bool? IsActive { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string Grade { get; set; }
        public long? CurrentStoreId { get; set; }
        public long? CurrentRackId { get; set; }
        public string InspectionStatus { get; set; }
        public string LabelStatus { get; set; }
        public long? AllocationId { get; set; }
        public string ShortCode { get; set; }

        public virtual RackMaster CurrentRack { get; set; }
        public virtual StoreMaster CurrentStore { get; set; }
        public virtual ProductMaster Product { get; set; }
        public virtual StockMaster Stock { get; set; }
        public virtual StockProductTable StockProduct { get; set; }
        public virtual ICollection<OutpassItemTable> OutpassItemTables { get; set; }
        public virtual ICollection<StockLabelMovementHistoryTable> StockLabelMovementHistoryTables { get; set; }
        public virtual ICollection<StockLabelTimeline> StockLabelTimelines { get; set; }
    }
}
