﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class NotificationTemplateParameterTable
    {
        public long ParameterId { get; set; }
        public long TemplateMasterId { get; set; }
        public string ParameterName { get; set; }
        public string ParameterType { get; set; }
        public bool? IsRequired { get; set; }
        public string DefaultValue { get; set; }
        public string ValidationRegex { get; set; }
        public int? Sequence { get; set; }

        public virtual WhatsAppTemplateMaster TemplateMaster { get; set; }
    }
}
