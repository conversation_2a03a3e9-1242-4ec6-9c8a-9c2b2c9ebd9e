﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class ProductMaster
    {
        public ProductMaster()
        {
            ProductSupplierMappings = new HashSet<ProductSupplierMapping>();
            ProductTransferTableFromProducts = new HashSet<ProductTransferTable>();
            ProductTransferTableToProducts = new HashSet<ProductTransferTable>();
            StockLabelTables = new HashSet<StockLabelTable>();
        }

        public long ProductId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string Unit { get; set; }
        public string ProductType { get; set; }
        public long? ProductCategoryId { get; set; }
        public long? ProductFirstSubCategoryId { get; set; }
        public long? ProductSecSubCategoryId { get; set; }
        public decimal? MinimumQuantity { get; set; }
        public decimal? AvgGsm { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public decimal? WidthInMeter { get; set; }
        public string ProductDescription { get; set; }

        public virtual ICollection<ProductSupplierMapping> ProductSupplierMappings { get; set; }
        public virtual ICollection<ProductTransferTable> ProductTransferTableFromProducts { get; set; }
        public virtual ICollection<ProductTransferTable> ProductTransferTableToProducts { get; set; }
        public virtual ICollection<StockLabelTable> StockLabelTables { get; set; }
    }
}
