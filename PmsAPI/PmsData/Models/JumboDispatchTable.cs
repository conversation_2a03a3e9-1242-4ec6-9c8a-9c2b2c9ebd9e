﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class JumboDispatchTable
    {
        public long JumboDispatchId { get; set; }
        public string PackingNumber { get; set; }
        public long TransportId { get; set; }
        public long? VehicleId { get; set; }
        public DateTime DispatchDate { get; set; }
        public string DispatchNumber { get; set; }
        public string Barcode { get; set; }
        public string Weight { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? IsGateIn { get; set; }
        public bool? IsGateOut { get; set; }
        public long? CustomerId { get; set; }
        public decimal? DispatchQuantity { get; set; }
        public long? StoreId { get; set; }
        public long? RackId { get; set; }
        public string Remark { get; set; }
        public string PackageId { get; set; }
    }
}
