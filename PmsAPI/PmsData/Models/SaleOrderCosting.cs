﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderCosting
    {
        public long SaleOrderId { get; set; }
        public decimal? FabricCost { get; set; }
        public decimal? CoatingCost { get; set; }
        public decimal? PasteCostLm { get; set; }
        public decimal? GrainCostLm { get; set; }
        public decimal? FabricCostLm { get; set; }
        public decimal? FinishingCostLm { get; set; }
        public decimal? RmcostLm { get; set; }
        public decimal? Rejection { get; set; }
        public decimal? ProductionCostLm { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
    }
}
