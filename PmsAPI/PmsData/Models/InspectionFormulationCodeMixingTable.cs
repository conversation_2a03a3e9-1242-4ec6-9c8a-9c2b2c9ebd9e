﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class InspectionFormulationCodeMixingTable
    {
        public long FormulationCodeMixingId { get; set; }
        public long? SaleFormulationCodeId { get; set; }
        public long? MixingId { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public long? SaleOrderId { get; set; }
        public decimal? CostPerKg { get; set; }
        public decimal? CostGsm { get; set; }
        public decimal? CostPerLm { get; set; }
        public long? InspectionSaleFormulationCodeId { get; set; }
        public decimal? StdPasteRequirementQuantity { get; set; }
        public decimal? StdPasteRequirementScquantity { get; set; }
    }
}
