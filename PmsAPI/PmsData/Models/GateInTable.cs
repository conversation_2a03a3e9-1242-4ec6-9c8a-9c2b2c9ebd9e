﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class GateInTable
    {
        public long GateInId { get; set; }
        public long VehicleId { get; set; }
        public bool? GateIn { get; set; }
        public DateTime? GateInDate { get; set; }
        public string <PERSON>In<PERSON><PERSON> { get; set; }
        public string GateInPersonContact { get; set; }
        public bool? GatePassIssue { get; set; }
        public DateTime? GatePassIssueDate { get; set; }
        public bool? GateOut { get; set; }
        public DateTime? GateOutDate { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public string GatePassAddedBy { get; set; }
        public string GateOutAddedBy { get; set; }
        public string Type { get; set; }
        public decimal? InWeight { get; set; }
        public decimal? OutWeight { get; set; }
    }
}
