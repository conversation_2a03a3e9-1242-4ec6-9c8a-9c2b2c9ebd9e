﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderProductionLacquerRawMaterialTable
    {
        public long SaleOrderProductionLacquerRawMaterialId { get; set; }
        public long? SaleOrderProductionId { get; set; }
        public long? LacquerMasterId { get; set; }
        public long? ProductId { get; set; }
        public decimal? Quantity { get; set; }
        public string Unit { get; set; }
        public decimal? Price { get; set; }
        public bool? Removed { get; set; }
        public string RemovedBy { get; set; }
        public DateTime? RemovedDate { get; set; }
        public int? Rank { get; set; }
    }
}
