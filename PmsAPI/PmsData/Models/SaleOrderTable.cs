﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderTable
    {
        public long SaleOrderId { get; set; }
        public string SaleOrderType { get; set; }
        public long? CustomerId { get; set; }
        public string SaleOrderNumber { get; set; }
        public DateTime? SaleOrderDate { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public string Remarks { get; set; }
        public bool? CostingAdded { get; set; }
        public string SaleOrderStatus { get; set; }
        public long? CategoryId { get; set; }
        public bool? WorkPlanStatus { get; set; }
        public bool? IsRawMaterialIssued { get; set; }
        public int Status { get; set; }
        public long? SaleFormulationCodeId { get; set; }
        public string ProductionCompletionRemarks { get; set; }
        public bool? IsJumboRequired { get; set; }
        public long? ProformaInvoiceId { get; set; }
        public string Bornumber { get; set; }
        public string SaleOrderCode { get; set; }
        public string FinishCode { get; set; }
        public string HoldBy { get; set; }
        public DateTime? HoldDate { get; set; }
        public string ApprovedBy { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
