﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class OutpassItemTable
    {
        public long OutpassItemId { get; set; }
        public long? OutpassId { get; set; }
        public long? StockProductId { get; set; }
        public string ProductName { get; set; }
        public decimal? Quantity { get; set; }
        public long? RackId { get; set; }
        public decimal? ReturnedQuantity { get; set; }
        public long? ReturnedRackId { get; set; }
        public decimal? Amount { get; set; }
        public string Unit { get; set; }
        public string ReturnCompletedBy { get; set; }
        public DateTime? ReturnCompletedDate { get; set; }
        public long? StockLabelId { get; set; }
        public string ReasonForLessQuantity { get; set; }

        public virtual StockLabelTable StockLabel { get; set; }
    }
}
