﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class WorkPlanJumboMaster
    {
        public long WorkPlanJumboMasterId { get; set; }
        public long? SaleOrderId { get; set; }
        public DateTime? JumboRollDate { get; set; }
        public DateTime? JumboRollStartTime { get; set; }
        public DateTime? JumboRollEndTime { get; set; }
        public string JumboNo { get; set; }
        public decimal? Rate { get; set; }
        public decimal? Amount { get; set; }
        public decimal? JumboRolQty { get; set; }
        public decimal? Weight { get; set; }
        public long? RackId { get; set; }
        public long? StoreId { get; set; }
        public string RackCode { get; set; }
        public string RackName { get; set; }
        public string StoreCode { get; set; }
        public string StoreName { get; set; }
        public string Remark { get; set; }
        public decimal? Yield { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? IsInspectionCompleted { get; set; }
        public decimal? ActualQuantity { get; set; }
        public decimal? WastagePrint { get; set; }
        public decimal? WastageEmbossing { get; set; }
        public decimal? WastageLacquer { get; set; }
        public decimal? WastageVacuum { get; set; }
        public decimal? WastageTumbling { get; set; }
        public long? ShiftSupervisorWorkerId { get; set; }
    }
}
