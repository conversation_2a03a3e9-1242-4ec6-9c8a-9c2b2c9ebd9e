﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class InvoiceMaster
    {
        public long InvoiceId { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public decimal? InvoiceTotalPrice { get; set; }
        public string InvoiceFile { get; set; }
        public string EwayBill { get; set; }
        public DateTime? EwayBillDate { get; set; }
        public long? SupplierId { get; set; }
        public string Grn { get; set; }
        public long? Poid { get; set; }
        public decimal? FreightInsurance { get; set; }
        public decimal? ShippingHandling { get; set; }
        public decimal? OtherCharges { get; set; }
        public decimal? InvoiceTotal { get; set; }
        public long? UpdatedById { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool? Active { get; set; }
        public long? DisabledById { get; set; }
        public DateTime? DisabledDate { get; set; }
        public long? ProductSupplierMappingId { get; set; }

        // Return Stock fields
        public string InvoiceType { get; set; }
        public long? OriginalInvoiceId { get; set; }

        public virtual UserMaster DisabledBy { get; set; }
        public virtual ProductSupplierMapping ProductSupplierMapping { get; set; }
        public virtual UserMaster UpdatedBy { get; set; }
    }
}
