﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class OverheadCostMonthlyMappingTable
    {
        public long OverheadCostMonthlyyMappingId { get; set; }
        public long OverheadCostId { get; set; }
        public long OverheadColumnId { get; set; }
        public decimal AvgLineSpeed { get; set; }
        public decimal AvgOverheadValue { get; set; }
        public string AddedBy { get; set; }
        public DateTime AddedDate { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? ApplicableDate { get; set; }
    }
}
