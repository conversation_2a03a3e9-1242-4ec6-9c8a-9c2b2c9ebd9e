﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class UserExceptionForceLogoutTable
    {
        public long UserExceptionId { get; set; }
        public long? UserId { get; set; }
        public bool? IsActive { get; set; }
        public long? AddedById { get; set; }
        public DateTime? AddedDate { get; set; }

        public virtual UserMaster AddedBy { get; set; }
        public virtual UserMaster User { get; set; }
    }
}
