﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class ProductionDowntimeTable
    {
        public long ProductionDowntimeId { get; set; }
        public long? ProductionDowntimeReasonId { get; set; }
        public string ProductionLineType { get; set; }
        public int? ProductionLineNo { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public decimal? ExcessDurationMinutes { get; set; }
        public string Comments { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public bool? IsDeleted { get; set; }
        public decimal? ActualDurationMinutes { get; set; }

        public virtual ProductionDowntimeReasonMaster ProductionDowntimeReason { get; set; }
    }
}
