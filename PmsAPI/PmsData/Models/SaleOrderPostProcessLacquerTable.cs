﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderPostProcessLacquerTable
    {
        public long SaleOrderPostProcessLacquerId { get; set; }
        public long? SaleOrderId { get; set; }
        public long? LacquerRack { get; set; }
        public decimal? LacquerCompletedQuantity { get; set; }
        public decimal? LacquerWastageQuantity { get; set; }
        public string LacquerMeasurementUnit { get; set; }
        public string LacquerStatus { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? ReceivedQuantity { get; set; }
        public string Remark { get; set; }
        public int? Rank { get; set; }
        public DateTime? StartDateTime { get; set; }
        public DateTime? EndDateTime { get; set; }
        public int? LineNo { get; set; }
        public long? ShiftSupervisorWorkerId { get; set; }
        public decimal? PricePerUnit { get; set; }

        public virtual FactoryWorkersMaster ShiftSupervisorWorker { get; set; }
    }
}
