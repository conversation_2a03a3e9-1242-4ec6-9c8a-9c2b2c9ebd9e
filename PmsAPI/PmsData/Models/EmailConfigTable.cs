﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class EmailConfigTable
    {
        public long EmailConfigId { get; set; }
        public string EmailConfigName { get; set; }
        public string EmailConfigSmtp { get; set; }
        public string EmailConfigFromEmailId { get; set; }
        public string EmailConfigFromEmailDisplayName { get; set; }
        public string EmailConfigAccountId { get; set; }
        public string EmailConfigPassword { get; set; }
        public string EmailConfigPort { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public bool? EnableSsl { get; set; }
        public int? MaxDailyEmails { get; set; }
        public int? MaxAttachmentSize { get; set; }
        public int RetryCount { get; set; }
    }
}
