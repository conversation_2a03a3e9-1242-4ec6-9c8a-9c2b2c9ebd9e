﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderPostProcessVacuumTable
    {
        public long SaleOrderPostProcessVacuumId { get; set; }
        public long? SaleOrderId { get; set; }
        public long? VacuumRack { get; set; }
        public decimal? VacuumCompletedQuantity { get; set; }
        public decimal? VacuumWastageQuantity { get; set; }
        public string VacuumMeasurementUnit { get; set; }
        public string VacuumStatus { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? ReceivedQuantity { get; set; }
        public string Remark { get; set; }
        public int? Rank { get; set; }
        public DateTime? StartDateTime { get; set; }
        public DateTime? EndDateTime { get; set; }
        public int? LineNo { get; set; }
        public long? ShiftSupervisorWorkerId { get; set; }
        public decimal? PricePerUnit { get; set; }
    }
}
