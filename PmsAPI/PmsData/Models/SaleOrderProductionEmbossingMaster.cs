﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderProductionEmbossingMaster
    {
        public long SaleOrderProductionEmbossingMasterId { get; set; }
        public long? SaleOrderProductionId { get; set; }
        public long? EmbossingMasterId { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? Price { get; set; }
        public decimal? Total { get; set; }
        public bool? Removed { get; set; }
        public string RemovedBy { get; set; }
        public DateTime? RemovedDate { get; set; }
        public int? Rank { get; set; }
    }
}
