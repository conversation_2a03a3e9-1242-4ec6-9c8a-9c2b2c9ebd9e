﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderPostProcessTumblingTable
    {
        public long SaleOrderPostProcessTumblingId { get; set; }
        public long? SaleOrderId { get; set; }
        public long? TumblingRack { get; set; }
        public decimal? TumblingCompletedQuantity { get; set; }
        public decimal? TumblingWastageQuantity { get; set; }
        public string TumblingMeasurementUnit { get; set; }
        public string TumblingStatus { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? ReceivedQuantity { get; set; }
        public string Remark { get; set; }
        public int? Rank { get; set; }
        public DateTime? StartDateTime { get; set; }
        public DateTime? EndDateTime { get; set; }
        public int? LineNo { get; set; }
        public long? ShiftSupervisorWorkerId { get; set; }
        public decimal? PricePerUnit { get; set; }
    }
}
