﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class StoreMaster
    {
        public StoreMaster()
        {
            StockLabelTables = new HashSet<StockLabelTable>();
        }

        public long StoreId { get; set; }
        public long? DeptId { get; set; }
        public long? BranchId { get; set; }
        public string StoreName { get; set; }
        public string StoreCode { get; set; }
        public string StoreDesc { get; set; }
        public DateTime? StoreAddedDate { get; set; }
        public string StoreAddedBy { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public bool? IsWorkInProgressStore { get; set; }

        public virtual ICollection<StockLabelTable> StockLabelTables { get; set; }
    }
}
