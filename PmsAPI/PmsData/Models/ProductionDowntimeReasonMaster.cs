﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class ProductionDowntimeReasonMaster
    {
        public ProductionDowntimeReasonMaster()
        {
            ProductionDowntimeScheduleds = new HashSet<ProductionDowntimeScheduled>();
            ProductionDowntimeTables = new HashSet<ProductionDowntimeTable>();
        }

        public long ProductionDowntimeReasonId { get; set; }
        public string ReasonCode { get; set; }
        public string ReasonName { get; set; }
        public string Description { get; set; }
        public string DowntimeType { get; set; }
        public string ProductionLineType { get; set; }
        public decimal? StandardDurationMinutes { get; set; }
        public bool? IsActive { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public bool? IsDeleted { get; set; }

        public virtual ICollection<ProductionDowntimeScheduled> ProductionDowntimeScheduleds { get; set; }
        public virtual ICollection<ProductionDowntimeTable> ProductionDowntimeTables { get; set; }
    }
}
