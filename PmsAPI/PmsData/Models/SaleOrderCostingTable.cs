﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderCostingTable
    {
        public long SaleOrderId { get; set; }
        public decimal? FabricCost { get; set; }
        public decimal? CoatingCost { get; set; }
        public decimal? PasteCostLm { get; set; }
        public decimal? GrainCostLm { get; set; }
        public decimal? FabricCostLm { get; set; }
        public decimal? FinishingCostLm { get; set; }
        public decimal? RmcostLm { get; set; }
        public decimal? Rejection { get; set; }
        public decimal? PerLmconstant { get; set; }
        public decimal? OverheadCost { get; set; }
        public decimal? InlineScraping { get; set; }
        public decimal? ProductionCostLm { get; set; }
        public string SaleOrderMaterialType { get; set; }
        public decimal? PrintCostPerUnit { get; set; }
        public decimal? EmbossingCostPerUnit { get; set; }
        public decimal? TumblingCostPerUnit { get; set; }
        public decimal? LacquerCostPerUnit { get; set; }
        public decimal? VacuumCostPerUnit { get; set; }
        public decimal? PackagingCostPerUnit { get; set; }
        public decimal? MiscellaneousCostPerUnit { get; set; }
        public DateTime? AddedDate { get; set; }
        public string AddedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string UpdatedBy { get; set; }
    }
}
