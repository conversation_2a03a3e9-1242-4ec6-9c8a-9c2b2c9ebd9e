﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderPostProcessEmbossingTable
    {
        public long SaleOrderPostProcessEmbossingId { get; set; }
        public long? SaleOrderId { get; set; }
        public long? EmbossingRack { get; set; }
        public decimal? EmbossingCompletedQuantity { get; set; }
        public decimal? EmbossingWastageQuantity { get; set; }
        public string EmbossingMeasurementUnit { get; set; }
        public string EmbossingStatus { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? ReceivedQuantity { get; set; }
        public string Remark { get; set; }
        public int? Rank { get; set; }
        public DateTime? StartDateTime { get; set; }
        public DateTime? EndDateTime { get; set; }
        public int? LineNo { get; set; }
        public long? ShiftSupervisorWorkerId { get; set; }
        public decimal? PricePerUnit { get; set; }

        public virtual FactoryWorkersMaster ShiftSupervisorWorker { get; set; }
    }
}
