﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderProductionMiscellaneousRawMaterialTable
    {
        public long SaleOrderProductionMiscellaneousRawMaterialId { get; set; }
        public long? SaleOrderProductionId { get; set; }
        public long? ProductId { get; set; }
        public decimal? Quantity { get; set; }
        public string Unit { get; set; }
        public string MaterialCategory { get; set; }
        public decimal? PricePerUnit { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
    }
}
