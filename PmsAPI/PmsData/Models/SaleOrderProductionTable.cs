﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderProductionTable
    {
        public long SaleOrderProductionId { get; set; }
        public long? SaleOrderId { get; set; }
        public long ProductId { get; set; }
        public string ManufacturingProductCode { get; set; }
        public string ManufacturingProductName { get; set; }
        public decimal? Lot { get; set; }
        public decimal? Batch { get; set; }
        public decimal? OrderQuantity { get; set; }
        public decimal? Lmconstant { get; set; }
        public decimal? ExtraProduction { get; set; }
        public decimal? ManufacturingQuantity { get; set; }
        public string Unit { get; set; }
        public long? FabricProductId { get; set; }
        public long? FabricColorId { get; set; }
        public long? ColorId { get; set; }
        public decimal? ColorPrice { get; set; }
        public string Barcode { get; set; }
        public long? GrainId { get; set; }
        public decimal? GrainPrice { get; set; }
        public decimal? Thick { get; set; }
        public decimal? ThickPrice { get; set; }
        public decimal? Width { get; set; }
        public decimal? WidthPrice { get; set; }
        public string ProductionStatus { get; set; }
        public string CostingStatus { get; set; }
        public decimal? SlippagePercent { get; set; }
        public decimal? TotalCost { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public long? LacquerMasterId { get; set; }
        public string ProcessFormulationCode { get; set; }
        public string MixingFormulationCode { get; set; }
        public decimal? SalePrice { get; set; }
        public decimal? PreSkinGsm { get; set; }
        public decimal? SkinGsm { get; set; }
        public decimal? FoamGsm { get; set; }
        public decimal? AdhesiveGsm { get; set; }
        public decimal? FabricGsm { get; set; }
        public decimal? TotalGsm { get; set; }
        public long? SaleOrderStoreId { get; set; }
    }
}
