﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class StockPriceTrackingTable
    {
        public long StockPriceTrackingId { get; set; }
        public long? StockProductId { get; set; }
        public decimal? PricePerUnit { get; set; }
        public decimal? NewPricePerUnit { get; set; }
        public decimal? ShippingHandlingPerUnit { get; set; }
        public decimal? NewShippingHandlingPerUnit { get; set; }
        public decimal? FreightPerUnit { get; set; }
        public decimal? NewFreightPerUnit { get; set; }
        public decimal? InvoicePricePerUnit { get; set; }
        public decimal? NewInvoicePricePerUnit { get; set; }
        public decimal? MiscPerUnit { get; set; }
        public decimal? NewMiscPerUnit { get; set; }
        public long? UpdatedById { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public virtual UserMaster UpdatedBy { get; set; }
    }
}
