﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class OutpassMaster
    {
        public OutpassMaster()
        {
            OutpassStatusHistories = new HashSet<OutpassStatusHistory>();
        }

        public long OutpassId { get; set; }
        public string OutpassTo { get; set; }
        public string OutpassNumber { get; set; }
        public DateTime? OutpassDate { get; set; }
        public string OutpassType { get; set; }
        public string Purpose { get; set; }
        public string Remark { get; set; }
        public bool? IsOutpassIn { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public long? OutpassToCustomerId { get; set; }
        public long? PurposeId { get; set; }
        public DateTime? ExpectedReturnDate { get; set; }
        public string Status { get; set; }
        public long? TransportId { get; set; }
        public long? VehicleId { get; set; }
        public bool? IsGateIn { get; set; }
        public string CreateMode { get; set; }

        public virtual ICollection<OutpassStatusHistory> OutpassStatusHistories { get; set; }
    }
}
