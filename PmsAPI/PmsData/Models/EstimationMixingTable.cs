﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class EstimationMixingTable
    {
        public long EstimationCodeMixingId { get; set; }
        public long EstimationOrderId { get; set; }
        public long? MixingId { get; set; }
        public decimal? PreSkinGsm { get; set; }
        public decimal? SkinGsm { get; set; }
        public decimal? FoamGsm { get; set; }
        public decimal? AdhesiveGsm { get; set; }
        public decimal? AvgGsm { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
    }
}
