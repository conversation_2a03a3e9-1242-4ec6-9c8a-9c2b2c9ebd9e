﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class ProductTransferTable
    {
        public long TransferId { get; set; }
        public long? FromProductId { get; set; }
        public long? ToProductId { get; set; }
        public decimal? Quantity { get; set; }
        public string Status { get; set; }
        public string RequestReason { get; set; }
        public long? AddedById { get; set; }
        public DateTime? AddedDate { get; set; }
        public long? ActionById { get; set; }
        public DateTime? ActionDate { get; set; }
        public string ActionRemark { get; set; }

        public virtual UserMaster ActionBy { get; set; }
        public virtual UserMaster AddedBy { get; set; }
        public virtual ProductMaster FromProduct { get; set; }
        public virtual ProductMaster ToProduct { get; set; }
    }
}
