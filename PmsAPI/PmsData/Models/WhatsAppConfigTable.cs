﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class WhatsAppConfigTable
    {
        public long WhatsAppConfigId { get; set; }
        public string ConfigName { get; set; }
        public string ProviderName { get; set; }
        public string RegisteredSenderNumber { get; set; }
        public string ProviderKey { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public int? MaxDailyMessages { get; set; }
        public int? MaxMonthlyMessages { get; set; }
        public string ApiEndpoint { get; set; }
        public string WebhookUrl { get; set; }
    }
}
