﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SupplierMaster
    {
        public SupplierMaster()
        {
            ProductSupplierMappings = new HashSet<ProductSupplierMapping>();
        }

        public long SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string ContactPersonName { get; set; }
        public string Email { get; set; }
        public string SupplierContactNumber { get; set; }
        public string ContactPersonNumber { get; set; }
        public string Address { get; set; }
        public string Gst { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }

        public virtual ICollection<ProductSupplierMapping> ProductSupplierMappings { get; set; }
    }
}
