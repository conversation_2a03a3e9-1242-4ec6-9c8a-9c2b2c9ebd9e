using System.Threading.Tasks;
using PmsCore.DataAccessRepository.Interfaces;
using PmsCore.PDFGeneration.Models;
using PmsCommon;
using PmsData.Models;
using Microsoft.Extensions.Logging;
using System;
using PmsEntity.ViewModel;

namespace PmsData.DataFn
{
    public class CommonDataAccessFn : IDataAccessRepository, IStorageRepository
    {
        private readonly ILogger<CommonDataAccessFn> _logger;
        private readonly GlobalDataEntity _globalData;
        private readonly pmsdbContext _dbContext;
        public CommonDataAccessFn(
            ILogger<CommonDataAccessFn> logger,
            GlobalDataEntity globalData,
            pmsdbContext dbContext)
        {
            _logger = logger;
            _globalData = globalData;
            _dbContext = dbContext;
        }

        public async Task<object> GetPurchaseOrderById(long id)
        {
            PurchaseOrderDataFn purchaseOrderDataFn = new(_globalData, null);
            return purchaseOrderDataFn.GetPurchaseOrderById(id);
        }
        public async Task<PdfConfiguration> GetPdfConfiguration()
        {
            throw new NotImplementedException();
        }
        public async Task<long> AddFileUploadData(object fileUpload)
        {
            var storageDataFn = new StorageDataFn(_globalData);
            return await storageDataFn.AddFileUploadData((FileUploadTableVm)fileUpload);
        }
        public async Task<string> GetServiceSasUriForContainer(string containerName)
        {
            var storageDataFn = new StorageDataFn(_globalData);
            var token = storageDataFn.GetStorageContainerToken(containerName);
            return token.StorageAccountToken;
        }
    }
}