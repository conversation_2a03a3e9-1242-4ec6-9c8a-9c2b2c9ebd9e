﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class TransportDataFn
    {
        public List<TransportCompanyVm> GetAllTransport()
        {
            List<TransportCompanyVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.TransportCompanyMasters
                       where a.Disabled != true
                       select new TransportCompanyVm
                       {
                           TransportId = a.TransportId,
                           TransportCompanyName = a.TransportCompanyName,
                           TransportCompanyAddress = a.TransportCompanyAddress,
                           TransportCompanyContact = a.TransportCompanyContact,
                           TransportCompanyEmail = a.TransportCompanyEmail,
                           TransportVehicle = db.TransportVehicleTables.Where(x => x.TransportId == a.TransportId && x.Disabled != true).Select(x =>
                             new TransportVehicleVm
                             {
                                 VehicleId = x.VehicleId,
                                 TransportId = x.TransportId,
                                 VehicleNumber = x.VehicleNumber,
                                 VehicleType = x.VehicleType
                             }).ToList(),
                           Gst = a.Gst
                       }).OrderBy(x => x.TransportCompanyName).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateTransport(TransportCompanyVm trans)
        {
            using (var db = new Models.pmsdbContext())
            {
                TransportCompanyMaster res = new TransportCompanyMaster();
                if (trans.TransportId == 0)
                {
                    res.TransportCompanyName = trans.TransportCompanyName;
                    res.TransportCompanyAddress = trans.TransportCompanyAddress;
                    res.TransportCompanyContact = trans.TransportCompanyContact;
                    res.TransportCompanyEmail = trans.TransportCompanyEmail;
                    res.Gst = trans.Gst;
                    db.TransportCompanyMasters.Add(res);
                }
                else
                {
                    res = db.TransportCompanyMasters.Where(x => x.TransportId == trans.TransportId).FirstOrDefault();
                    if (res != null)
                    {
                        res.TransportCompanyName = trans.TransportCompanyName;
                        res.TransportCompanyAddress = trans.TransportCompanyAddress;
                        res.TransportCompanyContact = trans.TransportCompanyContact;
                        res.TransportCompanyEmail = trans.TransportCompanyEmail;
                        res.Gst = trans.Gst;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm AddUpdateTransportVehicle(TransportVehicleVm vehicle)
        {
            using (var db = new Models.pmsdbContext())
            {
                var rec = db.TransportVehicleTables.Where(x => x.TransportId == vehicle.TransportId && x.VehicleNumber == CommonFunctions.RemoveSpecialCharacters(vehicle.VehicleNumber)).FirstOrDefault();
                if (rec != null)
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                TransportVehicleTable res = new TransportVehicleTable();
                if (vehicle.VehicleId == 0)
                {
                    res.VehicleNumber = CommonFunctions.RemoveSpecialCharacters(vehicle.VehicleNumber);
                    res.VehicleType = vehicle.VehicleType;
                    res.TransportId = vehicle.TransportId;
                    db.TransportVehicleTables.Add(res);
                }
                else
                {
                    res = db.TransportVehicleTables.Where(x => x.VehicleId == vehicle.VehicleId).FirstOrDefault();
                    if (res != null)
                    {
                        res.VehicleNumber = CommonFunctions.RemoveSpecialCharacters(vehicle.VehicleNumber);
                        res.VehicleType = vehicle.VehicleType;
                        res.TransportId = vehicle.TransportId;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm DeleteTransport(long transportId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = db.TransportCompanyMasters.Where(x => x.TransportId == transportId).FirstOrDefault();
                if (res != null)
                {
                    db.TransportCompanyMasters.Remove(res);
                    db.SaveChanges();
                }
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Transport removed successfully");
            }
        }
        public ApiFunctionResponseVm DisableTransportCompany(TransportCompanyVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        TransportCompanyMaster item = db.TransportCompanyMasters.FirstOrDefault(x => x.TransportId == param.TransportId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
        public ApiFunctionResponseVm DisableTransportVehicle(TransportVehicleVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        TransportVehicleTable item = db.TransportVehicleTables.FirstOrDefault(x => x.VehicleId == param.VehicleId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
