﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class LacquerDataFn
    {
        public List<LacquerMasterVm> GetAllLacquers()
        {
            List<LacquerMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.LacquerMasters
                       where a.Disabled != true
                       select new LacquerMasterVm
                       {
                           LacquerMasterId = a.LacquerMasterId,
                           Name = a.Name,
                           Code = a.Code,
                           Description = a.Description,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           LacquerRawMaterial = (from op in db.LacquerRawMaterialTables
                                                 join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                 where op.LacquerMasterId == a.LacquerMasterId
                                                 select new LacquerRawMaterialTableVm
                                                 {
                                                     LacquerRawMaterialId = op.LacquerRawMaterialId,
                                                     LacquerMasterId = op.LacquerMasterId,
                                                     ProductId = op.ProductId,
                                                     ProductName = p.ProductName,
                                                     ProductCode = p.ProductCode,
                                                     Quantity = op.Quantity,
                                                     Unit = op.Unit,
                                                     Price = op.Price
                                                 }).ToList()
                       }).OrderByDescending(x => x.LacquerMasterId).ToList();
            }
            return res;
        }
        public ApiFunctionResponseVm AddLacquers(LacquerMasterVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                LacquerMaster mm = new LacquerMaster();
                mm.Name = mix.Name;
                mm.Code = mix.Code;
                mm.Description = mix.Description;
                mm.AddedBy = mix.AddedBy;
                mm.AddedDate = System.DateTime.Now;
                db.LacquerMasters.Add(mm);
                db.SaveChanges();
                foreach (var item in mix.LacquerRawMaterial)
                {
                    LacquerRawMaterialTable spt = new LacquerRawMaterialTable();
                    spt.LacquerMasterId = mm.LacquerMasterId;
                    spt.ProductId = item.ProductId;
                    spt.Quantity = item.Quantity;
                    spt.Unit = item.Unit;
                    spt.Price = item.Price;
                    db.LacquerRawMaterialTables.Add(spt);
                }
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public ApiFunctionResponseVm UpdateLacquers(LacquerMasterVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                LacquerMaster mm = db.LacquerMasters.FirstOrDefault(x => x.LacquerMasterId == mix.LacquerMasterId);
                mm.Name = mix.Name;
                mm.Code = mix.Code;
                mm.Description = mix.Description;
                mm.AddedBy = mix.AddedBy;
                mm.AddedDate = System.DateTime.Now;
                //db.LacquerMasters.Add(mm);
                db.SaveChanges();
                List<LacquerRawMaterialTable> stAllList = db.LacquerRawMaterialTables.Where(x => x.LacquerMasterId == mix.LacquerMasterId).ToList();
                var deleteRecords = stAllList.Except(stAllList.Where(o => mix.LacquerRawMaterial.Select(s => s.LacquerRawMaterialId).ToList().Contains(o.LacquerRawMaterialId))).ToList();
                var AddRecords = mix.LacquerRawMaterial.Where(x => x.LacquerRawMaterialId == 0).ToList();
                if (AddRecords.Count > 0)
                {
                    foreach (var item in AddRecords)
                    {
                        LacquerRawMaterialTable spt = new LacquerRawMaterialTable();
                        spt.LacquerMasterId = mm.LacquerMasterId;
                        spt.ProductId = item.ProductId;
                        spt.Quantity = item.Quantity;
                        spt.Unit = item.Unit;
                        spt.Price = item.Price;
                        db.LacquerRawMaterialTables.Add(spt);
                    }
                }

                var resrec = mix.LacquerRawMaterial.Where(x => x.LacquerRawMaterialId > 0).ToList();
                foreach (var itm in resrec)
                {
                    var rec = db.LacquerRawMaterialTables.Where(x => x.LacquerRawMaterialId == itm.LacquerRawMaterialId).FirstOrDefault();
                    if (rec != null)
                    {
                        rec.LacquerMasterId = itm.LacquerMasterId;
                        rec.ProductId = itm.ProductId;
                        rec.Quantity = itm.Quantity;
                        rec.Unit = itm.Unit;
                        rec.Price = itm.Price;
                    }
                }
                db.SaveChanges();
                if (deleteRecords.Count > 0)
                {
                    foreach (var item in deleteRecords)
                    {
                        var dr = db.LacquerRawMaterialTables.SingleOrDefault(x => x.LacquerRawMaterialId == item.LacquerRawMaterialId);
                        if (dr != null)
                            db.LacquerRawMaterialTables.Remove(dr);
                    }
                }
                if (deleteRecords.Count > 0)
                    db.LacquerRawMaterialTables.RemoveRange(deleteRecords);
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm DeleteLacquer(LacquerMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        LacquerMaster item = db.LacquerMasters.FirstOrDefault(x => x.LacquerMasterId == param.LacquerMasterId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}