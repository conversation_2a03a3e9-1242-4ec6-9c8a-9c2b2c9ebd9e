﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class DeptDataFn
    {
        public List<DeptMasterVm> GetAllDepts()
        {
            List<DeptMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.DeptMasters
                       select new DeptMasterVm
                       {
                           DeptId = a.DeptId,
                           DeptName = a.DeptName,
                           DeptCode = a.DeptCode,
                           DeptDesc = a.DeptDesc,
                           DeptAddedBy = a.DeptAddedBy,
                           DeptAddedDate = a.DeptAddedDate
                       }).OrderBy(x => x.DeptCode).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateDept(DeptMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.DeptId == 0)
                {
                    var rec = db.DeptMasters.Where(x => x.DeptCode == br.DeptCode).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                DeptMaster res = new DeptMaster();
                if (br.DeptId == 0)
                {
                    res.DeptName = br.DeptName;
                    res.DeptCode = br.DeptCode;
                    res.DeptDesc = br.DeptDesc;
                    res.DeptAddedBy = br.DeptAddedBy;
                    res.DeptAddedDate = System.DateTime.Now;
                    db.DeptMasters.Add(res);
                }
                else
                {
                    res = db.DeptMasters.Where(x => x.DeptId == br.DeptId).FirstOrDefault();
                    if (res != null)
                    {
                        res.DeptName = br.DeptName;
                        res.DeptCode = br.DeptCode;
                        res.DeptDesc = br.DeptDesc;
                        res.DeptAddedBy = br.DeptAddedBy;
                        res.DeptAddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                StoreDataFn sdf = new StoreDataFn();
                sdf.AddUpdateStore(new StoreMasterVm
                {
                    DeptId = res.DeptId,
                    StoreName = br.DeptName + " Store",
                    StoreCode = br.DeptCode + "_Store",
                    StoreAddedBy = br.DeptAddedBy,
                    StoreAddedDate = System.DateTime.Now
                });
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
    }
}
