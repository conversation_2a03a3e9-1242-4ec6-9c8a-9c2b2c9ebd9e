﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class DeliveryTermDataFn
    {
        public List<DeliveryTermMasterVm> GetAllDeliveryTerms()
        {
            List<DeliveryTermMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.DeliveryTermMasters
                       where a.Disabled != true
                       select new DeliveryTermMasterVm
                       {
                           DeliveryTermId = a.DeliveryTermId,
                           DeliveryTerm = a.DeliveryTerm,
                           DeliveryTermDesc = a.DeliveryTermDesc,
                           DeliveryTermAddedBy = a.DeliveryTermAddedBy,
                           DeliveryTermAddedDate = a.DeliveryTermAddedDate,
                           NumberOfDays = a.NumberOfDays
                       }).OrderBy(x => x.DeliveryTerm).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateDeliveryTerm(DeliveryTermMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.DeliveryTermId == 0)
                {
                    var rec = db.DeliveryTermMasters.Where(x => x.DeliveryTerm == br.DeliveryTerm && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                DeliveryTermMaster res = new DeliveryTermMaster();
                if (br.DeliveryTermId == 0)
                {
                    res.DeliveryTerm = br.DeliveryTerm;
                    res.DeliveryTermDesc = br.DeliveryTermDesc;
                    res.DeliveryTermAddedBy = br.DeliveryTermAddedBy;
                    res.DeliveryTermAddedDate = System.DateTime.Now;
                    res.NumberOfDays = br.NumberOfDays;
                    db.DeliveryTermMasters.Add(res);
                }
                else
                {
                    res = db.DeliveryTermMasters.Where(x => x.DeliveryTermId == br.DeliveryTermId).FirstOrDefault();
                    if (res != null)
                    {
                        res.DeliveryTerm = br.DeliveryTerm;
                        res.DeliveryTermDesc = br.DeliveryTermDesc;
                        res.DeliveryTermAddedBy = br.DeliveryTermAddedBy;
                        res.DeliveryTermAddedDate = System.DateTime.Now;
                        res.NumberOfDays = br.NumberOfDays;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DeleteDeliveryTerm(DeliveryTermMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        DeliveryTermMaster item = db.DeliveryTermMasters.FirstOrDefault(x => x.DeliveryTermId == param.DeliveryTermId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
