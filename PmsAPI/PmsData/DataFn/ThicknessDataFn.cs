﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class ThicknessDataFn
    {
        public List<ThicknessMasterVm> GetAllThicknessData()
        {
            List<ThicknessMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ThicknessMasters
                       where a.Disabled != true
                       select new ThicknessMasterVm
                       {
                           ThicknessId = a.ThicknessId,
                           ThicknessNumber = a.ThicknessNumber,
                           Unit = a.Unit,
                           Code = a.Code,
                           Description = a.Description,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderBy(x => x.Code).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateThickness(ThicknessMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.ThicknessId == 0)
                {
                    var rec = db.ThicknessMasters.Where(x => x.Code == br.Code && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                ThicknessMaster res = new ThicknessMaster();
                if (br.ThicknessId == 0)
                {

                    res.ThicknessNumber = br.ThicknessNumber;
                    res.Unit = br.Unit;
                    res.Code = br.Code;
                    res.Description = br.Description;
                    res.AddedBy = br.AddedBy;
                    res.AddedDate = System.DateTime.Now;
                    db.ThicknessMasters.Add(res);
                }
                else
                {
                    res = db.ThicknessMasters.Where(x => x.ThicknessId == br.ThicknessId).FirstOrDefault();
                    if (res != null)
                    {
                        res.ThicknessNumber = br.ThicknessNumber;
                        res.Unit = br.Unit;
                        res.Code = br.Code;
                        res.Description = br.Description;
                        res.AddedBy = br.AddedBy;
                        res.AddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DeleteThickness(ThicknessMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        ThicknessMaster item = db.ThicknessMasters.FirstOrDefault(x => x.ThicknessId == param.ThicknessId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
