﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class IoTDeviceDataFn
    {
        public GlobalDataEntity GlobalData;
        public IoTDeviceDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<KnittingFabricWeightInputTableVm> GetAllKnittingFabricStocks()
        {
            List<KnittingFabricWeightInputTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from kfwi in db.KnittingFabricWeightInputTables
                       join pm in db.ProductMasters on kfwi.ProductId equals pm.ProductId
                       join po in db.PurchaseOrderTables on kfwi.Poid equals po.Poid into pod
                       from po in pod.DefaultIfEmpty()
                       where kfwi.Disabled != true
                       select new KnittingFabricWeightInputTableVm
                       {
                           KnittingFabricWeightInputId = kfwi.KnittingFabricWeightInputId,
                           ProductId = kfwi.ProductId,
                           ProductName = pm.ProductName,
                           ProductCode = pm.ProductCode,
                           Weight = kfwi.Weight,
                           Unit = kfwi.Unit,
                           IsStockAccepted = kfwi.IsStockAccepted,
                           Status = kfwi.IsStockAccepted == true ? "Accepted" : "Pending",
                           Poid = kfwi.Poid,
                           PONumber = po.Ponumber,
                           AddedBy = kfwi.AddedBy,
                           AddedDate = kfwi.AddedDate,
                       }).OrderByDescending(x => x.AddedDate).ToList();
            }
            return res;
        }
        public List<KnittingFabricWeightInputTableVm> GetAllKnittingFabricStocksWithFilter(KnittingFabricWeightInputTableFilterVm filter)
        {
            List<KnittingFabricWeightInputTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from kfwi in db.KnittingFabricWeightInputTables
                       join pm in db.ProductMasters on kfwi.ProductId equals pm.ProductId
                       join po in db.PurchaseOrderTables on kfwi.Poid equals po.Poid into pod
                       from po in pod.DefaultIfEmpty()
                       where kfwi.Disabled != true
                       && (filter.FromDate == null || kfwi.AddedDate >= filter.FromDate)
                       && (filter.ToDate == null || kfwi.AddedDate <= filter.ToDate)
                       && (filter.ProductId == 0 || kfwi.ProductId == filter.ProductId)
                       && (filter.Poid == 0 || kfwi.Poid == filter.Poid)
                       && (filter.Status == null ||
                          (filter.Status == "Accepted" && kfwi.IsStockAccepted == true) ||
                          (filter.Status == "Pending" && kfwi.IsStockAccepted == false))
                       select new KnittingFabricWeightInputTableVm
                       {
                           KnittingFabricWeightInputId = kfwi.KnittingFabricWeightInputId,
                           ProductId = kfwi.ProductId,
                           ProductName = pm.ProductName,
                           ProductCode = pm.ProductCode,
                           Weight = kfwi.Weight,
                           Unit = kfwi.Unit,
                           IsStockAccepted = kfwi.IsStockAccepted,
                           Status = kfwi.IsStockAccepted == true ? "Accepted" : "Pending",
                           Poid = kfwi.Poid,
                           PONumber = po.Ponumber,
                           AddedBy = kfwi.AddedBy,
                           AddedDate = kfwi.AddedDate,
                           BambooRollWeightInKgs = kfwi.BambooRollWeightInKgs,
                       }).OrderByDescending(x => x.AddedDate).ToList();
            }
            return res;
        }
        public List<KnittingFabricWeightInputTableVm> GetKnittingFabricStocksByIds(List<long> fabricStockIds)
        {
            List<KnittingFabricWeightInputTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from kfwi in db.KnittingFabricWeightInputTables
                       join pm in db.ProductMasters on kfwi.ProductId equals pm.ProductId
                       join po in db.PurchaseOrderTables on kfwi.Poid equals po.Poid into pod
                       from po in pod.DefaultIfEmpty()
                       where fabricStockIds.Contains(kfwi.KnittingFabricWeightInputId)
                       && kfwi.Disabled == false
                       && kfwi.IsStockAccepted == false
                       && kfwi.Poid == null
                       select new KnittingFabricWeightInputTableVm
                       {
                           KnittingFabricWeightInputId = kfwi.KnittingFabricWeightInputId,
                           ProductId = kfwi.ProductId,
                           ProductName = pm.ProductName,
                           ProductCode = pm.ProductCode,
                           Weight = kfwi.Weight,
                           Unit = kfwi.Unit,
                           PONumber = po.Ponumber,
                           AddedBy = kfwi.AddedBy,
                           AddedDate = kfwi.AddedDate,
                       }).OrderByDescending(x => x.AddedDate).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddKnittingFabricWithWeight(KnittingFabricWeightInputTableVm item)
        {
            using (var db = new Models.pmsdbContext())
            {
                KnittingFabricWeightInputTable res = new()
                {
                    ProductId = item.ProductId,
                    Weight = item.Weight,
                    Unit = "Kgs",
                    BambooRollWeightInKgs = item.BambooRollWeightInKgs,
                    AddedBy = GlobalData.loggedInUser,
                    AddedDate = DateTime.Now,
                    IsStockAccepted = false,
                    Disabled = false
                };
                db.KnittingFabricWeightInputTables.Add(res);
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product Stock Recorded Successfully");
            }
        }

        public ApiFunctionResponseVm DeleteKnittingFabricRecord(long recordId)
        {
            using var db = new Models.pmsdbContext();

            var res = db.KnittingFabricWeightInputTables.Where(x => x.KnittingFabricWeightInputId == recordId).FirstOrDefault();
            if (res != null)
            {

                res.Disabled = true;
                res.DisabledBy = GlobalData.loggedInUser;
                res.DisabledDate = DateTime.Now;
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product Stock Removed Successfully");
        }
    }
}
