﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class MixingDataFn
    {
        public List<MixingMasterVm> GetAllMixings()
        {
            List<MixingMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.MixingMasters
                       where a.Disabled != true
                       select new MixingMasterVm
                       {
                           MixingId = a.MixingId,
                           Total = a.Total,
                           MixingName = a.MixingName,
                           Wastage = a.Wastage,
                           WastageType = a.WastageType,
                           WeightGsm = a.WeightGsm,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           MixingRawMaterial = (from op in db.MixingRawMaterialTables
                                                join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                where op.MixingId == a.MixingId
                                                select new MixingRawMaterialTableVm
                                                {
                                                    MixingRawMaterialId = op.MixingRawMaterialId,
                                                    MixingId = op.MixingId,
                                                    ProductId = op.ProductId,
                                                    ProductName = p.ProductName,
                                                    ProductCode = p.ProductCode,
                                                    Quantity = op.Quantity,
                                                    Scquantity = op.Scquantity,
                                                    AvgGsm = p.AvgGsm,
                                                    Unit = op.Unit,
                                                    Price = op.Price
                                                }).ToList()
                       }).OrderByDescending(x => x.MixingId).ToList();
            }
            return res;
        }
        public ApiFunctionResponseVm AddMixings(MixingMasterVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                MixingMaster mm = new MixingMaster();
                mm.MixingName = mix.MixingName;
                mm.Wastage = mix.Wastage;
                mm.WastageType = mix.WastageType;
                mm.WeightGsm = mix.WeightGsm;
                mm.Total = mix.Total;
                mm.AddedBy = mix.AddedBy;
                mm.AddedDate = System.DateTime.Now;
                db.MixingMasters.Add(mm);
                db.SaveChanges();
                foreach (var item in mix.MixingRawMaterial)
                {
                    MixingRawMaterialTable spt = new MixingRawMaterialTable();
                    spt.MixingId = mm.MixingId;
                    spt.ProductId = item.ProductId;
                    spt.Quantity = item.Quantity;
                    spt.Unit = item.Unit;
                    spt.Price = item.Price;
                    spt.Scquantity = item.Scquantity;
                    db.MixingRawMaterialTables.Add(spt);
                }
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public ApiFunctionResponseVm UpdateMixings(MixingMasterVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                MixingMaster mm = db.MixingMasters.FirstOrDefault(x => x.MixingId == mix.MixingId);
                mm.MixingName = mix.MixingName;
                mm.Wastage = mix.Wastage;
                mm.WastageType = mix.WastageType;
                mm.WeightGsm = mix.WeightGsm;
                mm.Total = mix.Total;
                mm.AddedBy = mix.AddedBy;
                mm.AddedDate = System.DateTime.Now;
                //db.MixingMasters.Add(mm);
                db.SaveChanges();
                List<MixingRawMaterialTable> stAllList = db.MixingRawMaterialTables.Where(x => x.MixingId == mix.MixingId).ToList();
                var deleteRecords = stAllList.Except(stAllList.Where(o => mix.MixingRawMaterial.Select(s => s.MixingRawMaterialId).ToList().Contains(o.MixingRawMaterialId))).ToList();
                var AddRecords = mix.MixingRawMaterial.Where(x => x.MixingRawMaterialId == 0).ToList();
                if (AddRecords.Count > 0)
                {
                    foreach (var item in AddRecords)
                    {
                        MixingRawMaterialTable spt = new MixingRawMaterialTable();
                        spt.MixingId = mm.MixingId;
                        spt.ProductId = item.ProductId;
                        spt.Quantity = item.Quantity;
                        spt.Unit = item.Unit;
                        spt.Price = item.Price;
                        spt.Scquantity = item.Scquantity;
                        db.MixingRawMaterialTables.Add(spt);
                    }
                }

                var resrec = mix.MixingRawMaterial.Where(x => x.MixingRawMaterialId > 0).ToList();
                foreach (var itm in resrec)
                {
                    var rec = db.MixingRawMaterialTables.Where(x => x.MixingRawMaterialId == itm.MixingRawMaterialId).FirstOrDefault();
                    if (rec != null)
                    {
                        rec.MixingId = itm.MixingId;
                        rec.ProductId = itm.ProductId;
                        rec.Quantity = itm.Quantity;
                        rec.Unit = itm.Unit;
                        rec.Price = itm.Price;
                        rec.Scquantity = itm.Scquantity;
                    }
                }
                db.SaveChanges();
                if (deleteRecords.Count > 0)
                {
                    foreach (var item in deleteRecords)
                    {
                        var dr = db.MixingRawMaterialTables.SingleOrDefault(x => x.MixingRawMaterialId == item.MixingRawMaterialId);
                        if (dr != null)
                            db.MixingRawMaterialTables.Remove(dr);
                    }
                }
                if (deleteRecords.Count > 0)
                    db.MixingRawMaterialTables.RemoveRange(deleteRecords);
                db.SaveChanges();

                db.AuditTables.Add(new AuditTable
                {
                    RecId = mix.MixingId,
                    TableName = "MixingMaster",
                    EntityName = "MixingMaster",
                    AddedBy = mix.AddedBy,
                    AddedDate = System.DateTime.Now,
                });
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm DeleteMixing(MixingMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        MixingMaster item = db.MixingMasters.FirstOrDefault(x => x.MixingId == param.MixingId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
