﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using PmsData.Models;
using PmsCommon;

namespace PmsData.DataFn
{
    public class GeneralConfigurationDataFn
    {
        public GlobalDataEntity GlobalData;
        public GeneralConfigurationDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ApiFunctionResponseVm AddConfig(ConfigTableVm Config)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        ConfigTable objConfig = new ConfigTable();
                        {
                            objConfig.ConfigItem = Config.ConfigItem;
                            objConfig.ConfigValue = Config.ConfigValue;
                            objConfig.AddedBy = GlobalData.loggedInUser;
                            objConfig.AddedDate = System.DateTime.Now;
                            
                            db.ConfigTables.Add(objConfig);
                            db.SaveChanges();
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, ex.Message);
                        throw;
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Configuration Added Successfully");
        }
        public List<ConfigTableVm> GetConfig()
        {
            List<ConfigTableVm> GetConfig = null;

            using (var db = new Models.pmsdbContext())
            {
                GetConfig = (from config in db.ConfigTables select new ConfigTableVm
                                {
                                    ConfigId = config.ConfigId,
                                    ConfigItem = config.ConfigItem,
                                    ConfigValue = config.ConfigValue,
                                    AddedBy = config.AddedBy,
                                    AddedDate = config.AddedDate,
                                }).OrderBy(c => c.ConfigId).ToList();
            }

            return GetConfig;
        }
        public ApiFunctionResponseVm GetConfigByConfigItem(string configItem)
        {
            ConfigTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = db.ConfigTables.Where(x => x.ConfigItem == configItem).Select(x => new ConfigTableVm { ConfigId = x.ConfigId, ConfigItem = x.ConfigItem, ConfigValue = x.ConfigValue }).FirstOrDefault();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
        }
        public ApiFunctionResponseVm EditConfig(ConfigTableVm Config)
        {
            if(Config.ConfigId > 0)
            {
                using (var db = new Models.pmsdbContext())
                {
                    using (var transaction = db.Database.BeginTransaction())
                    {
                        try
                        {
                            ConfigTable objConfig = db.ConfigTables.FirstOrDefault(C => C.ConfigId == Config.ConfigId);
                            {
                                objConfig.ConfigItem = Config.ConfigItem;
                                objConfig.ConfigValue = Config.ConfigValue;
                                objConfig.AddedBy = Config.AddedBy;
                                objConfig.AddedDate = Config.AddedDate;

                                db.SaveChanges();
                            }

                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Configuration Updated Successfully");
            }
            else { return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Configuration Id Not Found"); }
        }
    }
}
