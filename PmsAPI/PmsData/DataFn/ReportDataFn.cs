﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using PmsCommon;
using System.Net;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace PmsData.DataFn
{
    public class ReportDataFn
    {
        public GlobalDataEntity GlobalData;

        public ReportDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }

        public List<YieldReportVm> YieldReport(YieldReportRequestVm filter)
        {
            using (var db = new pmsdbContext())
            {
                var res = (from model in db.WorkPlanJumboMasters
                           join a in db.SaleOrderTables on model.SaleOrderId equals a.SaleOrderId
                           join sop in db.SaleOrderProductionTables on a.SaleOrderId equals sop.SaleOrderId
                           join sot in db.SaleOrderTimelineTables on a.SaleOrderId equals sot.SaleOrderId
                           join c in db.CustomerMasters on a.CustomerId equals c.CustomerId
                           join p in db.ProductMasters on sop.FabricProductId equals p.ProductId
                           where model.IsInspectionCompleted == true
                            && sot.Status == (int)ESalesOrderStatus.MoveToDispatch
                            && (filter.DateFrom == null || sot.AddedDate >= filter.DateFrom)
                            && (filter.DateTo == null || sot.AddedDate <= filter.DateTo)
                            && (filter.FabricProductId == 0 || filter.FabricProductId == null || p.ProductId == filter.FabricProductId)
                            && (filter.SaleFormulationCodeId == 0 || filter.SaleFormulationCodeId == null || a.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                            && (filter.FormulationCategoryId == 0 || filter.FormulationCategoryId == null || a.CategoryId == filter.FormulationCategoryId)
                            && (string.IsNullOrEmpty(filter.SaleOrderNumber) || a.SaleOrderNumber.Contains(filter.SaleOrderNumber))
                            && (filter.Status == null || a.Status == (int)filter.Status)
                            && (string.IsNullOrEmpty(filter.ArticleName) || sop.ManufacturingProductName.ToLower().Contains(filter.ArticleName.ToLower()))
                            && (filter.ColorId == 0 || filter.ColorId == null || sop.ColorId == filter.ColorId)
                            && (filter.GrainId == 0 || filter.GrainId == null || sop.GrainId == filter.GrainId)
                            && (filter.CustomerId == 0 || filter.CustomerId == null || a.CustomerId == filter.CustomerId)
                           select new WorkPlanJumboMasterVm
                           {
                               WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                               JumboRollDate = model.JumboRollDate,
                               JumboRollStartTime = model.JumboRollStartTime,
                               JumboRollEndTime = model.JumboRollEndTime,
                               JumboNo = model.JumboNo,
                               JumboCount = db.WorkPlanJumboMasters.Count(x => x.SaleOrderId == model.SaleOrderId),
                               FabricName = p.ProductName,
                               Rate = model.Rate,
                               Amount = model.Amount,
                               JumboRolQty = model.JumboRolQty,
                               ActualQuantity = model.ActualQuantity,
                               WastageEmbossing = model.WastageEmbossing,
                               WastageLacquer = model.WastageLacquer,
                               WastagePrint = model.WastagePrint,
                               WastageTumbling = model.WastageTumbling,
                               WastageVacuum = model.WastageVacuum,
                               Weight = model.Weight,
                               RackId = model.RackId,
                               StoreId = model.StoreId,
                               RackCode = model.RackCode,
                               RackName = model.RackName,
                               StoreCode = model.StoreCode,
                               StoreName = model.StoreName,
                               Remark = model.Remark,
                               AddedBy = model.AddedBy,
                               AddedDate = sot.AddedDate,
                               CustomerId = c.CustomerId,
                               CustomerName = c.CustomerName,
                               SaleOrderId = a.SaleOrderId,
                               SaleOrderNumber = a.SaleOrderNumber,
                               IsInspectionCompleted = model.IsInspectionCompleted,
                               Yield = model.Yield,
                               ManufacturingQuantity = sop.ManufacturingQuantity,
                               SaleOrderQuantity = sop.OrderQuantity,
                               SaleOrderCode = a.SaleOrderCode,
                               SaleOrderStatus = (PmsCommon.ESalesOrderStatus)a.Status,
                           }).OrderByDescending(x => x.WorkPlanJumboMasterId).Distinct().ToList();
                var wpjlist = res.Select(x => x.WorkPlanJumboMasterId).Distinct().ToList();
                var WorkPlanJumboMasterData = db.JumboInspectionTables.Where(x => wpjlist.Contains(x.WorkPlanJumboMasterId.Value)).ToList();
                foreach (var item in res)
                {
                    item.Yield = (item.FirstGrade + item.AGrade + item.SampleQuantity) * 100 / item.ManufacturingQuantity;
                    item.Amount = item.FirstGrade + item.AGrade + item.CUTPCGrade + item.FILMGrade + item.LOTGrade + item.NSGrade + item.WASTEGrade + item.SampleQuantity;
                    item.FirstGrade = WorkPlanJumboMasterData.Where(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "1st").AsEnumerable().Sum(x => x.Quantity) ?? 0;
                    item.FirstGradeCount = WorkPlanJumboMasterData.Count(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "1st");
                    item.AGrade = WorkPlanJumboMasterData.Where(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "A").Sum(x => x.Quantity) ?? 0;
                    item.AGradeCount = WorkPlanJumboMasterData.Count(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "A");
                    item.CUTPCGrade = WorkPlanJumboMasterData.Where(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "CUT-PC").Sum(x => x.Quantity) ?? 0;
                    item.CUTPCGradeCount = WorkPlanJumboMasterData.Count(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "CUT-PC");
                    item.FILMGrade = WorkPlanJumboMasterData.Where(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "FILM").Sum(x => x.Quantity) ?? 0;
                    item.FILMGradeCount = WorkPlanJumboMasterData.Count(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "FILM");
                    item.LOTGrade = WorkPlanJumboMasterData.Where(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "LOT").Sum(x => x.Quantity) ?? 0;
                    item.LOTGradeCount = WorkPlanJumboMasterData.Count(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "LOT");
                    item.NSGrade = WorkPlanJumboMasterData.Where(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "NS").Sum(x => x.Quantity) ?? 0;
                    item.NSGradeCount = WorkPlanJumboMasterData.Count(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "NS");
                    item.WASTEGrade = WorkPlanJumboMasterData.Where(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "WASTE").Sum(x => x.Quantity) ?? 0;
                    item.WASTEGradeCount = WorkPlanJumboMasterData.Count(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.Grade == "WASTE");
                    item.SampleQuantity = WorkPlanJumboMasterData.Where(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.RollType == "SAMPLE").Sum(x => x.Quantity) ?? 0;
                    item.SampleCount = WorkPlanJumboMasterData.Count(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId && x.RollType == "SAMPLE");
                }

                var data = res.Select(x =>
                      new YieldReportVm
                      {
                          SaleOrderId = x.SaleOrderId,
                          AddedDate = x.AddedDate,
                          SaleOrderNumber = x.SaleOrderNumber,
                          SaleOrderCode = x.SaleOrderCode,
                          CustomerName = x.CustomerName,
                          JumboCount = x.JumboCount,
                          FabricName = x.FabricName,
                          ActualQuantity = x.ActualQuantity,
                          FirstGrade = x.FirstGrade,
                          FirstGradeCount = x.FirstGradeCount,
                          AGrade = x.AGrade,
                          AGradeCount = x.AGradeCount,
                          LOTGrade = x.LOTGrade,
                          LOTGradeCount = x.LOTGradeCount,
                          NSGrade = x.NSGrade,
                          NSGradeCount = x.NSGradeCount,
                          CUTPCGrade = x.CUTPCGrade,
                          CUTPCGradeCount = x.CUTPCGradeCount,
                          FILMGrade = x.FILMGrade,
                          FILMGradeCount = x.FILMGradeCount,
                          WASTEGrade = x.WASTEGrade,
                          WASTEGradeCount = x.WASTEGradeCount,
                          Yield = x.Yield,
                          ManufacturingQuantity = x.ManufacturingQuantity,
                          SaleOrderQuantity = x.SaleOrderQuantity,
                          SampleQuantity = x.SampleQuantity,
                          SampleCount = x.SampleCount,
                          Amount = x.Amount,
                      }
                      ).ToList();

                var qwe = (from a in data
                           group a by new
                           {
                               a.SaleOrderId,
                               a.AddedDate,
                               a.SaleOrderNumber,
                               a.SaleOrderCode,
                               a.FabricName,
                               a.JumboCount,
                               a.ManufacturingQuantity,
                               a.SaleOrderQuantity
                           } into pg
                           select new YieldReportVm
                           {
                               SaleOrderId = pg.Key.SaleOrderId,
                               AddedDate = pg.Key.AddedDate,
                               SaleOrderNumber = pg.Key.SaleOrderNumber,
                               SaleOrderCode = pg.Key.SaleOrderCode,
                               CustomerName = pg.FirstOrDefault().CustomerName,
                               JumboCount = pg.Key.JumboCount,
                               FabricName = pg.Key.FabricName,
                               ActualQuantity = pg.Sum(x => x.ActualQuantity),
                               FirstGrade = pg.Sum(x => x.FirstGrade),
                               FirstGradeCount = pg.Sum(x => x.FirstGradeCount),
                               AGrade = pg.Sum(x => x.AGrade),
                               AGradeCount = pg.Sum(x => x.AGradeCount),
                               LOTGrade = pg.Sum(x => x.LOTGrade),
                               LOTGradeCount = pg.Sum(x => x.LOTGradeCount),
                               NSGrade = pg.Sum(x => x.NSGrade),
                               NSGradeCount = pg.Sum(x => x.NSGradeCount),
                               CUTPCGrade = pg.Sum(x => x.CUTPCGrade),
                               CUTPCGradeCount = pg.Sum(x => x.CUTPCGradeCount),
                               FILMGrade = pg.Sum(x => x.FILMGrade),
                               FILMGradeCount = pg.Sum(x => x.FILMGradeCount),
                               WASTEGrade = pg.Sum(x => x.WASTEGrade),
                               WASTEGradeCount = pg.Sum(x => x.WASTEGradeCount),
                               Yield = pg.Sum(x => x.Yield),
                               ManufacturingQuantity = pg.Key.ManufacturingQuantity,
                               SaleOrderQuantity = pg.Key.SaleOrderQuantity,
                               SampleQuantity = pg.Sum(x => x.SampleQuantity),
                               SampleCount = pg.Sum(x => x.SampleCount),
                               Amount = pg.Sum(x => x.Amount)
                           }).OrderBy(x => x.AddedDate).ToList();

                return qwe;
            }
        }

        public List<WastageReportVm> WastageReport(ReportRequestVm filter)
        {
            using (var db = new pmsdbContext())
            {
                var res = (from model in db.WorkPlanJumboMasters
                           join a in db.SaleOrderTables on model.SaleOrderId equals a.SaleOrderId
                           join wo in db.WorkPlanOrders on a.SaleOrderId equals wo.OrderId
                           join wp in db.WorkPlanMasters on wo.WorkplanId equals wp.WorkPlanId
                           join sot in db.SaleOrderTimelineTables on a.SaleOrderId equals sot.SaleOrderId
                           where model.IsInspectionCompleted == true && wp.Disabled != true
                           && sot.Status == (int)ESalesOrderStatus.MoveToDispatch
                           && (filter.DateFrom == null || sot.AddedDate >= filter.DateFrom)
                           && (filter.DateTo == null || sot.AddedDate <= filter.DateTo)
                           select new WorkPlanJumboMasterVm
                           {
                               Amount = model.Amount,
                               JumboRolQty = model.JumboRolQty,
                               JumboNo = model.JumboNo,
                               WorkPlanNo = wp.WorkPlanNo,
                               ActualQuantity = model.ActualQuantity,
                               WastageEmbossing = model.WastageEmbossing,
                               WastageLacquer = model.WastageLacquer,
                               WastagePrint = model.WastagePrint,
                               WastageTumbling = model.WastageTumbling,
                               WastageVacuum = model.WastageVacuum,
                               Weight = model.Weight,
                               SaleOrderId = a.SaleOrderId,
                               SaleOrderNumber = a.SaleOrderNumber,
                               IsInspectionCompleted = model.IsInspectionCompleted,
                               SaleOrderCode = a.SaleOrderCode,
                               SaleOrderStatus = (PmsCommon.ESalesOrderStatus)a.Status,
                           }).OrderByDescending(x => x.SaleOrderId).ToList().Distinct().ToList();

                var qwe = (from a in res
                           group a by new
                           {
                               a.SaleOrderId,
                               a.SaleOrderNumber,
                               a.SaleOrderCode,
                               a.WorkPlanNo,
                               a.JumboNo
                           } into pg
                           select new WastageReportVm
                           {
                               SaleOrderId = pg.Key.SaleOrderId,
                               WorkPlanNo = pg.Key.WorkPlanNo,
                               JumboNo = pg.Key.JumboNo,
                               SaleOrderNumber = pg.Key.SaleOrderNumber,
                               SaleOrderCode = pg.Key.SaleOrderCode,
                               ActualQuantity = pg.Sum(x => x.ActualQuantity),
                               JumboRolQty = pg.Sum(x => x.JumboRolQty),
                               WastageEmbossing = pg.Sum(x => x.WastageEmbossing),
                               WastageLacquer = pg.Sum(x => x.WastageLacquer),
                               WastagePrint = pg.Sum(x => x.WastagePrint),
                               WastageTumbling = pg.Sum(x => x.WastageTumbling),
                               WastageVacuum = pg.Sum(x => x.WastageVacuum)
                           }).OrderByDescending(x => x.SaleOrderId).ToList();

                return qwe;
            }
        }

        public List<ProductionPlanningReportResponseVm> ProductionPlanningReport(ProductionPlanningReportRequestVm filter)
        {
            using (var db = new pmsdbContext())
            {
                var res = (from so in db.SaleOrderTables
                           join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                           join sopc in db.SaleOrderProductionCompleteTables on so.SaleOrderId equals sopc.SaleOrderId into sopcd
                           from sopc in sopcd.DefaultIfEmpty()
                           join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                           join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                           join fc in db.SaleFormulationCodeMasters on so.SaleFormulationCodeId equals fc.SaleFormulationCodeId
                           join fcp in db.ProductMasters on sop.FabricProductId equals fcp.ProductId into fcpd
                           from fcp in fcpd.DefaultIfEmpty()
                           join gr in db.GrainMasters on sop.GrainId equals gr.GrainId into grn
                           from gr in grn.DefaultIfEmpty()
                           join cl in db.ColorMasters on sop.ColorId equals cl.ColorId into cln
                           from cl in cln.DefaultIfEmpty()
                           join tk in db.ThicknessMasters on sop.Thick equals tk.ThicknessId into tkn
                           from tk in tkn.DefaultIfEmpty()
                           join wth in db.WidthMasters on sop.Width equals wth.WidthId into wthn
                           from wth in wthn.DefaultIfEmpty()
                           join cust in db.CustomerMasters on so.CustomerId equals cust.CustomerId
                           where so.Status != (int)ESalesOrderStatus.NotYet && wp.Disabled != true
                            && (((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday") && (filter.WorkPlanFromDate == null || wp.WorkPlanDate.Date >= filter.WorkPlanFromDate.Value.Date)
                            && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday") && (filter.WorkPlanToDate == null || wp.WorkPlanDate.Date <= filter.WorkPlanToDate.Value.Date))
                            || ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime") && (filter.WorkPlanFromDate == null || wp.WorkPlanDate >= filter.WorkPlanFromDate)
                            && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime") && (filter.WorkPlanToDate == null || wp.WorkPlanDate <= filter.WorkPlanToDate)))
                            && (string.IsNullOrEmpty(filter.ProductType) || fc.SaleFormulationCode.ToLower().StartsWith(filter.ProductType.ToLower()))
                            && (string.IsNullOrEmpty(filter.WorkShift) || wp.WorkShift == filter.WorkShift)
                            && (filter.ProductionLineNo == 0 || filter.ProductionLineNo == null || wp.ProductionLineNo == filter.ProductionLineNo)
                            && (filter.SaleFormulationCodeId == 0 || filter.SaleFormulationCodeId == null || so.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                            && (string.IsNullOrEmpty(filter.SaleOrderNumber) || so.SaleOrderNumber.Contains(filter.SaleOrderNumber))
                            && (filter.Status == null || so.Status == (int)filter.Status)
                            && (string.IsNullOrEmpty(filter.AddedBy) || so.AddedBy.ToLower().Contains(filter.AddedBy))
                            && (string.IsNullOrEmpty(filter.ArticleName) || sop.ManufacturingProductName.ToLower().Contains(filter.ArticleName.ToLower()))
                            && (filter.ColorId == 0 || filter.ColorId == null || sop.ColorId == filter.ColorId)
                            && (filter.GrainId == 0 || filter.GrainId == null || sop.GrainId == filter.GrainId)
                            && (filter.CustomerId == 0 || filter.CustomerId == null || so.CustomerId == filter.CustomerId)
                           select new ProductionPlanningReportResponseVm
                           {
                               OrderDate = so.SaleOrderDate.Value.ToString("dd/MM/yyyy"),
                               SaleOrderNo = so.SaleOrderNumber,
                               SaleOrderId = so.SaleOrderId,
                               WorkPlanNo = wp.WorkPlanNo,
                               PCode = fc.SaleFormulationCode,
                               Grain = gr.GrainName,
                               Colours = cl.ColorName,
                               Width = wth.WidthNumber,
                               Fabric = fcp.ProductName,
                               Thick = tk.ThicknessNumber,
                               OrderQty = sop.OrderQuantity,
                               MFGQty = sop.ManufacturingQuantity,
                               PreSkin = sop.PreSkinGsm,
                               Adhesive = sop.AdhesiveGsm,
                               Foam = sop.FoamGsm,
                               FabricGsm = sop.FabricGsm,
                               Skin = sop.SkinGsm,
                               ItemName = sop.ManufacturingProductName,
                               Finish = so.FinishCode,
                               PartyName = cust.CustomerName,
                               //Rate = sop.SalePrice,
                               Remarks = so.Remarks,
                               ProductionCompletedBy = sopc.Addedby,
                               ProductionCompletedDate = sopc.AddedDate,
                               ProductionCompletionRemarks = so.ProductionCompletionRemarks,
                               Status = (ESalesOrderStatus)so.Status,
                           }).OrderBy(x => x.Grain).ThenBy(y => y.WorkPlanNo).ToList();
                return res;
            }
        }

        public List<ProductionPlanningReportResponseVm> ProductionStatusReport(ProductionPlanningReportRequestVm filter)
        {
            using (var db = new pmsdbContext())
            {
                var res = (from so in db.SaleOrderTables
                           join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                           join sopc in db.SaleOrderProductionCompleteTables on so.SaleOrderId equals sopc.SaleOrderId into sopcd
                           from sopc in sopcd.DefaultIfEmpty()
                           join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                           join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                           join fc in db.SaleFormulationCodeMasters on so.SaleFormulationCodeId equals fc.SaleFormulationCodeId
                           join fcp in db.ProductMasters on sop.FabricProductId equals fcp.ProductId into fcpd
                           from fcp in fcpd.DefaultIfEmpty()
                           join gr in db.GrainMasters on sop.GrainId equals gr.GrainId into grn
                           from gr in grn.DefaultIfEmpty()
                           join cl in db.ColorMasters on sop.ColorId equals cl.ColorId into cln
                           from cl in cln.DefaultIfEmpty()
                           join tk in db.ThicknessMasters on sop.Thick equals tk.ThicknessId into tkn
                           from tk in tkn.DefaultIfEmpty()
                           join wth in db.WidthMasters on sop.Width equals wth.WidthId into wthn
                           from wth in wthn.DefaultIfEmpty()
                           join cust in db.CustomerMasters on so.CustomerId equals cust.CustomerId
                           where so.Status != (int)ESalesOrderStatus.NotYet && wp.Disabled != true
                            && (((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday") && (filter.WorkPlanFromDate == null || wp.WorkPlanDate.Date >= filter.WorkPlanFromDate.Value.Date)
                            && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday") && (filter.WorkPlanToDate == null || wp.WorkPlanDate.Date <= filter.WorkPlanToDate.Value.Date))
                            || ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime") && (filter.WorkPlanFromDate == null || wp.WorkPlanDate >= filter.WorkPlanFromDate)
                            && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime") && (filter.WorkPlanToDate == null || wp.WorkPlanDate <= filter.WorkPlanToDate)))
                            && (string.IsNullOrEmpty(filter.ProductType) || fc.SaleFormulationCode.ToLower().StartsWith(filter.ProductType.ToLower()))
                            && (string.IsNullOrEmpty(filter.WorkShift) || wp.WorkShift == filter.WorkShift)
                            && (filter.ProductionLineNo == 0 || filter.ProductionLineNo == null || wp.ProductionLineNo == filter.ProductionLineNo)
                            && (filter.SaleFormulationCodeId == 0 || filter.SaleFormulationCodeId == null || so.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                            && (string.IsNullOrEmpty(filter.SaleOrderNumber) || so.SaleOrderNumber.Contains(filter.SaleOrderNumber))
                            && (filter.Status == null || so.Status == (int)filter.Status)
                            && (string.IsNullOrEmpty(filter.AddedBy) || so.AddedBy.ToLower().Contains(filter.AddedBy))
                            && (string.IsNullOrEmpty(filter.ArticleName) || sop.ManufacturingProductName.ToLower().Contains(filter.ArticleName.ToLower()))
                            && (filter.ColorId == 0 || filter.ColorId == null || sop.ColorId == filter.ColorId)
                            && (filter.GrainId == 0 || filter.GrainId == null || sop.GrainId == filter.GrainId)
                            && (filter.CustomerId == 0 || filter.CustomerId == null || so.CustomerId == filter.CustomerId)
                           select new ProductionPlanningReportResponseVm
                           {
                               OrderDate = so.SaleOrderDate.Value.ToString("dd/MM/yyyy"),
                               SaleOrderNo = so.SaleOrderNumber,
                               SaleOrderId = so.SaleOrderId,
                               WorkPlanNo = wp.WorkPlanNo,
                               PCode = fc.SaleFormulationCode,
                               Grain = gr.GrainName,
                               Colours = cl.ColorName,
                               Width = wth.WidthNumber,
                               Fabric = fcp.ProductName,
                               Thick = tk.ThicknessNumber,
                               OrderQty = sop.OrderQuantity,
                               MFGQty = sop.ManufacturingQuantity,
                               PreSkin = sop.PreSkinGsm,
                               Adhesive = sop.AdhesiveGsm,
                               Foam = sop.FoamGsm,
                               FabricGsm = sop.FabricGsm,
                               Skin = sop.SkinGsm,
                               ItemName = sop.ManufacturingProductName,
                               Finish = so.FinishCode,
                               PartyName = cust.CustomerName,
                               Remarks = so.Remarks,
                               ProductionCompletedBy = sopc.Addedby,
                               ProductionCompletedDate = sopc.AddedDate,
                               ProductionCompletionRemarks = so.ProductionCompletionRemarks,
                               Status = (ESalesOrderStatus)so.Status,
                               JumboData = (from wpjm in db.WorkPlanJumboMasters
                                            where wpjm.SaleOrderId == so.SaleOrderId
                                            select new WorkPlanJumboMasterVm
                                            {
                                                JumboRolQty = wpjm.JumboRolQty,
                                                JumboRollStartTime = wpjm.JumboRollStartTime,
                                                JumboRollEndTime = wpjm.JumboRollEndTime
                                            }).ToList()
                           }).OrderByDescending(x => x.ProductionCompletedDate).ToList();

                var orderidlist = res.Select(x => x.SaleOrderId).Distinct().ToList();
                var insform = (from a in db.InspectionSaleFormulationCodeMasters
                               join la in db.InspectionFormulationCodeMixingTables on a.InspectionSaleFormulationCodeId equals la.InspectionSaleFormulationCodeId
                               join p in db.ProductMasters on a.FabricProductId equals p.ProductId
                               where orderidlist.Contains(la.SaleOrderId.Value)
                               select new
                               {
                                   SaleOrderId = la.SaleOrderId,
                                   FabricProductId = a.FabricProductId,
                                   FabricProductName = p.ProductName,
                               }).Distinct().ToList();

                foreach (var item in res)
                {
                    item.Fabric = insform != null && insform.FirstOrDefault(x => x.SaleOrderId == item.SaleOrderId) != null ? insform.FirstOrDefault(x => x.SaleOrderId == item.SaleOrderId).FabricProductName : item.Fabric;
                    var calres = new CalculationsDataFn();
                    item.PrdLineSpeed = calres.GetProductionLineSpeedIncAllJumbo(item.JumboData);
                }
                return res;
            }
        }

        public List<PasteConsumptionReportResponseVm> PasteConsumptionReport(ProductionPlanningReportRequestVm filter)
        {
            using (var db = new pmsdbContext())
            {
                var res = (from so in db.SaleOrderTables
                           join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                           join insfcm in db.InspectionFormulationCodeMixingTables on so.SaleOrderId equals insfcm.SaleOrderId into insfcmd
                           from insfcm in insfcmd.DefaultIfEmpty()
                           join sopc in db.SaleOrderProductionCompleteTables on so.SaleOrderId equals sopc.SaleOrderId
                           join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                           join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                           join fc in db.SaleFormulationCodeMasters on so.SaleFormulationCodeId equals fc.SaleFormulationCodeId
                           join fcp in db.ProductMasters on sop.FabricProductId equals fcp.ProductId into fcpd
                           from fcp in fcpd.DefaultIfEmpty()
                           join gr in db.GrainMasters on sop.GrainId equals gr.GrainId into grn
                           from gr in grn.DefaultIfEmpty()
                           join cl in db.ColorMasters on sop.ColorId equals cl.ColorId into cln
                           from cl in cln.DefaultIfEmpty()
                           join tk in db.ThicknessMasters on sop.Thick equals tk.ThicknessId into tkn
                           from tk in tkn.DefaultIfEmpty()
                           join wth in db.WidthMasters on sop.Width equals wth.WidthId into wthn
                           from wth in wthn.DefaultIfEmpty()
                           join cust in db.CustomerMasters on so.CustomerId equals cust.CustomerId
                           where so.Status != (int)ESalesOrderStatus.NotYet && wp.Disabled != true
                            && (((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday") && (filter.WorkPlanFromDate == null || sopc.AddedDate.Value.Date >= filter.WorkPlanFromDate.Value.Date)
                            && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday") && (filter.WorkPlanToDate == null || sopc.AddedDate.Value.Date <= filter.WorkPlanToDate.Value.Date))
                            || ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime") && (filter.WorkPlanFromDate == null || sopc.AddedDate >= filter.WorkPlanFromDate)
                            && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime") && (filter.WorkPlanToDate == null || sopc.AddedDate <= filter.WorkPlanToDate)))
                            && (string.IsNullOrEmpty(filter.ProductType) || fc.SaleFormulationCode.ToLower().StartsWith(filter.ProductType.ToLower()))
                            && (string.IsNullOrEmpty(filter.WorkShift) || wp.WorkShift == filter.WorkShift)
                            && (filter.ProductionLineNo == 0 || filter.ProductionLineNo == null || wp.ProductionLineNo == filter.ProductionLineNo)
                            && (filter.SaleFormulationCodeId == 0 || filter.SaleFormulationCodeId == null || so.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                            && (string.IsNullOrEmpty(filter.SaleOrderNumber) || so.SaleOrderNumber.Contains(filter.SaleOrderNumber))
                            && (filter.Status == null || so.Status == (int)filter.Status)
                            && (string.IsNullOrEmpty(filter.AddedBy) || so.AddedBy.ToLower() == filter.AddedBy)
                            && (filter.ColorId == 0 || filter.ColorId == null || sop.ColorId == filter.ColorId)
                            && (filter.GrainId == 0 || filter.GrainId == null || sop.GrainId == filter.GrainId)
                            && (filter.CustomerId == 0 || filter.CustomerId == null || so.CustomerId == filter.CustomerId)
                           select new PasteConsumptionReportResponseVm
                           {
                               ProductionCompletedDate = sopc.AddedDate,
                               SaleOrderNo = so.SaleOrderNumber,
                               WorkPlanNo = wp.WorkPlanNo,
                               PCode = fc.SaleFormulationCode,
                               Grain = gr.GrainName,
                               Colours = cl.ColorName,
                               Width = wth.WidthNumber,
                               Fabric = fcp.ProductName,
                               Thick = tk.ThicknessNumber,
                               OrderQty = sop.OrderQuantity,
                               MFGQty = sop.ManufacturingQuantity,
                               FabricGsm = sopc.FabricGsm,
                               ItemName = sop.ManufacturingProductName,
                               Finish = so.FinishCode,
                               PartyName = cust.CustomerName,
                               ExtraProduction = sop.ExtraProduction,
                               LMConstant = sop.Lmconstant,
                               StdAdhesiveGsm = sop.AdhesiveGsm,
                               StdPreSkinGsm = sop.PreSkinGsm,
                               StdSkinGsm = sop.SkinGsm,
                               StdFoamGsm = sop.FoamGsm,
                               ExtraPreSkinGsm = sopc.PreSkinScGsm - sop.PreSkinGsm,
                               ExtraSkinGsm = sopc.SkinScGsm - sop.SkinGsm,
                               ExtraFoamGsm = sopc.FoamScGsm - sop.FoamGsm,
                               ExtraAdhesiveGsm = sopc.AdhesiveScGsm - sop.AdhesiveGsm,

                               SaleOrderCompleteProduction = new SaleOrderProductionCompleteTableVm
                               {
                                   PreSkinRemainingPasteQty = sopc.PreSkinRemainingPasteQty,
                                   SkinRemainingPasteQty = sopc.SkinRemainingPasteQty,
                                   FoamRemainingPasteQty = sopc.FoamRemainingPasteQty,
                                   AdhesiveRemainingPasteQty = sopc.AdhesiveRemainingPasteQty,
                                   PreSkinActualPasteQty = sopc.PreSkinActualPasteQty,
                                   SkinActualPasteQty = sopc.SkinActualPasteQty,
                                   FoamActualPasteQty = sopc.FoamActualPasteQty,
                                   AdhesiveActualPasteQty = sopc.AdhesiveActualPasteQty,
                                   PreSkinScGsm = sopc.PreSkinScGsm ?? sopc.PreSkinGsm,
                                   SkinScGsm = sopc.SkinScGsm ?? sopc.SkinGsm,
                                   FoamScGsm = sopc.FoamScGsm ?? sopc.FoamGsm,
                                   AdhesiveScGsm = sopc.AdhesiveScGsm ?? sopc.AdhesiveGsm,
                                   PreSkinGsm = sopc.PreSkinGsm,
                                   SkinGsm = sopc.SkinGsm,
                                   FoamGsm = sopc.FoamGsm,
                                   AdhesiveGsm = sopc.AdhesiveGsm,
                                   FabricGsm = sopc.FabricGsm,
                                   ManufacturedQuantity = sopc.ManufacturedQuantity,
                                   Addedby = sopc.Addedby,
                                   AddedDate = sopc.AddedDate
                               },

                               ProductionCompletionRemarks = so.ProductionCompletionRemarks,
                               Status = (ESalesOrderStatus)so.Status
                           }).Distinct().ToList();

                foreach (var item in res)
                {
                    CalculationsDataFn fcd = new();
                    FormulationProductCalculatePasteReqRequest StdRequest = new()
                    {
                        TotalProductionQty = decimal.Round(item.OrderQty.Value + (item.OrderQty.Value * item.ExtraProduction.Value) / 100, 2)
                    };
                    var soprequest = new SaleOrderProductionTableVm
                    {
                        PreSkinGsm = (item.StdPreSkinGsm ?? 0) > 0 ? decimal.Round(item.StdPreSkinGsm.Value, 2) : 0,
                        SkinGsm = (item.StdSkinGsm ?? 0) > 0 ? decimal.Round(item.StdSkinGsm.Value, 2) : 0,
                        FoamGsm = (item.StdFoamGsm ?? 0) > 0 ? decimal.Round(item.StdFoamGsm.Value, 2) : 0,
                        AdhesiveGsm = (item.StdAdhesiveGsm ?? 0) > 0 ? decimal.Round(item.StdAdhesiveGsm.Value, 2) : 0,
                        LMConstant = item.LMConstant
                    };
                    StdRequest.SaleOrderProduction = soprequest;

                    var StdAllPasteWeight = fcd.GetFormulationProductCalculatePasteReqQuantity(StdRequest);

                    item.StdPreSkinWeight = decimal.Round(StdAllPasteWeight.PreSkinGsmPasteReq.Value, 2);
                    item.StdSkinWeight = decimal.Round(StdAllPasteWeight.SkinGsmPasteReq.Value, 2);
                    item.StdFoamWeight = decimal.Round(StdAllPasteWeight.FoamGsmPasteReq.Value, 2);
                    item.StdAdhesiveWeight = decimal.Round(StdAllPasteWeight.AdhesiveGsmPasteReq.Value, 2);
                }
                return res.OrderByDescending(x => x.ProductionCompletedDate).ToList();
            }
        }

        public List<SalesReportResponseVm> SalesReport(SalesReportRequestVm filter)
        {
            using var db = new pmsdbContext();
            var res = (from so in db.SaleOrderTables
                       join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                       join fc in db.SaleFormulationCodeMasters on so.SaleFormulationCodeId equals fc.SaleFormulationCodeId
                       join fcp in db.ProductMasters on fc.FabricProductId equals fcp.ProductId
                       join gr in db.GrainMasters on sop.GrainId equals gr.GrainId into grn
                       from gr in grn.DefaultIfEmpty()
                       join cl in db.ColorMasters on sop.ColorId equals cl.ColorId into cln
                       from cl in cln.DefaultIfEmpty()
                       join tk in db.ThicknessMasters on sop.Thick equals tk.ThicknessId into tkn
                       from tk in tkn.DefaultIfEmpty()
                       join wth in db.WidthMasters on sop.Width equals wth.WidthId into wthn
                       from wth in wthn.DefaultIfEmpty()
                       join cust in db.CustomerMasters on so.CustomerId equals cust.CustomerId
                       where (((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "addeddate")
                                && (filter.FromAddedDate == null || so.AddedDate.Value.Date >= filter.FromAddedDate.Value.Date)
                        && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "addeddate")
                                && (filter.ToAddedDate == null || so.AddedDate.Value.Date <= filter.ToAddedDate.Value.Date))
                        || ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "saleorderdate")
                                && (filter.FromSaleOrderDate == null || so.SaleOrderDate.Value.Date >= filter.FromSaleOrderDate.Value.Date)
                        && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "saleorderdate")
                                && (filter.ToSaleOrderDate == null || so.SaleOrderDate.Value.Date <= filter.ToSaleOrderDate.Value.Date))
                        || ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "deliverydate")
                                && (filter.FromDeliveryDate == null || so.DeliveryDate.Value.Date >= filter.FromDeliveryDate.Value.Date)
                        && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "deliverydate")
                                && (filter.ToDeliveryDate == null || so.DeliveryDate.Value.Date <= filter.ToDeliveryDate.Value.Date))
                        || ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "addeddate")
                                && (filter.FromAddedDate == null || so.AddedDate >= filter.FromAddedDate)
                        && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "addeddate")
                                && (filter.ToAddedDate == null || so.AddedDate <= filter.ToAddedDate))
                        || ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "saleorderdate")
                                && (filter.FromSaleOrderDate == null || so.SaleOrderDate >= filter.FromSaleOrderDate)
                        && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "saleorderdate")
                                && (filter.ToSaleOrderDate == null || so.SaleOrderDate <= filter.ToSaleOrderDate))
                        || ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "deliverydate")
                                && (filter.FromDeliveryDate == null || so.DeliveryDate >= filter.FromDeliveryDate)
                        && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime")
                                && (string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "deliverydate")
                                && (filter.ToDeliveryDate == null || so.DeliveryDate <= filter.ToDeliveryDate)))

                        && (filter.SaleFormulationCodeId == 0 || filter.SaleFormulationCodeId == null || so.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                        && (string.IsNullOrEmpty(filter.SaleOrderNumber) || so.SaleOrderNumber.Contains(filter.SaleOrderNumber))
                        && (filter.Status == null || so.Status == (int)filter.Status)
                        && (string.IsNullOrEmpty(filter.AddedBy) || so.AddedBy.ToLower().Contains(filter.AddedBy))
                        && (string.IsNullOrEmpty(filter.ArticleName) || sop.ManufacturingProductName.ToLower().Contains(filter.ArticleName.ToLower()))
                        && (filter.ColorId == 0 || filter.ColorId == null || sop.ColorId == filter.ColorId)
                        && (filter.GrainId == 0 || filter.GrainId == null || sop.GrainId == filter.GrainId)
                        && (filter.CustomerId == 0 || filter.CustomerId == null || so.CustomerId == filter.CustomerId)
                        && (string.IsNullOrEmpty(filter.ProductType) || fc.SaleFormulationCode.ToLower().StartsWith(filter.ProductType.ToLower()))
                        && (string.IsNullOrEmpty(filter.OrderType) || so.SaleOrderType.ToLower().Contains(filter.OrderType.ToLower()))
                        && (string.IsNullOrEmpty(filter.OrderStatus) || so.SaleOrderStatus == filter.OrderStatus)
                        && (filter.ThicknessId == 0 || filter.ThicknessId == null || sop.Thick == filter.ThicknessId)
                       select new SalesReportResponseVm
                       {
                           OrderDate = so.SaleOrderDate.Value.ToString("dd/MM/yyyy"),
                           SaleOrderNo = so.SaleOrderNumber,
                           PCode = fc.SaleFormulationCode,
                           Grain = gr.GrainName,
                           Colours = cl.ColorName,
                           Width = wth.WidthNumber,
                           Fabric = fcp.ProductName,
                           Thick = tk.ThicknessNumber,
                           OrderQty = sop.OrderQuantity,
                           PreSkin = fc.PreSkinGsm,
                           Adhesive = fc.AdhesiveGsm,
                           Foam = fc.FoamGsm,
                           FabricGsm = fc.FabricGsm,
                           Skin = fc.SkinGsm,
                           ItemName = sop.ManufacturingProductName,
                           Finish = so.FinishCode,
                           PartyName = cust.CustomerName,
                           Remarks = so.Remarks,
                           Status = (ESalesOrderStatus)so.Status,
                           OrderStatus = so.SaleOrderStatus,
                           CreatedDate = so.AddedDate,
                           CreatedBy = so.AddedBy
                       }).OrderByDescending(x => x.CreatedDate).ToList();
            return res;
        }

        public TopSellingProductsResponseVm GetTopSellingProducts(TopSellingProductsRequestVm filter)
        {
            using var db = new pmsdbContext();
            var query = (from so in db.SaleOrderTables
                         join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                         join fc in db.SaleFormulationCodeMasters on so.SaleFormulationCodeId equals fc.SaleFormulationCodeId
                         join tk in db.ThicknessMasters on sop.Thick equals tk.ThicknessId into tkn
                         from tk in tkn.DefaultIfEmpty()
                         where ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday") && (filter.DateFrom == null || so.AddedDate.Value.Date >= filter.DateFrom.Value.Date)
                              && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "fullday") && (filter.DateTo == null || so.AddedDate.Value.Date <= filter.DateTo.Value.Date))
                              || ((string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime") && (filter.DateFrom == null || so.AddedDate >= filter.DateFrom)
                              && (string.IsNullOrEmpty(filter.DateFilterType) || filter.DateFilterType.ToLowerInvariant() == "datetime") && (filter.DateTo == null || so.AddedDate <= filter.DateTo))
                              && (string.IsNullOrEmpty(filter.ProductType) || fc.SaleFormulationCode.ToLower().StartsWith(filter.ProductType.ToLower()))
                         select new SalesReportResponseVm
                         {
                             PCode = fc.SaleFormulationCode,
                             Thick = tk.ThicknessNumber,
                             OrderQty = sop.OrderQuantity,
                             CreatedDate = so.AddedDate,
                             CreatedBy = so.AddedBy
                         }).GroupBy(x => new { x.PCode, x.Thick })
                        .Select(group => new TopSellingProductsTableDataVm
                        {
                            FormulationCode = group.Key.PCode,
                            Thickness = group.Key.Thick,
                            TotalOrderQty = group.Sum(x => x.OrderQty)
                        })
                        .OrderByDescending(x => x.TotalOrderQty)
                        .Take(filter.NumberOfRecords)
                        .ToList();
            var response = new TopSellingProductsResponseVm
            {
                TableData = query.ToList(),
                ChartData = query.Select(x => new TopSellingProductsChartDataVm
                {
                    XAxis = x.FormulationCode + "-" + x.Thickness,
                    YAxis = x.TotalOrderQty
                }).ToList()
            };

            return response;
        }

        public List<PurchaseOrderReportVm> PurchaseReport(PurchaseReportRequestVm filter)
        {
            List<PurchaseOrderReportVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                var highValuePOApprovalLimit = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "HighValuePOApprovalLimit").ConfigValue;
                res = (from a in db.PurchaseOrderTables
                       join pt in db.PaymentTermMasters on a.PaymentTermId equals pt.PaymentTermId
                       join dt in db.DeliveryTermMasters on a.DeliveryTermId equals dt.DeliveryTermId
                       join s in db.SupplierMasters on a.SupplierId equals s.SupplierId
                       join tr in db.TransportCompanyMasters on a.TransportId equals tr.TransportId into trr
                       from tr in trr.DefaultIfEmpty()
                       join dm in db.DeptMasters on a.DepartmentId equals dm.DeptId into dmd
                       from dm in dmd.DefaultIfEmpty()
                       join umab in db.UserMasters on a.AddedBy equals umab.Email
                       where
                       (filter.DateFrom == null || a.PocreationDate >= filter.DateFrom)
                           && (filter.DateTo == null || a.PocreationDate <= filter.DateTo)
                           && (filter.SupplierId == 0 || filter.SupplierId == null || a.SupplierId == filter.SupplierId)
                           && (string.IsNullOrEmpty(filter.Status) || filter.Status == a.Status)
                           && (string.IsNullOrEmpty(filter.ActionBy) || filter.ActionBy == a.ActionBy)
                       select new PurchaseOrderReportVm
                       {
                           Poid = a.Poid,
                           Ponumber = a.Ponumber,
                           PaymentTermId = a.PaymentTermId,
                           PaymentTerm = pt.PaymentTerm,
                           DeliveryTermId = a.DeliveryTermId,
                           DeliveryTerm = dt.DeliveryTerm,
                           DepartmentName = dm.DeptName,
                           PocreationDate = a.PocreationDate,
                           PototalAmount = a.PototalAmount,
                           SupplierId = a.SupplierId,
                           SupplierName = s.SupplierName,
                           TransportId = a.TransportId,
                           TransportCompanyName = tr.TransportCompanyName,
                           Reference = a.Reference,
                           Grn = a.Grn,
                           IsPocomplete = a.IsPocomplete,
                           AddedBy = new UserMasterVm
                           {
                               Name = umab.Name,
                           },
                           AddedDate = a.AddedDate,
                           DeliveryDate = a.DeliveryDate,
                           Status = a.Status,
                           Remarks = a.Remarks,
                           ContactPersonUserId = a.ContactPersonUserId,
                           ActionBy = (from us in db.UserMasters
                                       where us.Email.ToLower() == a.ActionBy.ToLower()
                                       select new UserMasterVm
                                       {
                                           UserId = us.UserId,
                                           Name = us.Name,
                                           Contact = us.Contact,
                                           Email = us.Email
                                       }).FirstOrDefault(),
                           ApprovedDate = a.ApprovedDate,
                           ApprovedBy = (from us in db.UserMasters
                                         where us.Email.ToLower() == a.ApprovedBy.ToLower()
                                         select new UserMasterVm
                                         {
                                             UserId = us.UserId,
                                             Name = us.Name,
                                             Contact = us.Contact,
                                             Email = us.Email
                                         }).FirstOrDefault(),
                           POHighValue = highValuePOApprovalLimit,
                           IsInvoiceAttached = db.InvoiceMasters.Any(x => x.Poid == a.Poid),
                           //PurchaseOrderProduct = (from p in db.PurchaseOrderProductTables
                           //                        join pr in db.ProductMasters on p.ProductId equals pr.ProductId
                           //                        join prc in db.ProductCategoryMasters on pr.ProductCategoryId equals prc.ProductCategoryId into ps
                           //                        from prc in ps.DefaultIfEmpty()
                           //                        join pf in db.ProductFirstSubCategoryMasters on pr.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                           //                        from pf in psf.DefaultIfEmpty()
                           //                        join psc in db.ProductSecSubCategoryMasters on pr.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                           //                        from psc in pssc.DefaultIfEmpty()
                           //                        where p.Poid == a.Poid
                           //                        && (filter.ProductId == 0 || p.ProductId == filter.ProductId)
                           //                        && (string.IsNullOrEmpty(filter.Unit) == true || p.Unit == filter.Unit)
                           //                        select new PurchaseOrderProductReportVm
                           //                        {
                           //                            PoproductId = p.PoproductId,
                           //                            ProductId = p.ProductId,
                           //                            ProductName = pr.ProductName,
                           //                            Unit = p.Unit,
                           //                            Rate = p.Rate,
                           //                            Currency = p.Currency,
                           //                            Quantity = p.Quantity,
                           //                            Amount = p.Amount,
                           //                            Grade = p.Grade,
                           //                            Igst = p.Igst,
                           //                            ProductCategoryId = pr.ProductCategoryId,
                           //                            ProductCategory = prc.ProductCategory,
                           //                            ProductFirstSubCategoryId = pr.ProductFirstSubCategoryId,
                           //                            ProductFirstSubCategory = pf.ProductFirstSubCategory,
                           //                            ProductSecSubCategoryId = pr.ProductSecSubCategoryId,
                           //                            ProductSecSubCategory = psc.ProductSecSubCategory,
                           //                            RecievedQuantity = (from ps in db.StockProductTables
                           //                                                join s in db.StockMasters on ps.StockId equals s.StockId
                           //                                                join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                           //                                                join ist in db.IssueProductTables on ps.StockProductId equals ist.ToNewStockProductId into ipssc
                           //                                                from ist in ipssc.DefaultIfEmpty()
                           //                                                where i.Poid == a.Poid && ps.ProductId == p.ProductId && ist == null
                           //                                                select ps.Quantity).Sum(),
                           //                            StockRecievedbyUserDetails = (from spt in db.StockProductTables
                           //                                                          join s in db.StockMasters on spt.StockId equals s.StockId
                           //                                                          join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                           //                                                          join us in db.UserMasters on s.AddedBy equals us.Email
                           //                                                          where i.Poid == a.Poid && spt.ProductId == p.ProductId
                           //                                                          select new UserMasterVm
                           //                                                          {
                           //                                                              UserId = us.UserId,
                           //                                                              Name = us.Name,
                           //                                                              Contact = us.Contact,
                           //                                                              Email = us.Email
                           //                                                          }).FirstOrDefault()
                           //                        }).ToList(),
                       }).OrderByDescending(x => x.Poid).ToList();
                var polist = res.Select(x => x.Poid).Distinct().ToList();
                var data = (from p in db.PurchaseOrderProductTables
                            join pr in db.ProductMasters on p.ProductId equals pr.ProductId
                            join prc in db.ProductCategoryMasters on pr.ProductCategoryId equals prc.ProductCategoryId into ps
                            from prc in ps.DefaultIfEmpty()
                            join pf in db.ProductFirstSubCategoryMasters on pr.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on pr.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            where polist.Contains(p.Poid.Value)
                            && (filter.ProductId == 0 || p.ProductId == filter.ProductId)
                            && (string.IsNullOrEmpty(filter.ProductType) || prc.ProductType == filter.ProductType)
                            && (filter.ProductCategoryId == 0 || prc.ProductCategoryId == filter.ProductCategoryId)
                            && (filter.ProductFirstSubCategoryId == 0 || pf.ProductFirstSubCategoryId == filter.ProductFirstSubCategoryId)
                            && (filter.ProductSecSubCategoryId == 0 || psc.ProductSecSubCategoryId == filter.ProductSecSubCategoryId)
                            && (string.IsNullOrEmpty(filter.Unit) == true || p.Unit == filter.Unit)
                            select new PurchaseOrderProductReportVm
                            {
                                Poid = p.Poid,
                                PoproductId = p.PoproductId,
                                ProductId = p.ProductId,
                                ProductName = pr.ProductName,
                                Unit = p.Unit,
                                Rate = p.Rate,
                                Currency = p.Currency,
                                Quantity = p.Quantity,
                                Amount = p.Amount,
                                Grade = p.Grade,
                                Igst = p.Igst,
                                ProductCategoryId = pr.ProductCategoryId,
                                ProductCategory = prc.ProductCategory,
                                ProductFirstSubCategoryId = pr.ProductFirstSubCategoryId,
                                ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                ProductSecSubCategoryId = pr.ProductSecSubCategoryId,
                                ProductSecSubCategory = psc.ProductSecSubCategory,
                                RecievedQuantity = (from ps in db.StockProductTables
                                                    join s in db.StockMasters on ps.StockId equals s.StockId
                                                    join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                                                    where i.Poid == p.Poid && ps.ProductId == p.ProductId
                                                    select ps.ReceivedQuantity).Sum(),
                                StockRecievedbyUserDetails = (from spt in db.StockProductTables
                                                              join s in db.StockMasters on spt.StockId equals s.StockId
                                                              join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                                                              join us in db.UserMasters on s.AddedBy equals us.Email
                                                              where i.Poid == p.Poid && spt.ProductId == p.ProductId
                                                              select new UserMasterVm
                                                              {
                                                                  UserId = us.UserId,
                                                                  Name = us.Name,
                                                                  Contact = us.Contact,
                                                                  Email = us.Email
                                                              }).FirstOrDefault()
                            }).ToList();

                foreach (var item in res)
                {
                    item.PurchaseOrderProduct = data.Where(x => x.Poid == item.Poid).ToList();
                }
            }
            return res;
        }

        public List<ConsumeStockProductReportResponseVm> GetAllConsumeStockProductsWithFilters(ConsumeStockProductRequestVm filters)
        {
            List<ConsumeStockProductReportResponseVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ConsumeStockProductMasters
                       join stm in db.StockMasters on a.StockId equals stm.StockId
                       join spt in db.StockProductTables on a.StockProductId equals spt.StockProductId
                       join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId
                       join s in db.StoreMasters on a.StoreId equals s.StoreId
                       join r in db.RackMasters on a.RackId equals r.RackId
                       join so in db.SaleOrderTables on a.SaleOrderId equals so.SaleOrderId into solg
                       from so in solg.DefaultIfEmpty()
                       where (((string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "fullday") && (filters.ConsumedDateFrom == null || a.AddedDate.Value.Date >= filters.ConsumedDateFrom.Value.Date)
                        && (string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "fullday") && (filters.ConsumedDateTo == null || a.AddedDate.Value.Date <= filters.ConsumedDateTo.Value.Date))
                        || ((string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "datetime") && (filters.ConsumedDateFrom == null || a.AddedDate >= filters.ConsumedDateFrom)
                        && (string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "datetime") && (filters.ConsumedDateTo == null || a.AddedDate <= filters.ConsumedDateTo)))
                        && (string.IsNullOrEmpty(filters.BatchNo) || stm.Batch == filters.BatchNo)
                         && (string.IsNullOrEmpty(filters.SaleOrderNo) || so.SaleOrderNumber == filters.SaleOrderNo)
                        && (string.IsNullOrEmpty(filters.ProductType) || pr.ProductType.ToLower() == filters.ProductType.ToLower())
                        && (string.IsNullOrEmpty(filters.Consumedby) || a.AddedBy.ToLower() == filters.Consumedby.ToLower())
                        && (string.IsNullOrEmpty(filters.Unit) || a.Unit.ToLower() == filters.Unit.ToLower())
                        && (filters.ProductCategoryId == 0 || filters.ProductCategoryId == null || pr.ProductCategoryId == filters.ProductCategoryId)
                        && (filters.ProductFirstSubCategoryId == 0 || filters.ProductFirstSubCategoryId == null || pr.ProductFirstSubCategoryId == filters.ProductFirstSubCategoryId)
                        && (filters.ProductSecSubCategoryId == 0 || filters.ProductSecSubCategoryId == null || pr.ProductSecSubCategoryId == filters.ProductSecSubCategoryId)
                        && (filters.ProductId == 0 || filters.ProductId == null || pr.ProductId == filters.ProductId)
                        && (filters.RackId == 0 || filters.RackId == null || a.RackId == filters.RackId)
                        && (filters.StoreId == 0 || filters.StoreId == null || r.StoreId == filters.StoreId)
                        && (filters.SupplierId == 0 || filters.SupplierId == null || inv.SupplierId == filters.SupplierId)
                       select new ConsumeStockProductReportResponseVm
                       {
                           ConsumeStockProductId = a.ConsumeStockProductId,
                           RackId = a.RackId,
                           RackCode = r.RackCode,
                           RackName = r.RackName,
                           StoreId = a.StoreId,
                           BatchNo = stm.Batch,
                           PricePerUnit = spt.PricePerUnit,
                           StoreCode = s.StoreCode,
                           StoreName = s.StoreName,
                           ProductId = a.ProductId,
                           ProductCode = pr.ProductCode,
                           ProductName = pr.ProductName,
                           Quantity = a.Quantity,
                           SCQuantity = a.Scquantity,
                           Unit = a.Unit,
                           ConsumedDate = a.ConsumedDate,
                           IsDamaged = a.IsDamaged,
                           SaleOrderId = a.SaleOrderId,
                           SaleOrderNumber = so.SaleOrderNumber,
                           StockProductId = a.StockProductId,
                           Purpose = a.Purpose,
                           StockId = a.StockId,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).Distinct().OrderByDescending(x => x.ConsumeStockProductId).ToList();
            }
            return res;
        }

        public List<SaleOrderPostProcessReportVm> GetSaleOrderPostProcessReport(PostProcessReportRequestVm filter)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = (from s in db.SaleOrderTables
                           join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                           from cust in scu.DefaultIfEmpty()
                           join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                           join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                           from c in cagr.DefaultIfEmpty()
                           join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                           from g in agr.DefaultIfEmpty()
                           select new SaleOrderPostProcessReportVm
                           {
                               SaleOrderId = s.SaleOrderId,
                               SaleOrderDate = s.SaleOrderDate,
                               SaleOrderNumber = s.SaleOrderNumber,
                               SaleOrderType = s.SaleOrderType,
                               SaleOrderCode = s.SaleOrderCode,
                               SaleOrderStatus = (PmsCommon.ESalesOrderStatus)s.Status,
                               FinishCode = s.FinishCode,
                               //Remarks = ((ppp.Remark == null ? null : "Print: " + ppp.Remark) + (ppp.Remark == null ? null : " | ") + (ppe.Remark == null ? null : "Emboss: " + ppe.Remark) + (ppe.Remark == null ? null : " | ") + (ppv.Remark == null ? null : "Vaccum: " + ppv.Remark)),
                               GrainName = g.GrainName,
                               ColorName = c.ColorName,
                               OrderQuantity = a.OrderQuantity,
                               CustomerName = cust.CustomerName,
                               PrintQuantity = db.SaleOrderPostProcessPrintTables.Where(x => x.SaleOrderId == s.SaleOrderId).Sum(x => x.ReceivedQuantity),
                               PrintAcceptedDate = db.SaleOrderPostProcessPrintTables.FirstOrDefault(x => x.SaleOrderId == s.SaleOrderId).AddedDate,
                               PrintRemark = db.SaleOrderPostProcessPrintTables.FirstOrDefault(x => x.SaleOrderId == s.SaleOrderId).Remark,
                               EmbossQuantity = db.SaleOrderPostProcessEmbossingTables.Where(x => x.SaleOrderId == s.SaleOrderId).Sum(x => x.ReceivedQuantity),
                               EmbossAcceptedDate = db.SaleOrderPostProcessEmbossingTables.FirstOrDefault(x => x.SaleOrderId == s.SaleOrderId).AddedDate,
                               EmbossingRemark = db.SaleOrderPostProcessEmbossingTables.FirstOrDefault(x => x.SaleOrderId == s.SaleOrderId).Remark,
                               VacuumQuantity = db.SaleOrderPostProcessVacuumTables.Where(x => x.SaleOrderId == s.SaleOrderId).Sum(x => x.ReceivedQuantity),
                               VaccumAcceptedDate = db.SaleOrderPostProcessVacuumTables.FirstOrDefault(x => x.SaleOrderId == s.SaleOrderId).AddedDate,
                               VacuumRemark = db.SaleOrderPostProcessVacuumTables.FirstOrDefault(x => x.SaleOrderId == s.SaleOrderId).Remark,
                           }).Distinct().OrderByDescending(x => x.SaleOrderId).ToList();
                res = (from s in res
                       where ((s.SaleOrderStatus == ESalesOrderStatus.PrintAssigned || s.SaleOrderStatus == ESalesOrderStatus.PrintInProcess) && (filter.PostProcessDate == null || (s.PrintAcceptedDate != null && s.PrintAcceptedDate.Value.Date <= filter.PostProcessDate.Value.Date)))
                       || ((s.SaleOrderStatus == ESalesOrderStatus.EmbossingAssigned || s.SaleOrderStatus == ESalesOrderStatus.EmbossingInProcess) && (filter.PostProcessDate == null || (s.EmbossAcceptedDate != null && s.EmbossAcceptedDate.Value.Date <= filter.PostProcessDate.Value.Date)))
                       || ((s.SaleOrderStatus == ESalesOrderStatus.VacuumAssigned || s.SaleOrderStatus == ESalesOrderStatus.VacuumInProcess) && (filter.PostProcessDate == null || (s.VaccumAcceptedDate != null && s.VaccumAcceptedDate.Value.Date <= filter.PostProcessDate.Value.Date)))
                       select s).ToList();

                return res;
            }
        }

        public List<ProductStockSummaryForJumboGradeVm> GetProductStockSummaryForJumboGrade(ProductStockSummaryFilters filter)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = (from j in db.JumboInspectionTables
                           join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                           join sale in db.SaleOrderTables on wj.SaleOrderId equals sale.SaleOrderId
                           join salepro in db.SaleOrderProductionTables on sale.SaleOrderId equals salepro.SaleOrderId
                           join sopembom in db.SaleOrderProductionEmbossingMasters on salepro.SaleOrderProductionId equals sopembom.SaleOrderProductionId into sopemboma
                           from sopembom in sopemboma.DefaultIfEmpty()
                           join embm in db.EmbossingMasters on sopembom.EmbossingMasterId equals embm.EmbossingMasterId into embma
                           from embm in embma.DefaultIfEmpty()
                           join gra in db.GrainMasters on salepro.GrainId equals gra.GrainId
                           join cus in db.CustomerMasters on sale.CustomerId equals cus.CustomerId
                           join col in db.ColorMasters on salepro.ColorId equals col.ColorId
                           where (j.Grade == "1st" || j.Grade == "A")
                           && (j.DispatchStatus != "Dispatched")
                           && (filter.RecordStatus == null || (filter.RecordStatus == "Available" ? (PmsCommon.ESalesOrderStatus)sale.Status != ESalesOrderStatus.DispatchCompleted : (PmsCommon.ESalesOrderStatus)sale.Status == ESalesOrderStatus.DispatchCompleted))
                           && (String.IsNullOrEmpty(filter.ArticleName) || salepro.ManufacturingProductName.ToLower().Contains(filter.ArticleName))
                           && (filter.ColorId == 0 || filter.ColorId == null || salepro.ColorId == filter.ColorId)
                           && (filter.GrainId == 0 || filter.GrainId == null || salepro.GrainId == filter.GrainId)
                           && (filter.CustomerId == 0 || filter.CustomerId == null || sale.CustomerId == filter.CustomerId)
                           && (filter.SaleFormulationCodeId == 0 || filter.SaleFormulationCodeId == null || sale.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                           && (filter.PrintMasterId == 0 || filter.PrintMasterId == null || db.SaleOrderProductionPrintMasters.Any(x => x.SaleOrderProductionId == salepro.SaleOrderProductionId && x.PrintMasterId == filter.PrintMasterId && x.Removed != true))
                           && (filter.EmbossingMasterId == 0 || filter.EmbossingMasterId == null || db.SaleOrderProductionEmbossingMasters.Any(x => x.SaleOrderProductionId == salepro.SaleOrderProductionId && x.EmbossingMasterId == filter.EmbossingMasterId && x.Removed != true))
                           && (filter.VacuumMasterId == 0 || filter.VacuumMasterId == null || db.SaleOrderProductionVacuumMasters.Any(x => x.SaleOrderProductionId == salepro.SaleOrderProductionId && x.VacuumMasterId == filter.VacuumMasterId && x.Removed != true))
                           select new ProductStockSummaryForJumboGradeVm
                           {
                               SaleOrderId = sale.SaleOrderId,
                               SaleOrderNumber = sale.SaleOrderNumber,
                               SaleOrderCode = sale.SaleOrderCode,
                               SaleOrderStatus = (PmsCommon.ESalesOrderStatus)sale.Status,
                               ArticleName = salepro.ManufacturingProductName,
                               GrainName = gra.GrainName,
                               GrainCode = gra.GrainCode,
                               Grade = j.Grade,
                               Quantity = j.Quantity,
                               CustomerName = cus.CustomerName,
                               ColorName = col.ColorName,
                           }).ToList();

                var qwe = (from a in res
                           group a by new
                           {
                               a.SaleOrderId,
                               a.SaleOrderNumber,
                               a.SaleOrderCode,
                               a.SaleOrderStatus,
                               a.ArticleName,
                               a.GrainName,
                               a.GrainCode,
                               a.Grade,
                               a.CustomerName,
                               a.ColorName,
                           } into pg
                           select new ProductStockSummaryForJumboGradeVm
                           {
                               SaleOrderId = pg.Key.SaleOrderId,
                               SaleOrderNumber = pg.Key.SaleOrderNumber,
                               SaleOrderCode = pg.Key.SaleOrderCode,
                               SaleOrderStatus = pg.Key.SaleOrderStatus,
                               GrainName = pg.Key.GrainName,
                               GrainCode = pg.Key.GrainCode,
                               ArticleName = pg.Key.ArticleName,
                               Grade = pg.Key.Grade,
                               CustomerName = pg.Key.CustomerName,
                               ColorName = pg.Key.ColorName,
                               Quantity = pg.Sum(x => x.Quantity),
                               Roll = pg.Count()
                           }).OrderByDescending(x => x.SaleOrderId).ToList();
                return qwe;
            }
        }

        public ReceivedOrderResponseVm GetReceivedOrderReport(ChartReportsRequestVm filters)
        {
            using var db = new pmsdbContext();
            List<ReceivedOrdersTableDataVm> query = null;
            if (filters.DataType.ToLowerInvariant() == "byorderqty")
            {
                query = (from so in db.SaleOrderTables
                         join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                         where ((string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "fullday") && (filters.DateFrom == null || so.SaleOrderDate.Value.Date >= filters.DateFrom.Value.Date)
                              && (string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "fullday") && (filters.DateTo == null || so.SaleOrderDate.Value.Date <= filters.DateTo.Value.Date))
                              || ((string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "datetime") && (filters.DateFrom == null || so.SaleOrderDate >= filters.DateFrom)
                              && (string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "datetime") && (filters.DateTo == null || so.SaleOrderDate <= filters.DateTo))
                         select new SalesReportResponseVm
                         {
                             CreatedDate = so.SaleOrderDate,
                             OrderQty = sop.OrderQuantity,
                         }).GroupBy(x => x.CreatedDate.Value.Date)
                             .Select(group => new ReceivedOrdersTableDataVm
                             {
                                 ReceivedDate = group.Key,
                                 TotalOrderQty = group.Sum(x => x.OrderQty)
                             })
                            .OrderBy(x => x.ReceivedDate)
                            .ToList();
            }
            else if (filters.DataType.ToLowerInvariant() == "byNumberOfOrders".ToLowerInvariant())
            {
                query = (from so in db.SaleOrderTables
                         join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                         where ((string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "fullday") && (filters.DateFrom == null || so.SaleOrderDate.Value.Date >= filters.DateFrom.Value.Date)
                              && (string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "fullday") && (filters.DateTo == null || so.SaleOrderDate.Value.Date <= filters.DateTo.Value.Date))
                              || ((string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "datetime") && (filters.DateFrom == null || so.SaleOrderDate >= filters.DateFrom)
                              && (string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "datetime") && (filters.DateTo == null || so.SaleOrderDate <= filters.DateTo))
                         select new SalesReportResponseVm
                         {
                             CreatedDate = so.SaleOrderDate,
                             OrderQty = sop.OrderQuantity
                         }).GroupBy(x => x.CreatedDate.Value.Date)
                             .Select(group => new ReceivedOrdersTableDataVm
                             {
                                 ReceivedDate = group.Key,
                                 TotalOrderQty = group.Count(x => x.OrderQty >= 50)
                             })
                            .OrderBy(x => x.ReceivedDate)
                            .ToList();
            }
            else if (filters.DataType.ToLowerInvariant() == "byMfdQty".ToLowerInvariant())
            {
                List<ESalesOrderStatus> stsList = new List<ESalesOrderStatus>
                {
                    PMSEnum.ParseEnum<ESalesOrderStatus>("RawMaterialIssued"),
                    PMSEnum.ParseEnum<ESalesOrderStatus>("ProductionStarted"),
                    PMSEnum.ParseEnum<ESalesOrderStatus>("Mixing"),
                    PMSEnum.ParseEnum<ESalesOrderStatus>("InJumbo")
                };

                query = (from so in db.SaleOrderTables
                         join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                         where !stsList.Contains((ESalesOrderStatus)so.Status)
                            && ((string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "fullday") && (filters.DateFrom == null || so.SaleOrderDate.Value.Date >= filters.DateFrom.Value.Date)
                            && (string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "fullday") && (filters.DateTo == null || so.SaleOrderDate.Value.Date <= filters.DateTo.Value.Date))
                            || ((string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "datetime") && (filters.DateFrom == null || so.SaleOrderDate >= filters.DateFrom)
                            && (string.IsNullOrEmpty(filters.DateFilterType) || filters.DateFilterType.ToLowerInvariant() == "datetime") && (filters.DateTo == null || so.SaleOrderDate <= filters.DateTo))
                         select new SalesReportResponseVm
                         {
                             CreatedDate = so.SaleOrderDate,
                             MFGQty = sop.ManufacturingQuantity,
                         }).GroupBy(x => x.CreatedDate.Value.Date)
                        .Select(group => new ReceivedOrdersTableDataVm
                        {
                            ReceivedDate = group.Key,
                            TotalOrderQty = group.Sum(x => x.MFGQty)
                        })
                        .OrderBy(x => x.ReceivedDate)
                        .ToList();
            }
            var response = new ReceivedOrderResponseVm
            {
                TableData = query.ToList(),
                ChartData = query.Select(x => new ReceivedOrdersChartDataVm
                {
                    XAxis = x.ReceivedDate,
                    YAxis = x.TotalOrderQty
                }).ToList()
            };
            return response;
        }

        public ApiFunctionResponseVm SendProductionPlanningReportEmailOnDemand(ProductionPlanningReportRequestVm filter)
        {
            var data = new ReportDataFn(GlobalData);
            var dataSet = data.ProductionPlanningReport(filter);
            string messageBody = "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>The Production planning records for today are as follows: </span><br><br>";

            if (dataSet == null || dataSet.Count == 0)
                messageBody = "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Production planning for today has no records </span><br><br>";
            else
            {
                string htmlTableStart = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
                string htmlTableEnd = "</table>";
                string htmlHeaderRowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>S. No.</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order Date</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order No.</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>WorkPlan No.</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Party name</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Item Name</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>P.Code</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Grain Name</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Colors</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Width</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Fabric</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Thickness</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Finish</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order QTY</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Pre Skin (GSM)</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Skin (GSM)</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Foam (GSM)</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Adhesive (GSM)</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Fabric (GSM)</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Final (GSM)</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Remarks</th>
                    </tr>";
                string htmlTrStart = "<tr style=\'text-align: left;font-size: small;border: 1px solid black;\' > ";
                string htmlTrEnd = "</tr>";
                string htmlTdStart = "<td style='border: 1px solid #ddd; padding: 8px;'>";
                string htmlTdEnd = "</td>";

                messageBody += htmlTableStart;
                messageBody += htmlHeaderRowStart;
                int i = 0;
                foreach (var row in dataSet)
                {
                    i++;
                    messageBody = messageBody + htmlTrStart;
                    messageBody = messageBody + htmlTdStart + i + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.OrderDate + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.SaleOrderNo + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.WorkPlanNo + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.PartyName + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.ItemName + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.PCode + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Grain + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Colours + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Width + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Fabric + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Thick + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Finish + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.OrderQty + htmlTdEnd;
                    //messageBody = messageBody + htmlTdStart + row.Rate + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.PreSkin + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Skin + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Foam + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Adhesive + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.FabricGsm + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + (row.PreSkin + row.Skin + row.Foam + row.Adhesive + row.FabricGsm) + htmlTdEnd;
                    messageBody = messageBody + htmlTdStart + row.Remarks + htmlTdEnd;
                    messageBody = messageBody + htmlTrEnd;
                }
                messageBody = messageBody + htmlTableEnd;
                messageBody = messageBody + @"<br><br>
                                 <span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>
                                Regards,<br>
                                Zaibunco Industries Automated System
                                Powered by KanzenFlow
                                <span>";
            }
            using (var db = new Models.pmsdbContext())
            {
                var emaillist = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.ScheduledReportsGroup && x.Enabled == "true").Select(x => x.EmailId).ToArray();
                EmailDataFn.SendEmail(messageBody, null, emaillist, null, "[On Demand] Production Planning Report : " + filter.WorkPlanDate.Value.ToString("dd-MMM-yyyy"));
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Email Sent Successfully");
        }

        public string GetYieldReportHtmlData(List<YieldReportVm> dataSet)
        {
            decimal? OrderQty = 0, PrdMtr = 0, OverProdPer = 0, First = 0, AGrade = 0, Lot = 0, NS = 0, CutPc = 0, WasteGrade = 0;
            decimal? YieldPer = 0, Sample = 0, Scrap = 0, GrandTotal = 0, Manufac = 0, FilmGrade = 0, TotalYield = 0;

            foreach (var Item in dataSet)
            {
                OrderQty = OrderQty + Item.SaleOrderQuantity;
                PrdMtr = PrdMtr + Item.ManufacturingQuantity;
                First = First + Item.FirstGrade;
                AGrade = AGrade + Item.AGrade;
                Lot = Lot + Item.LOTGrade;
                NS = NS + Item.NSGrade;
                CutPc = CutPc + Item.CUTPCGrade;
                Sample = Sample + Item.SampleQuantity;
                WasteGrade = WasteGrade + Item.WASTEGrade;
                Manufac = Manufac + Item.ManufacturingQuantity;
                FilmGrade = FilmGrade + Item.FILMGrade;
                TotalYield = (First + AGrade + Sample) * 100 / Manufac;
            }

            OverProdPer = (PrdMtr - OrderQty) / OrderQty * 100;
            Scrap = FilmGrade + WasteGrade;
            YieldPer = (First + AGrade + Sample) * 100 / Manufac;
            GrandTotal = First + AGrade + Lot + NS + CutPc + Sample + WasteGrade + FilmGrade;

            string messageBody = "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>The Yield Report for requested date(s) are as follows: </span><br><br>";

            messageBody += "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Below is the Total Yield Report Summary: </span><br><br>";

            if (dataSet == null || dataSet.Count == 0)
                messageBody = "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Yield Report for selected date(s) has no records </span><br><br>";
            else
            {
                string htmltable1 = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
                string htmltable1row = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order Quantity</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>PRD MTR</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Over Production %</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>First</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>A Grade</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Lot</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>NS</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>CP</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Yield %</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>SAMP</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>SCRP</td>
                    <td style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>GRD Total</td>
                    </tr>";
                string htmltabledatarow = "<tr style=\'text-align: left;font-size: small;border: 1px solid black;\' > " +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + OrderQty + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + PrdMtr + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + decimal.Round(OverProdPer.Value, 2) + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + First + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + AGrade + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + Lot + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + NS + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + CutPc + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + decimal.Round(YieldPer.Value, 2) + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + Sample + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + Scrap + "</td>" +
                    "<td style='border: 1px solid #ddd; padding: 8px;'>" + GrandTotal + "</td>" +
                    "</tr>";
                string htmltable1end = "</table>";

                string htmlTableStart = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
                string htmlTableEnd = "</table>";
                string htmlHeaderRowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>S. No.</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Inspection Date</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Sale Order No.</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Sale Order Code</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Fabric</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order QTY</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>PRD MTR</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Over PRD %</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>1st</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>A</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Lot</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>NS</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>CP</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Yield(%)</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>SAMP</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>SCRP</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Grd Total</th>
                    </tr>";
                string htmlTrStart = "<tr style=\'text-align: left;font-size: small;border: 1px solid black;\' > ";
                string htmlTrEnd = "</tr>";
                string htmlTdStart = "<td style='border: 1px solid #ddd; padding: 8px;'>";
                string htmlTdEnd = "</td>";

                messageBody += htmltable1;
                messageBody += htmltable1row;
                messageBody += htmltabledatarow;
                messageBody += htmltable1end;

                messageBody += @"<br><br>";
                messageBody += "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Below are the orders for which either Yield is below 90% or PRD MTR is above 5% of Order QTY : </span><br><br>";

                messageBody += htmlTableStart;
                messageBody += htmlHeaderRowStart;
                int i = 0;

                foreach (var row in dataSet)
                {
                    // Calculate yield percentage safely - if ActualQuantity is null or zero, consider it as 0% yield (below target)
                    var yield = (row.ActualQuantity ?? 0) > 0
                        ? ((row.FirstGrade ?? 0) + (row.AGrade ?? 0) + (row.SampleQuantity ?? 0)) * 100 / (row.ActualQuantity ?? 0)
                        : 0;

                    // Calculate over production percentage safely - if SaleOrderQuantity is null or zero, consider it as 0%
                    var OverProdPerItem = (row.SaleOrderQuantity ?? 0) > 0
                        ? (((row.ManufacturingQuantity ?? 0) - (row.SaleOrderQuantity ?? 0)) / (row.SaleOrderQuantity ?? 0)) * 100
                        : 0;

                    var GrantTotal = (row.FirstGrade ?? 0) + (row.AGrade ?? 0) + (row.LOTGrade ?? 0) + (row.NSGrade ?? 0) +
                        (row.CUTPCGrade ?? 0) + (row.SampleQuantity ?? 0) + (row.FILMGrade ?? 0) + (row.WASTEGrade ?? 0);

                    if (yield < 90 || OverProdPerItem > 5)  // Fixed: should be > 5, not < 5
                    {
                        messageBody = messageBody + htmlTrStart;
                        messageBody = messageBody + htmlTdStart + i + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + TimeZoneInfo.ConvertTimeFromUtc(row.AddedDate.Value, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("dd-MMM-yyyy hh:mm tt") + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.SaleOrderNumber + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.SaleOrderCode + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.FabricName + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.SaleOrderQuantity + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.ActualQuantity + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + string.Format("{0:0.00}", OverProdPerItem) + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.FirstGrade + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.AGrade + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.LOTGrade + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.NSGrade + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.CUTPCGrade + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + string.Format("{0:0.00}", yield) + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + row.SampleQuantity + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + (row.FILMGrade + row.WASTEGrade) + htmlTdEnd;
                        messageBody = messageBody + htmlTdStart + string.Format("{0:0.00}", GrantTotal) + htmlTdEnd;
                        messageBody = messageBody + htmlTrEnd;
                        i++;
                    }
                }
                messageBody = messageBody + htmlTableEnd;
                messageBody = messageBody + @"<br><br>
                                 <span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>
                                Regards,<br>
                                Zaibunco Industries Automated System<br>
                                Powered by KanzenFlow
                                <span>";
            }
            return messageBody;
        }

        public ApiFunctionResponseVm SendYieldReportEmailOnDemand(YieldReportRequestVm filter)
        {
            var data = new ReportDataFn(GlobalData);
            var dataSet = data.YieldReport(filter);

            var messageBody = GetYieldReportHtmlData(dataSet);

            using (var db = new Models.pmsdbContext())
            {
                var emaillist = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.ScheduledReportsGroup && x.Enabled == "true").Select(x => x.EmailId).ToArray();
                var istFromdate = TimeZoneInfo.ConvertTimeFromUtc(filter.DateFrom.Value, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time"));
                var istTodate = TimeZoneInfo.ConvertTimeFromUtc(filter.DateTo.Value, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time"));
                var subject = "";
                if (istFromdate.ToString("dd-MMM-yyyy") == istTodate.ToString("dd-MMM-yyyy"))
                {
                    subject = "[On Demand] PMS Yield Report for " + filter.DateTo?.ToString("dd-MMM-yyyy");
                }
                else
                {
                    subject = "[On Demand] PMS Yield Report - From: " + istFromdate.ToString("dd-MMM-yyyy") + " To: " + istTodate.ToString("dd-MMM-yyyy");
                }
                EmailDataFn.SendEmail(messageBody, null, emaillist, null, subject);
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Email Sent Successfully");
        }

        public async Task SendSaleOrderStatusUpdate(long saleorderid, string SaleOrderStatus)
        {
            try
            {
                var custEmailUpdateEnabled = "";
                var ActiveEmailConfigName = "";
                var custEmail = Array.Empty<string>();
                string messageBody = "";
                using (var db = new Models.pmsdbContext())
                {
                    custEmailUpdateEnabled = db.ConfigTables.Where(x => x.ConfigItem == "EnableSaleOrderUpdateEmailToCustomer").First().ConfigValue;
                    ActiveEmailConfigName = db.ConfigTables.Where(x => x.ConfigItem == "ActiveEmailConfigName").First().ConfigValue;
                    var custEmailRes = db.CustomerMasters.Where(x => x.CustomerId == db.SaleOrderTables.Where(x => x.SaleOrderId == saleorderid).Select(x => x.CustomerId).Single()).Select(x => x.Email).ToList();
                    custEmail = custEmailRes[0].ToString().Split(",").ToArray();
                }

                var data = new SaleOrderDataFn(new GlobalDataEntity());
                var dataSet = data.GetSaleOrderDataForViewById(saleorderid);
                if (custEmailUpdateEnabled == "true" && custEmail[0] != "")
                {
                    messageBody = "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Dear " + dataSet.CustomerName + ", </span><br><br>";
                }
                else if (custEmailUpdateEnabled == "false" && custEmail[0] != "")
                {
                    messageBody = "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Dear " + dataSet.CustomerName + ", </span><br><br>";
                }
                else if (custEmailUpdateEnabled == "true" && custEmail[0] == "")
                {
                    messageBody = "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Dear Sales Team and Factory Leadership Team, </span><br><br>";
                    messageBody = messageBody + "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>We don't have Email ID for Customer Name - " + dataSet.CustomerName +
                    ". Hence, we are sending the updates for this order to you. Please communicate to customer their order updates or get the Email ID updated in software to send updates directly.</span><br><br>";
                }

                messageBody = messageBody + "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>Here is your Order status.</span>";

                if (SaleOrderStatus == SaleOrderEmailStatus.InProductionPlanning)
                {
                    messageBody = messageBody + @"<br><br>
                                                <b> Update: Your order has been added in production planning, soon it will be sent for production.</b>";
                }
                if (SaleOrderStatus == SaleOrderEmailStatus.RemovedPlanning)
                {
                    messageBody = messageBody + @"<br><br>
                                                <b> Update: Your order has been removed from production planning, this can happen due to operational reasons or by your request. You can get in touch with your Sales Marketing Coordinator to get more details.</b>";
                }
                else if (SaleOrderStatus == SaleOrderEmailStatus.StartingProduction)
                {
                    messageBody = messageBody + @"<br><br>
                                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'><b> Update: No modification is accepted now as the order details are sent for production.</b></span>";
                }
                else if (SaleOrderStatus == SaleOrderEmailStatus.ProductionCompleted)
                {
                    messageBody = messageBody + @"<br><br>
                                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'><b> Update: Production of your order has been completed and sent for Final Inspection.</b></span>";
                }
                else if (SaleOrderStatus == SaleOrderEmailStatus.FinalInspectionCompleted)
                {
                    messageBody = messageBody + @"<br><br>
                                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'><b> Update: Final Inspection has been completed for your order and waiting for the dispatch to be ready.</b></span>";
                }
                else if (SaleOrderStatus == SaleOrderEmailStatus.PartialDispatchReady)
                {
                    messageBody = messageBody + @"<br><br>
                                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'><b> Update: Your order is partially ready for Dispatch. It will soon be dispatched and you will receive dispatch details.</b></span>";
                }
                else if (SaleOrderStatus == SaleOrderEmailStatus.ReadyToDispatch)
                {
                    messageBody = messageBody + @"<br><br>
                                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'><b> Update: Your order is ready for Dispatch. It will soon be dispatched and you will receive dispatch details.</b></span>";
                }
                else if (SaleOrderStatus == SaleOrderEmailStatus.OrderDispatched)
                {
                    messageBody = messageBody + @"<br><br>
                                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'><b> Update: Your order has been dispatched from Factory and below are the details of Transport.</b></span>";
                }

                messageBody = messageBody + "<br><br><span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>The details are as follows:</span><br><br>";

                messageBody = messageBody + GetSaleOrderDetailsHtmlTableForEmail(dataSet, SaleOrderStatus);

                if (SaleOrderStatus == SaleOrderEmailStatus.OrderReceived || SaleOrderStatus == SaleOrderEmailStatus.InProductionPlanning || SaleOrderStatus == SaleOrderEmailStatus.RemovedPlanning)
                {
                    messageBody = messageBody + @"<br><br>
                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>If you have any questions about the details of your order or you would like to modify before it goes into production then<br>
                                please reach out to your Sales Marketing Coordinator directly or by replying to this email.<br><br>
                                Your reply email will be sent to Sales Team.<span>";
                }
                else
                {
                    messageBody = messageBody + @"<br><br>
                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>If you have any questions about your order then please reach out to your Sales Marketing Coordinator directly or by replying to this email.<br>
                                Your reply email will be sent to Sales Team.<span>";
                }

                if (SaleOrderStatus == SaleOrderEmailStatus.OrderDispatched)
                {
                    using (var db = new Models.pmsdbContext())
                    {
                        var dispatchId = db.JumboInspectionTables.Where(x => x.WorkPlanJumboMasterId == db.WorkPlanJumboMasters.Where(x => x.SaleOrderId == saleorderid).First().WorkPlanJumboMasterId).First().JumboDispatchId;
                        var dispatchDetails = (from dt in db.JumboDispatchTables
                                               join j in db.JumboInspectionTables on dt.JumboDispatchId equals j.JumboDispatchId
                                               join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                                               join tcm in db.TransportCompanyMasters on dt.TransportId equals tcm.TransportId
                                               join tvm in db.TransportVehicleTables on dt.VehicleId equals tvm.VehicleId
                                               where wj.SaleOrderId == saleorderid
                                               select new JumboDispatchListVm
                                               {
                                                   TransportName = tcm.TransportCompanyName,
                                                   VehicleNumber = tvm.VehicleNumber
                                               }).FirstOrDefault();

                        messageBody = messageBody + @"<br><br>
                                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>Here are the details of Transport:</span>";

                        string htmlTable3Start = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
                        string htmlTable3End = "</table>";
                        string htmlHeader3RowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Transport Company Name</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Vehicle Number</th>
                    </tr>";
                        string htmlTr3Start = "<tr style=\'text-align: left;font-size: small;border: 1px solid black;\' > ";
                        string htmlTr3End = "</tr>";
                        string htmlTd3Start = "<td style='border: 1px solid #ddd; padding: 8px;'>";
                        string htmlTd3End = "</td>";

                        messageBody += htmlTable3Start;
                        messageBody += htmlHeader3RowStart;
                        messageBody = messageBody + htmlTr3Start;
                        messageBody = messageBody + htmlTd3Start + dispatchDetails.TransportName + htmlTd3End;
                        messageBody = messageBody + htmlTd3Start + dispatchDetails.VehicleNumber + htmlTd3End;
                        messageBody = messageBody + htmlTr3End;
                        messageBody = messageBody + htmlTable3End;
                    }
                }

                messageBody = messageBody + @"<br><br>
                                This email is sent from an automated system and from a DoNotReply mailbox which is not monitored.";
                messageBody = messageBody + @"<br><br>
                                If you want to unsubscribe from email updates, please send your <NAME_EMAIL>. To make sure this email is not sent to your 'junk/spam' folder, select the email and add the sender to your Address Book.";

                messageBody = messageBody + @"<br><br>
                                 <span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>
                                Regards,<br>
                                Zaibunco Industries Automated System<br>
                                Powered by KanzenFlow
                                <span>";

                using (var db = new Models.pmsdbContext())
                {
                    var emailSubjectDate = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("dd-MMM-yyyy");
                    var emailStatusSubject = "Zaibunco | " + SaleOrderStatus + " | Sale Order No. " + dataSet.SaleOrderNumber + ": " + emailSubjectDate;

                    if (custEmailUpdateEnabled == "true" && custEmail[0] != "")
                    {
                        var CCList = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.SaleOrderStatusCCGroup && x.Enabled == "true").Select(x => x.EmailId).ToArray();
                        var replyToList = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.SaleOrderStatusReplyTo && x.Enabled == "true").Select(x => x.EmailId).ToArray();
                        if (ActiveEmailConfigName.ToLower() == "primary")
                        {
                            EmailDataFn.SendEmailUsingAmazonSES(messageBody, null, custEmail, CCList, null, true, replyToList, null, emailStatusSubject);
                        }
                        else if (ActiveEmailConfigName.ToLower() == "secondary")
                        {
                            EmailDataFn.SendEmail(messageBody, null, custEmail, CCList, null, true, replyToList, null, emailStatusSubject);
                        }
                    }
                    else
                    {
                        var CCListInTo = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.SaleOrderStatusCCGroup && x.Enabled == "true").Select(x => x.EmailId).ToArray();
                        var replyToList = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.SaleOrderStatusReplyTo && x.Enabled == "true").Select(x => x.EmailId).ToArray();
                        if (ActiveEmailConfigName.ToLower() == "primary")
                        {
                            EmailDataFn.SendEmailUsingAmazonSES(messageBody, null, CCListInTo, null, null, true, replyToList, null, emailStatusSubject);
                        }
                        else if (ActiveEmailConfigName.ToLower() == "secondary")
                        {
                            EmailDataFn.SendEmail(messageBody, null, CCListInTo, null, null, true, replyToList, null, emailStatusSubject);
                        }
                    }
                }
                //return new ApiFunctionResponseVm(HttpStatusCode.OK, "Email Sent Successfully");
            }
            catch (System.Exception ex)
            {
                //return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, ex.Message);
                throw;
            }
        }

        public List<SaleOrderTimelineTableVm> SaleOrderTimelineReport(SaleOrderTimelineRequestFiltervm filter)
        {
            using (var db = new pmsdbContext())
            {
                var res = (from model in db.SaleOrderTimelineTables
                           join sot in db.SaleOrderTables on model.SaleOrderId equals sot.SaleOrderId
                           join wpm in db.WorkPlanMasters on model.WorkPlanId equals wpm.WorkPlanId into wpmd
                           from wpm in wpmd.DefaultIfEmpty()
                           join cust in db.CustomerMasters on sot.CustomerId equals cust.CustomerId
                           where (String.IsNullOrEmpty(filter.SaleOrderNumber) || sot.SaleOrderNumber.Contains(filter.SaleOrderNumber))
                           && (filter.DateFrom == null || model.AddedDate >= filter.DateFrom)
                           && (filter.DateTo == null || model.AddedDate <= filter.DateTo)
                           && (filter.Status == null || model.Status == (int)filter.Status)
                           && (String.IsNullOrEmpty(filter.AddedBy) || model.AddedBy.ToLower().Contains(filter.AddedBy.ToLower()))
                           select new SaleOrderTimelineTableVm
                           {
                               SaleOrderId = model.SaleOrderId,
                               SaleOrderNumber = sot.SaleOrderNumber,
                               SaleOrderType = sot.SaleOrderType,
                               DeliveryDate = sot.DeliveryDate,
                               SaleOrderDate = sot.SaleOrderDate,
                               CustomerName = cust.CustomerName,
                               Status = (ESalesOrderStatus)model.Status,
                               AddedDate = model.AddedDate,
                               AddedBy = model.AddedBy,
                               WorkPlanNo = wpm.WorkPlanNo
                           }).OrderByDescending(x => x.AddedDate).ToList().Distinct().ToList();

                if (res.Any(x => x.Status == ESalesOrderStatus.LiningOrderMerged))
                {
                    foreach (var item in res)
                    {
                        if (item.Status == ESalesOrderStatus.LiningOrderMerged)
                        {
                            item.LinkedSaleOrder = db.LinkedSaleOrderTables.Where(x => x.LinkedSaleOrder == item.SaleOrderId).Select(x => new LinkedSaleOrderTableVm
                            {
                                LinkedId = x.LinkedId,
                                ParentSaleOrder = x.ParentSaleOrder,
                                ParentSaleOrderNumber = db.SaleOrderTables.FirstOrDefault(y => y.SaleOrderId == x.ParentSaleOrder).SaleOrderNumber,
                                LinkedSaleOrder = x.LinkedSaleOrder,
                                LinkedSaleOrderNumber = item.SaleOrderNumber
                            }).ToList();
                        }
                    }
                }
                return res;
            }
        }

        public async Task LowProductQuantityNotification(long productId)
        {
            try
            {
                var db = new pmsdbContext();
                // var productList = brlist.Select(p => p.ProductId).Distinct().ToList();

                // Combine queries to fetch product details and stock information in one go
                var data = (from p in db.ProductMasters
                            join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId
                            join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            join a in db.StockProductTables on p.ProductId equals a.ProductId
                            join stm in db.StockMasters on a.StockId equals stm.StockId into fcms
                            from stm in fcms.DefaultIfEmpty()
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            join r in db.RackMasters on b.RackId equals r.RackId
                            join s in db.StoreMasters on r.StoreId equals s.StoreId
                            where productId == p.ProductId && p.Disabled != true && p.MinimumQuantity > 0
                                  && b.InspectionType.ToLower() == "accepted" && b.Quantity > 0 && s.IsWorkInProgressStore == false
                            let minQuantity = p.MinimumQuantity
                            select new
                            {
                                p.ProductId,
                                p.ProductName,
                                p.ProductCode,
                                p.ProductType,
                                pr.ProductCategory,
                                pf.ProductFirstSubCategory,
                                psc.ProductSecSubCategory,
                                stm.Batch,
                                s.StoreName,
                                s.StoreCode,
                                r.RackName,
                                r.RackCode,
                                b.Quantity,
                                MinQuantity = minQuantity,
                                a.Unit
                            }).ToList();

                // Group and transform the data
                var totaldata = data
                    .GroupBy(a => new
                    {
                        a.ProductId,
                        a.ProductName
                    })
                    .Select(g => new ProductStockStoreRackReportVm
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.ProductName,
                        ProdQuantity = g.Sum(x => x.Quantity),
                        MinimumQty = g.First().MinQuantity,
                        Unit = g.First().Unit
                    })
                    .Where(x => x.ProdQuantity < x.MinimumQty)
                    .ToList();

                if (totaldata.Any())
                {
                    StringBuilder messageBody = new StringBuilder();
                    messageBody.Append("<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>The Following Products Have Low Quantity:</span><br><br>");

                    foreach (var product in totaldata)
                    {
                        var productDetails = data.Where(d => d.ProductId == product.ProductId).ToList();

                        messageBody.Append("<div style='font-family: Arial, Helvetica, sans-serif; font-size: 15px; margin-bottom: 10px;'>");
                        messageBody.AppendFormat("<strong>Product Name: {0}</strong> || Minimum Quantity: {1} || Total Available Quantity in All Stores: {2} (Except Work In Progress Stores)",
                                                 product.ProductName, product.MinimumQty + " " + product.Unit, product.ProdQuantity + " " + product.Unit);
                        messageBody.Append("</div>");

                        string htmlTableStart = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
                        string htmlTableEnd = "</table>";
                        string htmlHeaderRowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Product Type</th>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Product Name</th>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Product Code</th>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Product Category</th>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>First Sub Category</th>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Second Sub Category</th>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Batch No.</th>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Store Name</th>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Rack Name</th>
                            <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Available Quantity</th>
                        </tr>";
                        string htmlTrStart = "<tr style=\'text-align: left;font-size: small;border: 1px solid black;\' > ";
                        string htmlTrEnd = "</tr>";
                        string htmlTdStart = "<td style='border: 1px solid #ddd; padding: 8px;'>";
                        string htmlTdEnd = "</td>";

                        // Start of product-specific table with headers
                        messageBody.Append(htmlTableStart);
                        messageBody.Append(htmlHeaderRowStart);

                        foreach (var item in productDetails)
                        {
                            messageBody.Append(htmlTrStart);
                            messageBody.Append(htmlTdStart + item.ProductType + htmlTdEnd);
                            messageBody.Append(htmlTdStart + item.ProductName + htmlTdEnd);
                            messageBody.Append(htmlTdStart + item.ProductCode + htmlTdEnd);
                            messageBody.Append(htmlTdStart + item.ProductCategory + htmlTdEnd);
                            messageBody.Append(htmlTdStart + item.ProductFirstSubCategory + htmlTdEnd);
                            messageBody.Append(htmlTdStart + item.ProductSecSubCategory + htmlTdEnd);
                            messageBody.Append(htmlTdStart + item.Batch + htmlTdEnd);
                            messageBody.Append(htmlTdStart + item.StoreName + htmlTdEnd);
                            messageBody.Append(htmlTdStart + item.RackName + htmlTdEnd);
                            messageBody.Append(htmlTdStart + item.Quantity + " " + item.Unit + htmlTdEnd);
                            messageBody.Append(htmlTrEnd);
                        }

                        // End of product-specific table
                        messageBody.Append(htmlTableEnd);
                        messageBody.Append("<br><br>");
                    }

                    messageBody.Append("<br><br>Regards,<br>Zaibunco Industries Automated System<br>Powered by KanzenFlow<br>");
                    // Send your email with messageBody.ToString() as the email
                    var emaillist = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.LowStockReportsGroup && x.Enabled == "true").Select(x => x.EmailId).ToArray();
                    var currentdatetime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("dd-MMM-yyyy hh:mm tt");
                    EmailDataFn.SendEmail(messageBody.ToString(), null, emaillist, null, "Stock Alert | Low Product Stock Quantity - " + currentdatetime);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task SendStockPriceUpdateMail(List<long?> stockProductIds, List<long?> saleOrderIds)
        {
            using (var db = new Models.pmsdbContext())
            {
                var dataSet = (from a in db.StockPriceTrackingTables
                               where a.UpdatedDate == db.StockPriceTrackingTables
                                                    .Where(x => x.StockProductId == a.StockProductId)
                                                    .Max(x => x.UpdatedDate)
                               join s in db.StockProductTables on a.StockProductId equals s.StockProductId
                               join pm in db.ProductMasters on s.ProductId equals pm.ProductId
                               join stm in db.StockMasters on s.StockId equals stm.StockId
                               where stockProductIds.Contains(a.StockProductId)
                               select new StockProductTrackingPriceVm
                               {
                                   ProductId = pm.ProductId,
                                   StockProductId = a.StockProductId,
                                   ProductName = pm.ProductName,
                                   Unit = pm.Unit,
                                   Batch = stm.Batch,
                                   PricePerUnit = a.PricePerUnit,
                                   NewPricePerUnit = a.NewPricePerUnit,
                                   ShippingHandlingPerUnit = a.ShippingHandlingPerUnit,
                                   NewShippingHandlingPerUnit = a.NewShippingHandlingPerUnit,
                                   FreightPerUnit = a.FreightPerUnit,
                                   NewFreightPerUnit = a.NewFreightPerUnit,
                                   InvoicePricePerUnit = a.InvoicePricePerUnit,
                                   NewInvoicePricePerUnit = a.NewInvoicePricePerUnit,
                                   MiscPerUnit = a.MiscPerUnit,
                                   NewMiscPerUnit = a.NewMiscPerUnit,
                               }).ToList();

                StringBuilder messageBody = new StringBuilder();
                messageBody.Append("<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Price Updated for following Product: </span><br><br>");

                if (dataSet != null)
                {
                    string htmlTableStart = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
                    string htmlTableEnd = "</table>";
                    string htmlHeaderRowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>S. No.</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Product Name</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Batch Number</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Previous Rate</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>New Rate</th>
                    </tr>";
                    string htmlTrStart = "<tr style=\'text-align: left;font-size: small;border: 1px solid black;\' > ";
                    string htmlTrEnd = "</tr>";
                    string htmlTdStart = "<td style='border: 1px solid #ddd; padding: 8px;'>";
                    string htmlTdEnd = "</td>";

                    messageBody.Append(htmlTableStart);
                    messageBody.Append(htmlHeaderRowStart);
                    int i = 0;
                    foreach (var row in dataSet)
                    {
                        i++;
                        messageBody.Append(htmlTrStart);
                        messageBody.Append(htmlTdStart + i + htmlTdEnd);
                        messageBody.Append(htmlTdStart + row.ProductName + htmlTdEnd);
                        messageBody.Append(htmlTdStart + row.Batch + htmlTdEnd);
                        messageBody.Append(htmlTdStart + "₹ " + row.PricePerUnit + " " + row.Unit + htmlTdEnd);
                        messageBody.Append(htmlTdStart + "₹ " + row.NewPricePerUnit + " " + row.Unit + htmlTdEnd);
                        messageBody.Append(htmlTrEnd);
                    }
                    messageBody.Append(htmlTableEnd);
                    messageBody.Append("<br><br>");

                    messageBody.Append("<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Additional Rate Details(Per Unit):</span><br><br>");

                    string htmlTable3Start = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
                    string htmlTable3End = "</table>";
                    string htmlHeader3RowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Previous Invoice Rate</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>New Invoice Rate</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Previous S&H Rate</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>New S&H Rate</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Previous Freight Rate</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>New Freight Rate</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Previous Misc Rate</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>New Misc Rate</th>
                    </tr>";

                    messageBody.Append(htmlTable3Start);
                    messageBody.Append(htmlHeader3RowStart);
                    messageBody.Append(htmlTrStart);
                    messageBody.Append(htmlTdStart + "₹ " + dataSet.FirstOrDefault().InvoicePricePerUnit + htmlTdEnd);
                    messageBody.Append(htmlTdStart + "₹ " + dataSet.FirstOrDefault().NewInvoicePricePerUnit + htmlTdEnd);
                    messageBody.Append(htmlTdStart + "₹ " + dataSet.FirstOrDefault().ShippingHandlingPerUnit + htmlTdEnd);
                    messageBody.Append(htmlTdStart + "₹ " + dataSet.FirstOrDefault().NewShippingHandlingPerUnit + htmlTdEnd);
                    messageBody.Append(htmlTdStart + "₹ " + dataSet.FirstOrDefault().FreightPerUnit + htmlTdEnd);
                    messageBody.Append(htmlTdStart + "₹ " + dataSet.FirstOrDefault().NewFreightPerUnit + htmlTdEnd);
                    messageBody.Append(htmlTdStart + "₹ " + dataSet.FirstOrDefault().MiscPerUnit + htmlTdEnd);
                    messageBody.Append(htmlTdStart + "₹ " + dataSet.FirstOrDefault().NewMiscPerUnit + htmlTdEnd);
                    messageBody.Append(htmlTrEnd);
                    messageBody.Append(htmlTable3End);
                    messageBody.Append("<br><br>");

                    var saleOrderData = db.SaleOrderTables
                                    .Join(db.CustomerMasters, x => x.CustomerId, c => c.CustomerId, (x, c) => new { x, c })
                                    .Join(db.SaleOrderProductionTables, x => x.x.SaleOrderId, d => d.SaleOrderId, (x, d) => new { x, d })
                                    .Where(x => saleOrderIds.Contains(x.x.x.SaleOrderId) && x.d.CostingStatus == "Submitted")
                                    .Select(x => new { x.x.x.SaleOrderId, x.x.x.SaleOrderNumber, x.x.x.SaleOrderType, x.x.c.CustomerName, x.x.x.SaleOrderDate })
                                    .OrderByDescending(x => x.SaleOrderId)
                                    .ToList();

                    if (saleOrderData != null)
                    {
                        messageBody.Append("<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Sale Orders Requiring Costing Recalculation:</span><br><br>");

                        string htmlTable2Start = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
                        string htmlTable2End = "</table>";
                        string htmlHeader2RowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>S. No.</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order Date</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order Type</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Sale Order Number</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Customer Name</th>
                        </tr>";

                        messageBody.Append(htmlTable2Start);
                        messageBody.Append(htmlHeader2RowStart);

                        int j = 0;
                        foreach (var saleOrder in saleOrderData)
                        {
                            j++;
                            messageBody.Append(htmlTrStart);
                            messageBody.Append(htmlTdStart + j + htmlTdEnd);
                            messageBody.Append(htmlTdStart + TimeZoneInfo.ConvertTimeFromUtc(saleOrder.SaleOrderDate.Value, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("dd-MMM-yyyy hh:mm tt") + htmlTdEnd);
                            messageBody.Append(htmlTdStart + saleOrder.SaleOrderType + htmlTdEnd);
                            messageBody.Append(htmlTdStart + saleOrder.SaleOrderNumber + htmlTdEnd);
                            messageBody.Append(htmlTdStart + saleOrder.CustomerName + htmlTdEnd);
                            messageBody.Append(htmlTrEnd);
                        }
                        messageBody.Append(htmlTable2End);
                    }

                    messageBody.Append(@"<br><br>
                                 <span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>
                                Regards,<br>
                                Zaibunco Industries Automated System<br>
                                Powered by KanzenFlow
                                <span>");
                }
                if (dataSet != null)
                {
                    var emaillist = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.CostingRecalculationGroup && x.Enabled == "true").Select(x => x.EmailId).ToArray();
                    EmailDataFn.SendEmail(messageBody.ToString(), null, emaillist, null, $"Price Update Alert | Sale Order Recalculation Required | {dataSet.FirstOrDefault().ProductName} | {dataSet.FirstOrDefault().Batch}");
                }
            }
        }

        public string GetSaleOrderDetailsHtmlTableForEmail(SaleOrderTableVm dataSet, string SaleOrderStatus)
        {
            var messageBody = "";

            string htmlTableStart = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
            string htmlTableEnd = "</table>";
            string htmlHeaderRowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order Type</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>CUSTOMER</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Sale Order No.</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Product Code</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Sale Order Date</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Estimated Delivery Date</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Sale Order Status</th>
                    </tr>";
            string htmlTrStart = "<tr style=\'text-align: left;font-size: small;border: 1px solid black;\' > ";
            string htmlTrEnd = "</tr>";
            string htmlTdStart = "<td style='border: 1px solid #ddd; padding: 8px;'>";
            string htmlTdEnd = "</td>";

            messageBody += htmlTableStart;
            messageBody += htmlHeaderRowStart;
            messageBody = messageBody + htmlTrStart;
            messageBody = messageBody + htmlTdStart + dataSet.SaleOrderType + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + dataSet.CustomerName + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + dataSet.SaleOrderNumber + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + dataSet.SaleFormulationCode + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + TimeZoneInfo.ConvertTimeFromUtc(dataSet.SaleOrderDate.Value, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("dd-MMM-yyyy hh:mm tt") + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + TimeZoneInfo.ConvertTimeFromUtc(dataSet.DeliveryDate.Value, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("dd-MMM-yyyy") + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + SaleOrderStatus + htmlTdEnd;
            messageBody = messageBody + htmlTrEnd;
            messageBody = messageBody + htmlTableEnd;

            string htmlTable2Start = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
            string htmlTable2End = "</table>";
            string htmlHeader2RowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Article Name</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order Quantity</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Thickness</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Width</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Color</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Grain</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Buyer Order Reference</th>
                    </tr>";
            string htmlTr2Start = "<tr style=\'text-align: left;font-size: small;border: 1px solid black;\' > ";
            string htmlTr2End = "</tr>";
            string htmlTd2Start = "<td style='border: 1px solid #ddd; padding: 8px;'>";
            string htmlTd2End = "</td>";

            messageBody += htmlTable2Start;
            messageBody += htmlHeader2RowStart;
            messageBody = messageBody + htmlTr2Start;
            messageBody = messageBody + htmlTd2Start + dataSet.SaleOrderProduction.ManufacturingProductName + htmlTd2End;
            messageBody = messageBody + htmlTd2Start + dataSet.SaleOrderProduction.OrderQuantity + htmlTd2End;
            messageBody = messageBody + htmlTd2Start + dataSet.SaleOrderProduction.ThicknessValue + htmlTd2End;
            messageBody = messageBody + htmlTd2Start + dataSet.SaleOrderProduction.WidthNumber + htmlTd2End;
            messageBody = messageBody + htmlTd2Start + dataSet.SaleOrderProduction.ColorName + htmlTd2End;
            messageBody = messageBody + htmlTd2Start + dataSet.SaleOrderProduction.GrainCode + htmlTd2End;
            messageBody = messageBody + htmlTd2Start + dataSet.BORNumber + htmlTd2End;
            messageBody = messageBody + htmlTr2End;
            messageBody = messageBody + htmlTable2End;
            return messageBody;
        }

        public string GetOrderDetailsHtmlWithoutCustomerForEmail(SaleOrderTableVm dataSet, string SaleOrderStatus)
        {
            var messageBody = "";

            string htmlTableStart = "<table style='font-family: Arial, Helvetica, sans-serif; border-collapse: collapse; width: 100%;' >";
            string htmlTableEnd = "</table>";
            string htmlHeaderRowStart = @"<tr style='text-align: left;font-size:small;border: 1px solid black;'>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Order Type</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Product Code</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Thickness</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Width</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Color</th>
                        <th style='border: 1px solid #ddd; padding: 8px; padding-top: 12px; padding-bottom: 12px; text-align: left; background-color: #04AA6D; color: white;'>Grain</th>
                    </tr>";
            string htmlTrStart = "<tr style=\'text-align: left;font-size: small;border: 1px solid black;\' > ";
            string htmlTrEnd = "</tr>";
            string htmlTdStart = "<td style='border: 1px solid #ddd; padding: 8px;'>";
            string htmlTdEnd = "</td>";

            messageBody += htmlTableStart;
            messageBody += htmlHeaderRowStart;
            messageBody = messageBody + htmlTrStart;
            messageBody = messageBody + htmlTdStart + dataSet.SaleOrderType + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + dataSet.SaleFormulationCode + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + dataSet.SaleOrderProduction.ThicknessValue + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + dataSet.SaleOrderProduction.WidthNumber + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + dataSet.SaleOrderProduction.ColorName + htmlTdEnd;
            messageBody = messageBody + htmlTdStart + dataSet.SaleOrderProduction.GrainCode + htmlTdEnd;
            messageBody = messageBody + htmlTrEnd;
            messageBody = messageBody + htmlTableEnd;

            return messageBody;
        }

        public string GetSaleOrderDetailsHtmlFooterEmail()
        {
            string messageBody = @"<br><br>
                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>If you have any questions about your order then please reach out to your Sales Marketing Coordinator directly or by replying to this email.<br>
                                Your reply email will be sent to Sales Team.<span>";
            messageBody += @"<br><br>
                                This email is sent from an automated system and from a DoNotReply mailbox which is not monitored.";
            messageBody += @"<br><br>
                                If you want to unsubscribe from email updates, please send your <NAME_EMAIL>. To make sure this email is not sent to your 'junk/spam' folder, select the email and add the sender to your Address Book.";

            messageBody += @"<br><br>
                                 <span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>
                                Regards,<br>
                                Zaibunco Industries Automated System<br>
                                Powered by KanzenFlow
                                <span>";
            return messageBody;
        }

        public string GetDispatchPackagingMessageHtml(SaleOrderTableVm dataset, string SaleOrderStatus)
        {
            string messageBody = "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Dear Sir / Madam, </span><br><br>";
            messageBody += "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>Here is your Order status.</span>";
            messageBody += @"<br><br>
                                    <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'><b> Update: Your order is ready for Dispatch. Please find attached dispatch packaging details.</b></span>";
            messageBody = messageBody + GetSaleOrderDetailsHtmlTableForEmail(dataset, SaleOrderStatus);

            messageBody = messageBody + GetSaleOrderDetailsHtmlFooterEmail();
            return messageBody;
        }

        public MfgHearbeatReportResponseVm GetMfgHearbeatReport(MfgHearbeatReportRequestVm filter)
        {
            // Input validation
            ValidateHeartbeatReportInput(filter);

            using var db = new pmsdbContext();
            List<ProductionTimelineData> query = new List<ProductionTimelineData>();

            switch (filter.ProductionLineType)
            {
                case "Manufacturing":
                    query = (from wpjm in db.WorkPlanJumboMasters
                             join so in db.SaleOrderTables on wpjm.SaleOrderId equals so.SaleOrderId
                             join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                             join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                             join fw in db.FactoryWorkersMasters on wpjm.ShiftSupervisorWorkerId equals fw.WorkerId into fwd
                             from fw in fwd.DefaultIfEmpty()
                             where wpjm.JumboRollStartTime != null && wpjm.JumboRollEndTime != null
                             // Fixed: Use overlapping time range logic instead of contained logic
                             && (filter.DateFrom == null || wpjm.JumboRollEndTime > filter.DateFrom)
                             && (filter.DateTo == null || wpjm.JumboRollStartTime < filter.DateTo)
                             && (string.IsNullOrEmpty(filter.WorkShift) || wp.WorkShift == filter.WorkShift)
                             && (filter.ProductionLineNo == 0 || filter.ProductionLineNo == null || wp.ProductionLineNo == filter.ProductionLineNo)
                             select new ProductionTimelineData
                             {
                                 ProcessDate = wpjm.JumboRollDate,
                                 StartTime = wpjm.JumboRollStartTime,
                                 EndTime = wpjm.JumboRollEndTime,
                                 AddedDate = wpjm.AddedDate,
                                 ProcessNo = wpjm.JumboNo,
                                 SaleOrderId = wpjm.SaleOrderId,
                                 SaleOrderNumber = so.SaleOrderNumber,
                                 ProductionLineNo = wp.ProductionLineNo,
                                 WorkShift = wp.WorkShift,
                                 SupervisorName = fw.Name,
                                 ProcessQty = wpjm.JumboRolQty
                             }).OrderBy(x => x.StartTime).ToList();
                    break;

                case "Printing":
                    query = (from print in db.SaleOrderPostProcessPrintTables
                             join so in db.SaleOrderTables on print.SaleOrderId equals so.SaleOrderId
                             join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                             join spp in db.SaleOrderProductionPrintMasters on sop.SaleOrderProductionId equals spp.SaleOrderProductionId
                             join pm in db.PrintMasters on spp.PrintMasterId equals pm.PrintMasterId
                             join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                             join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                             join fw in db.FactoryWorkersMasters on print.ShiftSupervisorWorkerId equals fw.WorkerId into fwd
                             from fw in fwd.DefaultIfEmpty()
                             where print.StartDateTime != null && print.EndDateTime != null
                             && print.PrintStatus == "Completed"
                             // Fixed: Use overlapping time range logic
                             && (filter.DateFrom == null || print.EndDateTime > filter.DateFrom)
                             && (filter.DateTo == null || print.StartDateTime < filter.DateTo)
                             //  && (string.IsNullOrEmpty(filter.WorkShift) || wp.WorkShift == filter.WorkShift)
                             && (filter.ProductionLineNo == 0 || filter.ProductionLineNo == null || wp.ProductionLineNo == filter.ProductionLineNo)
                             select new ProductionTimelineData
                             {
                                 ProcessDate = print.StartDateTime,
                                 StartTime = print.StartDateTime,
                                 EndTime = print.EndDateTime,
                                 AddedDate = print.AddedDate,
                                 ProcessNo = "Print: " + pm.Name + " - Sequence No.: " + spp.Rank,
                                 SaleOrderId = print.SaleOrderId,
                                 SaleOrderNumber = so.SaleOrderNumber,
                                 ProductionLineNo = wp.ProductionLineNo,
                                 WorkShift = wp.WorkShift,
                                 SupervisorName = fw.Name,
                                 ProcessQty = print.PrintCompletedQuantity
                             }).Distinct().OrderBy(x => x.StartTime).ToList();
                    break;

                case "Embossing":
                    query = (from emb in db.SaleOrderPostProcessEmbossingTables
                             join so in db.SaleOrderTables on emb.SaleOrderId equals so.SaleOrderId
                             join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                             join spp in db.SaleOrderProductionEmbossingMasters on sop.SaleOrderProductionId equals spp.SaleOrderProductionId
                             join em in db.EmbossingMasters on spp.EmbossingMasterId equals em.EmbossingMasterId
                             join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                             join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                             join fw in db.FactoryWorkersMasters on emb.ShiftSupervisorWorkerId equals fw.WorkerId into fwd
                             from fw in fwd.DefaultIfEmpty()
                             where emb.StartDateTime != null && emb.EndDateTime != null
                             && emb.EmbossingStatus == "Completed"
                             // Fixed: Use overlapping time range logic
                             && (filter.DateFrom == null || emb.EndDateTime > filter.DateFrom)
                             && (filter.DateTo == null || emb.StartDateTime < filter.DateTo)
                             //  && (string.IsNullOrEmpty(filter.WorkShift) || wp.WorkShift == filter.WorkShift)
                             && (filter.ProductionLineNo == 0 || filter.ProductionLineNo == null || wp.ProductionLineNo == filter.ProductionLineNo)
                             select new ProductionTimelineData
                             {
                                 ProcessDate = emb.StartDateTime,
                                 StartTime = emb.StartDateTime,
                                 EndTime = emb.EndDateTime,
                                 AddedDate = emb.AddedDate,
                                 ProcessNo = "Embossing: " + em.Name + " - Sequence No.: " + spp.Rank,
                                 SaleOrderId = emb.SaleOrderId,
                                 SaleOrderNumber = so.SaleOrderNumber,
                                 ProductionLineNo = wp.ProductionLineNo,
                                 WorkShift = wp.WorkShift,
                                 SupervisorName = fw.Name,
                                 ProcessQty = emb.EmbossingCompletedQuantity
                             }).OrderBy(x => x.StartTime).ToList();
                    break;

                case "Vacuum":
                    query = (from vac in db.SaleOrderPostProcessVacuumTables
                             join so in db.SaleOrderTables on vac.SaleOrderId equals so.SaleOrderId
                             join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                             join spp in db.SaleOrderProductionVacuumMasters on sop.SaleOrderProductionId equals spp.SaleOrderProductionId
                             join vm in db.VacuumMasters on spp.VacuumMasterId equals vm.VacuumMasterId
                             join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                             join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                             join fw in db.FactoryWorkersMasters on vac.ShiftSupervisorWorkerId equals fw.WorkerId into fwd
                             from fw in fwd.DefaultIfEmpty()
                             where vac.StartDateTime != null && vac.EndDateTime != null
                             && vac.VacuumStatus == "Completed"
                             // Fixed: Use overlapping time range logic
                             && (filter.DateFrom == null || vac.EndDateTime > filter.DateFrom)
                             && (filter.DateTo == null || vac.StartDateTime < filter.DateTo)
                             //  && (string.IsNullOrEmpty(filter.WorkShift) || wp.WorkShift == filter.WorkShift)
                             && (filter.ProductionLineNo == 0 || filter.ProductionLineNo == null || wp.ProductionLineNo == filter.ProductionLineNo)
                             select new ProductionTimelineData
                             {
                                 ProcessDate = vac.StartDateTime,
                                 StartTime = vac.StartDateTime,
                                 EndTime = vac.EndDateTime,
                                 AddedDate = vac.AddedDate,
                                 ProcessNo = "Vacuum: " + vm.Name + " - Sequence No.: " + spp.Rank,
                                 SaleOrderId = vac.SaleOrderId,
                                 SaleOrderNumber = so.SaleOrderNumber,
                                 ProductionLineNo = wp.ProductionLineNo,
                                 WorkShift = wp.WorkShift,
                                 SupervisorName = fw.Name,
                                 ProcessQty = vac.VacuumCompletedQuantity
                             }).OrderBy(x => x.StartTime).ToList();
                    break;

                case "Lacquer":
                    query = (from lac in db.SaleOrderPostProcessLacquerTables
                             join so in db.SaleOrderTables on lac.SaleOrderId equals so.SaleOrderId
                             join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                             join spp in db.SaleOrderProductionLacquerRawMaterialTables on sop.SaleOrderProductionId equals spp.SaleOrderProductionId
                             join lm in db.LacquerMasters on spp.LacquerMasterId equals lm.LacquerMasterId
                             join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                             join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                             join fw in db.FactoryWorkersMasters on lac.ShiftSupervisorWorkerId equals fw.WorkerId into fwd
                             from fw in fwd.DefaultIfEmpty()
                             where lac.StartDateTime != null && lac.EndDateTime != null
                             && lac.LacquerStatus == "Completed"
                             // Fixed: Use overlapping time range logic
                             && (filter.DateFrom == null || lac.EndDateTime > filter.DateFrom)
                             && (filter.DateTo == null || lac.StartDateTime < filter.DateTo)
                             && (string.IsNullOrEmpty(filter.WorkShift) || wp.WorkShift == filter.WorkShift)
                             && (filter.ProductionLineNo == 0 || filter.ProductionLineNo == null || wp.ProductionLineNo == filter.ProductionLineNo)
                             select new ProductionTimelineData
                             {
                                 ProcessDate = lac.StartDateTime,
                                 StartTime = lac.StartDateTime,
                                 EndTime = lac.EndDateTime,
                                 AddedDate = lac.AddedDate,
                                 ProcessNo = "Lacquer: " + lm.Name + " - Sequence No.: " + spp.Rank,
                                 SaleOrderId = lac.SaleOrderId,
                                 SaleOrderNumber = so.SaleOrderNumber,
                                 ProductionLineNo = wp.ProductionLineNo,
                                 WorkShift = wp.WorkShift,
                                 SupervisorName = fw.Name,
                                 ProcessQty = lac.LacquerCompletedQuantity
                             }).OrderBy(x => x.StartTime).ToList();
                    break;

                case "Tumbling":
                    query = (from tumbling in db.SaleOrderPostProcessTumblingTables
                             join so in db.SaleOrderTables on tumbling.SaleOrderId equals so.SaleOrderId
                             join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                             join spp in db.SaleOrderProductionTumblingMasters on sop.SaleOrderProductionId equals spp.SaleOrderProductionId
                             join tm in db.TumblingMasters on spp.TumblingMasterId equals tm.TumblingMasterId
                             join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                             join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                             join fw in db.FactoryWorkersMasters on tumbling.ShiftSupervisorWorkerId equals fw.WorkerId into fwd
                             from fw in fwd.DefaultIfEmpty()
                             where tumbling.StartDateTime != null && tumbling.EndDateTime != null
                             && tumbling.TumblingStatus == "Completed"
                             // Fixed: Use overlapping time range logic
                             && (filter.DateFrom == null || tumbling.EndDateTime > filter.DateFrom)
                             && (filter.DateTo == null || tumbling.StartDateTime < filter.DateTo)
                             //  && (string.IsNullOrEmpty(filter.WorkShift) || wp.WorkShift == filter.WorkShift)
                             && (filter.ProductionLineNo == 0 || filter.ProductionLineNo == null || wp.ProductionLineNo == filter.ProductionLineNo)
                             select new ProductionTimelineData
                             {
                                 ProcessDate = tumbling.StartDateTime,
                                 StartTime = tumbling.StartDateTime,
                                 EndTime = tumbling.EndDateTime,
                                 AddedDate = tumbling.AddedDate,
                                 ProcessNo = "Tumbling: " + tm.Name + " - Sequence No.: " + spp.Rank,
                                 SaleOrderId = tumbling.SaleOrderId,
                                 SaleOrderNumber = so.SaleOrderNumber,
                                 ProductionLineNo = wp.ProductionLineNo,
                                 WorkShift = wp.WorkShift,
                                 SupervisorName = fw.Name,
                                 ProcessQty = tumbling.TumblingCompletedQuantity
                             }).OrderBy(x => x.StartTime).ToList();
                    break;

                default:
                    throw new ArgumentException($"Unsupported ProductionLineType: {filter.ProductionLineType}");
            }

            // Validate and normalize production data timestamps
            query = ValidateAndNormalizeProductionData(query, filter.DateFrom.Value, filter.DateTo.Value);

            // Get scheduled downtimes for the period
            var productionDataFn = new ProductionDataFn(GlobalData);
            var scheduledDowntimes = productionDataFn.GetScheduledDowntimesForPeriod(db, filter.DateFrom.Value, filter.DateTo.Value, filter.ProductionLineNo.Value, filter.ProductionLineType);

            scheduledDowntimes.ForEach(x =>
            {
                x.StartDateTime = TimeZoneHelper.ConvertToTimeZone(x.StartDateTime, TimeZoneId.IndiaStandardTime);
                x.EndDateTime = TimeZoneHelper.ConvertToTimeZone(x.EndDateTime, TimeZoneId.IndiaStandardTime);
                x.EffectiveFrom = TimeZoneHelper.ConvertToTimeZone(x.EffectiveFrom.Value, TimeZoneId.IndiaStandardTime);
                x.EffectiveTo = x.EffectiveTo.HasValue ? TimeZoneHelper.ConvertToTimeZone(x.EffectiveTo.Value, TimeZoneId.IndiaStandardTime) : x.EffectiveTo;
            });

            // Get actual downtimes with overlapping time range logic
            var actualDowntimes = (from dt in db.ProductionDowntimeTables
                                   join reason in db.ProductionDowntimeReasonMasters on dt.ProductionDowntimeReasonId equals reason.ProductionDowntimeReasonId
                                   join createdBy in db.UserMasters on dt.CreatedBy equals createdBy.Email into createdByGroup
                                   from createdBy in createdByGroup.DefaultIfEmpty()
                                   join modifiedBy in db.UserMasters on dt.ModifiedBy equals modifiedBy.Email into modifiedByGroup
                                   from modifiedBy in modifiedByGroup.DefaultIfEmpty()
                                   where dt.IsDeleted == false &&
                                         dt.ProductionLineNo == filter.ProductionLineNo &&
                                         dt.ProductionLineType.ToLower() == filter.ProductionLineType.ToLower() &&
                                         // Fixed: Use overlapping time range logic for downtimes too
                                         dt.EndTime > filter.DateFrom &&
                                         dt.StartTime < filter.DateTo
                                   orderby dt.StartTime
                                   select new ProductionDowntimeTableVm
                                   {
                                       StartTime = dt.StartTime,
                                       EndTime = dt.EndTime,
                                       ReasonName = reason.ReasonName,
                                       ReasonCode = reason.ReasonCode,
                                       ProductionLineType = dt.ProductionLineType,
                                       ProductionLineNo = dt.ProductionLineNo,
                                       ActualDurationMinutes = dt.ActualDurationMinutes,
                                       ExcessDurationMinutes = dt.ExcessDurationMinutes,
                                       Comments = dt.Comments,
                                       CreatedBy = createdBy.Name,
                                       CreatedOn = dt.CreatedOn,
                                       ModifiedBy = modifiedBy.Name,
                                       ModifiedOn = dt.ModifiedOn,
                                       ProductionDowntimeReason = new ProductionDowntimeReasonMasterVm
                                       {
                                           ReasonName = reason.ReasonName,
                                           ReasonCode = reason.ReasonCode,
                                           StandardDurationMinutes = reason.StandardDurationMinutes,
                                           ProductionLineType = reason.ProductionLineType,
                                           DowntimeType = reason.DowntimeType
                                       }
                                   }).ToList();

            var chartData = new List<MfgHearbeatReportChartDataVm>();
            var filterToISTDate = TimeZoneHelper.ConvertToTimeZone(filter.DateTo.Value, TimeZoneId.IndiaStandardTime);
            var filterFromISTDate = TimeZoneHelper.ConvertToTimeZone(filter.DateFrom.Value, TimeZoneId.IndiaStandardTime);

            // Fixed: Always cover the complete requested time range, not just until last available data
            // Step 1: Initialize the entire timeline with scheduled downtimes and default gaps
            productionDataFn.AddTimeRangeWithScheduledDowntimes(
                chartData,
                filterFromISTDate,
                filterToISTDate,  // Cover the complete requested range
                scheduledDowntimes,
                "Production Down",
                "Unknown reason"
            );

            // Step 2: Add standard duration downtimes (overrides scheduled)
            foreach (var downtime in actualDowntimes.OrderBy(x => x.StartTime))
            {
                var startTime = TimeZoneHelper.ConvertToTimeZone(downtime.StartTime.Value, TimeZoneId.IndiaStandardTime);
                var standardDurationMinutes = downtime.ProductionDowntimeReason.StandardDurationMinutes ?? 0;

                // Fixed: Handle zero-duration downtimes as instant events
                if (standardDurationMinutes >= 0) // Include zero-duration downtimes
                {
                    var standardDurationEnd = standardDurationMinutes > 0
                        ? startTime.AddMinutes((double)standardDurationMinutes)
                        : startTime.AddMinutes(1); // Minimum 1-minute duration for zero-duration events

                    // Clip to requested time range
                    var clippedStart = startTime > filterFromISTDate ? startTime : filterFromISTDate;
                    var clippedEnd = standardDurationEnd < filterToISTDate ? standardDurationEnd : filterToISTDate;

                    if (clippedStart < clippedEnd)
                    {
                        RemoveOverlappingPeriods(chartData, clippedStart, clippedEnd);
                        chartData.Add(new MfgHearbeatReportChartDataVm
                        {
                            startTime = clippedStart,
                            endTime = clippedEnd,
                            status = "Acceptable Downtime",
                            reason = downtime.ProductionDowntimeReason?.ReasonName,
                            downtimeType = downtime.ProductionDowntimeReason?.DowntimeType
                        });
                    }
                }
            }

            // Step 3: Add excess duration downtimes (overrides standard)
            foreach (var downtime in actualDowntimes.OrderBy(x => x.StartTime))
            {
                var startTime = TimeZoneHelper.ConvertToTimeZone(downtime.StartTime.Value, TimeZoneId.IndiaStandardTime);
                var actualEnd = TimeZoneHelper.ConvertToTimeZone(downtime.EndTime.Value, TimeZoneId.IndiaStandardTime);
                var standardDurationMinutes = downtime.ProductionDowntimeReason.StandardDurationMinutes ?? 0;
                var standardDurationEnd = standardDurationMinutes > 0
                    ? startTime.AddMinutes((double)standardDurationMinutes)
                    : startTime.AddMinutes(1); // Handle zero-duration case

                if (actualEnd > standardDurationEnd)
                {
                    // Clip to requested time range
                    var clippedStart = standardDurationEnd > filterFromISTDate ? standardDurationEnd : filterFromISTDate;
                    var clippedEnd = actualEnd < filterToISTDate ? actualEnd : filterToISTDate;

                    if (clippedStart < clippedEnd)
                    {
                        RemoveOverlappingPeriods(chartData, clippedStart, clippedEnd);
                        chartData.Add(new MfgHearbeatReportChartDataVm
                        {
                            startTime = clippedStart,
                            endTime = clippedEnd,
                            status = "Excess Downtime",
                            reason = downtime.ProductionDowntimeReason?.ReasonName,
                            downtimeType = downtime.ProductionDowntimeReason?.DowntimeType
                        });
                    }
                }
            }

            // Step 4: Add production periods (highest priority)
            foreach (var item in query)
            {
                var startTime = TimeZoneHelper.ConvertToTimeZone(item.StartTime.Value, TimeZoneId.IndiaStandardTime);
                var endTime = TimeZoneHelper.ConvertToTimeZone(item.EndTime.Value, TimeZoneId.IndiaStandardTime);

                // Clip to requested time range
                var clippedStart = startTime > filterFromISTDate ? startTime : filterFromISTDate;
                var clippedEnd = endTime < filterToISTDate ? endTime : filterToISTDate;

                if (clippedStart < clippedEnd)
                {
                    RemoveOverlappingPeriods(chartData, clippedStart, clippedEnd);
                    chartData.Add(new MfgHearbeatReportChartDataVm
                    {
                        startTime = clippedStart,
                        endTime = clippedEnd,
                        status = "Running",
                        processNo = item.ProcessNo,
                        reason = item.SaleOrderNumber + " - " + item.ProcessNo,
                        saleOrderNumber = item.SaleOrderNumber
                    });
                }
            }

            // Step 5: Enhanced gap processing with proper future/past period handling
            var currentDateTimeIST = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);

            // Sort all periods and identify any remaining gaps
            var sortedChartData = chartData.OrderBy(x => x.startTime).ToList();
            var finalChartData = new List<MfgHearbeatReportChartDataVm>();
            var currentTime = filterFromISTDate;

            foreach (var period in sortedChartData)
            {
                // Fill gap before this period if it exists
                if (currentTime < period.startTime)
                {
                    var gapStatus = DetermineGapStatus(currentTime, period.startTime, currentDateTimeIST);
                    var gapReason = gapStatus == "No Production" ? "No activity scheduled" : "Unknown reason";

                    finalChartData.Add(new MfgHearbeatReportChartDataVm
                    {
                        startTime = currentTime,
                        endTime = period.startTime,
                        status = gapStatus,
                        reason = gapReason
                    });
                }

                finalChartData.Add(period);
                currentTime = period.endTime;
            }

            // Handle final gap to end of requested period
            if (currentTime < filterToISTDate)
            {
                var finalGapStatus = DetermineGapStatus(currentTime, filterToISTDate, currentDateTimeIST);
                var finalGapReason = finalGapStatus == "No Production" ? "No activity scheduled" : "Unknown reason";

                finalChartData.Add(new MfgHearbeatReportChartDataVm
                {
                    startTime = currentTime,
                    endTime = filterToISTDate,
                    status = finalGapStatus,
                    reason = finalGapReason
                });
            }

            chartData = finalChartData;

            var unknownDowntimeData = new List<UnknownDowntimeVm>();

            foreach (var item in chartData)
            {
                if (item.reason == "Unknown reason")
                {
                    unknownDowntimeData.Add(new UnknownDowntimeVm
                    {
                        StartDateTime = item.startTime,
                        EndDateTime = item.endTime
                    });
                }
            }

            var response = new MfgHearbeatReportResponseVm
            {
                TableData = query.Select(x => new MfgHearbeatReportTableDataVm
                {
                    StartTime = x.StartTime,
                    EndTime = x.EndTime,
                    ProcessDate = x.ProcessDate,
                    AddedDate = x.AddedDate,
                    ProcessNo = x.ProcessNo,
                    SaleOrderId = x.SaleOrderId,
                    SaleOrderNumber = x.SaleOrderNumber,
                    ProductionLineNo = x.ProductionLineNo,
                    WorkShift = x.WorkShift?.ToUpper(),
                    ShiftSupervisorName = x.SupervisorName?.ToUpper(),
                    ProcessQty = x.ProcessQty
                }).OrderByDescending(x => x.AddedDate).ToList(),
                ChartData = chartData,
                DowntimeData = actualDowntimes.Select(x => new ProductionDowntimeTableVm
                {
                    StartTime = x.StartTime,
                    EndTime = x.EndTime,
                    ReasonName = x.ProductionDowntimeReason.ReasonName,
                    ReasonCode = x.ProductionDowntimeReason.ReasonCode,
                    ProductionLineType = x.ProductionDowntimeReason.ProductionLineType,
                    ProductionLineNo = x.ProductionLineNo,
                    ActualDurationMinutes = x.ActualDurationMinutes,
                    ActualDurationFormatted = productionDataFn.FormatDurationToTime(x.ActualDurationMinutes),
                    ExcessDurationMinutes = x.ExcessDurationMinutes,
                    ExcessDurationFormatted = productionDataFn.FormatDurationToTime(x.ExcessDurationMinutes),
                    StandardDurationMinutes = x.ProductionDowntimeReason.StandardDurationMinutes,
                    StandardDurationFormatted = productionDataFn.FormatDurationToTime(x.ProductionDowntimeReason.StandardDurationMinutes),
                    Comments = x.Comments,
                    CreatedBy = x.CreatedBy,
                    CreatedOn = x.CreatedOn,
                    ModifiedBy = x.ModifiedBy,
                    ModifiedOn = x.ModifiedOn
                }).OrderByDescending(x => x.StartTime).ToList(),
                ScheduledDowntimeData = scheduledDowntimes.Select(x => new ScheduledDowntimeVm
                {
                    StartDateTime = x.StartDateTime,
                    EndDateTime = x.EndDateTime,
                    Reason = x.Reason,
                    ProductionLineNo = x.ProductionLineNo,
                    EffectiveFrom = x.EffectiveFrom,
                    EffectiveTo = x.EffectiveTo,
                    StartTime = x.StartTime,
                    EndTime = x.EndTime,
                    RecurrencePattern = x.RecurrencePattern,
                    ApplicableDays = x.ApplicableDays,
                    DayOfMonth = x.DayOfMonth,
                }).OrderByDescending(x => x.StartDateTime).ToList(),
                UnknownDowntimeData = unknownDowntimeData,
            };
            CalculateProductionDurationSummary(response, filter.DateFrom.Value, filter.DateTo.Value);
            return response;
        }

        private void RemoveOverlappingPeriods(List<MfgHearbeatReportChartDataVm> chartData, DateTime start, DateTime end)
        {
            // Fixed: Safe collection modification - collect changes first, then apply
            var periodsToRemove = new List<MfgHearbeatReportChartDataVm>();
            var periodsToAdd = new List<MfgHearbeatReportChartDataVm>();
            var periodsToModify = new List<(MfgHearbeatReportChartDataVm period, DateTime? newStart, DateTime? newEnd)>();

            // First, identify any periods that need to be adjusted
            var overlappingPeriods = chartData.Where(x =>
                (x.startTime < end && x.endTime > start) // Any overlap condition
            ).ToList();

            foreach (var period in overlappingPeriods)
            {
                // Case 1: Period starts before and ends within new period
                if (period.startTime < start && period.endTime <= end && period.endTime > start)
                {
                    periodsToModify.Add((period, null, start));
                }
                // Case 2: Period starts within and ends after new period
                else if (period.startTime >= start && period.startTime < end && period.endTime > end)
                {
                    periodsToModify.Add((period, end, null));
                }
                // Case 3: Period is completely contained within new period
                else if (period.startTime >= start && period.endTime <= end)
                {
                    periodsToRemove.Add(period);
                }
                // Case 4: Period completely contains new period
                else if (period.startTime < start && period.endTime > end)
                {
                    // Split into two periods
                    var newPeriod = new MfgHearbeatReportChartDataVm
                    {
                        startTime = end,
                        endTime = period.endTime,
                        status = period.status,
                        reason = period.reason,
                        processNo = period.processNo,
                        saleOrderNumber = period.saleOrderNumber,
                        downtimeType = period.downtimeType
                    };
                    periodsToAdd.Add(newPeriod);
                    periodsToModify.Add((period, null, start));
                }
            }

            // Apply all modifications safely
            foreach (var period in periodsToRemove)
            {
                chartData.Remove(period);
            }

            foreach (var (period, newStart, newEnd) in periodsToModify)
            {
                if (newStart.HasValue)
                    period.startTime = newStart.Value;
                if (newEnd.HasValue)
                    period.endTime = newEnd.Value;
            }

            chartData.AddRange(periodsToAdd);
        }

        public GateDashboardVm GetGateDashboardMetrics(GateDashboardRequestVm request)
        {
            GateDashboardVm result = new GateDashboardVm();

            try
            {
                using (var db = new pmsdbContext())
                {
                    // Performance Optimization: Use AsNoTracking for read-only operations
                    // Log performance optimization mode
                    Console.WriteLine($"Gate Dashboard API called with IncludeTotalCounts: {request.IncludeTotalCounts}, DateFilter: {(request.DateFrom.HasValue && request.DateTo.HasValue ? $"{request.DateFrom:yyyy-MM-dd} to {request.DateTo:yyyy-MM-dd}" : "None")}");

                    // Base queries for gate-in records (both total and filtered)
                    var totalGateQuery = db.GateInTables.AsNoTracking().AsQueryable();
                    var filteredGateQuery = db.GateInTables.AsNoTracking().AsQueryable();

                    // Apply date filter to filtered query if provided
                    if (request.DateFrom.HasValue && request.DateTo.HasValue)
                    {
                        filteredGateQuery = filteredGateQuery.Where(g => g.GateInDate >= request.DateFrom.Value
                                                                       && g.GateInDate <= request.DateTo.Value);
                    }

                    // 1. Pending Gate-Out - Vehicles with gate-in completed, gate pass issued, but no gate-out
                    // Filtered count (within date range) - Always calculated
                    var pendingGateOutFilteredCount = filteredGateQuery
                        .Where(g => g.GateIn == true
                                 && g.GatePassIssue == true
                                 && (g.GateOut == false || g.GateOut == null))
                        .Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var pendingGateOutTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        pendingGateOutTotalCount = totalGateQuery
                            .Where(g => g.GateIn == true
                                     && g.GatePassIssue == true
                                     && (g.GateOut == false || g.GateOut == null))
                            .Count();
                    }

                    result.PendingGateOutTotalCount = pendingGateOutTotalCount;
                    result.PendingGateOutFilteredCount = pendingGateOutFilteredCount;

                    // 2. Pending Gate Passes - Vehicles with gate-in completed but gate pass not issued
                    // Filtered count (within date range) - Always calculated
                    var pendingGatePassFilteredCount = filteredGateQuery
                        .Where(g => g.GateIn == true
                                 && g.VehicleId > 0
                                 && (g.GatePassIssue == false || g.GatePassIssue == null))
                        .Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var pendingGatePassTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        pendingGatePassTotalCount = totalGateQuery
                            .Where(g => g.GateIn == true
                                     && g.VehicleId > 0
                                     && (g.GatePassIssue == false || g.GatePassIssue == null))
                            .Count();
                    }

                    result.PendingGatePassTotalCount = pendingGatePassTotalCount;
                    result.PendingGatePassFilteredCount = pendingGatePassFilteredCount;

                    // 3. Number of Invoice Gate-ins Without PO - Invoices added through gate-in without a PO
                    // Apply date filter to the gate-in records in the join

                    // Filtered count (within date range) - Always calculated
                    var invoicesWithoutPOFilteredQuery = from gim in db.GateInInvoiceMappingTables.AsNoTracking()
                                                         join inv in db.InvoiceMasters on gim.InvoiceId equals inv.InvoiceId
                                                         join gate in filteredGateQuery on gim.GateInId equals gate.GateInId
                                                         where (inv.Poid == null || inv.Poid == 0)
                                                           && gim.GateInId > 0
                                                           && gim.GateInId != null
                                                           && !inv.InvoiceNumber.StartsWith("OpeningStock_")
                                                           && !inv.InvoiceNumber.StartsWith("Internal_")
                                                           && !inv.InvoiceNumber.StartsWith("ProductionStock_")
                                                         select inv.InvoiceId;


                    var invoicesWithoutPOFilteredCount = invoicesWithoutPOFilteredQuery.Distinct().Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var invoicesWithoutPOTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        var invoicesWithoutPOTotalQuery = from inv in db.InvoiceMasters.AsNoTracking()
                                                          join po in db.PurchaseOrderTables on inv.Poid equals po.Poid into poGroup
                                                          from po in poGroup.DefaultIfEmpty()
                                                          where (po == null || po.Poid == null)
                                                            && !inv.InvoiceNumber.StartsWith("OpeningStock_")
                                                            && !inv.InvoiceNumber.StartsWith("Internal_")
                                                            && !inv.InvoiceNumber.StartsWith("ProductionStock_")
                                                          select inv.InvoiceId;

                        invoicesWithoutPOTotalCount = invoicesWithoutPOTotalQuery.Distinct().Count();
                    }

                    result.InvoicesWithoutPOTotalCount = invoicesWithoutPOTotalCount;
                    result.InvoicesWithoutPOFilteredCount = invoicesWithoutPOFilteredCount;

                    // Purchase Order Analytics
                    // Apply same date filtering logic as gate operations

                    // Create base queries for Purchase Orders (both total and filtered)
                    var totalPOQuery = db.PurchaseOrderTables.AsNoTracking().AsQueryable();
                    var filteredPOQuery = db.PurchaseOrderTables.AsNoTracking().AsQueryable();

                    // Apply date filtering to filtered query if dates are provided
                    // FIXED: Use PocreationDate to match target page API default behavior
                    if (request.DateFrom.HasValue && request.DateTo.HasValue)
                    {
                        filteredPOQuery = filteredPOQuery.Where(po => po.AddedDate >= request.DateFrom.Value && po.AddedDate <= request.DateTo.Value);
                    }

                    // 4. Active Purchase Orders
                    // Filtered count (within date range) - Always calculated
                    var activePOFilteredQuery = filteredPOQuery
                        .Where(po => !string.IsNullOrEmpty(po.Status) && po.Status.ToLower() == "active");

                    var activePOFilteredCount = activePOFilteredQuery.Count();

                    // DEBUG: Log the active POs being counted
                    var activePOsList = activePOFilteredQuery.Select(po => new
                    {
                        po.Poid,
                        po.Ponumber,
                        po.Status,
                        po.PocreationDate,
                        po.AddedDate,
                        po.SupplierId
                    }).ToList();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var activePOTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        activePOTotalCount = totalPOQuery
                            .Where(po => !string.IsNullOrEmpty(po.Status) && po.Status.ToLower() == "active")
                            .Count();
                    }

                    result.ActivePOTotalCount = activePOTotalCount;
                    result.ActivePOFilteredCount = activePOFilteredCount;

                    // 5. Revised Purchase Orders
                    // Filtered count (within date range) - Always calculated
                    var revisedPOFilteredCount = filteredPOQuery
                        .Where(po => !string.IsNullOrEmpty(po.Status) && po.Status.ToLower() == "revised")
                        .Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var revisedPOTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        revisedPOTotalCount = totalPOQuery
                            .Where(po => !string.IsNullOrEmpty(po.Status) && po.Status.ToLower() == "revised")
                            .Count();
                    }

                    result.RevisedPOTotalCount = revisedPOTotalCount;
                    result.RevisedPOFilteredCount = revisedPOFilteredCount;

                    // 6. Delayed Purchase Orders Delivery
                    // POs not in Complete status and past their delivery term deadline
                    // Filtered count (within date range) - Always calculated
                    var delayedDeliveryPOFilteredCount = (from po in filteredPOQuery
                                                          join dt in db.DeliveryTermMasters on po.DeliveryTermId equals dt.DeliveryTermId
                                                          where !string.IsNullOrEmpty(po.Status)
                                                            && po.Status.ToLower() != "complete"
                                                            && po.ApprovedDate.HasValue
                                                            && DateTime.Now > po.ApprovedDate.Value.AddDays(dt.NumberOfDays ?? 0)
                                                          select po).Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var delayedDeliveryPOTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        delayedDeliveryPOTotalCount = (from po in totalPOQuery
                                                       join dt in db.DeliveryTermMasters on po.DeliveryTermId equals dt.DeliveryTermId
                                                       where !string.IsNullOrEmpty(po.Status)
                                                         && po.Status.ToLower() != "complete"
                                                         && po.ApprovedDate.HasValue
                                                         && DateTime.Now > po.ApprovedDate.Value.AddDays(dt.NumberOfDays ?? 0)
                                                       select po).Count();
                    }

                    result.DelayedDeliveryPOTotalCount = delayedDeliveryPOTotalCount;
                    result.DelayedDeliveryPOFilteredCount = delayedDeliveryPOFilteredCount;

                    // 7. Delayed Purchase Orders Payment
                    // POs not marked as Full Payment Completed in PO Timeline and past their payment term deadline
                    // Filtered count (within date range) - Always calculated
                    var delayedPaymentPOFilteredCount = (from po in filteredPOQuery
                                                         join pt in db.PaymentTermMasters on po.PaymentTermId equals pt.PaymentTermId
                                                         where po.ApprovedDate.HasValue
                                                           && DateTime.Now > po.ApprovedDate.Value.AddDays(pt.NumberOfDays ?? 0)
                                                           && !db.PurchaseOrderTimelineTables.Any(timeline =>
                                                               timeline.Poid == po.Poid
                                                               && !string.IsNullOrEmpty(timeline.Status)
                                                               && timeline.Status == PMSPurchaseOrderStatus.FullPaymentCompleted)
                                                         select po).Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var delayedPaymentPOTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        delayedPaymentPOTotalCount = (from po in totalPOQuery
                                                      join pt in db.PaymentTermMasters on po.PaymentTermId equals pt.PaymentTermId
                                                      where po.ApprovedDate.HasValue
                                                        && DateTime.Now > po.ApprovedDate.Value.AddDays(pt.NumberOfDays ?? 0)
                                                        && !db.PurchaseOrderTimelineTables.Any(timeline =>
                                                            timeline.Poid == po.Poid
                                                            && !string.IsNullOrEmpty(timeline.Status)
                                                            && timeline.Status == PMSPurchaseOrderStatus.FullPaymentCompleted)
                                                      select po).Count();
                    }

                    result.DelayedPaymentPOTotalCount = delayedPaymentPOTotalCount;
                    result.DelayedPaymentPOFilteredCount = delayedPaymentPOFilteredCount;

                    // Product Analytics
                    // Apply date filtering to product-related queries based on relevant date fields

                    // 08. Delayed Demands Count - Demands still in Active state after 5 days from added date
                    // Base query for delayed demands
                    var delayedDemandsBaseQuery = db.DemandTables.AsNoTracking()
                        .Where(d => !string.IsNullOrEmpty(d.Status) && d.Status.ToLower() == "active"
                                 && d.AddedDate.HasValue
                                 && DateTime.Now > d.AddedDate.Value.AddDays(5));

                    // Filtered count (within date range) - Always calculated
                    var delayedDemandsFilteredQuery = delayedDemandsBaseQuery;
                    if (request.DateFrom.HasValue && request.DateTo.HasValue)
                    {
                        delayedDemandsFilteredQuery = delayedDemandsFilteredQuery.Where(d => d.AddedDate >= request.DateFrom.Value && d.AddedDate <= request.DateTo.Value);
                    }

                    var delayedDemandsFilteredCount = delayedDemandsFilteredQuery.Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var delayedDemandsTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        delayedDemandsTotalCount = delayedDemandsBaseQuery.Count();
                    }

                    result.DelayedDemandsTotalCount = delayedDemandsTotalCount;
                    result.DelayedDemandsFilteredCount = delayedDemandsFilteredCount;

                    // 09. Products Below Minimum Quantity - Products with stock below minimum level in non-WIP stores
                    // This includes ALL products below minimum (including zero stock), differs from "Low Stock" which excludes zero stock
                    // Base query for products below minimum quantity
                    var productsBelowMinQuantityBaseQuery = from a in db.StockProductTables.AsNoTracking()
                                                            join stm in db.StockMasters on a.StockId equals stm.StockId
                                                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                                                            join p in db.ProductMasters on a.ProductId equals p.ProductId
                                                            join r in db.RackMasters on b.RackId equals r.RackId
                                                            join s in db.StoreMasters on r.StoreId equals s.StoreId
                                                            where b.InspectionType.ToLower() == "accepted"
                                                              && (p.Disabled == null || p.Disabled == false)
                                                              && p.MinimumQuantity.HasValue
                                                              && (s.IsWorkInProgressStore == null || s.IsWorkInProgressStore == false)
                                                            select new
                                                            {
                                                                p.ProductId,
                                                                Quantity = b.Quantity,
                                                                p.MinimumQuantity,
                                                                StockDate = stm.StockDate
                                                            };

                    // Filtered count (within date range) - Always calculated
                    var productsBelowMinQuantityFilteredQuery = productsBelowMinQuantityBaseQuery;
                    if (request.DateFrom.HasValue && request.DateTo.HasValue)
                    {
                        productsBelowMinQuantityFilteredQuery = productsBelowMinQuantityFilteredQuery.Where(x => x.StockDate >= request.DateFrom.Value && x.StockDate <= request.DateTo.Value);
                    }

                    var productsBelowMinQuantityFilteredData = productsBelowMinQuantityFilteredQuery
                                                              .GroupBy(x => x.ProductId)
                                                              .Select(g => new { ProductId = g.Key, TotalQuantity = g.Sum(x => x.Quantity), MinimumQuantity = g.First().MinimumQuantity })
                                                              .Where(x => x.TotalQuantity < x.MinimumQuantity && x.MinimumQuantity > 0);

                    var productsBelowMinQuantityFilteredCount = productsBelowMinQuantityFilteredData.Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var productsBelowMinQuantityTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        var productsBelowMinQuantityTotalData = productsBelowMinQuantityBaseQuery
                                                               .GroupBy(x => x.ProductId)
                                                               .Select(g => new { ProductId = g.Key, TotalQuantity = g.Sum(x => x.Quantity), MinimumQuantity = g.First().MinimumQuantity })
                                                               .Where(x => x.TotalQuantity < x.MinimumQuantity && x.MinimumQuantity > 0);

                        productsBelowMinQuantityTotalCount = productsBelowMinQuantityTotalData.Count();
                    }

                    result.ProductsBelowMinQuantityTotalCount = productsBelowMinQuantityTotalCount;
                    result.ProductsBelowMinQuantityFilteredCount = productsBelowMinQuantityFilteredCount;

                    // 10. Pending Issue Requests Count - Issue requests with Pending status
                    // Base query for pending issue requests
                    var pendingIssueRequestsBaseQuery = db.IssueProductTables.AsNoTracking()
                        .Where(i => !string.IsNullOrEmpty(i.Status) && i.Status.ToLower() == "pending");

                    // Filtered count (within date range) - Always calculated
                    var pendingIssueRequestsFilteredQuery = pendingIssueRequestsBaseQuery;
                    if (request.DateFrom.HasValue && request.DateTo.HasValue)
                    {
                        pendingIssueRequestsFilteredQuery = pendingIssueRequestsFilteredQuery.Where(i => i.CreatedDate >= request.DateFrom.Value && i.CreatedDate <= request.DateTo.Value);
                    }

                    var pendingIssueRequestsFilteredCount = pendingIssueRequestsFilteredQuery.Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var pendingIssueRequestsTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        pendingIssueRequestsTotalCount = pendingIssueRequestsBaseQuery.Count();
                    }

                    result.PendingIssueRequestsTotalCount = pendingIssueRequestsTotalCount;
                    result.PendingIssueRequestsFilteredCount = pendingIssueRequestsFilteredCount;

                    // 11. Total Products Count - All active products in the system
                    // REVERTED: Use original simple query to count ALL active products from ProductMasters
                    var totalProductsBaseQuery = db.ProductMasters.AsNoTracking()
                        .Where(p => p.Disabled == null || p.Disabled == false);

                    // Filtered count (within date range) - Always calculated
                    var totalProductsFilteredQuery = totalProductsBaseQuery;
                    if (request.DateFrom.HasValue && request.DateTo.HasValue)
                    {
                        totalProductsFilteredQuery = totalProductsFilteredQuery.Where(p => p.AddedDate >= request.DateFrom.Value && p.AddedDate <= request.DateTo.Value);
                    }

                    var totalProductsFilteredCount = totalProductsFilteredQuery.Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var totalProductsTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        totalProductsTotalCount = totalProductsBaseQuery.Count();
                    }

                    result.TotalProductsTotalCount = totalProductsTotalCount;
                    result.TotalProductsFilteredCount = totalProductsFilteredCount;

                    // 12. Low Stock Products Count - Products below minimum stock level but still have some stock
                    // This differs from "Products Below Minimum Quantity" by excluding out-of-stock items (quantity > 0)

                    // Base query for low stock products
                    var lowStockProductsBaseQuery = from a in db.StockProductTables.AsNoTracking()
                                                    join stm in db.StockMasters on a.StockId equals stm.StockId
                                                    join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                                                    join p in db.ProductMasters on a.ProductId equals p.ProductId
                                                    join r in db.RackMasters on b.RackId equals r.RackId
                                                    join s in db.StoreMasters on r.StoreId equals s.StoreId
                                                    where b.InspectionType.ToLower() == "accepted"
                                                      && (p.Disabled == null || p.Disabled == false)
                                                      && p.MinimumQuantity.HasValue
                                                      && (s.IsWorkInProgressStore == null || s.IsWorkInProgressStore == false)
                                                    select new
                                                    {
                                                        p.ProductId,
                                                        Quantity = b.Quantity,
                                                        p.MinimumQuantity,
                                                        StockDate = stm.StockDate
                                                    };

                    // Filtered count (within date range) - Always calculated
                    var lowStockProductsFilteredQuery = lowStockProductsBaseQuery;
                    if (request.DateFrom.HasValue && request.DateTo.HasValue)
                    {
                        lowStockProductsFilteredQuery = lowStockProductsFilteredQuery.Where(x => x.StockDate >= request.DateFrom.Value && x.StockDate <= request.DateTo.Value);
                    }

                    var lowStockProductsFilteredData = lowStockProductsFilteredQuery
                                                      .GroupBy(x => x.ProductId)
                                                      .Select(g => new { ProductId = g.Key, TotalQuantity = g.Sum(x => x.Quantity), MinimumQuantity = g.First().MinimumQuantity })
                                                      .Where(x => x.TotalQuantity < x.MinimumQuantity && x.MinimumQuantity > 0 && x.TotalQuantity > 0);

                    var lowStockProductsFilteredCount = lowStockProductsFilteredData.Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var lowStockProductsTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        var lowStockProductsTotalData = lowStockProductsBaseQuery
                                                       .GroupBy(x => x.ProductId)
                                                       .Select(g => new { ProductId = g.Key, TotalQuantity = g.Sum(x => x.Quantity), MinimumQuantity = g.First().MinimumQuantity })
                                                       .Where(x => x.TotalQuantity < x.MinimumQuantity && x.MinimumQuantity > 0 && x.TotalQuantity > 0);

                        lowStockProductsTotalCount = lowStockProductsTotalData.Count();
                    }

                    result.LowStockProductsTotalCount = lowStockProductsTotalCount;
                    result.LowStockProductsFilteredCount = lowStockProductsFilteredCount;

                    // 13. Out of Stock Products Count - Products with zero or null current stock

                    // Base query for products with stock
                    var productsWithStockBaseQuery = from a in db.StockProductTables.AsNoTracking()
                                                     join stm in db.StockMasters on a.StockId equals stm.StockId
                                                     join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                                                     join p in db.ProductMasters on a.ProductId equals p.ProductId
                                                     join r in db.RackMasters on b.RackId equals r.RackId
                                                     join s in db.StoreMasters on r.StoreId equals s.StoreId
                                                     where b.InspectionType.ToLower() == "accepted"
                                                       && (p.Disabled == null || p.Disabled == false)
                                                       && (s.IsWorkInProgressStore == null || s.IsWorkInProgressStore == false)
                                                     select new
                                                     {
                                                         p.ProductId,
                                                         StockDate = stm.StockDate
                                                     };

                    var allActiveProductsBaseQuery = db.ProductMasters.AsNoTracking()
                                                    .Where(p => p.Disabled == null || p.Disabled == false);

                    // Filtered count (within date range) - Always calculated
                    var productsWithStockFilteredQuery = productsWithStockBaseQuery;
                    if (request.DateFrom.HasValue && request.DateTo.HasValue)
                    {
                        productsWithStockFilteredQuery = productsWithStockFilteredQuery.Where(x => x.StockDate >= request.DateFrom.Value && x.StockDate <= request.DateTo.Value);
                    }

                    var productsWithStockFiltered = productsWithStockFilteredQuery.Select(x => x.ProductId).Distinct();

                    // For Out of Stock Products, we need to consider products that had stock in the date range but now have zero
                    // This aligns with the target page logic which shows products based on stock date filtering
                    var allActiveProductsFilteredQuery = from p in allActiveProductsBaseQuery
                                                         join spt in db.StockProductTables on p.ProductId equals spt.ProductId into stockJoin
                                                         from spt in stockJoin.DefaultIfEmpty()
                                                         join stm in db.StockMasters on spt.StockId equals stm.StockId into stockMasterJoin
                                                         from stm in stockMasterJoin.DefaultIfEmpty()
                                                         where (request.DateFrom == null || request.DateTo == null ||
                                                               stm.StockDate >= request.DateFrom.Value && stm.StockDate <= request.DateTo.Value)
                                                         select p.ProductId;

                    var allActiveProductsFiltered = allActiveProductsFilteredQuery.Distinct();
                    var outOfStockProductsFilteredCount = allActiveProductsFiltered.Except(productsWithStockFiltered).Count();

                    // Total count (all time) - Only calculated if requested for performance optimization
                    var outOfStockProductsTotalCount = 0;
                    if (request.IncludeTotalCounts)
                    {
                        var productsWithStockTotal = productsWithStockBaseQuery.Select(x => x.ProductId).Distinct();
                        var allActiveProductsTotal = allActiveProductsBaseQuery.Select(p => p.ProductId);
                        outOfStockProductsTotalCount = allActiveProductsTotal.Except(productsWithStockTotal).Count();
                    }

                    result.OutOfStockProductsTotalCount = outOfStockProductsTotalCount;
                    result.OutOfStockProductsFilteredCount = outOfStockProductsFilteredCount;
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine("Error in GetGateDashboardMetrics: " + ex.Message);
            }

            return result;
        }

        private void CalculateProductionDurationSummary(MfgHearbeatReportResponseVm response, DateTime requestFromDate, DateTime requestToDate)
        {
            var productionDataFn = new ProductionDataFn(GlobalData);

            // Calculate total requested duration (24-hour period)
            var totalRequestedDuration = (decimal)(requestToDate - requestFromDate).TotalMinutes;

            // Fixed: Calculate durations from chart data for accurate timeline coverage
            var chartData = response.ChartData ?? new List<MfgHearbeatReportChartDataVm>();

            // Calculate durations by status type from the actual timeline
            var totalProductionDuration = (decimal)chartData
                .Where(x => x.status == "Running")
                .Sum(x => (x.endTime - x.startTime).TotalMinutes);

            var totalScheduledDowntimeDuration = (decimal)chartData
                .Where(x => x.status == "Scheduled Downtime")
                .Sum(x => (x.endTime - x.startTime).TotalMinutes);

            var totalAcceptableDowntimeDuration = (decimal)chartData
                .Where(x => x.status == "Acceptable Downtime")
                .Sum(x => (x.endTime - x.startTime).TotalMinutes);

            var totalExcessDowntimeDuration = (decimal)chartData
                .Where(x => x.status == "Excess Downtime")
                .Sum(x => (x.endTime - x.startTime).TotalMinutes);

            var totalUnknownDowntimeDuration = (decimal)chartData
                .Where(x => x.status == "Production Down" && x.reason == "Unknown reason")
                .Sum(x => (x.endTime - x.startTime).TotalMinutes);

            var totalNoProductionDuration = (decimal)chartData
                .Where(x => x.status == "No Production")
                .Sum(x => (x.endTime - x.startTime).TotalMinutes);

            // Calculate total downtime (all non-production time)
            var totalDowntimeDuration = totalScheduledDowntimeDuration + totalAcceptableDowntimeDuration +
                                      totalExcessDowntimeDuration + totalUnknownDowntimeDuration + totalNoProductionDuration;

            // Fixed: Correct percentage calculation - production time as percentage of total requested time
            var totalProductionDurationPercentage = totalRequestedDuration > 0
                ? (totalProductionDuration / totalRequestedDuration) * 100
                : 0;

            // Fixed: Correct color logic - green for high production percentage
            var totalProductionDurationPercentageColor = totalProductionDurationPercentage >= 75 ? "green" : "red";

            var totalProductionQty = response.TableData?.Sum(x => x.ProcessQty) ?? 0;

            // Validate timeline coverage - should equal requested duration
            var totalTimelineCoverage = totalProductionDuration + totalDowntimeDuration;
            var coveragePercentage = totalRequestedDuration > 0
                ? (totalTimelineCoverage / totalRequestedDuration) * 100
                : 0;

            var summary = new ProductionDurationStatusSummaryVm
            {
                TotalProductionSummary = new TotalProductionDetailsSummaryVm
                {
                    TotalProductionDurationFormatted = productionDataFn.FormatDurationToTime(totalProductionDuration),
                    TotalProductionDurationPercentage = Math.Round(totalProductionDurationPercentage, 2),
                    TotalProductionDurationPercentageColor = totalProductionDurationPercentageColor,
                    TotalProductionQty = totalProductionQty,
                },
                TotalDowntimeSummary = new TotalDowntimeSummaryVm
                {
                    // Fixed: Use correct standard duration calculation (acceptable downtime only)
                    StandardDurationMinutes = totalAcceptableDowntimeDuration,
                    StandardDurationFormatted = productionDataFn.FormatDurationToTime(totalAcceptableDowntimeDuration),
                    // Fixed: Use total actual downtime (acceptable + excess)
                    ActualDurationMinutes = totalAcceptableDowntimeDuration + totalExcessDowntimeDuration,
                    ActualDurationFormatted = productionDataFn.FormatDurationToTime(totalAcceptableDowntimeDuration + totalExcessDowntimeDuration),
                    ExcessDurationMinutes = totalExcessDowntimeDuration,
                    ExcessDurationFormatted = productionDataFn.FormatDurationToTime(totalExcessDowntimeDuration),
                    TotalDowntimeDuration = totalDowntimeDuration,
                    TotalDowntimeDurationFormatted = productionDataFn.FormatDurationToTime(totalDowntimeDuration),
                },
                TotalUnknownDowntimeDurationFormatted = productionDataFn.FormatDurationToTime(totalUnknownDowntimeDuration),
                TotalScheduledDowntimeDurationFormatted = productionDataFn.FormatDurationToTime(totalScheduledDowntimeDuration),
                RequestDurationFormatted = productionDataFn.FormatDurationToTime(totalRequestedDuration)
            };

            // Add timeline coverage validation for debugging
            Console.WriteLine($"Timeline Coverage: {Math.Round(coveragePercentage, 2)}% ({productionDataFn.FormatDurationToTime(totalTimelineCoverage)} of {productionDataFn.FormatDurationToTime(totalRequestedDuration)})");

            response.ProductionDurationStatusSummary = summary;
        }

        /// <summary>
        /// Validates input parameters for heartbeat report generation
        /// </summary>
        private void ValidateHeartbeatReportInput(MfgHearbeatReportRequestVm filter)
        {
            if (filter == null)
                throw new ArgumentNullException(nameof(filter), "Filter cannot be null");

            if (!filter.DateFrom.HasValue || !filter.DateTo.HasValue)
                throw new ArgumentException("DateFrom and DateTo are required");

            if (filter.DateFrom.Value >= filter.DateTo.Value)
                throw new ArgumentException("DateFrom must be less than DateTo");

            // Validate timezone consistency - ensure dates are in UTC
            if (filter.DateFrom.Value.Kind != DateTimeKind.Utc || filter.DateTo.Value.Kind != DateTimeKind.Utc)
                throw new ArgumentException("DateFrom and DateTo must be in UTC timezone");

            if (!filter.ProductionLineNo.HasValue || filter.ProductionLineNo.Value <= 0)
                throw new ArgumentException("ProductionLineNo is required and must be greater than 0");

            if (string.IsNullOrEmpty(filter.ProductionLineType))
                throw new ArgumentException("ProductionLineType is required");

            var validProductionLineTypes = new[] { "Manufacturing", "Printing", "Embossing", "Vacuum", "Lacquer", "Tumbling" };
            if (!validProductionLineTypes.Contains(filter.ProductionLineType))
                throw new ArgumentException($"ProductionLineType must be one of: {string.Join(", ", validProductionLineTypes)}");

            // Validate reasonable time range (not more than 31 days for performance)
            // var timeSpan = filter.DateTo.Value - filter.DateFrom.Value;
            // if (timeSpan.TotalDays > 31)
            //     throw new ArgumentException("Date range cannot exceed 7 days for performance reasons");
        }

        /// <summary>
        /// Validates and normalizes production data timestamps, handles midnight boundary crossings
        /// </summary>
        private List<ProductionTimelineData> ValidateAndNormalizeProductionData(List<ProductionTimelineData> productionData, DateTime requestFromDate, DateTime requestToDate)
        {
            var normalizedData = new List<ProductionTimelineData>();

            foreach (var item in productionData)
            {
                // Validate timestamps
                if (!item.StartTime.HasValue || !item.EndTime.HasValue)
                    continue; // Skip invalid records

                if (item.StartTime.Value >= item.EndTime.Value)
                    continue; // Skip invalid time ranges

                // Handle midnight boundary crossings
                var processedItems = HandleMidnightBoundary(item, requestFromDate, requestToDate);
                normalizedData.AddRange(processedItems);
            }

            return normalizedData.OrderBy(x => x.StartTime).ToList();
        }

        /// <summary>
        /// Handles production periods that cross midnight boundaries by splitting them appropriately
        /// </summary>
        private List<ProductionTimelineData> HandleMidnightBoundary(ProductionTimelineData item, DateTime requestFromDate, DateTime requestToDate)
        {
            var result = new List<ProductionTimelineData>();
            var startTime = item.StartTime.Value;
            var endTime = item.EndTime.Value;

            // Convert to IST for midnight boundary detection
            var startTimeIST = TimeZoneHelper.ConvertToTimeZone(startTime, TimeZoneId.IndiaStandardTime);
            var endTimeIST = TimeZoneHelper.ConvertToTimeZone(endTime, TimeZoneId.IndiaStandardTime);

            // Check if the period crosses midnight in IST
            if (startTimeIST.Date != endTimeIST.Date)
            {
                // Split at midnight boundaries
                var currentDate = startTimeIST.Date;
                var currentStart = startTime;

                while (currentDate <= endTimeIST.Date)
                {
                    var midnightIST = currentDate.AddDays(1); // Next midnight in IST
                    var midnightUTC = ConvertToUtc(midnightIST, TimeZoneId.IndiaStandardTime);

                    var segmentEnd = endTime < midnightUTC ? endTime : midnightUTC;

                    // Only add segment if it's within the requested time range
                    if (currentStart < requestToDate && segmentEnd > requestFromDate)
                    {
                        var segment = new ProductionTimelineData
                        {
                            ProcessDate = item.ProcessDate,
                            StartTime = currentStart > requestFromDate ? currentStart : requestFromDate,
                            EndTime = segmentEnd < requestToDate ? segmentEnd : requestToDate,
                            AddedDate = item.AddedDate,
                            ProcessNo = item.ProcessNo + (currentDate != startTimeIST.Date ? $" (Day {(currentDate - startTimeIST.Date).Days + 1})" : ""),
                            SaleOrderId = item.SaleOrderId,
                            SaleOrderNumber = item.SaleOrderNumber,
                            ProductionLineNo = item.ProductionLineNo,
                            WorkShift = item.WorkShift,
                            SupervisorName = item.SupervisorName,
                            ProcessQty = item.ProcessQty // Note: Quantity is not split proportionally
                        };

                        if (segment.StartTime < segment.EndTime)
                            result.Add(segment);
                    }

                    currentStart = midnightUTC;
                    currentDate = currentDate.AddDays(1);

                    if (currentStart >= endTime)
                        break;
                }
            }
            else
            {
                // No midnight crossing, but still clip to requested range
                var clippedStart = startTime > requestFromDate ? startTime : requestFromDate;
                var clippedEnd = endTime < requestToDate ? endTime : requestToDate;

                if (clippedStart < clippedEnd)
                {
                    var clippedItem = new ProductionTimelineData
                    {
                        ProcessDate = item.ProcessDate,
                        StartTime = clippedStart,
                        EndTime = clippedEnd,
                        AddedDate = item.AddedDate,
                        ProcessNo = item.ProcessNo,
                        SaleOrderId = item.SaleOrderId,
                        SaleOrderNumber = item.SaleOrderNumber,
                        ProductionLineNo = item.ProductionLineNo,
                        WorkShift = item.WorkShift,
                        SupervisorName = item.SupervisorName,
                        ProcessQty = item.ProcessQty
                    };
                    result.Add(clippedItem);
                }
            }

            return result;
        }

        /// <summary>
        /// Determines the appropriate status for gaps in the timeline based on whether the period is in the past or future
        /// </summary>
        private string DetermineGapStatus(DateTime gapStart, DateTime gapEnd, DateTime currentTimeIST)
        {
            // If the entire gap is in the future, it's "No Production"
            if (gapStart >= currentTimeIST)
                return "No Production";

            // If the entire gap is in the past, it's "Production Down" (unknown downtime)
            if (gapEnd <= currentTimeIST)
                return "Production Down";

            // If the gap spans current time, we need to split it, but for simplicity
            // we'll classify based on the majority of the gap duration
            var totalDuration = (gapEnd - gapStart).TotalMinutes;
            var pastDuration = (currentTimeIST - gapStart).TotalMinutes;
            var futureDuration = (gapEnd - currentTimeIST).TotalMinutes;

            // If more than 50% is in the past, classify as "Production Down"
            return pastDuration > futureDuration ? "Production Down" : "No Production";
        }

        /// <summary>
        /// Adds missing ConvertToUtc method to TimeZoneHelper if not present
        /// </summary>
        private DateTime ConvertToUtc(DateTime dateTime, TimeZoneId timeZoneId)
        {
            TimeZoneInfo timeZoneInfo;
            switch (timeZoneId)
            {
                case TimeZoneId.IndiaStandardTime:
                    timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
                    break;

                default:
                    timeZoneInfo = TimeZoneInfo.Utc;
                    break;
            }
            return TimeZoneInfo.ConvertTimeToUtc(dateTime, timeZoneInfo);
        }
    }
}