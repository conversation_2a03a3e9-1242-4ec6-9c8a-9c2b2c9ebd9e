﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using System.Net.Mail;
using System.IO;
using Amazon.SimpleEmailV2;
using Amazon.SimpleEmailV2.Model;
using Amazon.Runtime;

namespace PmsData.DataFn
{
    public class EmailDataFn
    {
        public static void SendEmail(string htmlString, Stream qwe, string[] emaillist, string filename, string subject)
        {
            var res = new EmailConfigTable();
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.EmailConfigTables
                       where a.EmailConfigName == "Secondary"
                       select a).FirstOrDefault();
            }

            MailMessage message = new MailMessage();
            SmtpClient smtp = new SmtpClient();
            message.From = new MailAddress(res.EmailConfigAccountId, res.EmailConfigFromEmailDisplayName);
            foreach (var email in emaillist)
            {
                message.To.Add(new MailAddress(email));
            }
            //message.To.Add(new MailAddress("<EMAIL>"));
            message.Subject = subject;
            message.IsBodyHtml = true;
            message.Body = htmlString;
            if (qwe != null)
            {
                Attachment attach = new Attachment(qwe, filename);
                message.Attachments.Add(attach);
            }
            smtp.Port = Convert.ToInt32(res.EmailConfigPort);
            smtp.Host = res.EmailConfigSmtp;
            smtp.EnableSsl = true;
            smtp.UseDefaultCredentials = false;
            smtp.Credentials = new NetworkCredential(res.EmailConfigFromEmailId, res.EmailConfigPassword);
            smtp.DeliveryMethod = SmtpDeliveryMethod.Network;
            smtp.Send(message);
        }
        public static void SendEmail(string htmlString, Stream qwe, string[] emaillist, string[] CCEmailList,string[] BCCEmailList, bool setReplyTo, string[] ReplyToEmail, string filename, string subject)
        {
            var res = new EmailConfigTable();
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.EmailConfigTables
                       where a.EmailConfigName.ToLower() == "secondary"
                       select a).FirstOrDefault();
            }

            MailMessage message = new MailMessage();
            SmtpClient smtp = new SmtpClient();
            message.From = new MailAddress(res.EmailConfigAccountId, res.EmailConfigFromEmailDisplayName);
            foreach (var email in emaillist)
            {
                message.To.Add(new MailAddress(email));
            }
            if (CCEmailList != null)
            {
                foreach (var email in CCEmailList)
                {
                    message.CC.Add(new MailAddress(email));
                }
            }
            if (BCCEmailList != null)
            {
                foreach (var email in BCCEmailList)
                {
                    message.Bcc.Add(new MailAddress(email));
                }
            }
            if (setReplyTo)
            {
                foreach (var email in ReplyToEmail)
                {
                    message.ReplyToList.Add(new MailAddress(email));
                }
            }
            //message.To.Add(new MailAddress("<EMAIL>"));
            message.Subject = subject;
            message.IsBodyHtml = true;
            message.Priority = MailPriority.High;
            message.Body = htmlString;
            if (qwe != null)
            {
                Attachment attach = new Attachment(qwe, filename);
                message.Attachments.Add(attach);
            }
            smtp.Port = Convert.ToInt32(res.EmailConfigPort);
            smtp.Host = res.EmailConfigSmtp;
            smtp.EnableSsl = true;
            smtp.UseDefaultCredentials = false;
            smtp.Credentials = new NetworkCredential(res.EmailConfigFromEmailId, res.EmailConfigPassword);
            smtp.DeliveryMethod = SmtpDeliveryMethod.Network;
            smtp.Send(message);
        }

        public static async void SendEmailUsingAmazonSES(string htmlString, Stream qwe, string[] emaillist, string[] CCEmailList, string[] BCCEmailList, bool setReplyTo, string[] ReplyToEmail, string filename, string subject)
        {
            var res = new EmailConfigTable();
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.EmailConfigTables
                       where a.EmailConfigName.ToLower() == "primary"
                       select a).FirstOrDefault();
            }
            var awsCreds = new BasicAWSCredentials(res.EmailConfigFromEmailId, res.EmailConfigPassword);
            var client = new AmazonSimpleEmailServiceV2Client(awsCreds, Amazon.RegionEndpoint.APSouth1);
            var FromAddressWithName = new MailAddress(res.EmailConfigAccountId, res.EmailConfigFromEmailDisplayName);
            var EmailDest = new Destination();
            var request = new SendEmailRequest
            {
                FromEmailAddress = FromAddressWithName.ToString(),
                Content = new EmailContent
                {
                    Simple = new Message
                    {
                        Subject = new Content
                        {
                            Data = subject
                        },
                        Body = new Body
                        {
                            Html = new Content
                            {
                                Data = htmlString
                            }
                        }
                    }
                }
            };

            if (emaillist != null)
            {
                EmailDest.ToAddresses = emaillist.ToList();
            }
            if (CCEmailList != null)
            {
                EmailDest.CcAddresses = CCEmailList.ToList();
            }
            if (BCCEmailList != null)
            {
                EmailDest.BccAddresses = BCCEmailList.ToList();
            }
            if (setReplyTo)
            {
                request.ReplyToAddresses = ReplyToEmail.ToList();
            }
            request.Destination = EmailDest;
            var response = await client.SendEmailAsync(request);
        }
    }
}
