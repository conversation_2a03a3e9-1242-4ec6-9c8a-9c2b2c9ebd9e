﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class FactoryWorkersDataFn
    {
        public GlobalDataEntity GlobalData;
        public FactoryWorkersDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<FactoryWorkersMasterVm> GetAllFactoryWorkersWithFilters(FactoryWorkersRequestVm filters)
        {
            List<FactoryWorkersMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.FactoryWorkersMasters
                       join dm in db.DeptMasters on a.DepartmentId equals dm.DeptId
                       join wdm in db.WorkerDesignationMasters on a.DesignationId equals wdm.DesignationId
                       where a.Disabled != true
                       && (filters.DepartmentId == 0 || filters.DepartmentId == null || filters.DepartmentId == dm.DeptId)
                       && (filters.DesignationId == 0 || filters.DesignationId == null || filters.DesignationId == wdm.DesignationId)
                       && (string.IsNullOrEmpty(filters.AddedBy) || filters.AddedBy == "")
                       && (string.IsNullOrEmpty(filters.WorkShift) || filters.WorkShift == null || filters.WorkShift == a.WorkShift)
                       && (string.IsNullOrEmpty(filters.DepartmentName) || filters.DepartmentName == "" || dm.DeptName.Contains(filters.DepartmentName))
                       && (string.IsNullOrEmpty(filters.DesignationName) || filters.DesignationName == "" || wdm.Name.Contains(filters.DesignationName))
                       select new FactoryWorkersMasterVm
                       {
                           WorkerId = a.WorkerId,
                           Name = a.Name,
                           ShortName = a.ShortName,
                           WorkShift = a.WorkShift,
                           DesignationId = a.DesignationId,
                           DesignationName = wdm.Name,
                           DepartmentId = a.DepartmentId,
                           DepartmentName = dm.DeptName,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                       }).OrderByDescending(x => x.WorkerId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateFactoryWorker(FactoryWorkersMasterVm item)
        {
            using (var db = new Models.pmsdbContext())
            {
                FactoryWorkersMaster res = new FactoryWorkersMaster();
                if (item.WorkerId == 0)
                {
                    res.Name = item.Name;
                    res.ShortName = item.ShortName;
                    res.WorkShift = item.WorkShift;
                    res.DepartmentId = item.DepartmentId;
                    res.DesignationId = item.DesignationId;
                    res.AddedBy = GlobalData.loggedInUser;
                    res.AddedDate = System.DateTime.Now;
                    db.FactoryWorkersMasters.Add(res);
                    db.SaveChanges();
                }
                else
                {
                    res = db.FactoryWorkersMasters.Where(x => x.WorkerId == item.WorkerId).FirstOrDefault();

                    if (res != null)
                    {
                        res.Name = item.Name;
                        res.ShortName = item.ShortName;
                        res.WorkShift = item.WorkShift;
                        res.DepartmentId = item.DepartmentId;
                        res.DesignationId = item.DesignationId;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = res.WorkerId,
                            TableName = "FactoryWorkersMasters",
                            EntityName = "FactoryWorkersMaster",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                        });
                        db.SaveChanges();
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm DeleteFactoryWorker(long itemid)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var res = db.FactoryWorkersMasters.Where(x => x.WorkerId == itemid).FirstOrDefault();
                        if (res != null)
                        {
                            res.Disabled = true;
                            res.DisabledBy = GlobalData.loggedInUser;
                            res.DisabledDate = System.DateTime.Now;
                            db.SaveChanges();

                        }
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Factory Worker Removed Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }

    }
}
