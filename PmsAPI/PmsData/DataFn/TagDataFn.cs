﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class TagDataFn
    {
        public List<TagMasterVm> GetAllTags()
        {
            List<TagMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.TagMasters
                       select new TagMasterVm
                       {
                           TagId = a.TagId,
                           TagName = a.TagName,
                           TagDesc = a.TagDesc,
                           TagAddedBy = a.TagAddedBy,
                           TagAddedDate = a.TagAddedDate
                       }).OrderBy(x => x.TagName).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateTag(TagMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.TagId == 0)
                {
                    var rec = db.TagMasters.Where(x => x.TagName == br.TagName).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                TagMaster res = new TagMaster();
                if (br.TagId == 0)
                {
                    res.TagName = br.TagName;
                    res.TagDesc = br.TagDesc;
                    res.TagAddedBy = br.TagAddedBy;
                    res.TagAddedDate = System.DateTime.Now;
                    db.TagMasters.Add(res);
                }
                else
                {
                    res = db.TagMasters.Where(x => x.TagId == br.TagId).FirstOrDefault();
                    if (res != null)
                    {
                        res.TagName = br.TagName;
                        res.TagDesc = br.TagDesc;
                        res.TagAddedBy = br.TagAddedBy;
                        res.TagAddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
    }
}
