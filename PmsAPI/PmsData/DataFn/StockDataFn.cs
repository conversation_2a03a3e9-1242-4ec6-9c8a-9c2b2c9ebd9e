﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using PmsData.Models;
using System.Net;
using System.Globalization;
using PmsCommon;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace PmsData.DataFn
{
    public class StockDataFn
    {
        public GlobalDataEntity GlobalData;
        public StockDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public StockReportVm GetstockReport()
        {
            StockReportVm res = new StockReportVm();
            using (var db = new Models.pmsdbContext())
            {
                var data = (from a in db.StockProductTables
                            join stm in db.StockMasters on a.StockId equals stm.StockId
                            join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            where b.InspectionType.ToLower() == "accepted"
                            select new ProductStockReportVm
                            {
                                StockId = stm.StockId,
                                StockProductId = a.StockProductId,
                                ProductId = a.ProductId,
                                PricePerUnit = a.PricePerUnit,
                                RecievedQuantity = a.ReceivedQuantity,
                                Quantity = b.Quantity
                            }).ToList();

                var qwe = (from a in data
                           group a by new
                           {
                               a.StockId,
                               a.StockProductId,
                               a.ProductId,
                               a.PricePerUnit,
                               a.RecievedQuantity,
                               a.Quantity
                           } into pg
                           select new ProductStockReportVm
                           {
                               PricePerUnit = pg.Key.PricePerUnit,
                               RecievedQuantity = pg.Key.RecievedQuantity,
                               Quantity = pg.Sum(x => x.Quantity),
                               TotalValue = pg.Key.PricePerUnit * pg.Sum(x => x.Quantity)
                           }).OrderBy(x => x.ProductName).ToList();


                res.StockTotalCount = db.StockMasters.Count();
                res.StockInspectedCount = db.StockMasters.Count(x => x.InspectionCompleted == true);
                res.StockNotInspectedCount = res.StockTotalCount - res.StockInspectedCount;
                res.TotalProductCount = db.ProductMasters.Count();
                res.StockTotalQuantity = (from ps in db.StockProductTables
                                          join s in db.StockMasters on ps.StockId equals s.StockId
                                          join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                                          join ist in db.IssueProductTables on ps.StockProductId equals ist.ToNewStockProductId into ipssc
                                          from ist in ipssc.DefaultIfEmpty()
                                          where ist == null
                                          select ps.Quantity).Sum();
                res.StockTotalAcceptedQuantity = (from ps in db.StockProductTables
                                                  join s in db.StockMasters on ps.StockId equals s.StockId
                                                  join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                                                  join ist in db.IssueProductTables on ps.StockProductId equals ist.ToNewStockProductId into ipssc
                                                  from ist in ipssc.DefaultIfEmpty()
                                                  where ist == null
                                                  select ps.AcceptedQuantity).Sum();
                res.StockTotalRejectedQuantity = (from ps in db.StockProductTables
                                                  join s in db.StockMasters on ps.StockId equals s.StockId
                                                  join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                                                  join ist in db.IssueProductTables on ps.StockProductId equals ist.ToNewStockProductId into ipssc
                                                  from ist in ipssc.DefaultIfEmpty()
                                                  where ist == null
                                                  select ps.RejectedQuantity
                                                  ).Sum();

                res.StockTotalValue = qwe.Sum(x => x.TotalValue).Value;
                res.StockTotalAvailableQuantity = qwe.Sum(x => x.Quantity).Value;
                res.TotalGateIn = db.GateInTables.Count();
            }
            return res;
        }

        public List<ProductStockReportVm> GetProductWisestock()
        {
            List<ProductStockReportVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                var data = (from a in db.StockProductTables
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            join p in db.ProductMasters on a.ProductId equals p.ProductId
                            join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId into ps
                            from pr in ps.DefaultIfEmpty()
                            join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            where b.InspectionType.ToLower() == "accepted"
                            select new ProductStockReportVm
                            {
                                ProductId = a.ProductId,
                                ProductName = p.ProductName,
                                ProductCode = p.ProductCode,
                                ProductType = p.ProductType,
                                ProductCategoryId = p.ProductCategoryId,
                                ProductCategory = pr.ProductCategory,
                                ProductFirstSubCategoryId = p.ProductFirstSubCategoryId,
                                ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                ProductSecSubCategoryId = p.ProductSecSubCategoryId,
                                ProductSecSubCategory = psc.ProductSecSubCategory,
                                Quantity = b.Quantity,
                                Unit = a.Unit
                            }).ToList();

                res = (from a in data
                       group a by new
                       {

                           a.ProductId,
                           a.ProductName,
                           a.ProductType,
                           a.ProductCode,
                           a.ProductCategoryId,
                           a.ProductFirstSubCategoryId,
                           a.ProductSecSubCategoryId,
                           a.ProductCategory,
                           a.ProductFirstSubCategory,
                           a.ProductSecSubCategory,
                           a.Unit,
                       } into pg
                       select new ProductStockReportVm
                       {
                           //ProductId = pg.Key,
                           //AcceptedQuantity = pg.Sum(x => x.AcceptedQuantity)
                           ProductId = pg.Key.ProductId,
                           ProductName = pg.Key.ProductName,
                           ProductCode = pg.Key.ProductCode,
                           ProductType = pg.Key.ProductType,
                           ProductCategoryId = pg.Key.ProductCategoryId,
                           ProductFirstSubCategoryId = pg.Key.ProductFirstSubCategoryId,
                           ProductSecSubCategoryId = pg.Key.ProductSecSubCategoryId,
                           ProductCategory = pg.Key.ProductCategory,
                           ProductFirstSubCategory = pg.Key.ProductFirstSubCategory,
                           ProductSecSubCategory = pg.Key.ProductSecSubCategory,
                           Quantity = pg.Sum(x => x.Quantity),
                           Unit = pg.Key.Unit,
                       }).ToList();

            }
            return res.Where(x => x.Quantity > 0).ToList(); ;
        }

        public List<ProductStockReportVm> GetProductWisestockByFilter(SearchParamsProductCategoryReportVm param)
        {
            List<ProductStockReportVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                var data = (from a in db.StockProductTables
                            join stm in db.StockMasters on a.StockId equals stm.StockId
                            join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId
                            join sup in db.SupplierMasters on inv.SupplierId equals sup.SupplierId
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            join p in db.ProductMasters on a.ProductId equals p.ProductId
                            join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId into ps
                            from pr in ps.DefaultIfEmpty()
                            join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            join r in db.RackMasters on b.RackId equals r.RackId
                            join s in db.StoreMasters on r.StoreId equals s.StoreId
                            where b.InspectionType.ToLower() == "accepted"
                            && (pr.ProductType == param.ProductType || param.ProductType == null)
                            && (p.ProductCategoryId == param.ProductCategoryId || param.ProductCategoryId == 0)
                            && (p.ProductFirstSubCategoryId == param.ProductFirstSubCategoryId || param.ProductFirstSubCategoryId == 0)
                            && (p.ProductSecSubCategoryId == param.ProductSecSubCategoryId || param.ProductSecSubCategoryId == 0)
                            && (p.Unit == param.Unit || param.Unit == "" || param.Unit == null)
                            && (inv.SupplierId == param.SupplierId || param.SupplierId == 0)
                            && (param.IncludeWIPStore == null || param.IncludeWIPStore != s.IsWorkInProgressStore)
                            select new ProductStockReportVm
                            {
                                ProductId = a.ProductId,
                                ProductName = p.ProductName,
                                ProductCode = p.ProductCode,
                                ProductType = pr.ProductType,
                                ProductCategoryId = p.ProductCategoryId,
                                ProductCategory = pr.ProductCategory,
                                ProductFirstSubCategoryId = p.ProductFirstSubCategoryId,
                                ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                ProductSecSubCategoryId = p.ProductSecSubCategoryId,
                                ProductSecSubCategory = psc.ProductSecSubCategory,
                                Quantity = b.Quantity,
                                Unit = a.Unit,
                                PricePerUnit = a.PricePerUnit,
                                SupplierName = sup.SupplierName
                            }).ToList();

                res = (from a in data
                       group a by new
                       {

                           a.ProductId,
                           a.ProductName,
                           a.ProductType,
                           a.ProductCode,
                           a.ProductCategoryId,
                           a.ProductFirstSubCategoryId,
                           a.ProductSecSubCategoryId,
                           a.ProductCategory,
                           a.ProductFirstSubCategory,
                           a.ProductSecSubCategory,
                           a.Unit,
                           a.PricePerUnit,
                       } into pg
                       select new ProductStockReportVm
                       {
                           //ProductId = pg.Key,
                           //AcceptedQuantity = pg.Sum(x => x.AcceptedQuantity)
                           ProductId = pg.Key.ProductId,
                           ProductName = pg.Key.ProductName,
                           ProductCode = pg.Key.ProductCode,
                           ProductType = pg.Key.ProductType,
                           ProductCategoryId = pg.Key.ProductCategoryId,
                           ProductFirstSubCategoryId = pg.Key.ProductFirstSubCategoryId,
                           ProductSecSubCategoryId = pg.Key.ProductSecSubCategoryId,
                           ProductCategory = pg.Key.ProductCategory,
                           ProductFirstSubCategory = pg.Key.ProductFirstSubCategory,
                           ProductSecSubCategory = pg.Key.ProductSecSubCategory,
                           Quantity = pg.Sum(x => x.Quantity),
                           Unit = pg.Key.Unit,
                           PricePerUnit = pg.Key.PricePerUnit,
                       }).ToList();

            }
            return res.Where(x => x.Quantity > 0).ToList();
        }

        public List<ProductStockReportVm> GetProductWisestockWithSupplier(StockAvailabilityReportRequestVm filters)
        {
            using (var db = new Models.pmsdbContext())
            {
                // SAFETY: Handle out-of-stock as special case but preserve all original logic
                if (filters.isOutOfStockCheck)
                {
                    return GetOutOfStockProducts(db, filters);
                }

                // Main query for normal and low stock products
                var data = (from a in db.StockProductTables
                            join stm in db.StockMasters on a.StockId equals stm.StockId
                            join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId
                            join sup in db.SupplierMasters on inv.SupplierId equals sup.SupplierId
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            join p in db.ProductMasters on a.ProductId equals p.ProductId
                            join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId into ps
                            from pr in ps.DefaultIfEmpty()
                            join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            join r in db.RackMasters on b.RackId equals r.RackId
                            join s in db.StoreMasters on r.StoreId equals s.StoreId
                            where b.InspectionType.ToLower() == "accepted"
                                && (string.IsNullOrEmpty(filters.BatchNo) || stm.Batch == filters.BatchNo)
                                && (string.IsNullOrEmpty(filters.ProductType) || pr.ProductType.ToLower() == filters.ProductType.ToLower())
                                && (string.IsNullOrEmpty(filters.ReceivedBy) || stm.AddedBy.ToLower() == filters.ReceivedBy.ToLower())
                                && (string.IsNullOrEmpty(filters.Unit) || a.Unit.ToLower() == filters.Unit.ToLower())
                                && (filters.ProductCategoryId == 0 || filters.ProductCategoryId == null || p.ProductCategoryId == filters.ProductCategoryId)
                                && (filters.ProductFirstSubCategoryId == 0 || filters.ProductFirstSubCategoryId == null || p.ProductFirstSubCategoryId == filters.ProductFirstSubCategoryId)
                                && (filters.ProductSecSubCategoryId == 0 || filters.ProductSecSubCategoryId == null || p.ProductSecSubCategoryId == filters.ProductSecSubCategoryId)
                                && (filters.ProductId == 0 || filters.ProductId == null || p.ProductId == filters.ProductId)
                                && (string.IsNullOrEmpty(filters.ProductQuality) || stm.ProductQuality == filters.ProductQuality)
                                && (filters.RackId == 0 || filters.RackId == null || b.RackId == filters.RackId)
                                && (filters.StoreId == 0 || filters.StoreId == null || r.StoreId == filters.StoreId)
                                && (filters.SupplierId == 0 || filters.SupplierId == null || sup.SupplierId == filters.SupplierId)
                                && (filters.IncludeWIPStore == null || filters.IncludeWIPStore != s.IsWorkInProgressStore)
                                && (filters.DateFrom == null || filters.DateTo == null ||
                                   (filters.DateFrom.HasValue && filters.DateTo.HasValue &&
                                    stm.StockDate.Date >= filters.DateFrom.Value.Date && stm.StockDate.Date <= filters.DateTo.Value.Date))
                            select new ProductStockReportVm
                            {
                                StockId = stm.StockId,
                                StockProductId = a.StockProductId,
                                ProductId = a.ProductId,
                                ProductName = p.ProductName,
                                ProductCode = p.ProductCode,
                                ProductType = p.ProductType,
                                ProductCategoryId = p.ProductCategoryId,
                                ProductCategory = pr.ProductCategory,
                                ProductFirstSubCategoryId = p.ProductFirstSubCategoryId,
                                ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                ProductSecSubCategoryId = p.ProductSecSubCategoryId,
                                ProductSecSubCategory = psc.ProductSecSubCategory,
                                Barcode = a.Barcode,
                                ManufacturedDate = a.ManufacturedDate,
                                ExpiryDate = a.ExpiryDate,
                                PricePerUnit = a.PricePerUnit,
                                ProductQuality = stm.ProductQuality,
                                Unit = a.Unit,
                                Batch = stm.Batch,
                                StoreId = r.StoreId,
                                StoreName = s.StoreName,
                                StoreCode = s.StoreCode,
                                RackId = r.RackId,
                                RackName = r.RackName,
                                RackCode = r.RackCode,
                                ReceivedDate = stm.AddedDate,
                                ReceivedBy = stm.AddedBy,
                                SupplierName = sup.SupplierName,
                                RecievedQuantity = a.ReceivedQuantity,
                                Quantity = b.Quantity,
                                MinimumQuantity = p.MinimumQuantity
                            }).ToList();

                // CRITICAL FIX: Use dashboard-exact query for low stock products
                if (filters.isMinumQuantityCheck)
                {
                    // Get complete low stock product data using EXACT dashboard logic
                    data = GetLowStockProducts(data);
                }

                // Group and aggregate the data
                var res = (from a in data
                           group a by new
                           {
                               a.StockId,
                               a.StockProductId,
                               a.ProductId,
                               a.ProductName,
                               a.ProductType,
                               a.ProductCategoryId,
                               a.ProductFirstSubCategoryId,
                               a.ProductSecSubCategoryId,
                               a.ProductCategory,
                               a.ProductFirstSubCategory,
                               a.ProductSecSubCategory,
                               a.ProductCode,
                               a.Barcode,
                               a.ManufacturedDate,
                               a.ExpiryDate,
                               a.PricePerUnit,
                               a.ProductQuality,
                               a.Unit,
                               a.Batch,
                               a.StoreId,
                               a.StoreCode,
                               a.StoreName,
                               a.RackId,
                               a.RackName,
                               a.RackCode,
                               a.ReceivedDate,
                               a.ReceivedBy,
                               a.SupplierName,
                               a.RecievedQuantity,
                               a.MinimumQuantity
                           } into pg
                           select new ProductStockReportVm
                           {
                               //ProductId = pg.Key,
                               //AcceptedQuantity = pg.Sum(x => x.AcceptedQuantity)
                               StockId = pg.Key.StockId,
                               StockProductId = pg.Key.StockProductId,
                               ProductId = pg.Key.ProductId,
                               ProductName = pg.Key.ProductName,
                               ProductCode = pg.Key.ProductCode,
                               ProductType = pg.Key.ProductType,
                               ProductCategoryId = pg.Key.ProductCategoryId,
                               ProductFirstSubCategoryId = pg.Key.ProductFirstSubCategoryId,
                               ProductSecSubCategoryId = pg.Key.ProductSecSubCategoryId,
                               ProductCategory = pg.Key.ProductCategory,
                               ProductFirstSubCategory = pg.Key.ProductFirstSubCategory,
                               ProductSecSubCategory = pg.Key.ProductSecSubCategory,
                               Barcode = pg.Key.Barcode,
                               ManufacturedDate = pg.Key.ManufacturedDate,
                               ExpiryDate = pg.Key.ExpiryDate,
                               PricePerUnit = pg.Key.PricePerUnit,
                               ProductQuality = pg.Key.ProductQuality,
                               Unit = pg.Key.Unit,
                               Batch = pg.Key.Batch,
                               StoreId = pg.Key.StoreId,
                               StoreCode = pg.Key.StoreCode,
                               StoreName = pg.Key.StoreName,
                               RackId = pg.Key.RackId,
                               RackCode = pg.Key.RackCode,
                               RackName = pg.Key.RackName,
                               ReceivedDate = pg.Key.ReceivedDate,
                               ReceivedBy = pg.Key.ReceivedBy,
                               SupplierName = pg.Key.SupplierName,
                               RecievedQuantity = pg.Key.RecievedQuantity,
                               Quantity = pg.Sum(x => x.Quantity),
                               MinimumQuantity = pg.Key.MinimumQuantity
                           }).OrderBy(x => x.ProductName).ToList();

                var uniqueStockProductIds = res.Select(x => x.StockProductId).Distinct().ToList();
                var stockProductIdsWithLabels = db.StockLabelTables
                    .Where(sl => uniqueStockProductIds.Contains(sl.StockProductId))
                    .Distinct()
                    .ToList();

                foreach (var item in res)
                {
                    item.IsBarcodeLabelExist = stockProductIdsWithLabels.Any(x => x.StockProductId == item.StockProductId);
                }

                // Return appropriate results based on filter type
                if (filters.isMinumQuantityCheck)
                {
                    return res.ToList();
                }
                else
                {
                    return res.Where(x => x.Quantity > 0).ToList();
                }
            }
        }

        // CRITICAL FIX: Use EXACT dashboard query to get out-of-stock ProductIds, then get detailed data
        private List<ProductStockReportVm> GetOutOfStockProducts(Models.pmsdbContext db, StockAvailabilityReportRequestVm filters)
        {
            // SAFETY: Validate inputs
            if (db == null || filters == null)
            {
                return new List<ProductStockReportVm>();
            }

            try
            {
                // STEP 1: Use EXACT SAME query as dashboard to get out-of-stock ProductIds (ReportDataFn.cs lines 2983-3026)
                var productsWithStockTotalQuery = from a in db.StockProductTables.AsNoTracking()
                                                  join stm in db.StockMasters on a.StockId equals stm.StockId
                                                  join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                                                  join p in db.ProductMasters on a.ProductId equals p.ProductId
                                                  where b.InspectionType.ToLower() == "accepted"
                                                    && (p.Disabled == null || p.Disabled == false)
                                                  select new
                                                  {
                                                      p.ProductId,
                                                      StockDate = stm.StockDate
                                                  };

                var productsWithStockTotal = productsWithStockTotalQuery.Select(x => x.ProductId).Distinct();

                var allActiveProductsTotalQuery = db.ProductMasters.AsNoTracking()
                                                 .Where(p => p.Disabled == null || p.Disabled == false);

                var allActiveProductsTotal = allActiveProductsTotalQuery.Select(p => p.ProductId);

                // Apply date filtering if provided (EXACT dashboard logic)
                IQueryable<long> outOfStockProductIds;

                if (filters.DateFrom.HasValue && filters.DateTo.HasValue)
                {
                    // Filtered count (within date range) - EXACT dashboard logic
                    var productsWithStockFilteredQuery = productsWithStockTotalQuery
                        .Where(x => x.StockDate.Date >= filters.DateFrom.Value.Date && x.StockDate.Date <= filters.DateTo.Value.Date);

                    var productsWithStockFiltered = productsWithStockFilteredQuery.Select(x => x.ProductId).Distinct();

                    // EXACT dashboard logic for filtered active products (lines 3015-3024)
                    var allActiveProductsFilteredQuery = from p in allActiveProductsTotalQuery
                                                         join spt in db.StockProductTables on p.ProductId equals spt.ProductId into stockJoin
                                                         from spt in stockJoin.DefaultIfEmpty()
                                                         join stm in db.StockMasters on spt.StockId equals stm.StockId into stockMasterJoin
                                                         from stm in stockMasterJoin.DefaultIfEmpty()
                                                         where (filters.DateFrom == null || filters.DateTo == null ||
                                                               (stm.StockDate.Date >= filters.DateFrom.Value.Date && stm.StockDate.Date <= filters.DateTo.Value.Date))
                                                         select p.ProductId;

                    var allActiveProductsFiltered = allActiveProductsFilteredQuery.Distinct();

                    outOfStockProductIds = allActiveProductsFiltered.Except(productsWithStockFiltered);
                }
                else
                {
                    // Total count (all time) - EXACT dashboard logic
                    outOfStockProductIds = allActiveProductsTotal.Except(productsWithStockTotal);
                }

                var outOfStockProductIdsList = outOfStockProductIds.ToList();

                // STEP 2: Get detailed data for out-of-stock products (products without stock records)
                // Since these products have NO stock records, get data from ProductMasters with proper category joins
                var outOfStockProducts = (from p in db.ProductMasters
                                          join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId into ps
                                          from pr in ps.DefaultIfEmpty()
                                          join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                                          from pf in psf.DefaultIfEmpty()
                                          join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                                          from psc in pssc.DefaultIfEmpty()
                                          where outOfStockProductIdsList.Contains(p.ProductId)
                                          select new ProductStockReportVm
                                          {
                                              StockId = 0, // No stock record
                                              StockProductId = 0, // No stock record
                                              ProductId = p.ProductId,
                                              ProductName = p.ProductName,
                                              ProductCode = p.ProductCode,
                                              ProductType = p.ProductType,
                                              ProductCategoryId = p.ProductCategoryId,
                                              ProductCategory = pr.ProductCategory,
                                              ProductFirstSubCategoryId = p.ProductFirstSubCategoryId,
                                              ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                              ProductSecSubCategoryId = p.ProductSecSubCategoryId,
                                              ProductSecSubCategory = psc.ProductSecSubCategory,
                                              Barcode = null, // No stock record
                                              ManufacturedDate = null, // No stock record
                                              ExpiryDate = null, // No stock record
                                              PricePerUnit = 0, // No stock record
                                              ProductQuality = null, // No stock record
                                              Unit = null, // No stock record
                                              Batch = null, // No stock record
                                              StoreId = 0, // No stock record
                                              StoreName = null, // No stock record
                                              StoreCode = null, // No stock record
                                              RackId = 0, // No stock record
                                              RackName = null, // No stock record
                                              RackCode = null, // No stock record
                                              ReceivedDate = null, // No stock record
                                              ReceivedBy = null, // No stock record
                                              SupplierName = null, // No stock record
                                              RecievedQuantity = 0, // No stock record
                                              Quantity = 0, // Out of stock (no records)
                                              MinimumQuantity = p.MinimumQuantity,
                                              IsBarcodeLabelExist = false // No stock record
                                          }).OrderBy(x => x.ProductName).ToList();

                return outOfStockProducts;
            }
            catch (Exception ex)
            {
                // SAFETY: Log error and return empty list instead of crashing
                Console.WriteLine($"Error in GetOutOfStockProducts: {ex.Message}");
                return new List<ProductStockReportVm>();
            }
        }

        // CRITICAL FIX: Use EXACT dashboard query to get low stock products with COMPLETE data
        private List<ProductStockReportVm> GetLowStockProducts(List<ProductStockReportVm> data)
        {
            try
            {
                var groupedData = data
                        .GroupBy(item => item.ProductId)
                        .Select(group => new
                        {
                            ProductId = group.Key,
                            TotalQuantity = group.Sum(item => item.Quantity),
                            MinimumQuantity = group.First().MinimumQuantity
                        })
                        .Where(result => result.TotalQuantity < result.MinimumQuantity && result.TotalQuantity > 0 && result.MinimumQuantity > 0);

                data = data
                    .Where(item => groupedData.Any(group => group.ProductId == item.ProductId))
                    .ToList();

                return data;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetLowStockProducts: {ex.Message}");
                return new List<ProductStockReportVm>();
            }
        }

        public List<ProductStockReportVm> GetProductStockWithSupplierByStoreId(long storeid)
        {
            List<ProductStockReportVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                var data = (from a in db.StockProductTables
                            join stm in db.StockMasters on a.StockId equals stm.StockId
                            join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId
                            join sup in db.SupplierMasters on inv.SupplierId equals sup.SupplierId
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            join p in db.ProductMasters on a.ProductId equals p.ProductId
                            join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId into ps
                            from pr in ps.DefaultIfEmpty()
                            join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            join r in db.RackMasters on b.RackId equals r.RackId
                            join s in db.StoreMasters on r.StoreId equals s.StoreId
                            where b.InspectionType.ToLower() == "accepted" && s.StoreId == storeid
                            select new ProductStockReportVm
                            {
                                StockId = stm.StockId,
                                StockProductId = a.StockProductId,
                                ProductId = a.ProductId,
                                ProductName = p.ProductName,
                                ProductCode = p.ProductCode,
                                ProductType = p.ProductType,
                                ProductCategoryId = p.ProductCategoryId,
                                ProductCategory = pr.ProductCategory,
                                ProductFirstSubCategoryId = p.ProductFirstSubCategoryId,
                                ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                ProductSecSubCategoryId = p.ProductSecSubCategoryId,
                                ProductSecSubCategory = psc.ProductSecSubCategory,
                                Barcode = a.Barcode,
                                ManufacturedDate = a.ManufacturedDate,
                                ExpiryDate = a.ExpiryDate,
                                PricePerUnit = a.PricePerUnit,
                                Unit = a.Unit,
                                Batch = stm.Batch,
                                StoreId = r.StoreId,
                                StoreName = s.StoreName,
                                StoreCode = s.StoreCode,
                                RackId = r.RackId,
                                RackName = r.RackName,
                                RackCode = r.RackCode,
                                ReceivedDate = stm.AddedDate,
                                ReceivedBy = stm.AddedBy,
                                SupplierName = sup.SupplierName,
                                RecievedQuantity = a.ReceivedQuantity,
                                Quantity = b.Quantity
                            }).ToList();

                res = (from a in data
                       group a by new
                       {
                           a.StockId,
                           a.StockProductId,
                           a.ProductId,
                           a.ProductName,
                           a.ProductType,
                           a.ProductCategoryId,
                           a.ProductFirstSubCategoryId,
                           a.ProductSecSubCategoryId,
                           a.ProductCategory,
                           a.ProductFirstSubCategory,
                           a.ProductSecSubCategory,
                           a.ProductCode,
                           a.Barcode,
                           a.ManufacturedDate,
                           a.ExpiryDate,
                           a.PricePerUnit,
                           a.Unit,
                           a.Batch,
                           a.StoreId,
                           a.StoreCode,
                           a.StoreName,
                           a.RackId,
                           a.RackName,
                           a.RackCode,
                           a.ReceivedDate,
                           a.ReceivedBy,
                           a.SupplierName,
                           a.RecievedQuantity
                       } into pg
                       select new ProductStockReportVm
                       {
                           //ProductId = pg.Key,
                           //AcceptedQuantity = pg.Sum(x => x.AcceptedQuantity)
                           StockId = pg.Key.StockId,
                           StockProductId = pg.Key.StockProductId,
                           ProductId = pg.Key.ProductId,
                           ProductName = pg.Key.ProductName,
                           ProductCode = pg.Key.ProductCode,
                           ProductType = pg.Key.ProductType,
                           ProductCategoryId = pg.Key.ProductCategoryId,
                           ProductFirstSubCategoryId = pg.Key.ProductFirstSubCategoryId,
                           ProductSecSubCategoryId = pg.Key.ProductSecSubCategoryId,
                           ProductCategory = pg.Key.ProductCategory,
                           ProductFirstSubCategory = pg.Key.ProductFirstSubCategory,
                           ProductSecSubCategory = pg.Key.ProductSecSubCategory,
                           Barcode = pg.Key.Barcode,
                           ManufacturedDate = pg.Key.ManufacturedDate,
                           ExpiryDate = pg.Key.ExpiryDate,
                           PricePerUnit = pg.Key.PricePerUnit,
                           Unit = pg.Key.Unit,
                           Batch = pg.Key.Batch,
                           StoreId = pg.Key.StoreId,
                           StoreCode = pg.Key.StoreCode,
                           StoreName = pg.Key.StoreName,
                           RackId = pg.Key.RackId,
                           RackCode = pg.Key.RackCode,
                           RackName = pg.Key.RackName,
                           ReceivedDate = pg.Key.ReceivedDate,
                           ReceivedBy = pg.Key.ReceivedBy,
                           SupplierName = pg.Key.SupplierName,
                           RecievedQuantity = pg.Key.RecievedQuantity,
                           Quantity = pg.Sum(x => x.Quantity),
                       }).OrderBy(x => x.ProductName).ToList();

            }
            return res.Where(x => x.Quantity > 0).ToList();
        }
        public List<ProductStockReportVm> GetProductStockByStoreIdByProductId(long storeid, long productid)
        {
            List<ProductStockReportVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                var data = (from a in db.StockProductTables
                            join stm in db.StockMasters on a.StockId equals stm.StockId
                            join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId
                            join sup in db.SupplierMasters on inv.SupplierId equals sup.SupplierId
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            join p in db.ProductMasters on a.ProductId equals p.ProductId
                            join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId into ps
                            from pr in ps.DefaultIfEmpty()
                            join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            join r in db.RackMasters on b.RackId equals r.RackId
                            join s in db.StoreMasters on r.StoreId equals s.StoreId
                            where b.InspectionType.ToLower() == "accepted" && s.StoreId == storeid && p.ProductId == productid
                            select new ProductStockReportVm
                            {
                                StockId = stm.StockId,
                                StockProductId = a.StockProductId,
                                ProductId = a.ProductId,
                                ProductName = p.ProductName,
                                ProductCode = p.ProductCode,
                                ProductType = p.ProductType,
                                ProductCategoryId = p.ProductCategoryId,
                                ProductCategory = pr.ProductCategory,
                                ProductFirstSubCategoryId = p.ProductFirstSubCategoryId,
                                ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                ProductSecSubCategoryId = p.ProductSecSubCategoryId,
                                ProductSecSubCategory = psc.ProductSecSubCategory,
                                Barcode = a.Barcode,
                                ManufacturedDate = a.ManufacturedDate,
                                ExpiryDate = a.ExpiryDate,
                                PricePerUnit = a.PricePerUnit,
                                Unit = a.Unit,
                                Batch = stm.Batch,
                                StoreId = r.StoreId,
                                StoreName = s.StoreName,
                                StoreCode = s.StoreCode,
                                RackId = r.RackId,
                                RackName = r.RackName,
                                RackCode = r.RackCode,
                                ReceivedDate = stm.AddedDate,
                                ReceivedBy = stm.AddedBy,
                                SupplierName = sup.SupplierName,
                                SupplierId = sup.SupplierId,
                                RecievedQuantity = a.ReceivedQuantity,
                                Quantity = b.Quantity
                            }).ToList();

                res = (from a in data
                       group a by new
                       {
                           a.StockId,
                           a.StockProductId,
                           a.ProductId,
                           a.ProductName,
                           a.ProductType,
                           a.ProductCategoryId,
                           a.ProductFirstSubCategoryId,
                           a.ProductSecSubCategoryId,
                           a.ProductCategory,
                           a.ProductFirstSubCategory,
                           a.ProductSecSubCategory,
                           a.ProductCode,
                           a.Barcode,
                           a.ManufacturedDate,
                           a.ExpiryDate,
                           a.PricePerUnit,
                           a.Unit,
                           a.Batch,
                           a.StoreId,
                           a.StoreCode,
                           a.StoreName,
                           a.RackId,
                           a.RackName,
                           a.RackCode,
                           a.ReceivedDate,
                           a.ReceivedBy,
                           a.SupplierName,
                           a.SupplierId,
                           a.RecievedQuantity
                       } into pg
                       select new ProductStockReportVm
                       {
                           //ProductId = pg.Key,
                           //AcceptedQuantity = pg.Sum(x => x.AcceptedQuantity)
                           StockId = pg.Key.StockId,
                           StockProductId = pg.Key.StockProductId,
                           ProductId = pg.Key.ProductId,
                           ProductName = pg.Key.ProductName,
                           ProductCode = pg.Key.ProductCode,
                           ProductType = pg.Key.ProductType,
                           ProductCategoryId = pg.Key.ProductCategoryId,
                           ProductFirstSubCategoryId = pg.Key.ProductFirstSubCategoryId,
                           ProductSecSubCategoryId = pg.Key.ProductSecSubCategoryId,
                           ProductCategory = pg.Key.ProductCategory,
                           ProductFirstSubCategory = pg.Key.ProductFirstSubCategory,
                           ProductSecSubCategory = pg.Key.ProductSecSubCategory,
                           Barcode = pg.Key.Barcode,
                           ManufacturedDate = pg.Key.ManufacturedDate,
                           ExpiryDate = pg.Key.ExpiryDate,
                           PricePerUnit = pg.Key.PricePerUnit,
                           Unit = pg.Key.Unit,
                           Batch = pg.Key.Batch,
                           StoreId = pg.Key.StoreId,
                           StoreCode = pg.Key.StoreCode,
                           StoreName = pg.Key.StoreName,
                           RackId = pg.Key.RackId,
                           RackCode = pg.Key.RackCode,
                           RackName = pg.Key.RackName,
                           ReceivedDate = pg.Key.ReceivedDate,
                           ReceivedBy = pg.Key.ReceivedBy,
                           SupplierName = pg.Key.SupplierName,
                           SupplierId = pg.Key.SupplierId,
                           RecievedQuantity = pg.Key.RecievedQuantity,
                           Quantity = pg.Sum(x => x.Quantity),
                       }).OrderBy(x => x.ProductName).ToList();
                foreach (var item in res)
                {
                    item.IsBarcodeLabelExist = (from sl in db.StockLabelTables
                                                where sl.StockProductId == item.StockProductId
                                                select sl).FirstOrDefault() != null;
                }
            }
            return res.Where(x => x.Quantity > 0).ToList();
        }

        public List<StockTransaction> GenerateStockReport(List<ProductStockReportVm> res)
        {
            decimal? initialstock = 0;
            decimal? currentStock = 0;
            var transactions = new List<StockTransaction>();
            if (res.Count() > 0)
            {
                foreach (var item in res)
                {
                    if (item.RecievedQuantity != null)
                    {
                        if (currentStock == 0)
                        {
                            currentStock = initialstock;
                        }
                        transactions.Add(new StockTransaction
                        {
                            OpeningStock = currentStock == 0 ? item.RecievedQuantity : currentStock.Value,
                            TransactionQuantity = item.RecievedQuantity,
                            ClosingStock = initialstock + item.RecievedQuantity,
                            Date = item.ReceivedDate
                        });
                        currentStock = item.RecievedQuantity;

                        foreach (var consume in item.ConsumeStockProduct.OrderBy(c => c.ConsumedDate))
                        {
                            decimal transactionQuantity = consume.Quantity;
                            transactions.Add(new StockTransaction
                            {
                                OpeningStock = currentStock,
                                TransactionQuantity = transactionQuantity,
                                ClosingStock = currentStock - transactionQuantity
                            });
                            currentStock -= transactionQuantity;
                        }
                        initialstock = currentStock;
                    }
                    else
                    {

                        transactions.Add(new StockTransaction
                        {
                            OpeningStock = currentStock,
                            TransactionQuantity = item.AcceptedQuantity,
                            ClosingStock = currentStock,
                            Date = item.ReceivedDate
                        });

                    }

                }
            }

            return transactions;

        }


        public List<ProductStockReportVm> GetProductWisestockWithSupplierWithConsumption(SearchParamsProductStockReportVm param)
        {
            List<ProductStockReportVm> res = null;

            using (var db = new Models.pmsdbContext())
            {

                res = (from a in db.StockProductTables
                       join stm in db.StockMasters on a.StockId equals stm.StockId
                       join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId
                       join sup in db.SupplierMasters on inv.SupplierId equals sup.SupplierId
                       join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                       join p in db.ProductMasters on a.ProductId equals p.ProductId
                       join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId into ps
                       from pr in ps.DefaultIfEmpty()
                       join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                       from pf in psf.DefaultIfEmpty()
                       join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                       from psc in pssc.DefaultIfEmpty()
                       where b.InspectionType.ToLower() == "accepted"
                       && (a.ProductId == param.ProductId || param.ProductId == 0)
                       //&& (stm.StockDate >= param.FromDate || param.FromDate == null)
                       //&& (stm.StockDate <= param.ToDate || param.ToDate == null)
                       && (inv.SupplierId == param.SupplierId || param.SupplierId == 0)
                       select new ProductStockReportVm
                       {
                           StockId = stm.StockId,
                           StockProductId = a.StockProductId,
                           ProductId = a.ProductId,
                           ProductName = p.ProductName,
                           ProductCode = p.ProductCode,
                           ProductType = p.ProductType,
                           ProductCategoryId = p.ProductCategoryId,
                           ProductCategory = pr.ProductCategory,
                           ProductFirstSubCategoryId = p.ProductFirstSubCategoryId,
                           ProductFirstSubCategory = pf.ProductFirstSubCategory,
                           ProductSecSubCategoryId = p.ProductSecSubCategoryId,
                           ProductSecSubCategory = psc.ProductSecSubCategory,
                           Barcode = a.Barcode,
                           ManufacturedDate = a.ManufacturedDate,
                           ExpiryDate = a.ExpiryDate,
                           PricePerUnit = a.PricePerUnit,
                           Unit = a.Unit,
                           Batch = stm.Batch,
                           ReceivedDate = stm.StockDate,
                           ReceivedBy = stm.AddedBy,
                           SupplierName = sup.SupplierName,
                           RecievedQuantity = a.ReceivedQuantity,
                           AcceptedQuantity = a.AcceptedQuantity,
                           Quantity = b.Quantity,
                           RejectedQuantity = string.IsNullOrEmpty(a.RejectedQuantity.ToString()) ? 0 : a.RejectedQuantity,
                       }).ToList();

                res = (from a in res
                       group a by new
                       {
                           a.StockId,
                           a.StockProductId,
                           a.ProductId,
                           a.ProductName,
                           a.ProductType,
                           a.ProductCategoryId,
                           a.ProductFirstSubCategoryId,
                           a.ProductSecSubCategoryId,
                           a.ProductCategory,
                           a.ProductFirstSubCategory,
                           a.ProductSecSubCategory,
                           a.ProductCode,
                           a.Barcode,
                           a.ManufacturedDate,
                           a.ExpiryDate,
                           a.PricePerUnit,
                           a.Unit,
                           a.Batch,
                           a.ReceivedDate,
                           a.ReceivedBy,
                           a.SupplierName,
                           a.RecievedQuantity,
                           a.AcceptedQuantity,
                           a.RejectedQuantity,
                           a.StockProductAllocation,
                           //a.IsStockReissued,
                           //a.ConsumeStockProduct,
                           //a.IssueProduct
                       } into pg
                       select new ProductStockReportVm
                       {
                           //ProductId = pg.Key,
                           //AcceptedQuantity = pg.Sum(x => x.AcceptedQuantity)
                           StockId = pg.Key.StockId,
                           StockProductId = pg.Key.StockProductId,
                           ProductId = pg.Key.ProductId,
                           ProductName = pg.Key.ProductName,
                           ProductCode = pg.Key.ProductCode,
                           ProductType = pg.Key.ProductType,
                           ProductCategoryId = pg.Key.ProductCategoryId,
                           ProductFirstSubCategoryId = pg.Key.ProductFirstSubCategoryId,
                           ProductSecSubCategoryId = pg.Key.ProductSecSubCategoryId,
                           ProductCategory = pg.Key.ProductCategory,
                           ProductFirstSubCategory = pg.Key.ProductFirstSubCategory,
                           ProductSecSubCategory = pg.Key.ProductSecSubCategory,
                           Barcode = pg.Key.Barcode,
                           ManufacturedDate = pg.Key.ManufacturedDate,
                           ExpiryDate = pg.Key.ExpiryDate,
                           PricePerUnit = pg.Key.PricePerUnit,
                           Unit = pg.Key.Unit,
                           Batch = pg.Key.Batch,
                           ReceivedDate = pg.Key.ReceivedDate,
                           ReceivedBy = pg.Key.ReceivedBy,
                           SupplierName = pg.Key.SupplierName,
                           RecievedQuantity = pg.Key.RecievedQuantity,
                           AcceptedQuantity = pg.Key.AcceptedQuantity,
                           RejectedQuantity = pg.Key.RejectedQuantity,
                           StockProductAllocation = pg.Key.StockProductAllocation,
                           Quantity = pg.Sum(x => x.Quantity),
                       }).OrderBy(x => x.ProductName).ToList();

                foreach (var item in res)
                {
                    item.StockProductAllocation = (from alloc in db.StockProductAllocationTables
                                                   join r in db.RackMasters on alloc.RackId equals r.RackId
                                                   join s in db.StoreMasters on r.StoreId equals s.StoreId
                                                   where alloc.StockProductId == item.StockProductId
                                                   select new StockProductAllocationVm
                                                   {
                                                       AllocationId = alloc.AllocationId,
                                                       StockProductId = alloc.StockProductId,
                                                       InspectionType = alloc.InspectionType,
                                                       Quantity = alloc.Quantity,
                                                       RackId = alloc.RackId,
                                                       RackCode = r.RackCode,
                                                       RackName = r.RackName,
                                                       StoreId = s.StoreId,
                                                       StoreCode = s.StoreCode,
                                                       StoreName = s.StoreName
                                                   }).ToList();
                    item.IsStockReissued = db.IssueProductTables.Any(x => x.ToNewStockId == item.StockId);
                    item.ConsumeStockProduct = (from con in db.ConsumeStockProductMasters
                                                join pr in db.ProductMasters on con.ProductId equals pr.ProductId
                                                join s in db.StoreMasters on con.StoreId equals s.StoreId
                                                join r in db.RackMasters on con.RackId equals r.RackId
                                                join so in db.SaleOrderTables on con.SaleOrderId equals so.SaleOrderId into solg
                                                from so in solg.DefaultIfEmpty()
                                                where con.StockProductId == item.StockProductId
                                                select new ConsumeStockProductMasterVm
                                                {
                                                    ConsumeStockProductId = con.ConsumeStockProductId,
                                                    RackId = con.RackId,
                                                    RackCode = r.RackCode,
                                                    RackName = r.RackName,
                                                    StoreId = con.StoreId,
                                                    StoreCode = s.StoreCode,
                                                    StoreName = s.StoreName,
                                                    ProductId = con.ProductId,
                                                    ProductCode = pr.ProductCode,
                                                    ProductName = pr.ProductName,
                                                    Quantity = con.Quantity,
                                                    SCQuantity = con.Scquantity,
                                                    Unit = con.Unit,
                                                    ConsumedDate = con.ConsumedDate,
                                                    IsDamaged = con.IsDamaged,
                                                    SaleOrderId = con.SaleOrderId,
                                                    SaleOrderNumber = so.SaleOrderNumber,
                                                    StockProductId = con.StockProductId,
                                                    StockId = con.StockId,
                                                    AddedBy = con.AddedBy,
                                                    AddedDate = con.AddedDate
                                                }).OrderByDescending(x => x.ConsumeStockProductId).ToList();
                    item.IssueProduct = (from inv in db.IssueProductTables
                                         join stp in db.StockProductTables on inv.StockProductId equals stp.StockProductId
                                         join pr in db.ProductMasters on inv.ProductId equals pr.ProductId
                                         join stm in db.StockMasters on inv.StockId equals stm.StockId into fcms
                                         from stm in fcms.DefaultIfEmpty()
                                         join ts in db.StoreMasters on inv.ToStore equals ts.StoreId
                                         where inv.StockProductId == item.StockProductId
                                         select new IssueProductTableVm
                                         {
                                             IssueId = inv.IssueId,
                                             SaleOrderId = inv.SaleOrderId,
                                             FromStore = inv.FromStore,
                                             ToStore = inv.ToStore,
                                             ToStoreName = ts.StoreName,
                                             ProductId = inv.ProductId,
                                             ProductName = pr.ProductName,
                                             Quantity = inv.Quantity,
                                             Status = inv.Status,
                                             Remark = inv.Remark,
                                             CreatedBy = inv.CreatedBy,
                                             CreatedDate = inv.CreatedDate,
                                             AuthorizedAction = PmsCommon.PMSStatus.ToView,
                                             StockId = stp.StockId,
                                             StockProductId = inv.StockProductId,
                                             ToNewStockId = inv.ToNewStockId,
                                             ToNewStockProductId = inv.ToNewStockProductId,
                                         }).ToList();
                }
                ;

                var filteredTransactions = GenerateStockReport(res).Where(x =>
                                              (param.FromDate == null || x.Date >= param.FromDate) &&
                                              (param.ToDate == null || x.Date <= param.ToDate)).ToList();


                var result = res.Where(x =>
                        (param.FromDate == null || x.ReceivedDate >= param.FromDate) &&
                        (param.ToDate == null || x.ReceivedDate <= param.ToDate)).ToList();

                if (filteredTransactions.Count > 0)
                {
                    var firstTransactionOpeningStock = filteredTransactions.First().OpeningStock;
                    var lastTransactionClosingStock = filteredTransactions.Last().ClosingStock;
                    if (result.Any())
                    {
                        result.First().StockTransactions = new StockTransactions
                        {
                            OpeningStock = firstTransactionOpeningStock,
                            ClosingStock = lastTransactionClosingStock
                        };
                    }

                }

                return result;
            }
        }
        public List<StockVm> GetAllStocks()
        {
            List<StockVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.StockMasters
                       join i in db.InvoiceMasters on a.InvoiceId equals i.InvoiceId into spc
                       from i in spc.DefaultIfEmpty()
                       join po in db.PurchaseOrderTables on i.Poid equals po.Poid into pot
                       from po in pot.DefaultIfEmpty()
                       select new StockVm
                       {
                           StockId = a.StockId,
                           StockDate = a.StockDate,
                           InvoiceId = a.InvoiceId,
                           Batch = a.Batch,
                           IsOpeningStock = a.IsOpeningStock == null ? false : a.IsOpeningStock,
                           InspectionCompleted = a.InspectionCompleted == null ? false : a.InspectionCompleted,
                           AllocationCompleted = a.AllocationCompleted,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           ManageRejectedItemsCompleted = a.ManageRejectedItemsCompleted,
                           Products = string.Join(", ", (from spt in db.StockProductTables
                                                         join p in db.ProductMasters on spt.ProductId equals p.ProductId
                                                         where spt.StockId == a.StockId
                                                         select p.ProductName).ToList()),
                           Invoice = i.InvoiceId == null ? null : new InvoiceMasterVm
                           {
                               InvoiceId = i.InvoiceId,
                               InvoiceNumber = i.InvoiceNumber,
                               InvoiceDate = i.InvoiceDate,
                               InvoiceFile = i.InvoiceFile,
                               InvoiceTotalPrice = i.InvoiceTotalPrice,
                               InvoiceTotal = i.InvoiceTotal,
                               SupplierId = i.SupplierId,
                               Poid = po.Poid,
                               PONumber = po.Ponumber,
                               Grn = i.Grn
                           }

                       }).OrderByDescending(x => x.StockId).ToList();
            }
            //foreach (var item in res)
            //{
            //    item.AllocationCompleted = item.AllocationCompleted == true ? item.AllocationCompleted : IsStockAllocationComplete(item.StockId);
            //    item.ManageRejectedItemsCompleted = item.ManageRejectedItemsCompleted == true ? item.ManageRejectedItemsCompleted : IsManageRejectedItemsCompleted(item.StockId);
            //}
            return res;
        }
        public List<StockVm> GetAllStocksWithFilters(StockRequestVm filters)
        {
            List<StockVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.StockMasters
                       join spt in db.StockProductTables on a.StockId equals spt.StockId
                       join pm in db.ProductMasters on spt.ProductId equals pm.ProductId
                       join i in db.InvoiceMasters on a.InvoiceId equals i.InvoiceId into spc
                       from i in spc.DefaultIfEmpty()
                       join po in db.PurchaseOrderTables on i.Poid equals po.Poid into pot
                       from po in pot.DefaultIfEmpty()
                       where (filters.IsStockTransferIncluded == true || (filters.IsStockTransferIncluded == false && spt.ReceivedQuantity > 0 && spt.ReceivedQuantity != null))
                       && (((string.IsNullOrEmpty(filters.DateType) || filters.DateType == "stockdate") && (filters.FromDate == null || a.StockDate >= filters.FromDate)
                       && (string.IsNullOrEmpty(filters.DateType) || filters.DateType == "stockdate") && (filters.FromDate == null || a.StockDate <= filters.ToDate))
                       || ((string.IsNullOrEmpty(filters.DateType) || filters.DateType == "addeddate") && (filters.FromDate == null || a.AddedDate >= filters.FromDate)
                       && (string.IsNullOrEmpty(filters.DateType) || filters.DateType == "addeddate") && (filters.FromDate == null || a.AddedDate <= filters.ToDate))
                       || ((string.IsNullOrEmpty(filters.DateType) || filters.DateType == "invoicedate") && (filters.FromDate == null || i.InvoiceDate >= filters.FromDate)
                       && (string.IsNullOrEmpty(filters.DateType) || filters.DateType == "invoicedate") && (filters.FromDate == null || i.InvoiceDate <= filters.ToDate))
                       || ((string.IsNullOrEmpty(filters.DateType) || filters.DateType == "podate") && (filters.FromDate == null || po.PocreationDate >= filters.FromDate)
                       && (string.IsNullOrEmpty(filters.DateType) || filters.DateType == "podate") && (filters.FromDate == null || po.PocreationDate <= filters.ToDate)))
                       && (string.IsNullOrEmpty(filters.ProductType) || filters.ProductType == pm.ProductType)
                       && (filters.ProductId == 0 || filters.ProductId == null || filters.ProductId == pm.ProductId)
                       && (filters.ProductCategoryId == 0 || filters.ProductCategoryId == null || filters.ProductCategoryId == pm.ProductCategoryId)
                       && (filters.ProductFirstSubCategoryId == 0 || filters.ProductFirstSubCategoryId == null || filters.ProductFirstSubCategoryId == pm.ProductFirstSubCategoryId)
                       && (filters.ProductSecSubCategoryId == 0 || filters.ProductSecSubCategoryId == null || filters.ProductSecSubCategoryId == pm.ProductSecSubCategoryId)
                       && (string.IsNullOrEmpty(filters.StockType) || filters.StockType == "Opening" && a.IsOpeningStock == true || filters.StockType == "Purchase" && a.IsOpeningStock == null)
                       && (string.IsNullOrEmpty(filters.PONumber) || filters.PONumber == po.Ponumber)
                       && (string.IsNullOrEmpty(filters.InvoiceNumber) || filters.InvoiceNumber == i.InvoiceNumber)
                       && (filters.SupplierId == 0 || filters.SupplierId == null || filters.SupplierId == i.SupplierId)
                       && (filters.IsInspectionCompleted == null || filters.IsInspectionCompleted == a.InspectionCompleted)
                       && (filters.IsAllocationCompleted == null || filters.IsAllocationCompleted == a.AllocationCompleted)
                       && (filters.IsQualityInspectionCompleted == null || filters.IsQualityInspectionCompleted == a.IsQualityInspectionCompleted)
                       && (string.IsNullOrEmpty(filters.BatchNo) || filters.BatchNo == a.Batch)
                       select new StockVm
                       {
                           StockId = a.StockId,
                           StockDate = a.StockDate,
                           InvoiceId = a.InvoiceId,
                           Batch = a.Batch,
                           IsOpeningStock = a.IsOpeningStock == null ? false : a.IsOpeningStock,
                           InspectionCompleted = a.InspectionCompleted == null ? false : a.InspectionCompleted,
                           AllocationCompleted = a.AllocationCompleted,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           ManageRejectedItemsCompleted = a.ManageRejectedItemsCompleted,
                           IsTransferedStock = spt.ReceivedQuantity > 0 ? false : true,
                           ProductQuality = a.ProductQuality,
                           IsQualityInspectionCompleted = a.IsQualityInspectionCompleted == null ? false : a.IsQualityInspectionCompleted,
                           QualityInspectionCompletedBy = a.QualityInspectionCompletedByNavigation == null ? null : new UserMasterBasicVm
                           {
                               Name = a.QualityInspectionCompletedByNavigation.Name
                           },
                           Invoice = i.InvoiceId == null ? null : new InvoiceMasterVm
                           {
                               InvoiceId = i.InvoiceId,
                               InvoiceNumber = i.InvoiceNumber,
                               InvoiceDate = i.InvoiceDate,
                               InvoiceFile = i.InvoiceFile,
                               InvoiceTotalPrice = i.InvoiceTotalPrice,
                               InvoiceTotal = i.InvoiceTotal,
                               SupplierId = i.SupplierId,
                               Poid = po.Poid,
                               PONumber = po.Ponumber,
                               Grn = i.Grn
                           }
                       }).Distinct().ToList();

                var stockidlist = res.Select(x => x.StockId).Distinct().ToList();

                var productsdata = (from spt in db.StockProductTables
                                    join p in db.ProductMasters on spt.ProductId equals p.ProductId
                                    where stockidlist.Contains(spt.StockId)
                                    select new { p.ProductName, spt.StockId }).ToList();

                foreach (var item in res)
                {
                    item.Products = string.Join(", ", (productsdata.Where(x => x.StockId == item.StockId).Select(y => y.ProductName)));
                }

            }
            return res.OrderByDescending(x => x.StockId).Distinct().ToList();
        }

        private bool IsStockAllocationComplete(Models.pmsdbContext db, long stockId)
        {
            bool status = false;
            var stockProduct = (from a in db.StockProductTables
                                where a.StockId == stockId
                                select new StockProductTableVm
                                {
                                    StockProductId = a.StockProductId,
                                    StockId = a.StockId,
                                    Quantity = a.Quantity,
                                }).ToList();

            decimal? totalquantity = stockProduct.Sum(item => item.Quantity);


            var stockProductAllocation = (from a in db.StockProductAllocationTables
                                          join b in db.StockProductTables on a.StockProductId equals b.StockProductId
                                          join s in db.StockMasters on b.StockId equals s.StockId
                                          where s.StockId == stockId
                                          select new StockProductAllocationVm
                                          {
                                              AllocationId = a.AllocationId,
                                              StockProductId = a.StockProductId,
                                              InspectionType = a.InspectionType,
                                              Quantity = a.Quantity,
                                              RackId = a.RackId,
                                          }).ToList();
            decimal totalquantityacc = stockProductAllocation.Sum(item => item.Quantity);

            var stockProductManageRejected = (from a in db.StockProductManageRejectedTables
                                              join b in db.StockProductTables on a.StockProductId equals b.StockProductId
                                              join s in db.StockMasters on b.StockId equals s.StockId
                                              where s.StockId == stockId
                                              select new StockProductManageRejectedVm
                                              {
                                                  Id = a.Id,
                                                  StockProductId = a.StockProductId,
                                                  Quantity = a.Quantity,
                                              }).ToList();
            decimal totalquantityrej = stockProductManageRejected.Sum(item => item.Quantity);
            status = totalquantity == (totalquantityacc + totalquantityrej);

            //if (status)
            //{
            //    var res = db.StockMasters.FirstOrDefault(x => x.StockId == stockId);
            //    res.AllocationCompleted = true;
            //    db.SaveChanges();
            //}

            return status;

        }

        private bool IsManageRejectedItemsCompleted(Models.pmsdbContext db, long stockid)
        {
            bool status = false;
            var stockProduct = (from a in db.StockProductTables
                                where a.StockId == stockid
                                select new StockProductTableVm
                                {
                                    StockProductId = a.StockProductId,
                                    StockId = a.StockId,
                                    Quantity = a.Quantity,
                                    RejectedQuantity = a.RejectedQuantity
                                }).ToList();

            decimal? rejectedtotalquantity = stockProduct.Sum(item => item.RejectedQuantity);


            var stockProductManageRejected = (from a in db.StockProductManageRejectedTables
                                              join b in db.StockProductTables on a.StockProductId equals b.StockProductId
                                              join s in db.StockMasters on b.StockId equals s.StockId
                                              where s.StockId == stockid
                                              select new StockProductManageRejectedVm
                                              {
                                                  Id = a.Id,
                                                  StockProductId = a.StockProductId,
                                                  Quantity = a.Quantity,
                                              }).ToList();
            decimal totalquantityrej = stockProductManageRejected.Sum(item => item.Quantity);
            status = rejectedtotalquantity == 0;

            //if (status)
            //{
            //    var res = db.StockMasters.FirstOrDefault(x => x.StockId == stockid);
            //    res.ManageRejectedItemsCompleted = true;
            //    db.SaveChanges();
            //}
            return status;

        }

        public List<InvoiceMasterVm> GetAllUnstockedInvoices()
        {
            List<InvoiceMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from inv in db.InvoiceMasters
                       join s in db.SupplierMasters on inv.SupplierId equals s.SupplierId
                       join p in db.StockMasters on inv.InvoiceId equals p.InvoiceId into ps
                       from p in ps.DefaultIfEmpty()
                       where p.StockId == null
                       select new InvoiceMasterVm
                       {
                           InvoiceId = inv.InvoiceId,
                           InvoiceNumber = inv.InvoiceNumber,
                           InvoiceDate = inv.InvoiceDate,
                           InvoiceFile = inv.InvoiceFile,
                           InvoiceTotalPrice = inv.InvoiceTotalPrice,
                           EwayBill = inv.EwayBill,
                           EwayBillDate = inv.EwayBillDate,
                           SupplierId = inv.SupplierId,
                           SupplierName = s.SupplierName,
                           Grn = inv.Grn,
                           Poid = inv.Poid,
                           FreightInsurance = inv.FreightInsurance,
                           ShippingHandling = inv.ShippingHandling,
                           OtherCharges = inv.OtherCharges.Value,
                           InvoiceTotal = inv.InvoiceTotal
                       }).ToList();
            }
            return res;
        }


        public StockVm GetstockById(long stockId)
        {
            StockVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.StockMasters
                       where a.StockId == stockId
                       select new StockVm
                       {
                           StockId = a.StockId,
                           StockDate = a.StockDate,
                           Batch = a.Batch,
                           InvoiceId = a.InvoiceId,
                           InspectionCompleted = a.InspectionCompleted,
                           InspectionCompletedBy = a.InspectionCompletedBy,
                           InspectionCompletedDate = a.InspectionCompletedDate,
                           IsQualityInspectionCompleted = a.IsQualityInspectionCompleted,
                           QualityInspectionCompletedBy = a.QualityInspectionCompletedByNavigation == null ? null : new UserMasterBasicVm
                           {
                               Name = a.QualityInspectionCompletedByNavigation.Name
                           },
                           QualityInspectionCompletedDate = a.QualityInspectionCompletedDate,
                           Invoice = (from inv in db.InvoiceMasters
                                      where inv.InvoiceId == a.InvoiceId
                                      select new InvoiceMasterVm
                                      {
                                          InvoiceId = inv.InvoiceId,
                                          InvoiceNumber = inv.InvoiceNumber,
                                          InvoiceDate = inv.InvoiceDate,
                                          InvoiceFile = inv.InvoiceFile,
                                          InvoiceTotalPrice = inv.InvoiceTotalPrice,
                                          EwayBill = inv.EwayBill,
                                          EwayBillDate = inv.EwayBillDate,
                                          SupplierId = inv.SupplierId,
                                          FreightInsurance = inv.FreightInsurance,
                                          ShippingHandling = inv.ShippingHandling,
                                          OtherCharges = inv.OtherCharges,
                                          InvoiceTotal = inv.InvoiceTotal,
                                          SupplierName = db.SupplierMasters.FirstOrDefault(x => x.SupplierId == inv.SupplierId).SupplierName,
                                      }).FirstOrDefault(),
                           StockProduct = (from spt in db.StockProductTables
                                           join p in db.ProductMasters on spt.ProductId equals p.ProductId
                                           join pc in db.ProductCategoryMasters on p.ProductCategoryId equals pc.ProductCategoryId into ps
                                           from pc in ps.DefaultIfEmpty()
                                           join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                                           from pf in psf.DefaultIfEmpty()
                                           join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                                           from psc in pssc.DefaultIfEmpty()
                                           where spt.StockId == stockId
                                           select new StockProductTableVm
                                           {
                                               StockProductId = spt.StockProductId,
                                               StockId = spt.StockId,
                                               ProductId = spt.ProductId,
                                               ProductName = p.ProductName,
                                               ProductType = p.ProductType,
                                               Sku = spt.Sku,
                                               Barcode = spt.Barcode,
                                               Quantity = spt.Quantity,
                                               ManufacturedDate = spt.ManufacturedDate,
                                               ExpiryDate = spt.ExpiryDate,
                                               Unit = spt.Unit,
                                               PricePerUnit = spt.PricePerUnit,
                                               FreightPerUnit = spt.FreightPerUnit,
                                               MiscPerUnit = spt.MiscPerUnit,
                                               ShippingHandlingPerUnit = spt.ShippingHandlingPerUnit,
                                               InvoicePricePerUnit = spt.InvoicePricePerUnit,
                                               Grade = spt.Grade,
                                               AcceptedQuantity = spt.AcceptedQuantity,
                                               RejectedQuantity = spt.RejectedQuantity,
                                               Comments = spt.Comments,
                                               ProductCategoryId = p.ProductCategoryId,
                                               ProductCategory = pc.ProductCategory,
                                               ProductFirstSubCategoryId = p.ProductFirstSubCategoryId,
                                               ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                               ProductSecSubCategoryId = p.ProductSecSubCategoryId,
                                               ProductSecSubCategory = psc.ProductSecSubCategory,
                                               SupplierProductName = spt.SupplierProductName,
                                               ProductSupplierMappingId = spt.ProductSupplierMappingId,
                                               StockProductAllocation = (from a in db.StockProductAllocationTables
                                                                         join b in db.StockProductTables on a.StockProductId equals b.StockProductId
                                                                         join r in db.RackMasters on a.RackId equals r.RackId
                                                                         join s in db.StoreMasters on r.StoreId equals s.StoreId
                                                                         where a.StockProductId == spt.StockProductId
                                                                         select new StockProductAllocationVm
                                                                         {
                                                                             AllocationId = a.AllocationId,
                                                                             StockProductId = a.StockProductId,
                                                                             InspectionType = a.InspectionType,
                                                                             Quantity = a.Quantity,
                                                                             RackId = a.RackId,
                                                                             RackCode = r.RackCode,
                                                                             RackName = r.RackName,
                                                                             StoreId = s.StoreId,
                                                                             StoreCode = s.StoreCode,
                                                                             StoreName = s.StoreName,
                                                                             StockLabelIds = (from sl in db.StockLabelTables
                                                                                              where sl.StockProductId == spt.StockProductId
                                                                                              && sl.InspectionStatus == a.InspectionType
                                                                                              && sl.CurrentRackId == a.RackId
                                                                                              select sl.StockLabelId).ToList()
                                                                         }).ToList(),
                                               StockProductManageRejected = (from a in db.StockProductManageRejectedTables
                                                                             join b in db.StockProductTables on a.StockProductId equals b.StockProductId
                                                                             where a.StockProductId == spt.StockProductId
                                                                             select new StockProductManageRejectedVm
                                                                             {
                                                                                 Id = a.Id,
                                                                                 StockProductId = a.StockProductId,
                                                                                 Quantity = a.Quantity,
                                                                                 ItemAction = a.ItemAction,
                                                                                 StockProductRejectedDispatch = (from c in db.StockProductRejectedDispatchTables
                                                                                                                 join t in db.TransportCompanyMasters on c.TransportId equals t.TransportId
                                                                                                                 where c.StockProductManageRejectedId == a.Id
                                                                                                                 select new StockProductRejectedDispatchVm()
                                                                                                                 {
                                                                                                                     Id = c.Id,
                                                                                                                     StockProductManageRejectedId = c.StockProductManageRejectedId,
                                                                                                                     TransportId = c.TransportId,
                                                                                                                     TransportCompany = t.TransportCompanyName,
                                                                                                                     VehicleNumber = db.TransportVehicleTables.SingleOrDefault(x => x.VehicleId == c.VehicleId).VehicleNumber,
                                                                                                                     VehicleId = c.VehicleId,
                                                                                                                     DispatchDate = c.DispatchDate,
                                                                                                                     DispatchId = c.DispatchId
                                                                                                                 }).FirstOrDefault()
                                                                             }).ToList(),
                                               FileUploads = (from f in db.FileUploadTables
                                                              join u in db.UserMasters on f.UploadedBy equals u.UserId
                                                              where f.EntityId == spt.StockProductId && f.EntityName == "StockProductTable"
                                                              select new FileUploadTableVm
                                                              {
                                                                  FileUploadId = f.FileUploadId,
                                                                  FileName = f.FileName,
                                                                  ContainerName = f.ContainerName,
                                                                  FilePath = f.FilePath,
                                                                  FileType = CommonFunctions.FindFileType(f.FileName),
                                                                  UploadedBy = new UserMasterBasicVm
                                                                  {
                                                                      Name = u.Name,
                                                                      Email = u.Email
                                                                  },
                                                                  UploadedDate = f.UploadedDate
                                                              }).ToList()
                                           }).ToList(),
                       }).FirstOrDefault();
                foreach (var item in res.StockProduct)
                {
                    item.InspectionPendingLabelCount = (from sl in db.StockLabelTables
                                                        where sl.StockId == res.StockId && sl.StockProductId == item.StockProductId && sl.ProductId == item.ProductId && sl.InspectionStatus == null
                                                        select sl).Count();
                    item.IsBarcodeLabelGenerated = (from sl in db.StockLabelTables
                                                    where sl.StockId == res.StockId && sl.StockProductId == item.StockProductId && sl.ProductId == item.ProductId
                                                    select sl).FirstOrDefault() != null;
                }
            }
            return res;
        }
        public ApiFunctionResponseVm GetPendingStockInspectionById(long stockId)
        {
            StockVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.StockMasters
                       where a.StockId == stockId
                       select new StockVm
                       {
                           StockId = a.StockId,
                           StockProduct = (from spt in db.StockProductTables
                                           join p in db.ProductMasters on spt.ProductId equals p.ProductId
                                           where spt.StockId == stockId
                                           select new StockProductTableVm
                                           {
                                               StockProductId = spt.StockProductId,
                                               StockId = spt.StockId,
                                               ProductId = spt.ProductId,
                                               ProductName = p.ProductName,
                                               AcceptedQuantity = spt.AcceptedQuantity,
                                               RejectedQuantity = spt.RejectedQuantity,
                                           }).ToList(),
                       }).FirstOrDefault();

                foreach (var item in res.StockProduct)
                {
                    // Get all labels for this stock product
                    var labels = db.StockLabelTables
                        .Where(sl => sl.StockId == res.StockId &&
                               sl.StockProductId == item.StockProductId &&
                               sl.ProductId == item.ProductId &&
                               sl.LabelStatus != PmsCommon.StockLabelStatus.InActive)
                        .ToList();

                    // Count pending inspection labels
                    item.InspectionPendingLabelCount = labels.Count(l => l.InspectionStatus == null);

                    // Check if any labels exist
                    item.IsBarcodeLabelGenerated = labels.Any();
                    if (item.IsBarcodeLabelGenerated)
                    {
                        var labelAcceptedQuantity = labels.Where(l => l.InspectionStatus == "Accepted").Sum(l => l.Quantity);
                        var labelRejectedQuantity = labels.Where(l => l.InspectionStatus == "Rejected").Sum(l => l.Quantity);

                        item.RejectedQuantity = item.RejectedQuantity == null ? 0 : item.RejectedQuantity;
                        item.AcceptedQuantity = item.AcceptedQuantity == null ? 0 : item.AcceptedQuantity;

                        // Compare with stock product quantities
                        if ((item.AcceptedQuantity != null || item.RejectedQuantity != null) &&
                            (labelAcceptedQuantity != item.AcceptedQuantity || labelRejectedQuantity != item.RejectedQuantity))
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.ExpectationFailed, $"Quantity mismatch for product {item.ProductName}. " +
                                $"Stock shows Accepted Qty: {item.AcceptedQuantity}, Rejected Qty: {item.RejectedQuantity}. " +
                                $"Labels show Accepted Qty: {labelAcceptedQuantity}, Rejected Qty: {labelRejectedQuantity}");
                        }
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
        }

        public ApiFunctionResponseVm AddStock(StockVm stock)
        {
            using (var db = new Models.pmsdbContext())
            {
                try
                {
                    if (stock.Invoice.InvoiceId == 0)
                    {
                        var IsKnittingStock = db.KnittingFabricWeightInputTables.Find(stock.Invoice.Poid) != null;
                        if (IsKnittingStock)
                        {
                            var po = db.PurchaseOrderTables.FirstOrDefault(x => x.Poid == stock.Invoice.Poid);
                            stock.Invoice.InvoiceNumber = "Internal_Knitting_PO_" + po.Ponumber;
                        }
                        InvoiceMaster im = new InvoiceMaster();
                        im.InvoiceNumber = stock.Invoice.InvoiceNumber;
                        im.InvoiceTotalPrice = stock.Invoice.InvoiceTotalPrice;
                        im.InvoiceDate = stock.Invoice.InvoiceDate;
                        im.EwayBill = stock.Invoice.EwayBill;
                        im.EwayBillDate = stock.Invoice.EwayBillDate;
                        im.InvoiceFile = stock.Invoice.InvoiceFile;
                        im.SupplierId = stock.Invoice.SupplierId;
                        im.Grn = stock.Invoice.Grn;
                        im.Poid = stock.Invoice.Poid;
                        im.FreightInsurance = stock.Invoice.FreightInsurance;
                        im.ShippingHandling = stock.Invoice.ShippingHandling;
                        im.OtherCharges = stock.Invoice.OtherCharges;
                        im.InvoiceTotal = stock.Invoice.InvoiceTotal;
                        db.InvoiceMasters.Add(im);
                        db.SaveChanges();
                        stock.Invoice.InvoiceId = im.InvoiceId;
                    }
                    else
                    {
                        InvoiceMaster im = db.InvoiceMasters.FirstOrDefault(x => x.InvoiceId == stock.Invoice.InvoiceId);
                        im.InvoiceNumber = stock.Invoice.InvoiceNumber;
                        im.InvoiceTotalPrice = stock.Invoice.InvoiceTotalPrice;
                        im.InvoiceDate = stock.Invoice.InvoiceDate;
                        im.EwayBill = stock.Invoice.EwayBill;
                        im.EwayBillDate = stock.Invoice.EwayBillDate;
                        im.InvoiceFile = stock.Invoice.InvoiceFile;
                        im.SupplierId = stock.Invoice.SupplierId;
                        im.Grn = stock.Invoice.Grn;
                        im.Poid = stock.Invoice.Poid;
                        im.FreightInsurance = stock.Invoice.FreightInsurance;
                        im.ShippingHandling = stock.Invoice.ShippingHandling;
                        im.OtherCharges = stock.Invoice.OtherCharges;
                        im.InvoiceTotal = stock.Invoice.InvoiceTotal;
                        db.SaveChanges();
                    }


                    StockMaster sm = new StockMaster();
                    sm.StockId = stock.StockId;
                    sm.InvoiceId = stock.Invoice.InvoiceId;
                    sm.StockDate = stock.StockDate;
                    sm.AddedBy = GlobalData.loggedInUser;
                    sm.AddedDate = System.DateTime.Now;
                    sm.AllocationCompleted = false;
                    sm.InspectionCompleted = false;
                    sm.ManageRejectedItemsCompleted = false;

                    db.StockMasters.Add(sm);
                    db.SaveChanges();
                    var istdate = TimeZoneInfo.ConvertTimeFromUtc(sm.StockDate, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time"));
                    sm.Batch = istdate.ToString("dd/MM/yyyy") + "/" + sm.StockId;

                    var pot = db.PurchaseOrderTables.FirstOrDefault(x => x.Poid == stock.Invoice.Poid);
                    sm.ProductQuality = pot.Potype == "IMPORT" ? "IMPORTED" : "DOMESTIC";

                    db.SaveChanges();
                    foreach (var item in stock.StockProduct)
                    {
                        StockProductTable spt = new StockProductTable();
                        spt.StockId = sm.StockId;
                        spt.ProductId = item.ProductId;
                        spt.Sku = item.Sku;
                        spt.Barcode = (item.Barcode == null || item.Barcode == string.Empty) ? "N/A" : item.Barcode;
                        spt.Quantity = item.AcceptedQuantity;
                        spt.ManufacturedDate = item.ManufacturedDate;
                        spt.ExpiryDate = item.ExpiryDate;
                        spt.Unit = item.Unit;
                        spt.PricePerUnit = item.PricePerUnit;
                        spt.Grade = item.Grade;
                        spt.AcceptedQuantity = item.AcceptedQuantity;
                        spt.ThicknessId = item.ThicknessId;
                        spt.GrainId = item.GrainId;
                        spt.WidthId = item.WidthId;
                        spt.ColorId = item.ColorId;
                        spt.PostProcess = item.PostProcess;
                        spt.ReceivedQuantity = item.AcceptedQuantity;
                        spt.FreightPerUnit = item.FreightPerUnit;
                        spt.MiscPerUnit = item.MiscPerUnit;
                        spt.InvoicePricePerUnit = item.InvoicePricePerUnit;
                        spt.ShippingHandlingPerUnit = item.ShippingHandlingPerUnit;

                        // Handle supplier product name mapping creation
                        if (!string.IsNullOrEmpty(item.SupplierProductName) && item.ProductSupplierMappingId == 0)
                        {
                            // Create new mapping flag is implied when SupplierProductName is provided but ProductSupplierMappingId is 0
                            var currentUserId = db.UserMasters.FirstOrDefault(x => x.Email == GlobalData.loggedInUser)?.UserId ?? 1;

                            // Check if mapping already exists
                            var existingMapping = db.ProductSupplierMappings
                                .FirstOrDefault(psm => psm.ProductId == item.ProductId
                                                    && psm.SupplierId == stock.Invoice.SupplierId.Value
                                                    && psm.SupplierProductName.ToLower() == item.SupplierProductName.Trim().ToLower());

                            if (existingMapping != null)
                            {
                                // Update existing mapping
                                existingMapping.UsageCount++;
                                existingMapping.LastUsedDate = DateTime.Now;
                                existingMapping.LastUsedById = currentUserId;
                                spt.ProductSupplierMappingId = existingMapping.ProductSupplierMappingId;
                            }
                            else
                            {
                                // Create new mapping
                                var newMapping = new Models.ProductSupplierMapping
                                {
                                    ProductId = item.ProductId,
                                    SupplierId = stock.Invoice.SupplierId.Value,
                                    SupplierProductName = item.SupplierProductName.Trim(),
                                    UsageCount = 1,
                                    LastUsedDate = DateTime.Now,
                                    LastUsedById = currentUserId,
                                    CreatedDate = DateTime.Now,
                                    CreatedById = currentUserId
                                };

                                db.ProductSupplierMappings.Add(newMapping);
                                db.SaveChanges(); // Save to get the ID
                                spt.ProductSupplierMappingId = newMapping.ProductSupplierMappingId;
                            }

                            spt.SupplierProductName = item.SupplierProductName.Trim().ToLower();
                        }
                        else if (item.ProductSupplierMappingId > 0)
                        {
                            // Use existing mapping
                            var existingMapping = db.ProductSupplierMappings
                                .FirstOrDefault(psm => psm.ProductSupplierMappingId == item.ProductSupplierMappingId);

                            if (existingMapping != null)
                            {
                                var currentUserId = db.UserMasters.FirstOrDefault(x => x.Email == GlobalData.loggedInUser)?.UserId ?? 1;
                                existingMapping.UsageCount++;
                                existingMapping.LastUsedDate = DateTime.Now;
                                existingMapping.LastUsedById = currentUserId;

                                spt.ProductSupplierMappingId = existingMapping.ProductSupplierMappingId;
                                spt.SupplierProductName = existingMapping.SupplierProductName;
                            }
                        }

                        db.StockProductTables.Add(spt);
                    }
                    db.SaveChanges();
                    if (stock.Invoice.IsPocomplete == true)
                    {
                        var pod = db.PurchaseOrderTables.FirstOrDefault(x => x.Grn == stock.Invoice.Grn);
                        pod.Status = PmsCommon.PMSPurchaseOrderStatus.Complete;
                        pod.IsPocomplete = true;
                        pod.ActionBy = GlobalData.loggedInUser;

                        db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                        {
                            Poid = stock.Invoice.Poid.Value,
                            Status = PMSPurchaseOrderStatus.Complete,
                            Remark = "Manual PO Completion",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now
                        });
                        db.SaveChanges();
                    }
                    else if (IsPurchaseOrderComplete(stock.Invoice.Poid) == true)
                    {
                        var pod = db.PurchaseOrderTables.FirstOrDefault(x => x.Grn == stock.Invoice.Grn);
                        pod.Status = PmsCommon.PMSPurchaseOrderStatus.Complete;
                        pod.IsPocomplete = true;
                        pod.ActionBy = GlobalData.loggedInUser;

                        db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                        {
                            Poid = stock.Invoice.Poid.Value,
                            Status = PMSPurchaseOrderStatus.Complete,
                            Remark = "PO completed on all stocks received.",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now
                        });
                        db.SaveChanges();
                    }
                    else
                    {
                        db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                        {
                            Poid = stock.Invoice.Poid.Value,
                            Status = PMSPurchaseOrderStatus.PartialReceived,
                            Remark = "Partial Stock Received",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now
                        });
                        db.SaveChanges();
                    }
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                }
                catch (Exception ex)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occurred. Please contact administrator. " + ex.Message);
                }
            }
        }

        public long AddStockMasterPreFn(StockProductTableMasterVm item, pmsdbContext db)
        {
            try
            {
                if (item.StockProductId == 0)
                {
                    InvoiceMaster im = new InvoiceMaster();
                    if (item.isProductionStock)
                        im.InvoiceNumber = "ProductionStock_" + (db.InvoiceMasters.OrderByDescending(x => x.InvoiceId).First().InvoiceId + 1);
                    else
                        im.InvoiceNumber = "OpeningStock_" + (db.InvoiceMasters.OrderByDescending(x => x.InvoiceId).First().InvoiceId + 1);

                    im.SupplierId = item.SupplierId;
                    db.InvoiceMasters.Add(im);
                    db.SaveChanges();

                    StockMaster sm = new StockMaster();
                    sm.InvoiceId = im.InvoiceId;
                    sm.InspectionCompleted = true;
                    if (item.isProductionStock)
                        sm.IsOpeningStock = false;
                    else
                        sm.IsOpeningStock = true;

                    sm.AllocationCompleted = true;
                    sm.ManageRejectedItemsCompleted = true;
                    sm.SaleOrderId = item.SaleOrderId;
                    sm.StockDate = item.StockDate == null ? System.DateTime.UtcNow : item.StockDate.Value;
                    sm.AddedBy = GlobalData.loggedInUser;
                    sm.AddedDate = System.DateTime.Now;
                    db.StockMasters.Add(sm);

                    db.SaveChanges();
                    if (item.isProductionStock)
                    {
                        sm.Batch = GetSaleOrderJumboProductionBatchNo(item.SaleOrderId);
                        db.SaveChanges();
                    }
                    else
                    {
                        var istdate = TimeZoneInfo.ConvertTimeFromUtc(sm.StockDate, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time"));
                        sm.Batch = istdate.ToString("dd/MM/yyyy") + "/" + sm.StockId;
                        db.SaveChanges();
                    }

                    StockProductTable spt = new StockProductTable();
                    spt.StockId = sm.StockId;
                    spt.ProductId = item.ProductId;
                    spt.Sku = item.Sku;
                    spt.Barcode = (item.Barcode == null || item.Barcode == string.Empty) ? "N/A" : item.Barcode;
                    spt.Quantity = item.Quantity;
                    spt.ManufacturedDate = item.ManufacturedDate;
                    spt.ExpiryDate = item.ExpiryDate;
                    spt.Unit = item.Unit;
                    spt.PricePerUnit = item.PricePerUnit;
                    spt.Grade = item.Grade;
                    spt.AcceptedQuantity = item.Quantity;
                    spt.ReceivedQuantity = item.Quantity;
                    spt.ThicknessId = item.ThicknessId;
                    spt.GrainId = item.GrainId;
                    spt.WidthId = item.WidthId;
                    spt.ColorId = item.ColorId;
                    spt.PostProcess = item.PostProcess;
                    db.StockProductTables.Add(spt);
                    db.SaveChanges();
                    var spa = new StockProductAllocationTable()
                    {
                        StockProductId = spt.StockProductId,
                        Quantity = item.Quantity.Value,
                        InspectionType = PmsCommon.PMSStatus.Accepted,
                        RackId = item.RackId
                    };
                    db.StockProductAllocationTables.Add(spa);
                    db.SaveChanges();
                    return sm.StockId;
                }
                else
                {
                    StockMaster sm = db.StockMasters.FirstOrDefault(x => x.StockId == item.StockId);
                    sm.StockDate = item.StockDate == null ? System.DateTime.Now : item.StockDate.Value;
                    db.SaveChanges();

                    InvoiceMaster im = db.InvoiceMasters.FirstOrDefault(x => x.InvoiceId == sm.InvoiceId);
                    if (im == null)
                    {
                        im = new InvoiceMaster();
                        im.InvoiceNumber = "OpeningStock_" + (db.InvoiceMasters.OrderByDescending(x => x.InvoiceId).First().InvoiceId + 1);
                        im.SupplierId = item.SupplierId;
                        db.InvoiceMasters.Add(im);
                        db.SaveChanges();
                        sm.InvoiceId = im.InvoiceId;
                        db.SaveChanges();
                    }
                    else
                    {
                        im.SupplierId = item.SupplierId;
                        db.SaveChanges();
                    }

                    StockProductTable spt = db.StockProductTables.FirstOrDefault(x => x.StockProductId == item.StockProductId);
                    spt.Sku = item.Sku;
                    spt.Barcode = item.Barcode;
                    spt.Quantity = item.Quantity;
                    spt.ManufacturedDate = item.ManufacturedDate;
                    spt.ExpiryDate = item.ExpiryDate;
                    spt.Unit = item.Unit;
                    spt.PricePerUnit = item.PricePerUnit;
                    sm.StockDate = item.StockDate == null ? System.DateTime.Now : item.StockDate.Value;
                    spt.Grade = item.Grade;
                    spt.AcceptedQuantity = item.Quantity;
                    db.SaveChanges();
                    sm.Batch = sm.StockDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture) + "/" + sm.StockId;
                    db.SaveChanges();

                    var spa = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == item.StockProductId);
                    if (spa == null)
                    {
                        var newrec = new StockProductAllocationTable()
                        {
                            StockProductId = spt.StockProductId,
                            Quantity = item.Quantity.Value,
                            InspectionType = PmsCommon.PMSStatus.Accepted,
                            RackId = item.RackId
                        };
                        db.StockProductAllocationTables.Add(newrec);
                        db.SaveChanges();
                    }
                    else
                    {
                        spa.Quantity = item.Quantity.Value;
                        spa.InspectionType = PmsCommon.PMSStatus.Accepted;
                        spa.RackId = item.RackId;
                        db.SaveChanges();
                    }
                    return sm.StockId;
                }
                ;


            }
            catch (Exception ex)
            {
                return 0;
            }
        }
        public DateTime GetFinishedManufacturedProductExpiryDate(SaleFormulationCodeMaster data)
        {
            if (data.SaleFormulationCode.StartsWith("GZ-"))
            {
                var expiryDate = DateTime.Today.AddYears(2);
                return expiryDate;
            }
            else if (data.SaleFormulationCode.StartsWith("GZY-"))
            {
                var expiryDate = DateTime.Today.AddYears(1);
                return expiryDate;
            }
            else
            {
                var expiryDate = DateTime.Today.AddYears(1);
                return expiryDate;
            }
        }
        public string GetSaleOrderJumboProductionBatchNo(long? saleOrderId)
        {
            using var db = new pmsdbContext();

            var wpt = db.WorkPlanOrderTrackingTables.FirstOrDefault(x => x.SaleOrderId == saleOrderId);
            if (wpt != null)
            {
                var wpo = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == saleOrderId);
                var wpm = db.WorkPlanMasters.FirstOrDefault(x => x.WorkPlanId == wpo.WorkplanId);
                return wpm.BatchNo;
            }
            else
            {
                var sot = db.SaleOrderTimelineTables.FirstOrDefault(x => x.SaleOrderId == saleOrderId && x.Status == (int)ESalesOrderStatus.InJumbo); //This logic will need to be extended if Jumbo Rolls are getting created in different workPlans.
                var wpm = db.WorkPlanMasters.FirstOrDefault(x => x.WorkPlanId == sot.WorkPlanId);
                return wpm.BatchNo;
            }
        }

        public void RemoveStock(long stockid)
        {
            using (var db = new Models.pmsdbContext())
            {
                var stockrec = db.StockMasters.FirstOrDefault(x => x.StockId == stockid);
                var stockProductTableRec = db.StockProductTables.FirstOrDefault(x => x.StockId == stockid);
                var stockProductAllocationTablesList = db.StockProductAllocationTables.Where(x => x.StockProductId == stockProductTableRec.StockProductId).ToList();
                var InvoiceMasterRec = db.InvoiceMasters.FirstOrDefault(x => x.InvoiceId == stockrec.InvoiceId);
                if (stockProductAllocationTablesList != null)
                    db.StockProductAllocationTables.RemoveRange(stockProductAllocationTablesList);
                if (stockProductTableRec != null)
                    db.StockProductTables.Remove(stockProductTableRec);
                if (stockrec != null)
                    db.StockMasters.Remove(stockrec);
                if (InvoiceMasterRec != null)
                    db.InvoiceMasters.Remove(InvoiceMasterRec);
                db.SaveChanges();
            }
        }

        public ApiFunctionResponseVm AddStockMaster(StockProductTableMasterVm item)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        bool isRecordDuplicate = false;
                        var srec = db.StockProductTables.FirstOrDefault(x => x.ProductId == item.ProductId
                        && x.Sku == item.Sku
                        && x.Barcode == item.Barcode
                        && x.ManufacturedDate == item.ManufacturedDate
                        && x.ExpiryDate == item.ExpiryDate
                        && x.Unit == item.Unit
                        && x.Quantity == item.Quantity
                        && x.PricePerUnit == item.PricePerUnit
                        && x.Grade == item.Grade
                        );
                        if (srec != null)
                        {
                            var smrec = db.StockMasters.FirstOrDefault(x => x.StockId == srec.StockId);
                            var invrec = db.InvoiceMasters.FirstOrDefault(x => x.InvoiceId == smrec.InvoiceId);
                            if (invrec.SupplierId == item.SupplierId)
                            {
                                var allocrecs = db.StockProductAllocationTables.Where(x => x.StockProductId == srec.StockProductId && x.InspectionType == PmsCommon.PMSStatus.Accepted);
                                if (allocrecs.Any(x => x.RackId == item.RackId))
                                {
                                    isRecordDuplicate = true;
                                }
                            }
                        }
                        if (isRecordDuplicate)
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Record already exist");
                        }
                        else
                        {

                            var stock = AddStockMasterPreFn(item, db);
                            transaction.Commit();
                            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                        }
                    }
                    catch (System.Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }


        public ApiFunctionResponseVm StockInspection(StockVm stock)
        {
            using (var db = new Models.pmsdbContext())
            {
                var stk = db.StockMasters.FirstOrDefault(x => x.StockId == stock.StockId);
                foreach (var item in stock.StockProduct)
                {
                    StockProductTable spt = db.StockProductTables.FirstOrDefault(x => x.StockProductId == item.StockProductId);
                    spt.AcceptedQuantity = item.AcceptedQuantity;
                    spt.RejectedQuantity = item.RejectedQuantity;
                    spt.Comments = item.Comments;
                    if (item.RejectedQuantity > 0)
                    {
                        stk.ManageRejectedItemsCompleted = false;
                    }
                    db.SaveChanges();
                }
                stk.InspectionCompleted = true;
                stk.InspectionCompletedBy = GlobalData.loggedInUser;
                stk.InspectionCompletedDate = DateTime.Now;
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Inspection Completed Successfully");
        }
        public ApiFunctionResponseVm StockQualityInspection(StockVm stock)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var stk = db.StockMasters.FirstOrDefault(x => x.StockId == stock.StockId);
                var CurrentUserId = db.UserMasters.FirstOrDefault(x => x.Email == GlobalData.loggedInUser).UserId;
                foreach (var item in stock.StockProduct)
                {
                    StockProductTable spt = db.StockProductTables.FirstOrDefault(x => x.StockProductId == item.StockProductId);
                    spt.AcceptedQuantity = item.AcceptedQuantity;
                    spt.RejectedQuantity = item.RejectedQuantity;
                    spt.Comments = item.Comments;
                    if (item.RejectedQuantity > 0)
                    {
                        stk.ManageRejectedItemsCompleted = false;
                    }
                    if (item.FileUploads != null)
                    {
                        foreach (var file in item.FileUploads)
                        {
                            var fileUpload = new FileUploadTable()
                            {
                                EntityId = item.StockProductId,
                                EntityName = "StockProductTable",
                                FileName = file.FileName,
                                ContainerName = file.ContainerName,
                                FilePath = file.FilePath,
                                UploadedBy = CurrentUserId,
                                UploadedDate = DateTime.Now
                            };
                            db.FileUploadTables.Add(fileUpload);
                            db.SaveChanges();
                        }
                    }

                    db.SaveChanges();
                }
                stk.IsQualityInspectionCompleted = true;
                stk.QualityInspectionCompletedBy = CurrentUserId;
                stk.QualityInspectionCompletedDate = DateTime.Now;
                db.SaveChanges();
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Quality Inspection Completed Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
            }
        }

        public ApiFunctionResponseVm SaveStockAllocation(StockProductTableVm stockproduct)
        {
            using var db = new Models.pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                List<StockProductAllocationTable> existingAllocations = db.StockProductAllocationTables
                    .Where(x => x.StockProductId == stockproduct.StockProductId)
                    .ToList();

                var rackStoreMapping = db.RackMasters.ToDictionary(r => r.RackId, r => r.StoreId);

                // Keep track of all label IDs that should be allocated
                HashSet<long> allocatedLabelIds = new HashSet<long>();

                foreach (var allocation in stockproduct.StockProductAllocation)
                {
                    StockProductAllocationTable allocationData = new();
                    if (allocation.AllocationId == 0)
                    {
                        // New allocation
                        var newAllocation = new StockProductAllocationTable
                        {
                            StockProductId = allocation.StockProductId,
                            Quantity = allocation.Quantity,
                            InspectionType = allocation.InspectionType,
                            RackId = allocation.RackId
                        };
                        db.StockProductAllocationTables.Add(newAllocation);
                        db.SaveChanges();
                        allocationData = newAllocation;
                    }
                    else
                    {
                        // Existing allocation
                        var existingAllocation = existingAllocations.FirstOrDefault(x => x.AllocationId == allocation.AllocationId);
                        if (existingAllocation != null)
                        {
                            existingAllocation.Quantity = allocation.Quantity;
                            existingAllocation.RackId = allocation.RackId;
                            existingAllocation.InspectionType = allocation.InspectionType;

                            allocationData = existingAllocation;
                        }
                    }

                    // Update labels for this allocation
                    if (allocation.StockLabelIds != null)
                    {
                        allocatedLabelIds.UnionWith(allocation.StockLabelIds);
                        UpdateLabelsForAllocation(db, allocation, rackStoreMapping, allocationData.AllocationId);
                    }
                }

                // Remove allocations that are no longer present
                var allocationsToRemove = existingAllocations
                    .Where(x => !stockproduct.StockProductAllocation.Any(s => s.AllocationId == x.AllocationId))
                    .ToList();

                db.StockProductAllocationTables.RemoveRange(allocationsToRemove);

                // Clear allocations for labels that are no longer allocated
                ClearUnallocatedLabels(db, stockproduct.StockProductId, allocatedLabelIds);

                db.SaveChanges();

                // Existing logic for checking if allocation is complete
                bool flag = IsStockAllocationComplete(db, stockproduct.StockId);
                if (flag)
                {
                    var res = db.StockMasters.FirstOrDefault(x => x.StockId == stockproduct.StockId);
                    res.AllocationCompleted = true;
                    db.SaveChanges();
                }

                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Stock allocation saved successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                // Log the exception
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error occurred while saving stock allocation");
            }
        }

        private void UpdateLabelsForAllocation(Models.pmsdbContext db, StockProductAllocationVm allocation, Dictionary<long, long?> rackStoreMapping, long allocationId)
        {
            if (allocation.StockLabelIds != null && allocation.StockLabelIds.Any())
            {
                var stockLabels = db.StockLabelTables
                    .Where(sl => allocation.StockLabelIds.Contains(sl.StockLabelId))
                    .ToList();

                foreach (var label in stockLabels)
                {
                    label.CurrentRackId = allocation.RackId;
                    label.CurrentStoreId = rackStoreMapping[allocation.RackId];
                    label.AllocationId = allocationId;
                    UpdateStockLabelStatus(db, label.StockLabelId, StockLabelStatus.Allocated, "Store allocation completed", allocationId, "StockProductAllocationTable");
                    UpdateStockLabelStatus(db, label.StockLabelId, StockLabelStatus.Active, "Activated after store allocation", label.StockLabelId, "StockLabelTable");
                }
            }
        }

        private void ClearUnallocatedLabels(Models.pmsdbContext db, long stockProductId, HashSet<long> allocatedLabelIds)
        {
            var unallocatedLabels = db.StockLabelTables
                .Where(sl => sl.StockProductId == stockProductId && !allocatedLabelIds.Contains(sl.StockLabelId))
                .ToList();

            foreach (var label in unallocatedLabels)
            {
                label.CurrentRackId = null;
                label.CurrentStoreId = null;
                label.AllocationId = null;
                UpdateStockLabelStatus(db, label.StockLabelId, StockLabelStatus.DeAllocated, "DeAllocated", label.StockLabelId, "StockLabelTable");
            }
        }

        public ApiFunctionResponseVm SaveStockProductManageRejected(List<StockProductManageRejectedVm> stockproduct)
        {
            using var db = new Models.pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                foreach (var item in stockproduct)
                {
                    if (item.ItemAction == "Accept")
                    {
                        var sprec = db.StockProductTables.SingleOrDefault(x => x.StockProductId == item.StockProductId);
                        sprec.AcceptedQuantity = sprec.AcceptedQuantity + item.Quantity;
                        sprec.RejectedQuantity = sprec.RejectedQuantity - item.Quantity;
                        var rackrec = db.StockProductAllocationTables.FirstOrDefault(x => x.AllocationId == item.AllocationId);
                        rackrec.Quantity = rackrec.Quantity - item.Quantity;
                        var stockLabels = db.StockLabelTables
                        .Where(sl => item.AcceptStockLabelIds.Contains(sl.StockLabelId))
                        .ToList();

                        if (rackrec.Quantity == 0)
                        {
                            db.StockProductAllocationTables.Remove(rackrec);
                        }
                        var spt = new StockProductAllocationTable()
                        {
                            StockProductId = item.StockProductId,
                            Quantity = item.Quantity,
                            InspectionType = "Accepted",
                            RackId = item.ToRackId
                        };
                        db.StockProductAllocationTables.Add(spt);
                        db.SaveChanges();

                        foreach (var label in stockLabels)
                        {
                            label.InspectionStatus = "Accepted";
                            UpdateStockLabelStatus(db, label.StockLabelId, StockLabelStatus.Accepted, "Accepted again the rejected items.", label.StockLabelId, "StockLabelTable");
                        }
                    }
                    else
                    {
                        var spt = new StockProductManageRejectedTable()
                        {
                            StockProductId = item.StockProductId,
                            Quantity = item.Quantity,
                            ItemAction = item.ItemAction
                        };

                        var rackrec = db.StockProductAllocationTables.FirstOrDefault(x => x.AllocationId == item.AllocationId);
                        rackrec.Quantity = rackrec.Quantity - item.Quantity;
                        if (rackrec.Quantity == 0)
                        {
                            db.StockProductAllocationTables.Remove(rackrec);
                        }

                        db.StockProductManageRejectedTables.Add(spt);
                        db.SaveChanges();
                        if (item.ItemAction == "Dispatch")
                        {
                            var sptDis = new StockProductRejectedDispatchTable()
                            {
                                StockProductManageRejectedId = spt.Id,
                                TransportId = item.StockProductRejectedDispatch.TransportId,
                                VehicleId = item.StockProductRejectedDispatch.VehicleId,
                                DispatchDate = System.DateTime.Now,
                                DispatchId = item.StockProductRejectedDispatch.DispatchId
                            };
                            db.StockProductRejectedDispatchTables.Add(sptDis);
                            db.SaveChanges();
                        }
                    }
                }

                var stockId = db.StockProductTables.FirstOrDefault(x => x.StockProductId == stockproduct[0].StockProductId).StockId;
                bool flag = IsManageRejectedItemsCompleted(db, stockId);
                if (flag)
                {
                    var res = db.StockMasters.FirstOrDefault(x => x.StockId == stockId);
                    res.ManageRejectedItemsCompleted = true;
                    db.SaveChanges();
                }
                db.SaveChanges();
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
            }
        }

        public List<ProductStockStoreReportVm> GetProductWiseStorestock()
        {
            List<ProductStockStoreReportVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                var data = (from a in db.StockProductTables
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            join p in db.ProductMasters on a.ProductId equals p.ProductId
                            join r in db.RackMasters on b.RackId equals r.RackId
                            join s in db.StoreMasters on r.StoreId equals s.StoreId
                            where b.InspectionType.ToLower() == "accepted"
                            select new ProductStockStoreReportVm
                            {
                                ProductId = a.ProductId,
                                ProductName = p.ProductName,
                                ProductCode = p.ProductCode,
                                ProductType = p.ProductType,
                                StoreId = r.StoreId,
                                StoreName = s.StoreName,
                                StoreCode = s.StoreCode,
                                Quantity = b.Quantity,
                                Unit = a.Unit
                            }).ToList();

                res = (from a in data
                       group a by new
                       {
                           a.ProductId,
                           a.ProductName,
                           a.ProductType,
                           a.ProductCode,
                           a.StoreId,
                           a.StoreName,
                           a.StoreCode,
                           a.Unit
                       } into pg
                       select new ProductStockStoreReportVm
                       {
                           ProductId = pg.Key.ProductId,
                           ProductName = pg.Key.ProductName,
                           ProductCode = pg.Key.ProductCode,
                           ProductType = pg.Key.ProductType,
                           StoreId = pg.Key.StoreId,
                           StoreName = pg.Key.StoreName,
                           StoreCode = pg.Key.StoreCode,
                           Unit = pg.Key.Unit,
                           Quantity = pg.Sum(x => x.Quantity),
                       }).ToList();

            }
            return res.Where(x => x.Quantity > 0).ToList();
        }

        public List<ProductStockStoreRackReportVm> GetProductWiseStoreRackstock()
        {
            List<ProductStockStoreRackReportVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                var data = (from a in db.StockProductTables
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            join p in db.ProductMasters on a.ProductId equals p.ProductId
                            join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId into ps
                            from pr in ps.DefaultIfEmpty()
                            join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            join r in db.RackMasters on b.RackId equals r.RackId
                            join s in db.StoreMasters on r.StoreId equals s.StoreId
                            where b.InspectionType.ToLower() == "accepted"
                            select new ProductStockStoreRackReportVm
                            {
                                ProductId = a.ProductId,
                                ProductName = p.ProductName,
                                ProductCode = p.ProductCode,
                                ProductType = p.ProductType,
                                ProductCategoryId = p.ProductCategoryId,
                                ProductCategory = pr.ProductCategory,
                                ProductFirstSubCategoryId = p.ProductFirstSubCategoryId,
                                ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                ProductSecSubCategoryId = p.ProductSecSubCategoryId,
                                ProductSecSubCategory = psc.ProductSecSubCategory,
                                StoreId = r.StoreId,
                                StoreName = s.StoreName,
                                StoreCode = s.StoreCode,
                                RackId = r.RackId,
                                RackName = r.RackName,
                                RackCode = r.RackCode,
                                Quantity = b.Quantity,
                                Unit = a.Unit
                            }).ToList();

                res = (from a in data
                       group a by new
                       {
                           a.ProductId,
                           a.ProductName,
                           a.ProductType,
                           a.ProductCode,
                           a.ProductCategoryId,
                           a.ProductFirstSubCategoryId,
                           a.ProductSecSubCategoryId,
                           a.ProductCategory,
                           a.ProductFirstSubCategory,
                           a.ProductSecSubCategory,
                           a.StoreId,
                           a.StoreName,
                           a.StoreCode,
                           a.RackId,
                           a.RackName,
                           a.RackCode,
                           a.Unit,
                       } into pg
                       select new ProductStockStoreRackReportVm
                       {
                           ProductId = pg.Key.ProductId,
                           ProductName = pg.Key.ProductName,
                           ProductCode = pg.Key.ProductCode,
                           ProductType = pg.Key.ProductType,
                           ProductCategoryId = pg.Key.ProductCategoryId,
                           ProductFirstSubCategoryId = pg.Key.ProductFirstSubCategoryId,
                           ProductSecSubCategoryId = pg.Key.ProductSecSubCategoryId,
                           ProductCategory = pg.Key.ProductCategory,
                           ProductFirstSubCategory = pg.Key.ProductFirstSubCategory,
                           ProductSecSubCategory = pg.Key.ProductSecSubCategory,
                           StoreId = pg.Key.StoreId,
                           StoreName = pg.Key.StoreName,
                           StoreCode = pg.Key.StoreCode,
                           RackId = pg.Key.RackId,
                           RackName = pg.Key.RackName,
                           RackCode = pg.Key.RackCode,
                           Quantity = pg.Sum(x => x.Quantity),
                           Unit = pg.Key.Unit,
                       }).ToList();

            }
            return res.Where(x => x.Quantity > 0).ToList();
        }

        public List<ProductStockStoreRackReportVm> GetProductWiseStoreRackstockByStoreId(long storeId)
        {
            List<ProductStockStoreRackReportVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                var data = (from a in db.StockProductTables
                            join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                            join p in db.ProductMasters on a.ProductId equals p.ProductId
                            join r in db.RackMasters on b.RackId equals r.RackId
                            join s in db.StoreMasters on r.StoreId equals s.StoreId
                            where b.InspectionType.ToLower() == "accepted" &&
                            (storeId == 0 || r.StoreId == storeId)
                            select new ProductStockStoreRackReportVm
                            {
                                ProductId = a.ProductId,
                                ProductName = p.ProductName,
                                ProductCode = p.ProductCode,
                                ProductType = p.ProductType,
                                StoreId = r.StoreId,
                                StoreName = s.StoreName,
                                StoreCode = s.StoreCode,
                                RackId = r.RackId,
                                RackName = r.RackName,
                                RackCode = r.RackCode,
                                Quantity = b.Quantity,
                                Unit = a.Unit
                            }).ToList();

                res = (from a in data
                       group a by new
                       {
                           a.ProductId,
                           a.ProductName,
                           a.ProductType,
                           a.ProductCode,
                           a.StoreId,
                           a.StoreName,
                           a.StoreCode,
                           a.RackId,
                           a.RackName,
                           a.RackCode,
                           a.Unit,
                       } into pg
                       select new ProductStockStoreRackReportVm
                       {
                           ProductId = pg.Key.ProductId,
                           ProductName = pg.Key.ProductName,
                           ProductCode = pg.Key.ProductCode,
                           ProductType = pg.Key.ProductType,
                           StoreId = pg.Key.StoreId,
                           StoreName = pg.Key.StoreName,
                           StoreCode = pg.Key.StoreCode,
                           RackId = pg.Key.RackId,
                           RackName = pg.Key.RackName,
                           RackCode = pg.Key.RackCode,
                           Unit = pg.Key.Unit,
                           Quantity = pg.Sum(x => x.Quantity),
                       }).ToList();

            }
            return res.Where(x => x.Quantity > 0).ToList();
        }


        private bool IsPurchaseOrderComplete(long? poid)
        {
            if (poid == null)
                return false;

            bool IsPurchaseOrderComplete = true;
            using (var db = new Models.pmsdbContext())
            {
                var PurchaseOrderProduct = (from p in db.PurchaseOrderProductTables
                                            where p.Poid == poid
                                            select new PurchaseOrderProductVm
                                            {
                                                ProductId = p.ProductId,
                                                Quantity = p.Quantity,
                                            }).ToList();
                foreach (var item in PurchaseOrderProduct)
                {
                    decimal? res = (from p in db.StockProductTables
                                    join s in db.StockMasters on p.StockId equals s.StockId
                                    join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                                    where i.Poid == poid && p.ProductId == item.ProductId
                                    select p.Quantity).Sum();
                    if (item.Quantity > res)
                    {
                        IsPurchaseOrderComplete = false;
                        break;
                    }
                }
            }
            return IsPurchaseOrderComplete;
        }

        public ApiFunctionResponseVm UpdateStockProductPriceByStockProductId(StockProductPriceVm stockproductvm)
        {
            using (var db = new Models.pmsdbContext())
            using (var transaction = db.Database.BeginTransaction())
            {
                try
                {
                    // Get all related stock products (original and transferred)
                    var relatedStockProducts = new List<StockProductTable>();
                    var updateByUserId = db.UserMasters.FirstOrDefault(x => x.Email == GlobalData.loggedInUser).UserId;

                    // Get initial stock product
                    var initialStockProduct = db.StockProductTables
                        .FirstOrDefault(sp => sp.StockProductId == stockproductvm.StockProductId
                            && sp.StockId == stockproductvm.StockId);

                    if (initialStockProduct == null)
                        return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Stock product not found");
                    // Check if there are any changes in the rates
                    bool hasChanges = false;

                    if (initialStockProduct.PricePerUnit != stockproductvm.PricePerUnit ||
                        initialStockProduct.ShippingHandlingPerUnit != stockproductvm.ShippingHandlingPerUnit ||
                        initialStockProduct.FreightPerUnit != stockproductvm.FreightPerUnit)
                    {
                        hasChanges = true;
                    }

                    if (!hasChanges)
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "No changes found in the rates");
                    }

                    relatedStockProducts.Add(initialStockProduct);

                    // Track all transfers recursively
                    var processedIds = new HashSet<long>
                    {
                        initialStockProduct.StockProductId
                    };

                    void TrackTransfers(long stockProductId)
                    {
                        // Find all transfers where this stock product was transferred
                        var transfers = db.IssueProductTables
                            .Where(ip => ip.StockProductId == stockProductId)
                            .ToList();

                        foreach (var transfer in transfers)
                        {
                            if (transfer.ToNewStockProductId.HasValue &&
                                !processedIds.Contains(transfer.ToNewStockProductId.Value))
                            {
                                var transferredProduct = db.StockProductTables
                                    .FirstOrDefault(sp => sp.StockProductId == transfer.ToNewStockProductId);

                                if (transferredProduct != null)
                                {
                                    relatedStockProducts.Add(transferredProduct);
                                    processedIds.Add(transfer.ToNewStockProductId.Value);

                                    // Recursively track further transfers
                                    TrackTransfers(transfer.ToNewStockProductId.Value);
                                }
                            }
                        }
                    }

                    TrackTransfers(initialStockProduct.StockProductId);

                    // Create price tracking record
                    var priceTrackingRecord = new StockPriceTrackingTable
                    {
                        StockProductId = initialStockProduct.StockProductId,
                        PricePerUnit = initialStockProduct.PricePerUnit,
                        ShippingHandlingPerUnit = initialStockProduct.ShippingHandlingPerUnit,
                        FreightPerUnit = initialStockProduct.FreightPerUnit,
                        InvoicePricePerUnit = initialStockProduct.InvoicePricePerUnit,
                        MiscPerUnit = initialStockProduct.MiscPerUnit,
                        NewPricePerUnit = stockproductvm.PricePerUnit,
                        NewShippingHandlingPerUnit = stockproductvm.ShippingHandlingPerUnit,
                        NewFreightPerUnit = stockproductvm.FreightPerUnit,
                        NewInvoicePricePerUnit = stockproductvm.InvoicePricePerUnit,
                        NewMiscPerUnit = stockproductvm.MiscPerUnit,
                        UpdatedById = updateByUserId,
                        UpdatedDate = DateTime.Now
                    };
                    db.StockPriceTrackingTables.Add(priceTrackingRecord);

                    // Update price for all related stock products
                    foreach (var stockProduct in relatedStockProducts)
                    {
                        stockProduct.PricePerUnit = stockproductvm.PricePerUnit;
                        stockProduct.ShippingHandlingPerUnit = stockproductvm.ShippingHandlingPerUnit;
                        stockProduct.FreightPerUnit = stockproductvm.FreightPerUnit;
                        stockProduct.InvoicePricePerUnit = stockproductvm.InvoicePricePerUnit;
                        stockProduct.MiscPerUnit = stockproductvm.MiscPerUnit;
                    }

                    db.SaveChanges();
                    transaction.Commit();

                    // Check if any of these products are consumed
                    var stockProductIds = relatedStockProducts.Select(sp => sp.StockProductId).ToList();
                    var consumedStockProductData = db.ConsumeStockProductMasters
                        .Where(cp => stockProductIds.Contains(cp.StockProductId.Value))
                        .Select(cp => new { cp.StockProductId, cp.SaleOrderId }).ToList();
                    var consumedSaleOrderIds = consumedStockProductData.Select(x => x.SaleOrderId).Distinct().ToList();
                    if (consumedStockProductData.Count > 0 && consumedSaleOrderIds.Count > 0)
                    {
                        ReportDataFn reportDataFn = new(GlobalData);
                        var consumedStockProductIds = consumedStockProductData.Select(x => x.StockProductId).Distinct().ToList();
                        _ = reportDataFn.SendStockPriceUpdateMail(consumedStockProductIds, consumedSaleOrderIds);
                    }
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Price updated successfully for all related stock products");
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError,
                        "An error occurred while updating prices: " + ex.Message);
                }
            }
        }
        public List<StockProductTable> TrackTransferredStock(long stockProductId)
        {
            using var db = new pmsdbContext();
            var processedIds = new HashSet<long>();
            var allStockProducts = new List<StockProductTable>();

            void TrackTransfers(long currentStockProductId)
            {
                // Skip if already processed
                if (!processedIds.Add(currentStockProductId))
                    return;

                // Get the stock product
                var stockProduct = db.StockProductTables
                    .FirstOrDefault(sp => sp.StockProductId == currentStockProductId);

                if (stockProduct != null)
                {
                    allStockProducts.Add(stockProduct);

                    // Find all transfers where this stock product was transferred
                    var transfers = db.IssueProductTables
                        .Where(ip => ip.StockProductId == currentStockProductId &&
                               ip.ToNewStockProductId.HasValue)
                        .ToList();

                    // Recursively process each transfer
                    foreach (var transfer in transfers)
                    {
                        TrackTransfers(transfer.ToNewStockProductId.Value);
                    }
                }
            }

            // Start tracking from initial stock product
            TrackTransfers(stockProductId);

            return allStockProducts;
        }

        // Overload to track multiple stock products at once
        public List<StockProductTable> TrackTransferredStock(IEnumerable<long> stockProductIds)
        {
            var allStockProducts = new List<StockProductTable>();
            foreach (var stockProductId in stockProductIds)
            {
                var relatedProducts = TrackTransferredStock(stockProductId);
                allStockProducts.AddRange(relatedProducts);
            }
            return allStockProducts.DistinctBy(x => x.StockProductId).ToList();
        }

        // Overload to track by ProductId
        public List<StockProductTable> TrackTransferredStockByProductId(long productId)
        {
            using var db = new pmsdbContext();

            // Get initial stock products for the product
            var initialStockProducts = db.StockProductTables
                .Where(sp => sp.ProductId == productId)
                .Select(sp => sp.StockProductId)
                .ToList();

            return TrackTransferredStock(initialStockProducts);
        }
        public decimal GetStockQuantityByProductId(long productId, List<StockProductTable> StockProduct = null)
        {
            using var db = new pmsdbContext();
            var stockProduct = StockProduct ?? TrackTransferredStockByProductId(productId);

            if (stockProduct != null)
            {
                var allStockProductIds = stockProduct.Select(x => x.StockProductId).Distinct().ToList();
                var stockProductAllocation = db.StockProductAllocationTables.Where(x => allStockProductIds.Contains(x.StockProductId)).ToList();
                var totalQuantity = stockProductAllocation.Sum(x => x.Quantity);
                return totalQuantity;
            }
            else
            {
                return 0;
            }
        }
        public List<StockLabelResponseVm> GenerateProductStockLabel(StockLabelTableVm label)
        {
            List<StockLabelResponseVm> generatedLabels = new();

            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var productName = db.ProductMasters.Find(label.ProductId).ProductName;
                var StockBatch = db.StockMasters.Find(label.StockId).Batch;
                if (productName == null) throw new ArgumentException("Product not found");
                if (StockBatch == null) throw new ArgumentException("Stock not found");
                if (label.NumberOfLabels == 0) throw new ArgumentException("Number of labels cannot be zero");

                CommonDataFn commonDataFn = new(GlobalData);
                List<StockLabelTable> stockLabelsToAdd = new();
                var transationdate = DateTime.Now;


                // Generate all stock labels and add to the list for batch save operation later
                for (int i = 0; i < label.NumberOfLabels; i++)
                {
                    var (serialNo, shortCode) = commonDataFn.GenerateSerialNoWithShortCode(db);
                    StockLabelTable st = new()
                    {
                        StockProductId = label.StockProductId,
                        StockId = label.StockId,
                        ProductId = label.ProductId,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = transationdate,
                        LabelStatus = "Pending",
                        SerialNo = serialNo,
                        ShortCode = shortCode
                    };
                    stockLabelsToAdd.Add(st);
                }

                // Perform single batch save for all labels in the list
                db.StockLabelTables.AddRange(stockLabelsToAdd);
                db.SaveChanges();

                // Build response objects and update status after successful save
                foreach (var st in stockLabelsToAdd)
                {
                    generatedLabels.Add(new StockLabelResponseVm
                    {
                        StockLabelId = st.StockLabelId,
                        SerialNo = st.SerialNo,
                        ShortCode = st.ShortCode,
                        BatchNo = StockBatch,
                        ProductId = st.ProductId,
                        ProductName = productName
                    });
                    UpdateStockLabelStatus(db, st.StockLabelId, StockLabelStatus.Pending, "Pending", st.StockLabelId, "StockLabelTable");
                }

                transaction.Commit();
                return generatedLabels;
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw new ApplicationException("Error generating stock labels", ex);
            }
        }
        public ApiFunctionResponseVm UpdateStockProductLabel(StockLabelTableVm label)
        {
            using (var db = new Models.pmsdbContext())
            {
                var stockLabel = db.StockLabelTables.FirstOrDefault(sl => sl.SerialNo == label.SerialNo);
                stockLabel ??= db.StockLabelTables.FirstOrDefault(sl => sl.ShortCode == label.ShortCode);

                if (stockLabel != null)
                {
                    stockLabel.Quantity = label.Quantity;
                    stockLabel.PackagingUnit = label.PackagingUnit;
                    stockLabel.MfgDate = label.MfgDate;
                    stockLabel.ExpiryDate = label.ExpiryDate;
                    stockLabel.InspectionStatus = label.InspectionStatus;
                    stockLabel.UpdatedBy = GlobalData.loggedInUser;
                    stockLabel.UpdatedDate = DateTime.Now;
                    stockLabel.IsActive = true;
                    if (label.CurrentStoreId != 0)
                        stockLabel.CurrentStoreId = label.CurrentStoreId;
                    if (label.CurrentRackId != 0)
                        stockLabel.CurrentRackId = label.CurrentRackId;
                    db.SaveChanges();

                    if (!string.IsNullOrEmpty(label.InspectionStatus) && label.InspectionStatus != stockLabel.InspectionStatus)
                    {
                        UpdateStockLabelStatus(db, stockLabel.StockLabelId, StockLabelStatus.Inspected, "Inspected", stockLabel.StockLabelId, "StockLabelTable");

                        if (label.InspectionStatus == "Accepted")
                        {
                            UpdateStockLabelStatus(db, stockLabel.StockLabelId, StockLabelStatus.Accepted, "Accepted", stockLabel.StockLabelId, "StockLabelTable");
                        }
                        else if (label.InspectionStatus == "Rejected")
                        {
                            UpdateStockLabelStatus(db, stockLabel.StockLabelId, StockLabelStatus.Rejected, "Rejected", stockLabel.StockLabelId, "StockLabelTable");
                        }
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Stock label inspection status updated successfully");
                    }
                    else
                    {
                        UpdateStockLabelStatus(db, stockLabel.StockLabelId, StockLabelStatus.Updated, "Updated", stockLabel.StockLabelId, "StockLabelTable");
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Stock label updated successfully");
                    }
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Stock label not found");
                }
            }
        }
        public List<StockLabelTableVm> SplitProductStockLabel(SplitLabelRequestVm request)
        {
            List<StockLabelTableVm> generatedLabels = new List<StockLabelTableVm>();

            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var existingLabel = db.StockLabelTables.Find(request.ExistingLabelId) ?? throw new ArgumentException("Existing label not found");
                decimal totalNewQuantity = request.Quantities.Sum();
                if (totalNewQuantity > existingLabel.Quantity)
                {
                    throw new ArgumentException("Total quantity of new labels exceeds the existing label quantity");
                }

                CommonDataFn commonDataFn = new(GlobalData);

                foreach (var quantity in request.Quantities)
                {
                    var (serialNo, shortCode) = commonDataFn.GenerateSerialNoWithShortCode(db);
                    StockLabelTable newLabel = new StockLabelTable
                    {
                        StockProductId = existingLabel.StockProductId,
                        StockId = existingLabel.StockId,
                        ProductId = existingLabel.ProductId,
                        Quantity = quantity,
                        PackagingUnit = existingLabel.PackagingUnit,
                        MfgDate = existingLabel.MfgDate,
                        ExpiryDate = existingLabel.ExpiryDate,
                        InspectionStatus = existingLabel.InspectionStatus,
                        LabelStatus = StockLabelStatus.Active,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now,
                        SerialNo = serialNo,
                        ShortCode = shortCode,
                        OriginalLabelId = existingLabel.StockLabelId,
                        CurrentRackId = existingLabel.CurrentRackId,
                        CurrentStoreId = existingLabel.CurrentStoreId,
                        AllocationId = existingLabel.AllocationId
                    };

                    db.StockLabelTables.Add(newLabel);
                    db.SaveChanges();

                    generatedLabels.Add(new StockLabelTableVm
                    {
                        StockLabelId = newLabel.StockLabelId,
                        SerialNo = newLabel.SerialNo,
                        ShortCode = newLabel.ShortCode,
                        StockProductId = newLabel.StockProductId,
                        ProductId = newLabel.ProductId,
                        Quantity = newLabel.Quantity,
                        PackagingUnit = newLabel.PackagingUnit,
                        AddedBy = newLabel.AddedBy,
                        AddedDate = newLabel.AddedDate,
                        OriginalLabelId = newLabel.OriginalLabelId,
                        LabelStatus = newLabel.LabelStatus
                    });
                }

                // Update the quantity of the existing label
                existingLabel.Quantity -= totalNewQuantity;
                if (existingLabel.Quantity == 0)
                {
                    existingLabel.IsActive = false;  // Deactivate if quantity becomes zero
                    existingLabel.AllocationId = null;
                    UpdateStockLabelStatus(db, existingLabel.StockLabelId, StockLabelStatus.Splitted, "The label will be deactivated now as new labels are generated using Split option.", existingLabel.StockLabelId, "StockLabelTable");
                    UpdateStockLabelStatus(db, existingLabel.StockLabelId, StockLabelStatus.InActive, "The label is marked In-Active.", existingLabel.AllocationId, "StockProductAllocationTable");
                }
                else if (existingLabel.Quantity > 0)
                {
                    UpdateStockLabelStatus(db, existingLabel.StockLabelId, StockLabelStatus.Splitted, "Some quantity from this label is splitted. See Stock Label history for more details.", existingLabel.AllocationId, "StockProductAllocationTable");
                }
                db.SaveChanges();
                transaction.Commit();
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw new ApplicationException("Error splitting stock label", ex);
            }

            return generatedLabels;
        }
        public ApiFunctionResponseVm GetStockLabelBySerialNo(StockLabelSearchVm request)
        {
            if (request.IsSerialNo)
            {
                if (!string.IsNullOrEmpty(request.SerialNo) && (request.SerialNo.Length != 14 || !Regex.IsMatch(request.SerialNo, @"^\d+$")))
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Serial number must be 14 digits and contain only numbers.");
                }
            }
            if (request.IsShortCode)
            {
                if (!string.IsNullOrEmpty(request.ShortCode) && !Regex.IsMatch(request.ShortCode, @"^[a-zA-Z0-9]+$"))
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Manual Entry should be used for short code only and short code must be alphanumeric and 4 or more characters.");
                }
            }
            using var db = new Models.pmsdbContext();
            var stockLabel = db.StockLabelTables
                .Where(sl => (string.IsNullOrEmpty(request.SerialNo) || sl.SerialNo == request.SerialNo)
                             && (string.IsNullOrEmpty(request.ShortCode) || sl.ShortCode == request.ShortCode))
                .Select(sl => new StockLabelBasicDetailsVm
                {
                    StockLabelId = sl.StockLabelId,
                    SerialNo = sl.SerialNo,
                    ShortCode = sl.ShortCode,
                    ProductId = sl.ProductId,
                    ProductName = sl.Product.ProductName,
                    BatchNo = sl.Stock.Batch,
                    PackagingUnit = sl.PackagingUnit,
                    Unit = sl.StockProduct.Unit,
                    Quantity = sl.Quantity,
                    InspectionStatus = sl.InspectionStatus,
                    LabelStatus = sl.LabelStatus,
                    IsActive = sl.IsActive ?? false,
                    MfgDate = sl.MfgDate,
                    ExpiryDate = sl.ExpiryDate,
                    StockProductId = sl.StockProductId,
                    CurrentRackId = sl.CurrentRackId,
                    CurrentStoreId = sl.CurrentStoreId,
                    RackName = sl.CurrentRack.RackName,
                    StoreName = sl.CurrentStore.StoreName
                })
                .FirstOrDefault();

            if (stockLabel == null)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Stock label not found");
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, stockLabel);
        }
        public List<StockLabelResponseVm> GetStockLabelsByProductStock(StockLabelTableVm request)
        {
            using var db = new Models.pmsdbContext();
            List<StockLabelResponseVm> stockLabels = new();
            if ((request.StockId == 0 || request.ProductId == 0) && request.CurrentRackId == 0 && request.CurrentStoreId == 0)
            {
                stockLabels = db.StockLabelTables
                    .Where(sl => sl.StockProductId == request.StockProductId)
                    .Select(sl => new StockLabelResponseVm
                    {
                        StockLabelId = sl.StockLabelId,
                        SerialNo = sl.SerialNo,
                        ShortCode = sl.ShortCode,
                        ProductId = sl.ProductId,
                        ProductName = sl.Product.ProductName,
                        BatchNo = sl.Stock.Batch,
                        IsActive = sl.IsActive ?? false,
                        Quantity = sl.Quantity,
                        InspectionStatus = sl.InspectionStatus,
                        CurrentRackId = sl.CurrentRackId,
                        LabelStatus = sl.LabelStatus
                    })
                    .ToList();
            }
            else if (request.CurrentStoreId > 0 && request.ProductId > 0)
            {
                stockLabels = db.StockLabelTables
                    .Where(sl => sl.ProductId == request.ProductId && sl.CurrentStoreId == request.CurrentStoreId && sl.InspectionStatus == "Accepted")
                    .Select(sl => new StockLabelResponseVm
                    {
                        StockLabelId = sl.StockLabelId,
                        SerialNo = sl.SerialNo,
                        ShortCode = sl.ShortCode,
                        ProductId = sl.ProductId,
                        ProductName = sl.Product.ProductName,
                        BatchNo = sl.Stock.Batch,
                        IsActive = sl.IsActive ?? false,
                        Quantity = sl.Quantity,
                        InspectionStatus = sl.InspectionStatus,
                        CurrentRackId = sl.CurrentRackId,
                        LabelStatus = sl.LabelStatus
                    })
                    .ToList();
            }
            else if (request.CurrentRackId > 0 && request.StockProductId > 0)
            {
                stockLabels = db.StockLabelTables
                    .Where(sl => sl.StockProductId == request.StockProductId && sl.CurrentRackId == request.CurrentRackId && sl.InspectionStatus == "Accepted")
                    .Select(sl => new StockLabelResponseVm
                    {
                        StockLabelId = sl.StockLabelId,
                        SerialNo = sl.SerialNo,
                        ShortCode = sl.ShortCode,
                        ProductId = sl.ProductId,
                        ProductName = sl.Product.ProductName,
                        BatchNo = sl.Stock.Batch,
                        IsActive = sl.IsActive ?? false,
                        Quantity = sl.Quantity,
                        InspectionStatus = sl.InspectionStatus,
                        CurrentRackId = sl.CurrentRackId,
                        LabelStatus = sl.LabelStatus
                    })
                    .ToList();
            }
            else
            {
                stockLabels = db.StockLabelTables
                    .Where(sl => sl.ProductId == request.ProductId && sl.StockId == request.StockId && sl.StockProductId == request.StockProductId)
                    .Select(sl => new StockLabelResponseVm
                    {
                        StockLabelId = sl.StockLabelId,
                        SerialNo = sl.SerialNo,
                        ShortCode = sl.ShortCode,
                        ProductId = sl.ProductId,
                        ProductName = sl.Product.ProductName,
                        BatchNo = sl.Stock.Batch,
                        IsActive = sl.IsActive ?? false,
                        Quantity = sl.Quantity,
                        LabelStatus = sl.LabelStatus
                    })
                    .ToList();
            }
            return stockLabels;
        }
        public List<StockLabelResponseVm> GetStockLabelsByBatchProductId(StockLabelTableVm request)
        {
            using var db = new Models.pmsdbContext();
            List<StockLabelResponseVm> stockLabels = new();

            var InitialBatch = (from sm in db.StockMasters
                                join sp in db.StockProductTables on sm.StockId equals sp.StockId
                                where sm.StockId == request.StockId && sp.ProductId == request.ProductId
                                select sm.Batch).FirstOrDefault();

            stockLabels = db.StockLabelTables
                .Join(db.StockMasters, sl => sl.StockId, sm => sm.StockId, (sl, sm) => new { sl, sm })
                .Where(x => x.sl.ProductId == request.ProductId && x.sm.Batch == InitialBatch)
                .Select(x => new StockLabelResponseVm
                {
                    StockLabelId = x.sl.StockLabelId,
                    SerialNo = x.sl.SerialNo,
                    ShortCode = x.sl.ShortCode,
                    ProductId = x.sl.ProductId,
                    ProductName = x.sl.Product.ProductName,
                    BatchNo = x.sm.Batch,
                    IsActive = x.sl.IsActive ?? false,
                    Quantity = x.sl.Quantity,
                    InspectionStatus = x.sl.InspectionStatus,
                    CurrentRackId = x.sl.CurrentRackId,
                    LabelStatus = x.sl.LabelStatus
                })
                .ToList();

            return stockLabels;
        }
        public List<StockLabelResponseVm> GetStockLabelsByStockLabelId(List<long> StockLabelIds)
        {
            using var db = new Models.pmsdbContext();
            var stockLabels = db.StockLabelTables
                .Where(sl => StockLabelIds.Contains(sl.StockLabelId))
                .Select(sl => new StockLabelResponseVm
                {
                    StockLabelId = sl.StockLabelId,
                    SerialNo = sl.SerialNo,
                    ShortCode = sl.ShortCode,
                    ProductId = sl.ProductId,
                    ProductName = sl.Product.ProductName,
                    BatchNo = sl.Stock.Batch,
                    InspectionStatus = sl.InspectionStatus,
                    Quantity = sl.Quantity,
                    LabelStatus = sl.LabelStatus,
                    IsActive = sl.IsActive ?? false
                })
                .ToList();
            return stockLabels;
        }
        public ApiFunctionResponseVm GetFullStockDetailByLabelSerialNo(StockLabelSearchVm request)
        {
            if (request.IsSerialNo)
            {
                if (!string.IsNullOrEmpty(request.SerialNo) && (request.SerialNo.Length != 14 || !Regex.IsMatch(request.SerialNo, @"^\d+$")))
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Serial number must be 14 digits and contain only numbers.");
                }
            }
            if (request.IsShortCode)
            {
                if (!string.IsNullOrEmpty(request.ShortCode) && !Regex.IsMatch(request.ShortCode, @"^[a-zA-Z0-9]+$"))
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Manual Entry should be used for short code only and short code must be alphanumeric and 4 or more characters.");
                }
            }

            using var db = new Models.pmsdbContext();
            var stockLabel = (from sl in db.StockLabelTables
                              join s in db.StockProductTables on sl.StockProductId equals s.StockProductId
                              join spa in db.StockProductAllocationTables on s.StockProductId equals spa.StockProductId into spad
                              from spa in spad.DefaultIfEmpty()
                              join sp in db.StockMasters on s.StockId equals sp.StockId
                              join inv in db.InvoiceMasters on sp.InvoiceId equals inv.InvoiceId
                              join sm in db.SupplierMasters on inv.SupplierId equals sm.SupplierId
                              join p in db.ProductMasters on sl.ProductId equals p.ProductId
                              join pc in db.ProductCategoryMasters on p.ProductCategoryId equals pc.ProductCategoryId
                              join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into pfd
                              from pf in pfd.DefaultIfEmpty()
                              join ps in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals ps.ProductSecSubCategoryId into psd
                              from ps in psd.DefaultIfEmpty()
                              join po in db.PurchaseOrderTables on inv.Poid equals po.Poid into pod
                              from po in pod.DefaultIfEmpty()
                              where (string.IsNullOrEmpty(request.SerialNo) || sl.SerialNo == request.SerialNo)
                              && (string.IsNullOrEmpty(request.ShortCode) || sl.ShortCode == request.ShortCode)
                              select new LabelFullDetailsVm
                              {
                                  BatchNo = sl.Stock.Batch,
                                  ProductName = sl.Product.ProductName,
                                  Product = new ProductMasterVm
                                  {
                                      ProductId = sl.ProductId,
                                      ProductName = sl.Product.ProductName,
                                      ProductCode = sl.Product.ProductCode,
                                      ProductType = sl.Product.ProductType,
                                      AvgGsm = sl.Product.AvgGsm,
                                      Unit = sl.Product.Unit,
                                      ProductCategory = pc.ProductCategory,
                                      ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                      ProductSecSubCategory = ps.ProductSecSubCategory,
                                      MinimumQuantity = sl.Product.MinimumQuantity,
                                      WidthInMeter = sl.Product.WidthInMeter,
                                  },
                                  SupplierName = sm.SupplierName,
                                  InvoiceNo = inv.InvoiceNumber,
                                  PurchaseOrderNo = po.Ponumber,
                                  ReceivedDate = sl.Stock.AddedDate,
                                  ReceivedBy = sl.Stock.AddedBy,
                                  InspectionCompletedBy = sl.Stock.InspectionCompletedBy,
                                  InspectedCompletedDate = sl.Stock.InspectionCompletedDate,
                                  QualityInspectionCompletedBy = sl.Stock.QualityInspectionCompletedByNavigation.Email,
                                  QualityInspectedCompletedDate = sl.Stock.QualityInspectionCompletedDate,
                                  StockUnit = sl.StockProduct.Unit,
                                  PricePerUnit = sl.StockProduct.PricePerUnit,
                                  ProductQuality = sl.Stock.ProductQuality,
                                  StockLabel = new StockLabelTableVm
                                  {
                                      StockLabelId = sl.StockLabelId,
                                      SerialNo = sl.SerialNo,
                                      ShortCode = sl.ShortCode,
                                      ProductId = sl.ProductId,
                                      Quantity = sl.Quantity,
                                      PackagingUnit = sl.PackagingUnit,
                                      MfgDate = sl.MfgDate,
                                      ExpiryDate = sl.ExpiryDate,
                                      InspectionStatus = sl.InspectionStatus,
                                      AddedBy = sl.AddedBy,
                                      AddedDate = sl.AddedDate,
                                      OriginalLabelId = sl.OriginalLabelId,
                                      IsActive = sl.IsActive ?? false,
                                      UpdatedBy = sl.UpdatedBy,
                                      UpdatedDate = sl.UpdatedDate,
                                      StoreName = sl.CurrentStore.StoreName,
                                      RackName = sl.CurrentRack.RackName,
                                      LabelStatus = sl.LabelStatus,
                                      AllocationId = sl.AllocationId,
                                      CurrentStoreId = sl.CurrentStoreId ?? 0,
                                      CurrentRackId = sl.CurrentRackId ?? 0
                                  }
                              })
                .FirstOrDefault();

            if (stockLabel == null)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Stock label not found");
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, stockLabel);
        }
        public void RecordLabelMovement(pmsdbContext db, long labelId, long fromStoreId, long fromRackId,
                                 long toStoreId, long toRackId, string reason, long issueId)
        {
            db.StockLabelMovementHistoryTables.Add(new StockLabelMovementHistoryTable
            {
                StockLabelId = labelId,
                FromStoreId = fromStoreId,
                FromRackId = fromRackId,
                ToStoreId = toStoreId,
                ToRackId = toRackId,
                MovementDate = DateTime.Now,
                MovedBy = GlobalData.loggedInUser,
                Reason = reason,
                IssueId = issueId
            });
            db.SaveChanges();
        }
        public void UpdateStockLabelStatus(Models.pmsdbContext db, long stockLabelId, string newStatus, string remark = null, long? relatedEntityId = null, string relatedEntityType = null)
        {
            var stockLabel = db.StockLabelTables.Find(stockLabelId);
            if (stockLabel != null)
            {
                var oldStatus = stockLabel.LabelStatus;
                if (newStatus == StockLabelStatus.Allocated)
                {
                    stockLabel.LabelStatus = StockLabelStatus.Active;
                }
                else if (newStatus == StockLabelStatus.Active || newStatus == StockLabelStatus.InActive)
                {
                    stockLabel.LabelStatus = newStatus;
                }

                if (newStatus == StockLabelStatus.Active || newStatus == StockLabelStatus.InActive)
                {
                    stockLabel.IsActive = newStatus == StockLabelStatus.Active ? true : false;
                }

                db.StockLabelTimelines.Add(new StockLabelTimeline
                {
                    StockLabelId = stockLabelId,
                    OldStatus = oldStatus,
                    NewStatus = newStatus,
                    ChangeDate = DateTime.Now,
                    ChangedBy = GlobalData.loggedInUser,
                    Remark = remark,
                    RelatedEntityId = relatedEntityId,
                    RelatedEntityType = relatedEntityType
                });
                db.SaveChanges();
            }

        }
        public List<StockLabelTimelineVm> GetStockLabelTimeline(long stockLabelId)
        {
            using var db = new pmsdbContext();
            return db.StockLabelTimelines.Where(tl => tl.StockLabelId == stockLabelId)
                .Select(tl => new StockLabelTimelineVm
                {
                    LabelTimelineId = tl.LabelTimelineId,
                    StockLabelId = tl.StockLabelId,
                    OldStatus = tl.OldStatus,
                    NewStatus = tl.NewStatus,
                    ChangeDate = tl.ChangeDate,
                    ChangedBy = tl.ChangedBy,
                    Remark = tl.Remark,
                    RelatedEntityId = tl.RelatedEntityId,
                    RelatedEntityType = tl.RelatedEntityType,
                    StockLabel = new StockLabelBasicDetailsVm
                    {
                        StockLabelId = tl.StockLabelId,
                        SerialNo = tl.StockLabel.SerialNo,
                        ShortCode = tl.StockLabel.ShortCode,
                        ProductId = tl.StockLabel.ProductId,
                        ProductName = tl.StockLabel.Product.ProductName,
                        Quantity = tl.StockLabel.Quantity,
                        PackagingUnit = tl.StockLabel.PackagingUnit,
                        MfgDate = tl.StockLabel.MfgDate,
                        ExpiryDate = tl.StockLabel.ExpiryDate,
                        InspectionStatus = tl.StockLabel.InspectionStatus
                    }
                })
                .OrderBy(tl => tl.ChangeDate)
                .ToList();
        }

        public List<StockLabelComprehensiveTimelineVm> GetStockLabelComprehensiveTimeline(long stockLabelId)
        {
            using var db = new pmsdbContext();

            // Get status change timeline
            var statusTimeline = db.StockLabelTimelines
                .Join(db.UserMasters, tl => tl.ChangedBy, um => um.Email, (tl, um) => new { tl, um })
                .Where(tl => tl.tl.StockLabelId == stockLabelId)
                .Select(tl => new StockLabelComprehensiveTimelineVm
                {
                    StockLabelId = tl.tl.StockLabelId,
                    EventType = "Status Change",
                    EventDate = tl.tl.ChangeDate ?? DateTime.Now,
                    EventBy = tl.um.Name,
                    EventDescription = $"Status changed from '{tl.tl.OldStatus ?? "N/A"}' to '{tl.tl.NewStatus}'",
                    Details = tl.tl.Remark,
                    FromLocation = null,
                    ToLocation = null,
                    OldStatus = tl.tl.OldStatus,
                    NewStatus = tl.tl.NewStatus
                })
                .ToList();

            // Get movement history timeline with proper joins
            var movementTimeline = (from mh in db.StockLabelMovementHistoryTables
                                    join fromStore in db.StoreMasters on mh.FromStoreId equals fromStore.StoreId
                                    join fromRack in db.RackMasters on mh.FromRackId equals fromRack.RackId
                                    join toStore in db.StoreMasters on mh.ToStoreId equals toStore.StoreId
                                    join toRack in db.RackMasters on mh.ToRackId equals toRack.RackId
                                    join um in db.UserMasters on mh.MovedBy equals um.Email into umJoin
                                    from um in umJoin.DefaultIfEmpty()
                                    where mh.StockLabelId == stockLabelId
                                    select new StockLabelComprehensiveTimelineVm
                                    {
                                        StockLabelId = mh.StockLabelId,
                                        EventType = "Location Movement",
                                        EventDate = mh.MovementDate,
                                        EventBy = um.Name,
                                        EventDescription = $"Moved from {fromStore.StoreName} - {fromRack.RackName} to {toStore.StoreName} - {toRack.RackName}",
                                        Details = mh.Reason,
                                        FromLocation = $"{fromStore.StoreName} - {fromRack.RackName}",
                                        ToLocation = $"{toStore.StoreName} - {toRack.RackName}",
                                        OldStatus = null,
                                        NewStatus = null
                                    })
                                   .ToList();

            // Combine and sort by date
            var combinedTimeline = statusTimeline.Concat(movementTimeline)
                .OrderBy(t => t.EventDate)
                .ToList();

            // Add stock label basic details to the first item for reference
            if (combinedTimeline.Any())
            {
                var stockLabel = db.StockLabelTables
                    .Where(sl => sl.StockLabelId == stockLabelId)
                    .Select(sl => new StockLabelBasicDetailsVm
                    {
                        StockLabelId = sl.StockLabelId,
                        SerialNo = sl.SerialNo,
                        ShortCode = sl.ShortCode,
                        ProductId = sl.ProductId,
                        ProductName = sl.Product.ProductName,
                        BatchNo = sl.Stock.Batch,
                        Quantity = sl.Quantity,
                        PackagingUnit = sl.PackagingUnit,
                        MfgDate = sl.MfgDate,
                        ExpiryDate = sl.ExpiryDate,
                        InspectionStatus = sl.InspectionStatus,
                        LabelStatus = sl.LabelStatus,
                        IsActive = sl.IsActive ?? false,
                        StoreName = sl.CurrentStore.StoreName,
                        RackName = sl.CurrentRack.RackName
                    })
                    .FirstOrDefault();

                foreach (var item in combinedTimeline)
                {
                    item.StockLabel = stockLabel;
                }
            }

            return combinedTimeline;
        }
        public StockLabelDetailsListingVm GetAllStockLabelsbyFilters(StockLabelListFilterVm request)
        {
            using var db = new Models.pmsdbContext();

            // Start with base query including all possible joins but with left joins
            var query = db.StockLabelTables
                .AsNoTracking()
                .Join(db.StockProductTables,
                    sl => sl.StockProductId,
                    s => s.StockProductId,
                    (sl, s) => new { sl, s })
                .Join(db.StockMasters,
                    x => x.s.StockId,
                    sp => sp.StockId,
                    (x, sp) => new { x.sl, x.s, sp })
                .Join(db.ProductMasters,
                    x => x.sl.ProductId,
                    p => p.ProductId,
                    (x, p) => new { x.sl, x.s, x.sp, p })
                .GroupJoin(db.InvoiceMasters,
                    x => x.sp.InvoiceId,
                    inv => inv.InvoiceId,
                    (x, inv) => new { x.sl, x.s, x.sp, x.p, inv })
                .SelectMany(
                    x => x.inv.DefaultIfEmpty(),
                    (x, inv) => new { x.sl, x.s, x.sp, x.p, inv })
                .GroupJoin(db.SupplierMasters,
                    x => x.inv.SupplierId,
                    sm => sm.SupplierId,
                    (x, sm) => new { x.sl, x.s, x.sp, x.p, x.inv, sm })
                .SelectMany(
                    x => x.sm.DefaultIfEmpty(),
                    (x, sm) => new { x.sl, x.s, x.sp, x.p, x.inv, sm })
                .GroupJoin(db.PurchaseOrderTables, // Add PO join here
                    x => x.inv.Poid,
                    po => po.Poid,
                    (x, po) => new { x.sl, x.s, x.sp, x.p, x.inv, x.sm, po })
                .SelectMany(
                    x => x.po.DefaultIfEmpty(),
                    (x, po) => new { x.sl, x.s, x.sp, x.p, x.inv, x.sm, po });

            // Apply filters conditionally
            if (!string.IsNullOrEmpty(request.SerialNo))
            {
                query = query.Where(x => x.sl.SerialNo.Contains(request.SerialNo));
            }

            if (!string.IsNullOrEmpty(request.ShortCode))
            {
                query = query.Where(x => x.sl.ShortCode.Contains(request.ShortCode));
            }

            if (!string.IsNullOrEmpty(request.BatchNo))
            {
                query = query.Where(x => x.sp.Batch.Contains(request.BatchNo));
            }

            if (request.ProductId.HasValue && request.ProductId > 0)
            {
                query = query.Where(x => x.sl.ProductId == request.ProductId);
            }

            if (request.IsActive.HasValue)
            {
                query = query.Where(x => x.sl.IsActive == request.IsActive);
            }

            if (!string.IsNullOrEmpty(request.InspectionStatus))
            {
                query = query.Where(x => x.sl.InspectionStatus == request.InspectionStatus);
            }

            if (!string.IsNullOrEmpty(request.LabelStatus))
            {
                query = query.Where(x => x.sl.LabelStatus == request.LabelStatus);
            }

            if (request.CurrentRackId.HasValue && request.CurrentRackId > 0)
            {
                query = query.Where(x => x.sl.CurrentRackId == request.CurrentRackId);
            }

            if (request.CurrentStoreId.HasValue && request.CurrentStoreId > 0)
            {
                query = query.Where(x => x.sl.CurrentStoreId == request.CurrentStoreId);
            }

            // Apply PO, Invoice and Supplier filters
            if (!string.IsNullOrEmpty(request.PONumber))
            {
                query = query.Where(x => x.inv != null && x.inv.Poid != null &&
                    db.PurchaseOrderTables.Any(po => po.Poid == x.inv.Poid &&
                        po.Ponumber.Contains(request.PONumber)));
            }

            if (!string.IsNullOrEmpty(request.InvoiceNo))
            {
                query = query.Where(x => x.inv != null && x.inv.InvoiceNumber.Contains(request.InvoiceNo));
            }

            if (request.SupplierId.HasValue && request.SupplierId > 0)
            {
                query = query.Where(x => x.sm != null && x.sm.SupplierId == request.SupplierId);
            }

            // Apply search text if provided
            if (!string.IsNullOrEmpty(request.SearchText))
            {
                var searchText = request.SearchText.ToLower();
                query = query.Where(x =>
                    x.sl.SerialNo.Contains(searchText) ||
                    x.sp.Batch.Contains(searchText) ||
                    x.p.ProductName.ToLower().Contains(searchText));
            }

            // Get total count for pagination
            request.TotalCount = query.Count();

            // Apply pagination
            var pageSize = request.PageSize == 0 ? 10 : request.PageSize;
            var skip = (request.PageNo - 1) * pageSize;

            // Final select with pagination
            if (!string.IsNullOrEmpty(request.SerialNo))
            {
                var stockLabels = query
                    .OrderByDescending(x => x.sl.AddedDate)
                    .Skip(skip)
                    .Take(pageSize)
                    .Select(x => new LabelFullDetailsVm
                    {
                        BatchNo = x.sp.Batch,
                        ProductName = x.p.ProductName,
                        StockUnit = x.s.Unit,
                        ProductQuality = x.sp.ProductQuality,
                        SupplierName = x.sm != null ? x.sm.SupplierName : null,
                        InvoiceNo = x.inv != null ? x.inv.InvoiceNumber : null,
                        PurchaseOrderNo = x.po != null ? x.po.Ponumber : null,
                        StockLabels = new List<StockLabelTableVm>
                        {
                            new StockLabelTableVm
                            {
                                StockLabelId = x.sl.StockLabelId,
                                SerialNo = x.sl.SerialNo,
                                ProductId = x.sl.ProductId,
                                Quantity = x.sl.Quantity,
                                PackagingUnit = x.sl.PackagingUnit,
                                MfgDate = x.sl.MfgDate,
                                ExpiryDate = x.sl.ExpiryDate,
                                InspectionStatus = x.sl.InspectionStatus,
                                AddedBy = x.sl.AddedBy,
                                AddedDate = x.sl.AddedDate,
                                IsActive = x.sl.IsActive ?? false,
                                UpdatedBy = x.sl.UpdatedBy,
                                UpdatedDate = x.sl.UpdatedDate,
                                StoreName = x.sl.CurrentStore.StoreName,
                                RackName = x.sl.CurrentRack.RackName,
                                LabelStatus = x.sl.LabelStatus
                            }
                        }
                    })
                    .ToList();

                return new StockLabelDetailsListingVm
                {
                    StockDetails = stockLabels,
                    TotalRecords = query.Count()
                };
            }
            else
            {
                // Group by product details for all other cases
                var stockLabels = query
                    .OrderByDescending(x => x.sl.AddedDate)
                    .Skip(skip)
                    .Take(pageSize)
                    .GroupBy(x => new
                    {
                        x.sp.Batch,
                        x.p.ProductName,
                        x.s.Unit,
                        x.sp.ProductQuality,
                        SupplierName = x.sm != null ? x.sm.SupplierName : null,
                        InvoiceNo = x.inv != null ? x.inv.InvoiceNumber : null,
                        PurchaseOrderNo = x.po != null ? x.po.Ponumber : null
                    })
                    .Select(g => new LabelFullDetailsVm
                    {
                        BatchNo = g.Key.Batch,
                        ProductName = g.Key.ProductName,
                        StockUnit = g.Key.Unit,
                        ProductQuality = g.Key.ProductQuality,
                        SupplierName = g.Key.SupplierName,
                        InvoiceNo = g.Key.InvoiceNo,
                        PurchaseOrderNo = g.Key.PurchaseOrderNo,
                        StockLabels = g.Select(x => new StockLabelTableVm
                        {
                            StockLabelId = x.sl.StockLabelId,
                            SerialNo = x.sl.SerialNo,
                            ProductId = x.sl.ProductId,
                            Quantity = x.sl.Quantity,
                            PackagingUnit = x.sl.PackagingUnit,
                            MfgDate = x.sl.MfgDate,
                            ExpiryDate = x.sl.ExpiryDate,
                            InspectionStatus = x.sl.InspectionStatus ?? "Pending",
                            AddedBy = x.sl.AddedBy,
                            AddedDate = x.sl.AddedDate,
                            IsActive = x.sl.IsActive ?? false,
                            UpdatedBy = x.sl.UpdatedBy,
                            UpdatedDate = x.sl.UpdatedDate,
                            StoreName = x.sl.CurrentStore.StoreName ?? "Not Allocated",
                            RackName = x.sl.CurrentRack.RackName ?? "Not Allocated",
                            LabelStatus = x.sl.LabelStatus ?? "Pending"
                        }).OrderBy(x => x.SerialNo).ThenBy(x => x.IsActive).ThenBy(x => x.StoreName).ThenBy(x => x.RackName).ToList(),
                        TotalLabels = g.Count(),
                        TotalQtyByProductBatch = g.Sum(x => x.sl.Quantity)
                    })
                    .ToList();

                foreach (var item in stockLabels)
                {
                    item.TotalCount = query.Count();
                }

                return new StockLabelDetailsListingVm
                {
                    StockDetails = stockLabels,
                    TotalRecords = query.Count()
                };
            }
        }
        public ApiFunctionResponseVm UpdateStockLabelStatus(UpdateStockLabelStatusVm request)
        {
            using var db = new Models.pmsdbContext();
            var remark = "Manually updated by " + GlobalData.loggedInUser;
            var relatedEntityId = request.StockLabelId;
            var relatedEntityType = "StockLabelTables";
            UpdateStockLabelStatus(db, request.StockLabelId, request.Status, remark, relatedEntityId, relatedEntityType);
            return new ApiFunctionResponseVm { StatusCode = HttpStatusCode.OK, ResponseBody = "Stock label status manual update recorded by your username." };
        }
        public ProductAvailableStock GetProductAvailableStockByProductId(long productId)
        {
            using var db = new Models.pmsdbContext();
            var query = (from p in db.ProductMasters
                         join a in db.StockProductTables on p.ProductId equals a.ProductId
                         join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                         join r in db.RackMasters on b.RackId equals r.RackId
                         join s in db.StoreMasters on r.StoreId equals s.StoreId
                         where productId == p.ProductId && p.Disabled != true
                               && b.InspectionType.ToLower() == "accepted" && b.Quantity > 0 && s.IsWorkInProgressStore == false
                         select new
                         {
                             p.ProductId,
                             b.Quantity,
                             p.MinimumQuantity,
                         }).ToList();

            var totalQuantity = query.Sum(x => x.Quantity);

            return new ProductAvailableStock
            {
                ProductId = productId,
                MinimumQuantity = query.FirstOrDefault().MinimumQuantity,
                Quantity = totalQuantity
            };
        }
        public List<ProductAvailableStock> GetProductAvailableStockByProductIds(ProductAvailableStockRequestVm request)
        {
            using var db = new Models.pmsdbContext();
            var query = (from p in db.ProductMasters
                         join a in db.StockProductTables on p.ProductId equals a.ProductId
                         join sm in db.StockMasters on a.StockId equals sm.StockId
                         join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                         join r in db.RackMasters on b.RackId equals r.RackId
                         join s in db.StoreMasters on r.StoreId equals s.StoreId
                         where request.ProductIds.Contains(p.ProductId) && p.Disabled != true
                               && b.InspectionType.ToLower() == "accepted" && b.Quantity > 0
                               && s.IsWorkInProgressStore == false
                               && (
                                   (request.ProductQuality == "DOMESTIC" && (sm.ProductQuality == "DOMESTIC" || sm.ProductQuality == null)) ||
                                   (request.ProductQuality == "IMPORTED" && sm.ProductQuality == "IMPORTED")
                               )
                         select new
                         {
                             p.ProductId,
                             b.Quantity,
                             p.MinimumQuantity,
                         }).ToList();

            var result = new List<ProductAvailableStock>();

            foreach (var productId in request.ProductIds)
            {
                var totalQuantity = query.Where(x => x.ProductId == productId).Sum(x => x.Quantity);
                var minQuantity = query.FirstOrDefault(x => x.ProductId == productId)?.MinimumQuantity ?? 0;

                result.Add(new ProductAvailableStock
                {
                    ProductId = productId,
                    MinimumQuantity = minQuantity,
                    Quantity = totalQuantity
                });
            }

            return result;
        }

        public List<StoreMasterVm> GetStoresForProduct(long productId)
        {
            List<StoreMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                // Get all stores that have stock for the specified product
                var query = (from a in db.StockProductTables
                             join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                             join r in db.RackMasters on b.RackId equals r.RackId
                             join s in db.StoreMasters on r.StoreId equals s.StoreId
                             join d in db.DeptMasters on s.DeptId equals d.DeptId
                             join i in db.BranchMasters on s.BranchId equals i.BranchId into spc
                             from i in spc.DefaultIfEmpty()
                             where a.ProductId == productId && b.InspectionType.ToLower() == "accepted" && b.Quantity > 0
                             select new StoreMasterVm
                             {
                                 StoreId = s.StoreId,
                                 DeptId = s.DeptId,
                                 DeptCode = d.DeptCode,
                                 BranchId = s.BranchId,
                                 BranchCode = i.BranchCode,
                                 BranchName = i.BranchName,
                                 StoreName = s.StoreName,
                                 StoreCode = s.StoreCode,
                                 StoreDesc = s.StoreDesc,
                                 StoreAddedBy = s.StoreAddedBy,
                                 StoreAddedDate = s.StoreAddedDate,
                                 IsWorkInProgressStore = s.IsWorkInProgressStore
                             }).Distinct().OrderBy(x => x.StoreCode).ToList();

                res = query;
            }
            return res;
        }

        public List<RackMasterVm> GetRacksForProductInStore(long productId, long storeId)
        {
            List<RackMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                // Get all racks in the specified store that have stock for the specified product
                var query = (from a in db.StockProductTables
                             join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                             join r in db.RackMasters on b.RackId equals r.RackId
                             join s in db.StoreMasters on r.StoreId equals s.StoreId
                             where a.ProductId == productId && r.StoreId == storeId && b.InspectionType.ToLower() == "accepted" && b.Quantity > 0
                             select new RackMasterVm
                             {
                                 RackId = r.RackId,
                                 StoreId = r.StoreId,
                                 StoreName = s.StoreName,
                                 RackName = r.RackName,
                                 RackCode = r.RackCode,
                                 RackDesc = r.RackDesc,
                                 RackAddedBy = r.RackAddedBy,
                                 RackAddedDate = r.RackAddedDate
                             }).Distinct().OrderBy(x => x.RackCode).ToList();

                res = query;
            }
            return res;
        }
        public ApiFunctionResponseVm RequestProductStockTransfer(ProductStockTransferRequestVm request)
        {
            using var db = new Models.pmsdbContext();
            // 1. Validate products
            var sourceProduct = db.ProductMasters.FirstOrDefault(p => p.ProductId == request.FromProductId);
            var destProduct = db.ProductMasters.FirstOrDefault(p => p.ProductId == request.ToProductId);

            if (sourceProduct == null || destProduct == null)
                return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Source or destination product not found");

            ProductTransferTable transfer = new()
            {
                FromProductId = request.FromProductId,
                ToProductId = request.ToProductId,
                Status = "Approval Pending",
                RequestReason = request.RequestReason,
                AddedById = db.UserMasters.FirstOrDefault(u => u.Email == GlobalData.loggedInUser).UserId,
                AddedDate = DateTime.Now
            };
            db.ProductTransferTables.Add(transfer);
            db.SaveChanges();
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product stock transfer request submitted successfully");
        }
        public ApiFunctionResponseVm ActionProductStockTransfer(ProductStockTransferRequestVm request)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                if (request.Status == "Approved")
                {
                    var transfer = db.ProductTransferTables.FirstOrDefault(t => t.TransferId == request.TransferId);
                    if (transfer == null)
                        return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Transfer not found");

                    // Validate products
                    var sourceProduct = db.ProductMasters.FirstOrDefault(p => p.ProductId == transfer.FromProductId);
                    var destProduct = db.ProductMasters.FirstOrDefault(p => p.ProductId == transfer.ToProductId);

                    if (sourceProduct == null || destProduct == null)
                        return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Source or destination product not found");

                    // Get all stock products for source product
                    var stockProductsToTransfer = TrackTransferredStockByProductId(transfer.FromProductId.Value);
                    var totalQuantity = GetStockQuantityByProductId(transfer.FromProductId.Value, stockProductsToTransfer);
                    if (!stockProductsToTransfer.Any() || totalQuantity == 0)
                        return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "No stock found for source product");

                    // Update ProductId for all stock products
                    foreach (var stockProduct in stockProductsToTransfer)
                    {

                        var stockProductToUpdate = db.StockProductTables.FirstOrDefault(x => x.StockProductId == stockProduct.StockProductId);
                        stockProductToUpdate.ProductId = transfer.ToProductId.Value;
                    }

                    // Update ProductId for all stock labels
                    var stockLabelsToUpdate = db.StockLabelTables.Where(x => x.ProductId == transfer.FromProductId.Value).ToList();
                    foreach (var stockLabel in stockLabelsToUpdate)
                    {
                        stockLabel.ProductId = transfer.ToProductId.Value;
                    }

                    // Disable source product
                    sourceProduct.Disabled = true;
                    sourceProduct.DisabledBy = GlobalData.loggedInUser;
                    sourceProduct.DisabledDate = DateTime.Now;
                    db.SaveChanges();

                    // Update transfer history with action
                    transfer.Status = "Approved";
                    transfer.Quantity = totalQuantity;
                    transfer.ActionRemark = request.ActionRemark;
                    transfer.ActionDate = DateTime.Now;
                    transfer.ActionById = db.UserMasters.FirstOrDefault(u => u.Email == GlobalData.loggedInUser).UserId;
                    db.SaveChanges();

                    transaction.Commit();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK,
                            $"All stock transferred successfully from Product {sourceProduct.ProductName} to Product {destProduct.ProductName}");
                }
                else
                {
                    var transfer = db.ProductTransferTables.FirstOrDefault(t => t.TransferId == request.TransferId);
                    transfer.Status = "Rejected";
                    transfer.ActionRemark = request.ActionRemark;
                    transfer.ActionDate = DateTime.Now;
                    transfer.ActionById = db.UserMasters.FirstOrDefault(u => u.Email == GlobalData.loggedInUser).UserId;
                    db.SaveChanges();
                    transaction.Commit();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product stock transfer request rejected");
                }
            }
            catch (Exception ex)
            {
                if (request.Status == "Approved")
                {
                    transaction.Rollback();
                }
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError,
                    "An error occurred while transferring stock: " + ex.Message);
            }
        }
        public List<ProductTransferTableVm> GetProductTransferHistory()
        {
            using var db = new Models.pmsdbContext();
            return db.ProductTransferTables
            .Include(t => t.AddedBy)
            .Include(t => t.ActionBy)
            .Include(t => t.FromProduct)
            .Include(t => t.ToProduct)
            .Select(t => new ProductTransferTableVm
            {
                TransferId = t.TransferId,
                FromProductId = t.FromProductId,
                FromProductName = t.FromProduct != null ? t.FromProduct.ProductName : null,
                ToProductId = t.ToProductId,
                ToProductName = t.ToProduct != null ? t.ToProduct.ProductName : null,
                Quantity = t.Quantity,
                Status = t.Status,
                RequestReason = t.RequestReason,
                AddedByDetails = t.AddedBy != null ? new UserMasterVm
                {
                    UserId = t.AddedBy.UserId,
                    Name = t.AddedBy.Name,
                    Email = t.AddedBy.Email
                } : null,
                AddedDate = t.AddedDate,
                ActionByDetails = t.ActionBy != null ? new UserMasterVm
                {
                    UserId = t.ActionBy.UserId,
                    Name = t.ActionBy.Name,
                    Email = t.ActionBy.Email
                } : null,
                ActionDate = t.ActionDate,
                ActionRemark = t.ActionRemark
            }).ToList();
        }

        public List<LowStockReportVm> GetLowStockReportData(LowStockReportRequestVm request)
        {
            List<LowStockReportVm> result = new List<LowStockReportVm>();

            using (var db = new Models.pmsdbContext())
            {
                var query = (from p in db.ProductMasters.AsNoTracking()
                             join a in db.StockProductTables on p.ProductId equals a.ProductId
                             join sm in db.StockMasters on a.StockId equals sm.StockId
                             join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                             join r in db.RackMasters on b.RackId equals r.RackId
                             join s in db.StoreMasters on r.StoreId equals s.StoreId
                             join pc in db.ProductCategoryMasters on p.ProductCategoryId equals pc.ProductCategoryId into pcGroup
                             from pc in pcGroup.DefaultIfEmpty()
                             join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into pfGroup
                             from pf in pfGroup.DefaultIfEmpty()
                             join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pscGroup
                             from psc in pscGroup.DefaultIfEmpty()
                             where p.Disabled != true
                                   && p.MinimumQuantity.HasValue
                                   && p.MinimumQuantity > 0
                                   && b.InspectionType == "Accepted"
                                   //    && b.Quantity > 0
                                   && (request.IncludeWIPStore || s.IsWorkInProgressStore != true)
                             select new
                             {
                                 p.ProductId,
                                 p.ProductName,
                                 p.ProductType,
                                 ProductCategory = pc.ProductCategory ?? string.Empty,
                                 ProductFirstSubCategory = pf.ProductFirstSubCategory ?? string.Empty,
                                 ProductSecSubCategory = psc.ProductSecSubCategory ?? string.Empty,
                                 p.MinimumQuantity,
                                 a.Unit,
                                 Quantity = b.Quantity,
                                 ProductQuality = sm.ProductQuality ?? "DOMESTIC"
                             }).ToList();

                // Group by product and calculate totals
                var groupedData = query.GroupBy(x => new
                {
                    x.ProductId,
                    x.ProductName,
                    x.ProductType,
                    x.ProductCategory,
                    x.ProductFirstSubCategory,
                    x.ProductSecSubCategory,
                    x.MinimumQuantity,
                    x.Unit
                })
                .Select(g => new
                {
                    g.Key.ProductId,
                    g.Key.ProductName,
                    g.Key.ProductType,
                    g.Key.ProductCategory,
                    g.Key.ProductFirstSubCategory,
                    g.Key.ProductSecSubCategory,
                    MinimumLevel = g.Key.MinimumQuantity ?? 0,
                    g.Key.Unit,
                    TotalAvailableQty = g.Sum(x => x.Quantity),
                    DomesticQty = g.Where(x => x.ProductQuality == "DOMESTIC" || x.ProductQuality == null).Sum(x => x.Quantity),
                    ImportedQty = g.Where(x => x.ProductQuality == "IMPORTED").Sum(x => x.Quantity)
                })
                .Where(x => x.TotalAvailableQty < x.MinimumLevel) // Only products below minimum level
                .ToList();

                result = groupedData.Select(x => new LowStockReportVm
                {
                    ProductId = x.ProductId,
                    ProductName = x.ProductName,
                    ProductType = x.ProductType,
                    ProductCategory = x.ProductCategory,
                    ProductFirstSubCategory = x.ProductFirstSubCategory,
                    ProductSecSubCategory = x.ProductSecSubCategory,
                    MinimumLevel = x.MinimumLevel,
                    TotalAvailableQty = x.TotalAvailableQty,
                    DomesticQty = x.DomesticQty,
                    ImportedQty = x.ImportedQty,
                    Unit = x.Unit
                }).OrderBy(x => x.ProductName).ToList();
            }

            return result;
        }

        public List<SupplierProductNameVm> GetSupplierProductNames(long supplierId, long? productId = null)
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    var query = (from psm in db.ProductSupplierMappings
                                 join s in db.SupplierMasters on psm.SupplierId equals s.SupplierId
                                 where (psm.ProductId == productId || productId == null)
                                 && psm.SupplierId == supplierId
                                 orderby psm.UsageCount descending, psm.LastUsedDate descending
                                 select new SupplierProductNameVm
                                 {
                                     ProductSupplierMappingId = psm.ProductSupplierMappingId,
                                     SupplierId = psm.SupplierId,
                                     ProductId = psm.ProductId,
                                     SupplierName = s.SupplierName,
                                     SupplierProductName = psm.SupplierProductName,
                                     UsageCount = psm.UsageCount,
                                     LastUsedDate = psm.LastUsedDate
                                 }).ToList();
                    return query.ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error retrieving products with supplier names: " + ex.Message);
            }
        }

        public List<ProductSupplierMappingVm> GetSupplierMappings(long supplierId)
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    var mappings = (from psm in db.ProductSupplierMappings
                                    join p in db.ProductMasters on psm.ProductId equals p.ProductId
                                    join s in db.SupplierMasters on psm.SupplierId equals s.SupplierId
                                    join cu in db.UserMasters on psm.CreatedById equals cu.UserId into createdUsers
                                    from cu in createdUsers.DefaultIfEmpty()
                                    join lu in db.UserMasters on psm.LastUsedById equals lu.UserId into lastUsedUsers
                                    from lu in lastUsedUsers.DefaultIfEmpty()
                                    where psm.SupplierId == supplierId
                                    orderby psm.UsageCount descending, psm.LastUsedDate descending
                                    select new ProductSupplierMappingVm
                                    {
                                        ProductSupplierMappingId = psm.ProductSupplierMappingId,
                                        ProductId = psm.ProductId,
                                        ProductName = p.ProductName,
                                        SupplierId = psm.SupplierId,
                                        SupplierName = s.SupplierName,
                                        SupplierProductName = psm.SupplierProductName,
                                        UsageCount = psm.UsageCount,
                                        LastUsedDate = psm.LastUsedDate,
                                        LastUsedById = psm.LastUsedById,
                                        LastUsedBy = lu != null ? lu.Name : "",
                                        CreatedDate = psm.CreatedDate,
                                        CreatedById = psm.CreatedById,
                                        CreatedBy = cu != null ? cu.Name : ""
                                    }).ToList();

                    return mappings;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error retrieving supplier mappings: " + ex.Message);
            }
        }

        public ApiFunctionResponseVm CreateOrUpdateProductSupplierMapping(CreateProductSupplierMappingVm mapping)
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    // Check if mapping already exists
                    var existingMapping = db.ProductSupplierMappings
                        .FirstOrDefault(psm => psm.ProductId == mapping.ProductId
                                            && psm.SupplierId == mapping.SupplierId
                                            && psm.SupplierProductName.ToLower() == mapping.SupplierProductName.ToLower());

                    if (existingMapping != null)
                    {
                        // Update existing mapping
                        existingMapping.UsageCount++;
                        existingMapping.LastUsedDate = DateTime.Now;
                        existingMapping.LastUsedById = mapping.UserId;

                        db.SaveChanges();

                        return new ApiFunctionResponseVm
                        {
                            StatusCode = HttpStatusCode.OK,
                            ResponseBody = new { Message = "Product supplier mapping updated successfully", Data = existingMapping.ProductSupplierMappingId }
                        };
                    }
                    else
                    {
                        // Create new mapping
                        var newMapping = new Models.ProductSupplierMapping
                        {
                            ProductId = mapping.ProductId,
                            SupplierId = mapping.SupplierId,
                            SupplierProductName = mapping.SupplierProductName,
                            UsageCount = 1,
                            LastUsedDate = DateTime.Now,
                            LastUsedById = mapping.UserId,
                            CreatedDate = DateTime.Now,
                            CreatedById = mapping.UserId
                        };

                        db.ProductSupplierMappings.Add(newMapping);
                        db.SaveChanges();

                        return new ApiFunctionResponseVm
                        {
                            StatusCode = HttpStatusCode.OK,
                            ResponseBody = new { Message = "Product supplier mapping created successfully", Data = newMapping.ProductSupplierMappingId }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ApiFunctionResponseVm
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ResponseBody = "Error creating/updating product supplier mapping: " + ex.Message
                };
            }
        }

        public ApiFunctionResponseVm UpdateSupplierProductName(UpdateSupplierProductNameVm updateRequest)
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    using (var transaction = db.Database.BeginTransaction())
                    {
                        try
                        {
                            // Find the existing mapping
                            var existingMapping = db.ProductSupplierMappings
                                .FirstOrDefault(psm => psm.ProductSupplierMappingId == updateRequest.ProductSupplierMappingId);

                            if (existingMapping == null)
                            {
                                return new ApiFunctionResponseVm
                                {
                                    StatusCode = HttpStatusCode.NotFound,
                                    ResponseBody = "Product supplier mapping not found"
                                };
                            }

                            // Check if the new name already exists for the same product-supplier combination
                            var duplicateMapping = db.ProductSupplierMappings
                                .FirstOrDefault(psm => psm.ProductId == existingMapping.ProductId
                                                    && psm.SupplierId == existingMapping.SupplierId
                                                    && psm.SupplierProductName.ToLower() == updateRequest.SupplierProductName.ToLower()
                                                    && psm.ProductSupplierMappingId != updateRequest.ProductSupplierMappingId);

                            if (duplicateMapping != null)
                            {
                                return new ApiFunctionResponseVm
                                {
                                    StatusCode = HttpStatusCode.BadRequest,
                                    ResponseBody = "A mapping with this supplier product name already exists for this product-supplier combination"
                                };
                            }

                            // Update the mapping
                            existingMapping.SupplierProductName = updateRequest.SupplierProductName.Trim();
                            existingMapping.LastUsedDate = DateTime.Now;
                            existingMapping.LastUsedById = updateRequest.UserId;

                            db.SaveChanges();
                            transaction.Commit();

                            return new ApiFunctionResponseVm
                            {
                                StatusCode = HttpStatusCode.OK,
                                ResponseBody = "Supplier product name updated successfully"
                            };
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new ApiFunctionResponseVm
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ResponseBody = "Error updating supplier product name: " + ex.Message
                };
            }
        }

        public SupplierProductMappingReportResponseVm GetSupplierProductMappingReport(SupplierProductMappingReportRequestVm request)
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    // Base query with all joins
                    var baseQuery = from psm in db.ProductSupplierMappings
                                    join p in db.ProductMasters on psm.ProductId equals p.ProductId
                                    join pc in db.ProductCategoryMasters on p.ProductCategoryId equals pc.ProductCategoryId into pcGroup
                                    from pc in pcGroup.DefaultIfEmpty()
                                    join pfc in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pfc.ProductFirstSubCategoryId into pfcGroup
                                    from pfc in pfcGroup.DefaultIfEmpty()
                                    join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pscGroup
                                    from psc in pscGroup.DefaultIfEmpty()
                                    join s in db.SupplierMasters on psm.SupplierId equals s.SupplierId
                                    join cu in db.UserMasters on psm.CreatedById equals cu.UserId into createdUsers
                                    from cu in createdUsers.DefaultIfEmpty()
                                    join lu in db.UserMasters on psm.LastUsedById equals lu.UserId into lastUsedUsers
                                    from lu in lastUsedUsers.DefaultIfEmpty()
                                    select new
                                    {
                                        psm.ProductSupplierMappingId,
                                        psm.ProductId,
                                        p.ProductName,
                                        p.ProductCode,
                                        p.ProductType,
                                        ProductCategory = pc != null ? pc.ProductCategory : "",
                                        ProductFirstSubCategory = pfc != null ? pfc.ProductFirstSubCategory : "",
                                        ProductSecSubCategory = psc != null ? psc.ProductSecSubCategory : "",
                                        psm.SupplierId,
                                        s.SupplierName,
                                        psm.SupplierProductName,
                                        psm.UsageCount,
                                        psm.LastUsedDate,
                                        LastUsedBy = lu != null ? lu.Name : "",
                                        psm.CreatedDate,
                                        CreatedBy = cu != null ? cu.Name : ""
                                    };

                    // Apply filters
                    if (request.ProductId.HasValue && request.ProductId.Value > 0)
                    {
                        baseQuery = baseQuery.Where(x => x.ProductId == request.ProductId.Value);
                    }

                    if (request.SupplierId.HasValue && request.SupplierId.Value > 0)
                    {
                        baseQuery = baseQuery.Where(x => x.SupplierId == request.SupplierId.Value);
                    }

                    if (request.FrequentlyUsed.HasValue)
                    {
                        if (request.FrequentlyUsed.Value)
                        {
                            baseQuery = baseQuery.Where(x => x.UsageCount >= 3);
                        }
                        else
                        {
                            baseQuery = baseQuery.Where(x => x.UsageCount < 3);
                        }
                    }

                    if (request.CreatedDateFrom.HasValue)
                    {
                        baseQuery = baseQuery.Where(x => x.CreatedDate >= request.CreatedDateFrom.Value);
                    }

                    if (request.CreatedDateTo.HasValue)
                    {
                        baseQuery = baseQuery.Where(x => x.CreatedDate <= request.CreatedDateTo.Value);
                    }

                    // Get total count for pagination
                    var totalCount = baseQuery.Count();

                    // Apply pagination and ordering
                    var items = baseQuery
                        .OrderByDescending(x => x.UsageCount)
                        .ThenByDescending(x => x.LastUsedDate)
                        .Skip((request.PageNumber - 1) * request.PageSize)
                        .Take(request.PageSize)
                        .Select(x => new SupplierProductMappingReportItemVm
                        {
                            ProductSupplierMappingId = x.ProductSupplierMappingId,
                            ProductId = x.ProductId,
                            ProductName = x.ProductName,
                            ProductCode = x.ProductCode,
                            ProductType = x.ProductType,
                            ProductCategory = x.ProductCategory,
                            ProductFirstSubCategory = x.ProductFirstSubCategory,
                            ProductSecSubCategory = x.ProductSecSubCategory,
                            SupplierId = x.SupplierId,
                            SupplierName = x.SupplierName,
                            SupplierProductName = x.SupplierProductName,
                            UsageCount = x.UsageCount,
                            LastUsedDate = x.LastUsedDate,
                            LastUsedBy = x.LastUsedBy,
                            CreatedDate = x.CreatedDate,
                            CreatedBy = x.CreatedBy
                        })
                        .ToList();

                    return new SupplierProductMappingReportResponseVm
                    {
                        Items = items,
                        TotalCount = totalCount,
                        PageNumber = request.PageNumber,
                        PageSize = request.PageSize
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error retrieving supplier product mapping report: " + ex.Message);
            }
        }

        #region Return Stock Methods

        /// <summary>
        /// Get dispatched items for a sale order to facilitate return stock creation
        /// </summary>
        public DispatchedItemsForReturnVm GetDispatchedItemsForReturn(long saleOrderId)
        {
            using var db = new pmsdbContext();

            var saleOrder = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleOrderId);
            if (saleOrder == null)
                throw new ArgumentException("Sale Order not found");

            var customer = db.CustomerMasters.FirstOrDefault(x => x.CustomerId == saleOrder.CustomerId);

            // Get dispatched items from JumboInspection records that have been dispatched
            var dispatchedItems = (from ji in db.JumboInspectionTables
                                   join wpjm in db.WorkPlanJumboMasters on ji.WorkPlanJumboMasterId equals wpjm.WorkPlanJumboMasterId
                                   join sot in db.SaleOrderTables on wpjm.SaleOrderId equals sot.SaleOrderId
                                   join sop in db.SaleOrderProductionTables on sot.SaleOrderId equals sop.SaleOrderId
                                   join pm in db.ProductMasters on sop.ProductId equals pm.ProductId
                                   where sot.SaleOrderId == saleOrderId && ji.DispatchStatus == "Dispatched"
                                   select new DispatchedItemDetailVm
                                   {
                                       ProductId = pm.ProductId,
                                       ProductName = pm.ProductName,
                                       DispatchedQuantity = ji.Quantity ?? 0,
                                       Unit = ji.Unit,
                                       ManufacturedDate = ji.AddedDate,
                                       Grade = ji.Grade,
                                       ThicknessId = sop.Thick.HasValue ? (long?)Convert.ToInt64(sop.Thick.Value) : null,
                                       GrainId = sop.GrainId,
                                       WidthId = sop.Width.HasValue ? (long?)Convert.ToInt64(sop.Width.Value) : null,
                                       ColorId = sop.ColorId,
                                       PostProcess = GetPostProcessSummary(sop.SaleOrderProductionId, db)
                                   }).ToList();

            // Calculate previously returned quantities
            foreach (var item in dispatchedItems)
            {
                var previousReturns = (from sm in db.StockMasters
                                       join spt in db.StockProductTables on sm.StockId equals spt.StockId
                                       where sm.IsReturnStock == true && sm.OriginalSaleOrderId == saleOrderId
                                             && spt.ProductId == item.ProductId
                                       select spt.Quantity ?? 0).Sum();

                item.PreviouslyReturnedQuantity = previousReturns;
                item.AvailableForReturn = item.DispatchedQuantity - previousReturns;
            }

            return new DispatchedItemsForReturnVm
            {
                SaleOrderId = saleOrderId,
                SaleOrderNumber = saleOrder.SaleOrderNumber,
                CustomerId = saleOrder.CustomerId ?? 0,
                CustomerName = customer?.CustomerName ?? "Unknown",
                DispatchDate = saleOrder.ModifiedDate, // Approximate dispatch date
                DispatchedItems = dispatchedItems
            };
        }

        /// <summary>
        /// Create return stock from customer returns
        /// </summary>
        public ApiFunctionResponseVm AddReturnStock(ReturnStockVm returnStock)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();

            try
            {
                // Get customer as supplier for returns (create if doesn't exist)
                var customerAsSupplier = GetOrCreateCustomerAsSupplierHelper(returnStock.CustomerId, db);

                // Create return invoice
                var returnInvoice = new InvoiceMaster
                {
                    InvoiceNumber = "RETURN_" + DateTime.Now.ToString("yyyyMMdd") + "_" + returnStock.OriginalSaleOrderId,
                    InvoiceType = "Return",
                    InvoiceDate = returnStock.ReturnDate ?? DateTime.Now,
                    SupplierId = customerAsSupplier.SupplierId,
                    OriginalInvoiceId = returnStock.OriginalDispatchInvoiceId,
                    Active = true
                };

                db.InvoiceMasters.Add(returnInvoice);
                db.SaveChanges();

                // Create return stock master
                var stockMaster = new StockMaster
                {
                    InvoiceId = returnInvoice.InvoiceId,
                    StockDate = returnStock.ReturnDate ?? DateTime.Now,
                    IsReturnStock = true,
                    OriginalSaleOrderId = returnStock.OriginalSaleOrderId,
                    OriginalDispatchId = returnStock.OriginalDispatchId,
                    ReturnReason = returnStock.ReturnReason,
                    ReturnDate = returnStock.ReturnDate,
                    ReturnedBy = returnStock.ReturnedBy,
                    CustomerId = returnStock.CustomerId,
                    Batch = "RETURN_" + returnStock.OriginalSaleOrderId + "_" + DateTime.Now.ToString("yyyyMMdd"),
                    InspectionCompleted = false, // Requires re-inspection
                    AllocationCompleted = false,
                    AddedBy = GlobalData.loggedInUser,
                    AddedDate = DateTime.Now
                };

                db.StockMasters.Add(stockMaster);
                db.SaveChanges();

                // Create stock product records for returned items
                foreach (var item in returnStock.ReturnedItems)
                {
                    var stockProduct = new StockProductTable
                    {
                        StockId = stockMaster.StockId,
                        ProductId = item.ProductId,
                        Quantity = item.ReturnedQuantity,
                        Unit = item.Unit,
                        Grade = "Return_Pending_Inspection", // Special grade for returns
                        ManufacturedDate = item.OriginalManufacturedDate,
                        Comments = $"Return Reason: {returnStock.ReturnReason}. Condition: {item.ReturnCondition}. Notes: {item.ReturnNotes}",
                        // Copy original specifications
                        ThicknessId = item.ThicknessId,
                        GrainId = item.GrainId,
                        WidthId = item.WidthId,
                        ColorId = item.ColorId,
                        PostProcess = item.PostProcess
                    };

                    db.StockProductTables.Add(stockProduct);
                }

                db.SaveChanges();
                transaction.Commit();

                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Return stock created successfully. Inspection required.");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        /// <summary>
        /// Get list of return stock records with filtering
        /// </summary>
        public List<ReturnStockListVm> GetReturnStockList(ReturnStockFilterVm filter = null)
        {
            using var db = new pmsdbContext();

            var query = from sm in db.StockMasters
                        join im in db.InvoiceMasters on sm.InvoiceId equals im.InvoiceId
                        join cm in db.CustomerMasters on sm.CustomerId equals cm.CustomerId
                        join sot in db.SaleOrderTables on sm.OriginalSaleOrderId equals sot.SaleOrderId
                        where sm.IsReturnStock == true
                        select new ReturnStockListVm
                        {
                            StockId = sm.StockId,
                            StockDate = sm.StockDate,
                            InvoiceNumber = im.InvoiceNumber,
                            OriginalSaleOrderId = sm.OriginalSaleOrderId ?? 0,
                            OriginalSaleOrderNumber = sot.SaleOrderNumber,
                            CustomerId = sm.CustomerId ?? 0,
                            CustomerName = cm.CustomerName,
                            ReturnReason = sm.ReturnReason,
                            ReturnDate = sm.ReturnDate,
                            ReturnedBy = sm.ReturnedBy,
                            InspectionCompleted = sm.InspectionCompleted ?? false,
                            AllocationCompleted = sm.AllocationCompleted ?? false,
                            Batch = sm.Batch,
                            AddedBy = sm.AddedBy,
                            AddedDate = sm.AddedDate
                        };

            // Apply filters
            if (filter != null)
            {
                if (filter.FromReturnDate.HasValue)
                    query = query.Where(x => x.ReturnDate >= filter.FromReturnDate);

                if (filter.ToReturnDate.HasValue)
                    query = query.Where(x => x.ReturnDate <= filter.ToReturnDate);

                if (filter.CustomerId.HasValue && filter.CustomerId > 0)
                    query = query.Where(x => x.CustomerId == filter.CustomerId);

                if (filter.OriginalSaleOrderId.HasValue && filter.OriginalSaleOrderId > 0)
                    query = query.Where(x => x.OriginalSaleOrderId == filter.OriginalSaleOrderId);

                if (!string.IsNullOrEmpty(filter.ReturnReason))
                    query = query.Where(x => x.ReturnReason.Contains(filter.ReturnReason));

                if (filter.InspectionCompleted.HasValue)
                    query = query.Where(x => x.InspectionCompleted == filter.InspectionCompleted);

                if (filter.AllocationCompleted.HasValue)
                    query = query.Where(x => x.AllocationCompleted == filter.AllocationCompleted);

                if (!string.IsNullOrEmpty(filter.AddedBy))
                    query = query.Where(x => x.AddedBy == filter.AddedBy);
            }

            var result = query.OrderByDescending(x => x.StockDate).ToList();

            // Calculate totals for each return stock
            foreach (var item in result)
            {
                var stockProducts = db.StockProductTables.Where(x => x.StockId == item.StockId).ToList();
                item.TotalItems = stockProducts.Count;
                item.TotalQuantity = stockProducts.Sum(x => x.Quantity ?? 0);
            }

            return result;
        }

        /// <summary>
        /// Helper method to get or create customer as supplier for return transactions
        /// </summary>
        private SupplierMaster GetOrCreateCustomerAsSupplierHelper(long customerId, pmsdbContext db)
        {
            var customer = db.CustomerMasters.FirstOrDefault(x => x.CustomerId == customerId);
            if (customer == null)
                throw new ArgumentException("Customer not found");

            // Check if customer already exists as supplier
            var existingSupplier = db.SupplierMasters.FirstOrDefault(x =>
                x.SupplierName == $"CUSTOMER_RETURN_{customer.CustomerName}");

            if (existingSupplier != null)
                return existingSupplier;

            // Create new supplier record for customer returns
            var newSupplier = new SupplierMaster
            {
                SupplierName = $"CUSTOMER_RETURN_{customer.CustomerName}",
                Address = customer.Address ?? "Customer Address",
                SupplierContactNumber = customer.CustomerContactNumber,
                Email = customer.Email,
                Gst = customer.Gstnumber,
                Disabled = false
            };

            db.SupplierMasters.Add(newSupplier);
            db.SaveChanges();
            return newSupplier;
        }

        /// <summary>
        /// Helper method to get post process summary for return stock
        /// </summary>
        private string GetPostProcessSummary(long saleOrderProductionId, pmsdbContext db)
        {
            try
            {
                var processes = new List<string>();

                // Get print processes
                var prints = db.SaleOrderProductionPrintMasters
                    .Where(x => x.SaleOrderProductionId == saleOrderProductionId && x.Removed != true)
                    .Join(db.PrintMasters, p => p.PrintMasterId, pm => pm.PrintMasterId, (p, pm) => pm.Code)
                    .Where(code => !string.IsNullOrEmpty(code))
                    .ToList();
                processes.AddRange(prints);

                // Get embossing processes
                var embossings = db.SaleOrderProductionEmbossingMasters
                    .Where(x => x.SaleOrderProductionId == saleOrderProductionId && x.Removed != true)
                    .Join(db.EmbossingMasters, e => e.EmbossingMasterId, em => em.EmbossingMasterId, (e, em) => em.Code)
                    .Where(code => !string.IsNullOrEmpty(code))
                    .ToList();
                processes.AddRange(embossings);

                return processes.Any() ? string.Join("/", processes) : "DIRECT";
            }
            catch
            {
                return "DIRECT";
            }
        }

        #endregion
    }
}