﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class BankDataFn
    {
        public List<BankMasterVm> GetAllBanks()
        {
            List<BankMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from br in db.BankMasters
                       select new BankMasterVm
                       {
                           BankId = br.BankId,
                           BankName = br.BankName,
                           AccountHolderName = br.AccountHolderName,
                           AccountType = br.AccountType,
                           AccountNumber = br.AccountNumber,
                           Ifsc = br.Ifsc,
                           Branch = br.Branch,
                           SwiftCode = br.SwiftCode,
                           Micr = br.Micr,
                           AddedBy = br.AddedBy,
                           AddedDate = br.AddedDate,
                       }).OrderByDescending(x => x.BankId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateBank(BankMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.BankId == 0)
                {
                    var rec = db.BankMasters.Where(x => x.BankId == br.BankId).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                var res = new BankMaster();
                if (br.BankId == 0)
                {
                    res = new BankMaster()
                    {
                        BankName = br.BankName,
                        AccountHolderName = br.AccountHolderName,
                        AccountType = br.AccountType,
                        AccountNumber = br.AccountNumber,
                        Ifsc = br.Ifsc,
                        Branch = br.Branch,
                        SwiftCode = br.SwiftCode,
                        Micr = br.Micr,
                        AddedBy = br.AddedBy,
                        AddedDate = DateTime.Now
                    };
                    db.BankMasters.Add(res);
                }
                else
                {
                    res = db.BankMasters.Where(x => x.BankId == br.BankId).FirstOrDefault();
                    if (res != null)
                    {
                        res.BankName = br.BankName;
                        res.AccountHolderName = br.AccountHolderName;
                        res.AccountType = br.AccountType;
                        res.AccountNumber = br.AccountNumber;
                        res.Ifsc = br.Ifsc;
                        res.Branch = br.Branch;
                        res.SwiftCode = br.SwiftCode;
                        res.Micr = br.Micr;
                        res.AddedBy = br.AddedBy;
                        res.AddedDate = DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
    }
}
