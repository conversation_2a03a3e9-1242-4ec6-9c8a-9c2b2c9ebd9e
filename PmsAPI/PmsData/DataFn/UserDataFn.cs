﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;

namespace PmsData.DataFn
{
    public class UserDataFn
    {
        //public UserRoleVm GetUserRole(string username)
        //{
        //    UserRoleVm res = null;
        //    using (var db = new Models.pmsdbContext())
        //    {
        //        res = (from a in db.UserRoles
        //               where a.Username == username
        //               select new UserRoleVm
        //               {
        //                   RoleId = a.RoleId,
        //                   Username = a.Username,
        //                   Admin = a.Admin,
        //                   MasterModule = a.MasterModule,
        //                   GateModule = a.GateModule,
        //                   GatePassPermission = a.GatePassPermission,
        //                   InventoryModule = a.InventoryModule,
        //                   OrderModule = a.OrderModule,
        //                   ProductionModule = a.ProductionModule,
        //                   PurchaseOrderModule = a.PurchaseOrderModule,
        //                   IssueModule=a.IssueModule
        //               }).FirstOrDefault();
        //    }
        //    return res;
        //}

        public List<long> GetUserStores(string username)
        {
            using (var db = new Models.pmsdbContext())
            {
                return db.UserStoreMappingTables.Where(x => x.Username == username).Select(y => y.StoreId).ToList();
            }
        }

        //public List<UserRoleVm> GetAllUserRole()
        //{
        //    List<UserRoleVm> res = null;
        //    using (var db = new Models.pmsdbContext())
        //    {
        //        res = (from a in db.UserRoles
        //               select new UserRoleVm
        //               {
        //                   RoleId = a.RoleId,
        //                   Username = a.Username,
        //                   Admin = a.Admin,
        //                   MasterModule = a.MasterModule,
        //                   GateModule = a.GateModule,
        //                   GatePassPermission = a.GatePassPermission,
        //                   InventoryModule = a.InventoryModule,
        //                   OrderModule = a.OrderModule,
        //                   ProductionModule = a.ProductionModule,
        //                   PurchaseOrderModule = a.PurchaseOrderModule,
        //                   IssueModule = a.IssueModule
        //               }).OrderBy(x => x.Username).ToList();
        //    }
        //    return res;
        //}

        //public ApiFunctionResponseVm AddUpdateRole(UserRoleVm br)
        //{
        //    using (var db = new Models.pmsdbContext())
        //    {
        //        UserRole res = new UserRole();
        //        if (db.UserRoles.SingleOrDefault(x => x.Username == br.Username) == null)
        //        {
        //            res.Username = br.Username;
        //            res.Admin = br.Admin;
        //            res.MasterModule = br.MasterModule;
        //            res.GateModule = br.GateModule;
        //            res.GatePassPermission = br.GatePassPermission;
        //            res.InventoryModule = br.InventoryModule;
        //            res.OrderModule = br.OrderModule;
        //            res.ProductionModule = br.ProductionModule;
        //            res.PurchaseOrderModule = br.PurchaseOrderModule;
        //            res.IssueModule = br.IssueModule;
        //            db.UserRoles.Add(res);
        //        }
        //        else
        //        {
        //            res = db.UserRoles.SingleOrDefault(x => x.Username == br.Username);
        //            if (res != null)
        //            {
        //                res.Admin = br.Admin;
        //                res.MasterModule = br.MasterModule;
        //                res.GateModule = br.GateModule;
        //                res.GatePassPermission = br.GatePassPermission;
        //                res.InventoryModule = br.InventoryModule;
        //                res.OrderModule = br.OrderModule;
        //                res.ProductionModule = br.ProductionModule;
        //                res.PurchaseOrderModule = br.PurchaseOrderModule;
        //                res.IssueModule = br.IssueModule;
        //            }
        //        }
        //        db.SaveChanges();
        //        return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
        //    }
        //}

        public ApiFunctionResponseVm AddUpdateUserStoreMapping(List<UserStoreMappingTableVm> br)
        {
            using (var db = new Models.pmsdbContext())
            {
                var deleteRecords = br.Where(x=>x.Action.ToLower() == "remove").ToList();
                var addrecords = br.Where(x=>x.Action.ToLower() == "add").ToList();
                foreach (var item in addrecords)
                {
                    var rec = db.UserStoreMappingTables.FirstOrDefault(x => x.Username == item.Username && x.StoreId == item.StoreId);
                    if (rec == null)
                    {
                        UserStoreMappingTable spt = new UserStoreMappingTable();
                        spt.Username = item.Username;
                        spt.StoreId = item.StoreId;
                        db.UserStoreMappingTables.Add(spt);
                    }
                }
                foreach (var item in deleteRecords)
                {
                    var dr = db.UserStoreMappingTables.FirstOrDefault(x => x.StoreId == item.StoreId && x.Username == item.Username);
                    if (dr != null)
                        db.UserStoreMappingTables.Remove(dr);
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
            }
        }
    }
}
