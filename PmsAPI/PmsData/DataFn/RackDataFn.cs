﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class RackDataFn
    {
        public List<RackMasterVm> GetAllRacks()
        {
            List<RackMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.RackMasters
                       join i in db.StoreMasters on a.StoreId equals i.StoreId
                       select new RackMasterVm
                       {
                           RackId = a.RackId,
                           StoreId = a.StoreId,
                           StoreName = i.StoreName,
                           RackName = a.RackName,
                           RackCode = a.RackCode,
                           RackDesc = a.RackDesc,
                           RackAddedBy = a.RackAddedBy,
                           RackAddedDate = a.RackAddedDate
                       }).OrderBy(x => x.RackCode).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateRack(RackMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.RackId == 0)
                {
                    var rec = db.RackMasters.Where(x => x.RackCode == br.RackCode).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                RackMaster res = new RackMaster();
                if (br.RackId == 0)
                {
                    res.RackName = br.RackName;
                    res.StoreId = br.StoreId;
                    res.RackCode = GenerateUniqueRackCode(br.StoreId.Value);
                    res.RackDesc = br.RackDesc;
                    res.RackAddedBy = br.RackAddedBy;
                    res.RackAddedDate = System.DateTime.Now;
                    db.RackMasters.Add(res);
                }
                else
                {
                    res = db.RackMasters.Where(x => x.RackId == br.RackId).FirstOrDefault();
                    if (res != null)
                    {
                        res.RackName = br.RackName;
                        res.StoreId = br.StoreId;
                        res.RackDesc = br.RackDesc;
                        res.RackAddedBy = br.RackAddedBy;
                        res.RackAddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public List<RackMasterVm> GetAllRacksByStoreId(long storeId)
        {
            List<RackMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.RackMasters
                       join i in db.StoreMasters on a.StoreId equals i.StoreId
                       where i.StoreId == storeId
                       select new RackMasterVm
                       {
                           RackId = a.RackId,
                           StoreId = a.StoreId,
                           StoreName = i.StoreName,
                           RackName = a.RackName,
                           RackCode = a.RackCode,
                           RackDesc = a.RackDesc,
                           RackAddedBy = a.RackAddedBy,
                           RackAddedDate = a.RackAddedDate
                       }).OrderBy(x => x.RackId).ToList();
            }
            return res;
        }

        /// <summary>
        /// Generates a unique rack code for a given store.
        /// The code is formed by taking the first letter of each word in the store name
        /// followed by 'R' and a sequence number (e.g., "ABCR-001", "ABCR-002").
        /// If the base prefix conflicts with other stores, adds additional letters for uniqueness.
        /// </summary>
        /// <param name="storeId">The ID of the store for which to generate the rack code</param>
        /// <returns>A unique rack code string, or null if store not found</returns>
        public string GenerateUniqueRackCode(long storeId)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Get the store information
                var store = db.StoreMasters.Where(x => x.StoreId == storeId).FirstOrDefault();
                if (store == null || string.IsNullOrWhiteSpace(store.StoreName))
                {
                    return null;
                }

                // Generate the base prefix from store name
                string basePrefix = GenerateStorePrefix(store.StoreName);
                if (string.IsNullOrEmpty(basePrefix))
                {
                    return null;
                }

                // Check if this prefix is already used by other stores
                string finalPrefix = EnsureUniquePrefixAcrossStores(db, basePrefix, storeId, store.StoreName);

                // Get existing rack codes for this store that start with the final prefix
                var existingCodes = db.RackMasters
                    .Where(x => x.StoreId == storeId && x.RackCode.StartsWith(finalPrefix + "R-"))
                    .Select(x => x.RackCode)
                    .ToList();

                // Find the next available sequence number
                int nextSequence = 1;
                string newRackCode;

                do
                {
                    newRackCode = $"{finalPrefix}R-{nextSequence:D3}";
                    nextSequence++;
                } while (existingCodes.Contains(newRackCode));

                return newRackCode;
            }
        }

        /// <summary>
        /// Ensures the prefix is unique across all stores by adding additional letters if needed.
        /// </summary>
        /// <param name="db">Database context</param>
        /// <param name="basePrefix">The base prefix generated from store name</param>
        /// <param name="currentStoreId">The current store ID</param>
        /// <param name="storeName">The store name for generating additional letters</param>
        /// <returns>A unique prefix for the store</returns>
        private string EnsureUniquePrefixAcrossStores(Models.pmsdbContext db, string basePrefix, long currentStoreId, string storeName)
        {
            // Get all existing rack codes from other stores that start with the base prefix
            var conflictingPrefixes = db.RackMasters
                .Join(db.StoreMasters, r => r.StoreId, s => s.StoreId, (r, s) => new { r.RackCode, r.StoreId, s.StoreName })
                .Where(x => x.StoreId != currentStoreId && x.RackCode.StartsWith(basePrefix))
                .Select(x => new { x.RackCode, x.StoreName })
                .ToList();

            // If no conflicts, return the base prefix
            if (!conflictingPrefixes.Any())
            {
                return basePrefix;
            }

            // Extract existing prefixes to avoid conflicts
            var existingPrefixes = conflictingPrefixes
                .Select(x => ExtractPrefixFromRackCode(x.RackCode))
                .Where(p => !string.IsNullOrEmpty(p))
                .Distinct()
                .ToList();

            // If base prefix is not in use, return it
            if (!existingPrefixes.Contains(basePrefix))
            {
                return basePrefix;
            }

            // Generate additional letters from store name
            var additionalLetters = GetAdditionalLettersFromStoreName(storeName, basePrefix);

            // Try combinations with additional letters
            foreach (char additionalLetter in additionalLetters)
            {
                string candidatePrefix = basePrefix + additionalLetter;
                if (!existingPrefixes.Contains(candidatePrefix))
                {
                    return candidatePrefix;
                }
            }

            // If still conflicts, add numbers
            int suffix = 1;
            string finalPrefix;
            do
            {
                finalPrefix = basePrefix + suffix;
                suffix++;
            } while (existingPrefixes.Contains(finalPrefix));

            return finalPrefix;
        }

        /// <summary>
        /// Extracts the prefix part from a rack code (everything before 'R-').
        /// </summary>
        /// <param name="rackCode">The rack code to extract prefix from</param>
        /// <returns>The prefix part of the rack code</returns>
        private string ExtractPrefixFromRackCode(string rackCode)
        {
            if (string.IsNullOrEmpty(rackCode))
                return string.Empty;

            int rIndex = rackCode.LastIndexOf("R-");
            if (rIndex > 0)
            {
                return rackCode.Substring(0, rIndex);
            }

            return string.Empty;
        }

        /// <summary>
        /// Gets additional letters from store name that weren't used in the base prefix.
        /// </summary>
        /// <param name="storeName">The store name</param>
        /// <param name="basePrefix">The base prefix already generated</param>
        /// <returns>Additional letters that can be used for uniqueness</returns>
        private string GetAdditionalLettersFromStoreName(string storeName, string basePrefix)
        {
            if (string.IsNullOrWhiteSpace(storeName))
                return string.Empty;

            var usedLetters = basePrefix.ToUpper().ToHashSet();
            var additionalLetters = new StringBuilder();

            // Get all letters from store name that weren't used in base prefix
            foreach (char c in storeName.ToUpper())
            {
                if (char.IsLetter(c) && !usedLetters.Contains(c))
                {
                    additionalLetters.Append(c);
                    usedLetters.Add(c);
                }
            }

            return additionalLetters.ToString();
        }

        /// <summary>
        /// Generates a prefix from store name by taking the first letter of each word.
        /// Handles multiple spaces and special characters.
        /// </summary>
        /// <param name="storeName">The store name to process</param>
        /// <returns>A prefix string made from first letters of each word</returns>
        private string GenerateStorePrefix(string storeName)
        {
            if (string.IsNullOrWhiteSpace(storeName))
            {
                return string.Empty;
            }

            // Split by spaces and filter out empty entries
            var words = storeName.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

            if (words.Length == 0)
            {
                return string.Empty;
            }

            var prefix = new StringBuilder();
            foreach (var word in words)
            {
                if (!string.IsNullOrEmpty(word))
                {
                    // Take the first alphabetic character from each word
                    char firstChar = word.FirstOrDefault(c => char.IsLetter(c));
                    if (firstChar != default(char))
                    {
                        prefix.Append(char.ToUpper(firstChar));
                    }
                }
            }

            return prefix.ToString();
        }
    }
}
