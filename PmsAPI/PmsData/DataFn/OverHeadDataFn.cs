﻿using System;
using PmsCommon;
using PmsEntity.ViewModel;
using System.Collections.Generic;
using System.Linq;
using PmsData.Models;
using System.Net;
using Microsoft.Graph;

namespace PmsData.DataFn
{
    public class OverHeadDataFn
    {
        public GlobalDataEntity GlobalData;
        public OverHeadDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }

        public List<OverheadColumnMasterListVm> GetOverHeadColoumnList()
        {
            List<OverheadColumnMasterListVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.OverheadColumnMasters
                       select new OverheadColumnMasterListVm
                       {
                           OverheadColumnId = a.OverheadColumnId,
                           OverheadColumnName = a.OverheadColumnName,
                           OverheadType = a.OverheadType
                       }).OrderBy(x => x.OverheadColumnName).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddOverHeadColumn(OverheadColumnMasterVm overheadColumnMasterVm)
        {
            using (var db = new Models.pmsdbContext())
            {
                var newOverhead = new OverheadColumnMaster
                {
                    OverheadColumnName = overheadColumnMasterVm.OverheadColumnName,
                    OverheadType = overheadColumnMasterVm.OverheadType,
                    AddedBy = GlobalData.loggedInUser,
                    AddedDate = System.DateTime.Now
                };

                db.OverheadColumnMasters.Add(newOverhead);
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }


        public ApiFunctionResponseVm AddOverHeadCost(List<OverheadCostVm> overHeadCostVms)
        {
            using (var db = new Models.pmsdbContext())
            {
                List<OverheadCostVm> newOverheadCosts = new List<OverheadCostVm>();

                foreach (var vm in overHeadCostVms)
                {
                    OverheadCostTable overHeadCost = db.OverheadCostTables.FirstOrDefault(m => m.OverheadColumnId == vm.OverheadColumnId && m.BranchId == vm.BranchId);

                    if (overHeadCost == null)
                    {
                        OverheadCostTable efpt = new OverheadCostTable();
                        efpt.OverheadColumnId = vm.OverheadColumnId;
                        efpt.OverheadValue = vm.OverheadValue;
                        efpt.BranchId = vm.BranchId;
                        efpt.AddedBy = GlobalData.loggedInUser;
                        efpt.AddedDate = System.DateTime.Now;
                        efpt.DivisionName = vm.DivisionName;
                        db.OverheadCostTables.Add(efpt);
                        db.SaveChanges();
                    }
                    else
                    {

                        overHeadCost.OverheadValue = vm.OverheadValue;
                        overHeadCost.UpdatedBy = GlobalData.loggedInUser;
                        overHeadCost.UpdatedDate = System.DateTime.Now;
                        db.SaveChanges();
                    }
                }
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran successfully");
        }


        // public CustomerOverheadCostsVm GetCustomerOverheadCosts(long BranchId)
        // {
        //     using (var db = new Models.pmsdbContext())
        //     {

        //         var customerCosts = new CustomerOverheadCostsVm() { BranchId = BranchId };
        //         var branchName = db.BranchMasters
        //                    .Where(b => b.BranchId == BranchId)
        //                    .Select(b => b.BranchName)
        //                    .FirstOrDefault();

        //         if (branchName != null)
        //         {
        //             customerCosts.BranchName = branchName;
        //         }

        //         var costs = db.OverHeadCostTables.Where(c => c.BranchId == BranchId).ToList();
        //         if (costs != null)
        //         {
        //             foreach (var cost in costs)
        //             {
        //                 customerCosts.DivisionName = cost.DivisionName;
        //                 customerCosts.OverheadCosts[cost.OverheadColumnName] = cost.OverheadColumnCost;
        //             }
        //         }

        //         return customerCosts;
        //     }
        // }
        public List<OverheadCostingTableVm> GetAllOverheadCost()
        {
            List<OverheadCostingTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.OverheadCostingTables
                       select new OverheadCostingTableVm
                       {
                           OverheadCostingId = a.OverheadCostingId,
                           OverheadCost = a.OverheadCost,
                           ApplicableOn = a.ApplicableOn,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           UpdatedBy = a.UpdatedBy ?? "New",
                           UpdatedDate = a.UpdatedDate
                       }).OrderByDescending(x => x.ApplicableOn).ToList();
            }
            return res;
        }
        public ApiFunctionResponseVm AddUpdateOverheadCost(OverheadCostingTableVm item)
        {
            using (var db = new Models.pmsdbContext())
            {
                var ApplicableOnInIST = DateTime.Parse(TimeZoneInfo.ConvertTimeFromUtc(item.ApplicableOn.ToUniversalTime(), TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("yyyy/MM/dd"));
                var existingcheck = db.OverheadCostingTables.Where(x => x.ApplicableOn.Date == ApplicableOnInIST.Date).ToList();
                if (existingcheck.Count > 0)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Overhead Cost for this date already exist");
                }

                if (item.OverheadCostingId == 0)
                {
                    var newOverhead = new OverheadCostingTable
                    {
                        OverheadCost = item.OverheadCost,
                        ApplicableOn = ApplicableOnInIST,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = System.DateTime.Now
                    };
                    db.OverheadCostingTables.Add(newOverhead);
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Overhead Cost Added Successfully");
                }
                else
                {
                    var overhead = db.OverheadCostingTables.Find(item.OverheadCostingId);
                    overhead.OverheadCost = item.OverheadCost;
                    overhead.ApplicableOn = ApplicableOnInIST;
                    overhead.UpdatedBy = GlobalData.loggedInUser;
                    overhead.UpdatedDate = System.DateTime.Now;
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Overhead Cost Updated Successfully");
                }
            }
        }
    }
}


