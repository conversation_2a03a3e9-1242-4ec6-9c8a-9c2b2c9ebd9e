﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class PrintDataFn
    {
        public List<PrintMasterVm> GetAllPrints()
        {
            List<PrintMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.PrintMasters
                       where a.Disabled != true
                       select new PrintMasterVm
                       {
                           PrintMasterId = a.PrintMasterId,
                           Name = a.Name,
                           Code = a.Code,
                           ImageName = a.ImageName,
                           Description = a.Description,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderBy(x => x.Code).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdatePrint(PrintMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.PrintMasterId == 0)
                {
                    var rec = db.PrintMasters.Where(x => x.Code == br.Code && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                PrintMaster res = new PrintMaster();
                if (br.PrintMasterId == 0)
                {

                    res.Name = br.Name;
                    res.Code = br.Code;
                    res.ImageName = br.ImageName;
                    res.Description = br.Description;
                    res.AddedBy = br.AddedBy;
                    res.AddedDate = System.DateTime.Now;
                    db.PrintMasters.Add(res);
                }
                else
                {
                    res = db.PrintMasters.Where(x => x.PrintMasterId == br.PrintMasterId).FirstOrDefault();
                    if (res != null)
                    {
                        res.Name = br.Name;
                        res.Code = br.Code;
                        res.ImageName = br.ImageName;
                        res.Description = br.Description;
                        res.AddedBy = br.AddedBy;
                        res.AddedDate = System.DateTime.Now;
                        res.PrintMasterId = br.PrintMasterId;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DeletePrint(PrintMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        PrintMaster item = db.PrintMasters.FirstOrDefault(x => x.PrintMasterId == param.PrintMasterId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Print deleted successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
