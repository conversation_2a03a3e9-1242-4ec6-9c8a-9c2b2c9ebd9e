﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using Microsoft.Graph.TermStore;
using Microsoft.IdentityModel.Tokens;
using PmsCommon;
using PmsData.Models;
using PmsEntity.ViewModel;

namespace PmsData.DataFn
{
    public class CostEstimationDataFn
    {
        public GlobalDataEntity GlobalData;
        public CostEstimationDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ApiFunctionResponseVm AddEstimationOrder(EstimationOrderTableVm est)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (est.EstimationOrderId == 0)
                {
                    EstimationOrderTable mm = new EstimationOrderTable();
                    mm.ProductCategoryId = est.ProductCategoryId;
                    mm.EstimationFormulationCodeId = est.EstimationFormulationCodeId;
                    mm.ManufacturingProductName = est.ManufacturingProductName;
                    mm.OrderQuantity = est.OrderQuantity;
                    mm.Lmconstant = est.Lmconstant;
                    mm.ExtraProduction = est.ExtraProduction;
                    mm.ManufacturingQuantity = est.ManufacturingQuantity;
                    mm.ColorId = est.ColorId;
                    mm.GrainId = est.GrainId;
                    mm.GrainPrice = est.GrainPrice;
                    mm.Thick = est.Thick;
                    mm.Width = est.Width;
                    mm.ProductionCostLm = est.ProductionCostLm;
                    mm.TotalFinishPrice = est.TotalFinishPrice;
                    mm.OverheadCost = est.OverheadCost;
                    mm.Rejection = est.Rejection;
                    mm.TotalCostPerLm = est.TotalCostPerLm;
                    mm.CustomerId = est.CustomerId;
                    mm.Remarks = est.Remarks;
                    mm.AddedBy = GlobalData.loggedInUser;
                    mm.DisabledBy = est.DisabledBy;
                    mm.AddedDate = System.DateTime.Now;
                    mm.DisabledDate = est.DisabledDate;
                    mm.TotalProfitLoss = est.TotalProfitLoss;
                    mm.EstimationOrderType = est.EstimationOrderType;
                    db.EstimationOrderTables.Add(mm);
                    db.SaveChanges();

                    EstimationOrderStatus estimationOrderStatus = new EstimationOrderStatus();
                    estimationOrderStatus.EstimationOrderStatus1 = EstimationStatus.Active;
                    estimationOrderStatus.EstimationOrderId = mm.EstimationOrderId;
                    estimationOrderStatus.AddedBy = GlobalData.loggedInUser;
                    estimationOrderStatus.AddedDate = System.DateTime.Now;
                    db.EstimationOrderStatuses.Add(estimationOrderStatus);
                    db.SaveChanges();
                    if (est.EstimationFabricProductDetail != null)
                    {
                        foreach (var item in est.EstimationFabricProductDetail)
                        {
                            EstimationFabricProductDetail efpt = new EstimationFabricProductDetail();
                            efpt.EstimationOrderId = mm.EstimationOrderId;
                            efpt.FabricProductId = item.FabricProductId;
                            efpt.FabricColorId = item.FabricColorId;
                            efpt.FabricEstimationQuantity = item.FabricEstimationQuantity;
                            efpt.FabricProductCostPerLm = item.FabricProductCostPerLm;
                            efpt.FabricGsm = item.FabricGsm;
                            efpt.AddedBy = GlobalData.loggedInUser;
                            efpt.AddedDate = System.DateTime.Now;
                            db.EstimationFabricProductDetails.Add(efpt);
                            db.SaveChanges();


                        }
                    }
                    if (est.EstimationFinishingTable != null)
                    {
                        foreach (var item in est.EstimationFinishingTable)
                        {
                            EstimationFinishingTable eft = new EstimationFinishingTable();
                            eft.EstimationOrderId = mm.EstimationOrderId;
                            eft.EstimationMaterialType = item.EstimationMaterialType;
                            eft.Finishing = item.Finishing;
                            eft.FinishingType = item.FinishingType;
                            eft.Price = item.Price;
                            eft.AddedBy = GlobalData.loggedInUser;
                            eft.AddedDate = System.DateTime.Now;
                            db.EstimationFinishingTables.Add(eft);
                            db.SaveChanges();

                        }
                    }
                    if (est.EstimationMixingTable != null)
                    {
                        foreach (var item in est.EstimationMixingTable)
                        {
                            EstimationMixingTable spt = new EstimationMixingTable();
                            spt.EstimationOrderId = mm.EstimationOrderId;
                            spt.MixingId = item.MixingId;
                            spt.PreSkinGsm = item.PreSkinGsm;
                            spt.SkinGsm = item.SkinGsm;
                            spt.FoamGsm = item.FoamGsm;
                            spt.AdhesiveGsm = item.AdhesiveGsm;
                            spt.AvgGsm = item.AvgGsm;
                            spt.AddedBy = GlobalData.loggedInUser;
                            spt.AddedDate = System.DateTime.Now;
                            db.EstimationMixingTables.Add(spt);
                            db.SaveChanges();
                            foreach (var mrm in item.EstimationCodeMixingRawMaterialTable)
                            {
                                EstimationCodeMixingRawMaterialTable sptraw = new EstimationCodeMixingRawMaterialTable();
                                sptraw.EstimationCodeMixingId = spt.EstimationCodeMixingId;
                                sptraw.ProductId = mrm.ProductId;
                                sptraw.Quantity = mrm.Quantity;
                                sptraw.Unit = mrm.Unit;
                                sptraw.Price = mrm.Price;
                                db.EstimationCodeMixingRawMaterialTables.Add(sptraw);
                                db.SaveChanges();
                            }

                        }
                    }
                }
                else
                {
                    EstimationOrderTable existingOrder = db.EstimationOrderTables.FirstOrDefault(o => o.EstimationOrderId == est.EstimationOrderId);
                    if (existingOrder != null)
                    {
                        existingOrder.ProductCategoryId = est.ProductCategoryId;
                        existingOrder.ManufacturingProductName = est.ManufacturingProductName;
                        existingOrder.EstimationFormulationCodeId = est.EstimationFormulationCodeId;
                        existingOrder.OrderQuantity = est.OrderQuantity;
                        existingOrder.Lmconstant = est.Lmconstant;
                        existingOrder.ExtraProduction = est.ExtraProduction;
                        existingOrder.ManufacturingQuantity = est.ManufacturingQuantity;
                        existingOrder.ColorId = est.ColorId;
                        existingOrder.GrainId = est.GrainId;
                        existingOrder.GrainPrice = est.GrainPrice;
                        existingOrder.Thick = est.Thick;
                        existingOrder.Width = est.Width;
                        existingOrder.ProductionCostLm = est.ProductionCostLm;
                        existingOrder.TotalFinishPrice = est.TotalFinishPrice;
                        existingOrder.OverheadCost = est.OverheadCost;
                        existingOrder.Rejection = est.Rejection;
                        existingOrder.TotalCostPerLm = est.TotalCostPerLm;
                        existingOrder.CustomerId = est.CustomerId;
                        existingOrder.Remarks = est.Remarks;
                        existingOrder.DisabledBy = est.DisabledBy;
                        existingOrder.DisabledDate = est.DisabledDate;
                        existingOrder.TotalProfitLoss = est.TotalProfitLoss;
                        existingOrder.EstimationOrderType = est.EstimationOrderType;


                        db.SaveChanges();
                        if(est.EstimationFabricProductDetail != null)
                        {
                            foreach (var item in est.EstimationFabricProductDetail)
                            {
                                if (item.FabricProductId == 0)
                                {
                                    EstimationFabricProductDetail efpt = new EstimationFabricProductDetail();
                                    efpt.EstimationOrderId = existingOrder.EstimationOrderId;
                                    efpt.FabricProductId = item.FabricProductId;
                                    efpt.FabricColorId = item.FabricColorId;
                                    efpt.FabricEstimationQuantity = item.FabricEstimationQuantity;
                                    efpt.FabricProductCostPerLm = item.FabricProductCostPerLm;
                                    efpt.FabricGsm = item.FabricGsm;
                                    efpt.AddedBy = GlobalData.loggedInUser;
                                    efpt.AddedDate = System.DateTime.Now;
                                    db.EstimationFabricProductDetails.Add(efpt);
                                    db.SaveChanges();
                                }
                                else
                                {
                                    EstimationFabricProductDetail estimationFabricProduct = db.EstimationFabricProductDetails.FirstOrDefault(m => m.FabricProductId == item.FabricProductId);
                                    if (estimationFabricProduct != null)
                                    {
                                        estimationFabricProduct.FabricColorId = item.FabricColorId;
                                        estimationFabricProduct.FabricEstimationQuantity = item.FabricEstimationQuantity;
                                        estimationFabricProduct.FabricProductCostPerLm = item.FabricProductCostPerLm;
                                        estimationFabricProduct.FabricGsm = item.FabricGsm;
                                        estimationFabricProduct.AddedBy = GlobalData.loggedInUser;
                                        estimationFabricProduct.AddedDate = System.DateTime.Now;
                                        db.SaveChanges();
                                    }

                                }
                            }

                        }

                        if (est.EstimationFinishingTable != null)
                        {
                            foreach (var item in est.EstimationFinishingTable)
                            {
                                if (item.EstimationFinishingId == 0)
                                {
                                    EstimationFinishingTable eft = new EstimationFinishingTable();
                                    eft.EstimationOrderId = existingOrder.EstimationOrderId;
                                    eft.EstimationMaterialType = item.EstimationMaterialType;
                                    eft.Finishing = item.Finishing;
                                    eft.FinishingType = item.FinishingType;
                                    eft.Price = item.Price;
                                    eft.AddedBy = GlobalData.loggedInUser;
                                    eft.AddedDate = System.DateTime.Now;
                                    db.EstimationFinishingTables.Add(eft);
                                    db.SaveChanges();
                                }
                                else
                                {
                                    EstimationFinishingTable estimationFinishingTable = db.EstimationFinishingTables.FirstOrDefault(m => m.EstimationFinishingId == item.EstimationFinishingId);
                                    if (estimationFinishingTable != null)
                                    {
                                        estimationFinishingTable.EstimationMaterialType = item.EstimationMaterialType;
                                        estimationFinishingTable.Finishing = item.Finishing;
                                        estimationFinishingTable.FinishingType = item.FinishingType;
                                        estimationFinishingTable.Price = item.Price;
                                        estimationFinishingTable.AddedBy = GlobalData.loggedInUser;
                                        estimationFinishingTable.AddedDate = System.DateTime.Now;
                                        db.SaveChanges();
                                    }

                                }
                            }

                        }
                        if (est.EstimationMixingTable != null)
                        {
                            foreach (var item in est.EstimationMixingTable)
                            {
                                if (item.EstimationCodeMixingId == 0)
                                {
                                    EstimationMixingTable spt = new EstimationMixingTable();
                                    spt.EstimationOrderId = existingOrder.EstimationOrderId;
                                    spt.MixingId = item.MixingId;
                                    spt.PreSkinGsm = item.PreSkinGsm;
                                    spt.SkinGsm = item.SkinGsm;
                                    spt.FoamGsm = item.FoamGsm;
                                    spt.AdhesiveGsm = item.AdhesiveGsm;
                                    spt.AvgGsm = item.AvgGsm;
                                    spt.AddedBy = GlobalData.loggedInUser;
                                    spt.AddedDate = System.DateTime.Now;
                                    db.EstimationMixingTables.Add(spt);
                                    db.SaveChanges();
                                    foreach (var mrm in item.EstimationCodeMixingRawMaterialTable)
                                    {
                                        if (mrm.EstimationCodeRawMaterialMixingId == 0)
                                        {
                                            EstimationCodeMixingRawMaterialTable sptraw = new EstimationCodeMixingRawMaterialTable();
                                            sptraw.EstimationCodeMixingId = spt?.EstimationCodeMixingId;
                                            sptraw.ProductId = mrm.ProductId;
                                            sptraw.Quantity = mrm.Quantity;
                                            sptraw.Unit = mrm.Unit;
                                            sptraw.Price = mrm.Price;
                                            db.EstimationCodeMixingRawMaterialTables.Add(sptraw);
                                            db.SaveChanges();
                                        }
                                        else
                                        {
                                            EstimationCodeMixingRawMaterialTable existingRawMaterialRecord = db.EstimationCodeMixingRawMaterialTables.FirstOrDefault(m => m.EstimationCodeRawMaterialMixingId == mrm.EstimationCodeRawMaterialMixingId);
                                            if (existingRawMaterialRecord != null)
                                            {
                                                existingRawMaterialRecord.ProductId = mrm.ProductId;
                                                existingRawMaterialRecord.Quantity = mrm.Quantity;
                                                existingRawMaterialRecord.Unit = mrm.Unit;
                                                existingRawMaterialRecord.Price = mrm.Price;
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    EstimationMixingTable existingMixingRecord = db.EstimationMixingTables.FirstOrDefault(m => m.EstimationCodeMixingId == item.EstimationCodeMixingId);
                                    if (existingMixingRecord != null)
                                    {
                                        existingMixingRecord.MixingId = item.MixingId;
                                        existingMixingRecord.PreSkinGsm = item.PreSkinGsm;
                                        existingMixingRecord.SkinGsm = item.SkinGsm;
                                        existingMixingRecord.FoamGsm = item.FoamGsm;
                                        existingMixingRecord.AdhesiveGsm = item.AdhesiveGsm;
                                        existingMixingRecord.AvgGsm = item.AvgGsm;
                                        existingMixingRecord.AddedBy = GlobalData.loggedInUser;
                                        existingMixingRecord.AddedDate = System.DateTime.Now;
                                    }
                                    foreach (var mrm in item.EstimationCodeMixingRawMaterialTable)
                                    {
                                        if (mrm.EstimationCodeRawMaterialMixingId == 0)
                                        {
                                            EstimationCodeMixingRawMaterialTable sptraw = new EstimationCodeMixingRawMaterialTable();
                                            sptraw.EstimationCodeMixingId = existingMixingRecord.EstimationCodeMixingId;
                                            sptraw.ProductId = mrm.ProductId;
                                            sptraw.Quantity = mrm.Quantity;
                                            sptraw.Unit = mrm.Unit;
                                            sptraw.Price = mrm.Price;
                                            db.EstimationCodeMixingRawMaterialTables.Add(sptraw);
                                        }
                                        else
                                        {
                                            EstimationCodeMixingRawMaterialTable existingRawMaterialRecord = db.EstimationCodeMixingRawMaterialTables.FirstOrDefault(m => m.EstimationCodeRawMaterialMixingId == mrm.EstimationCodeRawMaterialMixingId);
                                            if (existingRawMaterialRecord != null)
                                            {
                                                existingRawMaterialRecord.ProductId = mrm.ProductId;
                                                existingRawMaterialRecord.Quantity = mrm.Quantity;
                                                existingRawMaterialRecord.Unit = mrm.Unit;
                                                existingRawMaterialRecord.Price = mrm.Price;
                                            }
                                        }
                                    }
                                }
                            }

                        }
                        db.SaveChanges();

                    }
                }

            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
       
        public ApiFunctionResponseVm DisableEstimationOrderById(long estOrderId)
        {
            using (var db = new Models.pmsdbContext())
            {
                EstimationOrderTable mm = db.EstimationOrderTables.FirstOrDefault(x => x.EstimationOrderId == estOrderId);
                mm.Disabled = true;
                mm.DisabledBy = GlobalData.loggedInUser;
                mm.DisabledDate = System.DateTime.Now;

                db.SaveChanges();

            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "EstimationOrder disabled successfully.");
        }

        public List<EstimationOrderTableVm> GetEstimationListByFilter(EstimationFilterVm filters)
        {
            List<EstimationOrderTableVm> estProductions = null;
            using (var db = new Models.pmsdbContext())
            {
                estProductions = (from s in db.EstimationOrderTables
                                  join c in db.ProductCategoryMasters on s.ProductCategoryId equals c.ProductCategoryId into csf
                                  from c in csf.DefaultIfEmpty()
                                  join st in db.SaleFormulationCodeMasters on s.EstimationFormulationCodeId equals st.SaleFormulationCodeId into sts
                                  from st in sts.DefaultIfEmpty()
                                  join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into custf
                                  from cust in custf.DefaultIfEmpty()
                                  join cm in db.ColorMasters on s.ColorId equals cm.ColorId
                                  join th in db.ThicknessMasters on s.Thick equals th.ThicknessId into psf
                                  from th in psf.DefaultIfEmpty()
                                  join gr in db.GrainMasters on s.GrainId equals gr.GrainId into grn
                                  from gr in grn.DefaultIfEmpty()
                                  join wth in db.WidthMasters on s.Width equals wth.WidthId into wthn
                                  from wth in wthn.DefaultIfEmpty()
                                  join es in db.EstimationOrderStatuses on s.EstimationOrderId equals es.EstimationOrderId
                                  where s.Disabled != true
                                  && (filters.EstimationFromDate == null || s.AddedDate >= filters.EstimationFromDate)
                                  && (filters.EstimationToDate == null || s.AddedDate <= filters.EstimationToDate)
                                  && (filters.EstimationOrderId == 0 || filters.EstimationOrderId == s.EstimationOrderId)
                                  && (filters.CustomerId == 0 || filters.CustomerId == null || filters.CustomerId == s.CustomerId)
                                  && (filters.Thick == 0 || filters.Thick == null || filters.Thick == s.Thick)
                                  && (String.IsNullOrEmpty(filters.AddedBy) || s.AddedBy.ToLower().Contains(filters.AddedBy))
                                  && (String.IsNullOrEmpty(filters.ArticleName) || s.ManufacturingProductName.ToLower().Contains(filters.ArticleName.ToLower()))
                                  && (filters.ColorId == 0 || filters.ColorId == null || s.ColorId == filters.ColorId)
                                  && (filters.GrainId == 0 || filters.GrainId == null || s.GrainId == filters.GrainId)
                                  && (string.IsNullOrEmpty(filters.EstimationOrderStatus) || es.EstimationOrderStatus1 == filters.EstimationOrderStatus)
                                  select new EstimationOrderTableVm
                                  {
                                      EstimationOrderId = s.EstimationOrderId,
                                      ProductCategoryId = s.ProductCategoryId,
                                      EstimationFormulationCodeId = s.EstimationFormulationCodeId,
                                      EstimationSaleFormulationCode = st.SaleFormulationCode,
                                      ProductCategoryName = c.ProductCategory,
                                      ManufacturingProductName = s.ManufacturingProductName,
                                      OrderQuantity = s.OrderQuantity,
                                      Lmconstant = s.Lmconstant,
                                      ExtraProduction = s.ExtraProduction,
                                      ManufacturingQuantity = s.ManufacturingQuantity,
                                      ColorId = s.ColorId,
                                      ColorName = cm.ColorName,
                                      GrainId = s.GrainId,
                                      GrainName = gr.GrainName,
                                      GrainPrice = s.GrainPrice,
                                      Thick = s.Thick,
                                      ThicknessNumber = th.ThicknessNumber,
                                      Width = s.Width,
                                      WidthNumber = wth.WidthNumber,
                                      EstimationPrice = s.EstimationPrice,//EstimationPrice
                                      ProductionCostLm = s.ProductionCostLm,
                                      TotalFinishPrice = s.TotalFinishPrice,
                                      OverheadCost = s.OverheadCost,
                                      Rejection = s.Rejection,
                                      TotalCostPerLm = s.TotalCostPerLm,
                                      LineSpeed = s.LineSpeed,
                                      CustomerId = s.CustomerId,
                                      CustomerName = cust.CustomerName,
                                      EstimationOrderType = s.EstimationOrderType,
                                      EstimationOrderStatus = (from os in db.EstimationOrderStatuses
                                                               where os.EstimationOrderId == s.EstimationOrderId
                                                               orderby os.AddedDate
                                                               select new EstimationOrderStatusVm
                                                               {
                                                                  EstimationOrderStatusId = os.EstimationOrderStatusId,
                                                                  EstimationOrderId = os.EstimationOrderStatusId,
                                                                  EstimationOrderStatus = os.EstimationOrderStatus1,
                                                                  AddedBy = os.AddedBy,
                                                                  AddedDate = os.AddedDate
                                                               }).FirstOrDefault(),

                                      EstimationFabricProductDetail = (from pr in db.EstimationFabricProductDetails
                                                                        join p in db.ProductMasters on pr.FabricProductId equals p.ProductId into ps
                                                                       from p in ps.DefaultIfEmpty()
                                                                       where pr.EstimationOrderId == s.EstimationOrderId
                                                                     && (filters.FabricProductId == 0 || filters.FabricProductId == pr.FabricProductId)
                                                                       select new EstimationFabricProductDetailVm
                                                                       {
                                                                           EstimationOrderId = pr.EstimationOrderId,
                                                                           EstimationFabricProductId = pr.EstimationFabricProductId,
                                                                           FabricProductId = pr.FabricProductId,
                                                                         FabricProductName = p.ProductName,
                                                                           FabricColorId = pr.FabricColorId,
                                                                           FabricGsm = pr.FabricGsm,
                                                                           FabricEstimationQuantity= pr.FabricEstimationQuantity,
                                                                           FabricProductCostPerLm = pr.FabricProductCostPerLm,
                                                                           AddedDate = pr.AddedDate,
                                                                           AddedBy = pr.AddedBy
                                                                      }).ToList(),

                                     //EstimationMixingTable = (from la in db.EstimationMixingTables
                                     //                                     join a in db.MixingMasters on la.MixingId equals a.MixingId
                                     //                                     where la.EstimationOrderId == s.EstimationOrderId
                                     //                                     select new EstimationMixingTableVm
                                     //                                     {
                                     //                                         EstimationCodeMixingId = la.EstimationCodeMixingId,
                                     //                                         EstimationOrderId = la.EstimationOrderId,
                                     //                                         MixingId = la.MixingId,
                                     //                                         MixingName = a.MixingName,
                                     //                                         PreSkinGsm = la.PreSkinGsm,
                                     //                                         SkinGsm = la.SkinGsm,
                                     //                                         FoamGsm = la.FoamGsm,
                                     //                                         AdhesiveGsm = la.AdhesiveGsm,
                                     //                                         AvgGsm = la.AvgGsm,
                                     //                                         AddedDate = la.AddedDate,
                                     //                                         AddedBy = la.AddedBy,
                                     //                                         EstimationCodeMixingRawMaterialTable = (from op in db.EstimationCodeMixingRawMaterialTables
                                     //                                                                                 join p in db.ProductMasters on op.ProductId equals p.ProductId
                                     //                                                                                 where op.EstimationCodeMixingId == la.EstimationCodeMixingId
                                     //                                                                                 select new EstimationCodeMixingRawMaterialTableVm
                                     //                                                                                 {
                                     //                                                                                     EstimationCodeRawMaterialMixingId = op.EstimationCodeRawMaterialMixingId,
                                     //                                                                                     EstimationCodeMixingId = op.EstimationCodeMixingId,
                                     //                                                                                     ProductId = op.ProductId,
                                     //                                                                                     ProductName = p.ProductName,
                                     //                                                                                     ProductCode = p.ProductCode,
                                     //                                                                                     Quantity = op.Quantity,
                                     //                                                                                     Unit = op.Unit,
                                     //                                                                                     Price = op.Price
                                     //                                                                                 }).ToList()                                                                         }).ToList(),
                                  }).OrderByDescending(x => x.EstimationOrderId).ToList();
            }
            return estProductions;
        }



        public EstimationOrderTableVm GetEstimationOrderById(long estOrderId)
        {
            EstimationOrderTableVm est = null;
            using (var db = new Models.pmsdbContext())
            {
               var estProductions = (from s in db.EstimationOrderTables
                                     join c in db.ProductCategoryMasters on s.ProductCategoryId equals c.ProductCategoryId into csf
                                     from c in csf.DefaultIfEmpty()
                                     join st in db.SaleFormulationCodeMasters on s.EstimationFormulationCodeId equals st.SaleFormulationCodeId into sts
                                     from st in sts.DefaultIfEmpty()
                                     join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into custf
                                     from cust in custf.DefaultIfEmpty()
                                     join cm in db.ColorMasters on s.ColorId equals cm.ColorId
                                     join th in db.ThicknessMasters on s.Thick equals th.ThicknessId into psf
                                     from th in psf.DefaultIfEmpty()
                                     join gr in db.GrainMasters on s.GrainId equals gr.GrainId into grn
                                     from gr in grn.DefaultIfEmpty()
                                     join wth in db.WidthMasters on s.Width equals wth.WidthId into wthn
                                     from wth in wthn.DefaultIfEmpty()
                                     join es in db.EstimationOrderStatuses on s.EstimationOrderId equals es.EstimationOrderId
                                     where s.Disabled != true
                                     select new EstimationOrderTableVm
                                  {
                                      EstimationOrderId = s.EstimationOrderId,
                                      ProductCategoryId = s.ProductCategoryId,
                                      EstimationFormulationCodeId = s.EstimationFormulationCodeId,
                                      EstimationSaleFormulationCode = st.SaleFormulationCode,
                                      ProductCategoryName = c.ProductCategory,
                                      ManufacturingProductName = s.ManufacturingProductName,
                                      OrderQuantity = s.OrderQuantity,
                                      Lmconstant = s.Lmconstant,
                                      ExtraProduction = s.ExtraProduction,
                                      ManufacturingQuantity = s.ManufacturingQuantity,
                                      ColorId = s.ColorId,
                                      ColorName = cm.ColorName,
                                      GrainId = s.GrainId,
                                      GrainName = gr.GrainName,
                                      GrainPrice = s.GrainPrice,
                                      Thick = s.Thick,
                                      ThicknessNumber = th.ThicknessNumber,
                                      Width = s.Width,
                                      WidthNumber = wth.WidthNumber,
                                      EstimationPrice = s.EstimationPrice,//EstimationPrice
                                      ProductionCostLm = s.ProductionCostLm,
                                      TotalFinishPrice = s.TotalFinishPrice,
                                      OverheadCost = s.OverheadCost,
                                      Rejection = s.Rejection,
                                      TotalCostPerLm = s.TotalCostPerLm,
                                      LineSpeed = s.LineSpeed,
                                      CustomerId = s.CustomerId,
                                      CustomerName = cust.CustomerName,
                                      EstimationOrderType = s.EstimationOrderType,
                                      EstimationOrderStatus = (from os in db.EstimationOrderStatuses
                                                               where os.EstimationOrderId == s.EstimationOrderId
                                                               orderby os.AddedDate
                                                               select new EstimationOrderStatusVm
                                                               {
                                                                   EstimationOrderStatusId = os.EstimationOrderStatusId,
                                                                   EstimationOrderId = os.EstimationOrderStatusId,
                                                                   EstimationOrderStatus = os.EstimationOrderStatus1,
                                                                   AddedBy = os.AddedBy,
                                                                   AddedDate = os.AddedDate
                                                               }).FirstOrDefault(),

                                      EstimationFabricProductDetail = (from pr in db.EstimationFabricProductDetails
                                                                       join p in db.ProductMasters on pr.FabricProductId equals p.ProductId into ps
                                                                       from p in ps.DefaultIfEmpty()
                                                                       where pr.EstimationOrderId == s.EstimationOrderId
                                                                       select new EstimationFabricProductDetailVm
                                                                       {
                                                                           EstimationOrderId = pr.EstimationOrderId,
                                                                           EstimationFabricProductId = pr.EstimationFabricProductId,
                                                                           FabricProductName = p.ProductName,
                                                                           FabricProductId = pr.FabricProductId,
                                                                           FabricColorId = pr.FabricColorId,
                                                                           FabricGsm = pr.FabricGsm,
                                                                           FabricEstimationQuantity = pr.FabricEstimationQuantity,
                                                                           FabricProductCostPerLm = pr.FabricProductCostPerLm,
                                                                           AddedDate = pr.AddedDate,
                                                                           AddedBy = pr.AddedBy
                                                                       }).ToList(),
                                      EstimationFinishingTable = (from eft in db.EstimationFinishingTables
                                                                  where eft.EstimationOrderId == s.EstimationOrderId
                                                                  select new EstimationFinishingTableVm
                                                                  {
                                                                      EstimationOrderId = eft.EstimationOrderId,
                                                                      EstimationFinishingId = eft.EstimationFinishingId,
                                                                      EstimationMaterialType = eft.EstimationMaterialType,
                                                                      Finishing = eft.Finishing,
                                                                      FinishingType = eft.FinishingType,
                                                                      Price = eft.Price,
                                                                      AddedDate = eft.AddedDate,
                                                                      AddedBy = eft.AddedBy
                                                                  }).ToList(),

                                      EstimationMixingTable = (from la in db.EstimationMixingTables
                                                               join a in db.MixingMasters on la.MixingId equals a.MixingId into mmf
                                                               from a in mmf.DefaultIfEmpty()
                                                               where la.EstimationOrderId == s.EstimationOrderId
                                                               select new EstimationMixingTableVm
                                                               {
                                                                   EstimationCodeMixingId = la.EstimationCodeMixingId,
                                                                   EstimationOrderId = la.EstimationOrderId,
                                                                   MixingId = la.MixingId,
                                                                   MixingName = a.MixingName,
                                                                   PreSkinGsm = la.PreSkinGsm,
                                                                   SkinGsm = la.SkinGsm,
                                                                   FoamGsm = la.FoamGsm,
                                                                   AdhesiveGsm = la.AdhesiveGsm,
                                                                   AvgGsm = la.AvgGsm,
                                                                   AddedDate = la.AddedDate,
                                                                   AddedBy = la.AddedBy,
                                                                   EstimationCodeMixingRawMaterialTable = (from op in db.EstimationCodeMixingRawMaterialTables
                                                                                                           join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                                           where op.EstimationCodeMixingId == la.EstimationCodeMixingId
                                                                                                           select new EstimationCodeMixingRawMaterialTableVm
                                                                                                           {
                                                                                                               EstimationCodeRawMaterialMixingId = op.EstimationCodeRawMaterialMixingId,
                                                                                                               EstimationCodeMixingId = op.EstimationCodeMixingId,
                                                                                                               ProductId = op.ProductId,
                                                                                                               ProductName = p.ProductName,
                                                                                                               ProductCode = p.ProductCode,
                                                                                                               Quantity = op.Quantity,
                                                                                                               Unit = op.Unit,
                                                                                                               Price = op.Price
                                                                                                           }).ToList()
                                                               }).ToList(),
                                  }).OrderByDescending(x => x.EstimationOrderId).ToList();
                est = estProductions.FirstOrDefault(x => x.EstimationOrderId == estOrderId);

            }
            return est;
        }


        public ApiFunctionResponseVm UpdateEstimationOrderStatus(EstimationStatusVm estimationStatus)
        {
            if (string.IsNullOrEmpty(estimationStatus.EstimationStatus))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Status parameter is required.");
            }

            using (var db = new Models.pmsdbContext())
            {
                EstimationOrderStatus estimationOrderStatus = db.EstimationOrderStatuses
                    .FirstOrDefault(eos => eos.EstimationOrderId == estimationStatus.EstimationOrderId);

                if (estimationOrderStatus != null)
                {
                    if (estimationStatus.EstimationStatus == EstimationStatus.Draft ||
                        estimationStatus.EstimationStatus == EstimationStatus.Active ||
                        estimationStatus.EstimationStatus == EstimationStatus.Reviewed ||
                        estimationStatus.EstimationStatus == EstimationStatus.Cancelled ||
                        estimationStatus.EstimationStatus == EstimationStatus.Approved)
                    {
                        estimationOrderStatus.EstimationOrderStatus1 = estimationStatus.EstimationStatus;
                        estimationOrderStatus.AddedBy = GlobalData.loggedInUser;
                        estimationOrderStatus.AddedDate = DateTime.Now;
                        estimationOrderStatus.Remark = estimationStatus.Remark;

                        db.SaveChanges();

                        return new ApiFunctionResponseVm(HttpStatusCode.OK, $"EstimationOrder {estimationStatus} successfully.");
                    }
                    else
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Invalid status parameter.");
                    }
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "EstimationOrderStatus not found.");
                }
            }
        }





    }
}

