﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using Newtonsoft.Json;

namespace PmsData.DataFn
{
    public class CustomerDataFn
    {
        public List<CustomerMasterVm> GetAllCustomers()
        {
            List<CustomerMasterVm> res = null;
            using (var db = new pmsdbContext())
            {
                res = (from a in db.CustomerMasters
                       join esc in db.EmailSubscriptionCustomerTables on a.CustomerId equals esc.CustomerId into escd
                       from esc in escd.DefaultIfEmpty()
                       join wasc in db.WhatsAppSubscriptionCustomerTables on a.CustomerId equals wasc.CustomerId into wascd
                       from wasc in wascd.DefaultIfEmpty()
                       where a.Disabled != true
                       select new CustomerMasterVm
                       {
                           CustomerId = a.CustomerId,
                           CustomerName = a.CustomerName,
                           CustomerContactNumber = a.CustomerContactNumber,
                           Gstnumber = a.Gstnumber,
                           Email = a.Email,
                           Address = a.Address,
                           State = a.State,
                           Country = a.Country,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           CustomerShortName = a.CustomerShortName,
                           CustomerCode = a.CustomerCode,
                           IsEmailNotification = esc.Enabled == true,
                           IsWhatsappNotification = wasc.Enabled == true,

                           // Load notification subscriptions from NotificationGroupsTable
                           NotificationSubscriptions = (from ng in db.NotificationGroupsTables
                                                        join config in db.NotificationTemplateConfigurationTables
                                                            on ng.ConfigurationId equals config.ConfigurationId
                                                        where ng.UserType == "Customer"
                                                            && ng.UserMasterId == a.CustomerId
                                                            && ng.Disabled != true
                                                        select new CustomerNotificationSubscriptionVm
                                                        {
                                                            NotificationGroupUserId = ng.NotificationGroupUserId,
                                                            ConfigurationId = config.ConfigurationId,
                                                            NotificationTypeName = config.DisplayName,
                                                            IsScheduleRequired = config.SchedulingRequired,
                                                            UseDefaultSchedule = ng.UseDefaultSchedule,
                                                            CronScheduleExpression = ng.CronScheduleExpression,
                                                            ScheduleDescription = ng.NotificationFrequency,
                                                            PreferredNotificationTime = ng.PreferredNotificationTime
                                                        }).ToList()
                       }).OrderBy(x => x.CustomerName).ToList();
            }
            return res;
        }


        public ApiFunctionResponseVm AddUpdateCustomer(CustomerMasterVm cust)
        {
            using (var db = new Models.pmsdbContext())
            {
                CustomerMaster res = new CustomerMaster();
                if (cust.CustomerId == 0)
                {
                    res.CustomerId = cust.CustomerId;
                    res.CustomerName = cust.CustomerName;
                    res.Gstnumber = cust.Gstnumber;
                    res.CustomerContactNumber = cust.CustomerContactNumber;
                    res.Email = cust.Email;
                    res.Address = cust.Address;
                    res.State = cust.State;
                    res.Country = cust.Country;
                    res.AddedBy = cust.AddedBy;
                    res.AddedDate = DateTime.Now;
                    res.Disabled = false;
                    res.CustomerShortName = cust.CustomerShortName;
                    res.CustomerCode = cust.CustomerCode;
                    db.CustomerMasters.Add(res);
                    db.SaveChanges();

                    AddOrUpdateSubscription(db, res.CustomerId, cust);

                    // Save customer notification subscriptions to NotificationGroupsTable
                    SaveCustomerNotificationSubscriptions(db, res.CustomerId, cust);

                }
                else
                {
                    res = db.CustomerMasters.Where(x => x.CustomerId == cust.CustomerId).FirstOrDefault();
                    if (res != null)
                    {
                        res.CustomerId = cust.CustomerId;
                        res.CustomerName = cust.CustomerName;
                        res.Gstnumber = cust.Gstnumber;
                        res.CustomerContactNumber = cust.CustomerContactNumber;
                        res.Email = cust.Email;
                        res.Address = cust.Address;
                        res.State = cust.State;
                        res.Country = cust.Country;
                        res.AddedBy = cust.AddedBy;
                        res.AddedDate = DateTime.Now;
                        res.CustomerShortName = cust.CustomerShortName;
                        res.CustomerCode = cust.CustomerCode;

                    }
                    AddOrUpdateSubscription(db, cust.CustomerId, cust);

                    // Update customer notification subscriptions in NotificationGroupsTable
                    SaveCustomerNotificationSubscriptions(db, cust.CustomerId, cust);
                }

                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        private void AddOrUpdateSubscription(Models.pmsdbContext db, long CustomerId, CustomerMasterVm cust)
        {
            try
            {
                var emailSubscription = db.EmailSubscriptionCustomerTables.FirstOrDefault(x => x.CustomerId == CustomerId);
                if (emailSubscription != null)
                {
                    emailSubscription.Enabled = cust.IsEmailNotification;
                    emailSubscription.LastUpdatedBy = cust.AddedBy;
                    emailSubscription.LastUpdatedDate = DateTime.Now;
                }
                else
                {
                    emailSubscription = new EmailSubscriptionCustomerTable
                    {
                        CustomerId = CustomerId,
                        Enabled = cust.IsEmailNotification,
                        AddedBy = cust.AddedBy,
                        AddedDate = DateTime.Now,
                    };
                    db.EmailSubscriptionCustomerTables.Add(emailSubscription);
                }

                var whatsappSubscription = db.WhatsAppSubscriptionCustomerTables.FirstOrDefault(x => x.CustomerId == CustomerId);
                if (whatsappSubscription != null)
                {
                    whatsappSubscription.Enabled = cust.IsWhatsappNotification;
                    whatsappSubscription.LastUpdatedBy = cust.AddedBy;
                    whatsappSubscription.LastUpdatedDate = DateTime.Now;
                }
                else
                {
                    whatsappSubscription = new WhatsAppSubscriptionCustomerTable
                    {
                        CustomerId = CustomerId,
                        Enabled = cust.IsWhatsappNotification,
                        AddedBy = cust.AddedBy,
                        AddedDate = DateTime.Now,
                    };
                    db.WhatsAppSubscriptionCustomerTables.Add(whatsappSubscription);
                }
            }
            catch (Exception ex)
            {

            }
        }
        public ApiFunctionResponseVm DeleteCustomer(CustomerMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        CustomerMaster item = db.CustomerMasters.FirstOrDefault(x => x.CustomerId == param.CustomerId);
                        if (item == null)
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Customer not found.");
                        }

                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;

                        var emailSubscriptions = db.EmailSubscriptionCustomerTables.Where(e => e.CustomerId == param.CustomerId);
                        if (emailSubscriptions != null)
                        {
                            foreach (var emailSubscription in emailSubscriptions)
                            {
                                emailSubscription.Enabled = false;
                                emailSubscription.LastUpdatedBy = param.DisabledBy;
                                emailSubscription.LastUpdatedDate = System.DateTime.Now;
                            }
                        }

                        var whatsappSubscriptions = db.WhatsAppSubscriptionCustomerTables.Where(w => w.CustomerId == param.CustomerId);
                        if (whatsappSubscriptions != null)
                        {
                            foreach (var whatsappSubscription in whatsappSubscriptions)
                            {
                                whatsappSubscription.Enabled = false;
                                whatsappSubscription.LastUpdatedBy = param.DisabledBy;
                                whatsappSubscription.LastUpdatedDate = System.DateTime.Now;
                            }
                        }

                        // Disable customer notification groups
                        var customerNotificationGroups = db.NotificationGroupsTables
                            .Where(ng => ng.UserType == "Customer" && ng.UserMasterId == param.CustomerId && ng.Disabled != true);
                        foreach (var notificationGroup in customerNotificationGroups)
                        {
                            notificationGroup.Disabled = true;
                            notificationGroup.DisabledBy = param.DisabledBy;
                            notificationGroup.DisabledDate = System.DateTime.Now;
                        }

                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occurred. Please contact the administrator. " + ex);
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// Save customer notification subscriptions to NotificationGroupsTable
        /// </summary>
        /// <param name="db">Database context</param>
        /// <param name="customerId">Customer ID</param>
        /// <param name="cust">Customer view model with notification subscriptions</param>
        private void SaveCustomerNotificationSubscriptions(Models.pmsdbContext db, long customerId, CustomerMasterVm cust)
        {
            try
            {
                // Remove existing customer notification groups
                var existingNotifications = db.NotificationGroupsTables
                    .Where(x => x.UserType == "Customer" && x.UserMasterId == customerId && x.Disabled != true)
                    .ToList();

                foreach (var existing in existingNotifications)
                {
                    existing.Disabled = true;
                    existing.DisabledBy = cust.AddedBy;
                    existing.DisabledDate = DateTime.Now;
                }

                // Add new notification subscriptions if WhatsApp is enabled and subscriptions exist
                if (cust.IsWhatsappNotification && cust.NotificationSubscriptions?.Any() == true)
                {
                    foreach (var subscription in cust.NotificationSubscriptions)
                    {
                        // Get configuration details
                        var config = db.NotificationTemplateConfigurationTables
                            .FirstOrDefault(c => c.ConfigurationId == subscription.ConfigurationId);

                        if (config != null)
                        {
                            var notificationGroup = new NotificationGroupsTable
                            {
                                UserType = "Customer",
                                UserMasterId = customerId,
                                ConfigurationId = subscription.ConfigurationId,
                                NotificationType = config.NotificationType,
                                ReportName = config.SubType,
                                Name = cust.CustomerName,
                                Email = cust.Email,
                                MobileNumber = cust.CustomerContactNumber,
                                IsWhatsAppNotificationEnabled = true,
                                EnableToEmail = cust.IsEmailNotification,
                                WhatsAppTemplateMasterId = config.WhatsAppTemplateMasterId,

                                // Scheduling configuration
                                CronScheduleExpression = subscription.CronScheduleExpression ?? "0 10 * * *",
                                UseDefaultSchedule = subscription.UseDefaultSchedule,
                                PreferredNotificationTime = subscription.PreferredNotificationTime ?? TimeSpan.FromHours(10),
                                NotificationFrequency = subscription.ScheduleDescription ?? "Daily at 10:00 AM IST",
                                TimeZone = "Asia/Kolkata",

                                // Set trigger type based on configuration as JSON array
                                TriggerType = CreateTriggerTypeJson(config),

                                // Audit fields
                                AddedBy = cust.AddedBy,
                                AddedDate = DateTime.Now,
                                Disabled = false
                            };

                            db.NotificationGroupsTables.Add(notificationGroup);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't fail customer save
                Console.WriteLine($"Error saving customer notification subscriptions: {ex.Message}");
                // In production, use proper logging framework
                // _logger.LogError(ex, "Error saving customer notification subscriptions for customer {CustomerId}", customerId);
            }
        }

        /// <summary>
        /// Create trigger type JSON string based on notification configuration
        /// </summary>
        /// <param name="config">Notification template configuration</param>
        /// <returns>JSON string representing trigger types</returns>
        private static string CreateTriggerTypeJson(NotificationTemplateConfigurationTable config)
        {
            var triggerTypes = new List<string>();

            // Determine trigger types based on configuration
            if (config.SchedulingRequired == true)
            {
                // If scheduling is required, this is primarily a scheduled notification
                triggerTypes.Add("Scheduled");

                // But it might also support on-demand triggers for immediate execution
                if (config.OnDemandAllowed == true)
                {
                    triggerTypes.Add("OnDemand");
                }
            }
            else
            {
                // If scheduling is not required, this is primarily an event-driven notification
                if (config.EventAllowed == true)
                {
                    triggerTypes.Add("Event");
                }

                // It might also support on-demand triggers
                if (config.OnDemandAllowed == true)
                {
                    triggerTypes.Add("OnDemand");
                }

                // It might also support optional scheduling
                if (config.SchedulingAllowed == true)
                {
                    triggerTypes.Add("Scheduled");
                }
            }

            // Fallback to ensure we always have at least one trigger type
            if (!triggerTypes.Any())
            {
                triggerTypes.Add(config.SchedulingRequired == true ? "Scheduled" : "Event");
            }

            // Serialize to JSON array format
            return JsonConvert.SerializeObject(triggerTypes);
        }

    }
}
