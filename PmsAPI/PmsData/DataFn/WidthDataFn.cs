﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class WidthDataFn
    {
        public List<WidthMasterVm> GetAllWidthData()
        {
            List<WidthMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.WidthMasters
                       where a.Disabled != true
                       select new WidthMasterVm
                       {
                           WidthId = a.WidthId,
                           WidthNumber = a.WidthNumber,
                           Unit = a.Unit,
                           Description = a.Description,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderByDescending(x => x.WidthId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateWidth(WidthMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                WidthMaster res = new WidthMaster();
                if (br.WidthId == 0)
                {

                    res.WidthNumber = br.WidthNumber;
                    res.Unit = br.Unit;
                    res.Description = br.Description;
                    res.AddedBy = br.AddedBy;
                    res.AddedDate = System.DateTime.Now;
                    db.WidthMasters.Add(res);
                }
                else
                {
                    res = db.WidthMasters.Where(x => x.WidthId == br.WidthId).FirstOrDefault();
                    if (res != null)
                    {
                        res.WidthNumber = br.WidthNumber;
                        res.Unit = br.Unit;
                        res.Description = br.Description;
                        res.AddedBy = br.AddedBy;
                        res.AddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DeleteWidth(WidthMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        WidthMaster item = db.WidthMasters.FirstOrDefault(x => x.WidthId == param.WidthId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
