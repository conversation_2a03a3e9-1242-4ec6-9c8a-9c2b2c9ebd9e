using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using System.IO;
using PmsData.Adapters.PdfAdapters;
using PmsCore.PDFGeneration.Interfaces;

namespace PmsData.DataFn
{
    public class PurchaseOrderDataFn
    {
        private readonly IPdfService _pdfService;

        public GlobalDataEntity GlobalData;
        public PurchaseOrderDataFn(GlobalDataEntity gd, IPdfService pdfService)
        {
            GlobalData = gd;
            _pdfService = pdfService;
        }
        public ApiFunctionResponseVm AddPurchaseOrder(PurchaseOrderVm po)
        {
            string povalue;
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var (financialYearStart, financialYearEnd) = CommonFunctions.GetFinancialYearDates();

                var PreviousSaleOrder = db.PurchaseOrderTables
                                  .Where(order => order.PocreationDate.Value.Date >= financialYearStart && order.PocreationDate.Value.Date <= financialYearEnd)
                                  .OrderByDescending(order => order.PocreationDate)
                                  .Select(order => new { order.Poid, order.Ponumber })
                                  .FirstOrDefault();

                string newgrn;
                var gw = db.Grnmasters.OrderByDescending(x => x.AddedDate).FirstOrDefault();
                if (gw == null)
                {
                    newgrn = CommonFunctions.GRNFormat + "1";
                }
                else
                {
                    newgrn = CommonFunctions.GRNFormat + (gw.Grnid + 1);
                }
                Grnmaster grn = new Grnmaster();
                grn.Grn = newgrn;
                grn.AddedBy = GlobalData.loggedInUser;
                grn.AddedDate = System.DateTime.Now;
                db.Grnmasters.Add(grn);
                db.SaveChanges();
                //int poflagno = db.PurchaseOrderTables.Where(x => x.AddedDate.Value.Date == System.DateTime.Now.Date).Count() + 101;
                PurchaseOrderTable res = new PurchaseOrderTable();
                var newdate = DateTime.Now;
                //res.Ponumber = System.DateTime.Now.Year + "-" + System.DateTime.Now.Month + "/" + poflagno;
                povalue = res.Ponumber;
                res.PaymentTermId = po.PaymentTermId;
                res.DeliveryTermId = po.DeliveryTermId;
                res.PocreationDate = po.PocreationDate;
                res.PototalAmount = po.PototalAmount;
                res.SupplierId = po.SupplierId;
                res.TransportId = po.TransportId;
                res.Reference = po.Reference;
                res.DeliveryDate = po.DeliveryDate;
                res.Grn = newgrn;
                res.IsPocomplete = false;
                res.AddedBy = GlobalData.loggedInUser;
                res.AddedDate = newdate;
                res.Status = PmsCommon.PMSPurchaseOrderStatus.New;
                res.Remarks = po.Remarks;
                res.ContactPersonUserId = po.ContactPersonUserId;
                res.DepartmentId = po.DepartmentId;
                res.Potype = po.POType;

                db.PurchaseOrderTables.Add(res);
                db.SaveChanges();
                var yrSuf = (po.PocreationDate.Value.Month >= 4 ? po.PocreationDate.Value.Year + 1 : po.PocreationDate.Value.Year);
                var yrPre = (po.PocreationDate.Value.Month < 4 ? po.PocreationDate.Value.Year - 1 : po.PocreationDate.Value.Year);
                var uniqueSeq = CommonFunctions.FinancialYearUniqueNumber(res.Poid, PreviousSaleOrder?.Poid, PreviousSaleOrder?.Ponumber, false);
                res.Ponumber = yrPre + "-" + yrSuf + "/" + uniqueSeq;

                db.SaveChanges();
                foreach (var item in po.PurchaseOrderProduct)
                {
                    PurchaseOrderProductTable spt = new PurchaseOrderProductTable();
                    spt.Poid = res.Poid;
                    spt.ProductId = item.ProductId;
                    spt.Unit = item.Unit;
                    spt.Rate = item.Rate;
                    spt.Quantity = item.Quantity;
                    spt.Amount = item.Amount;
                    spt.Grade = item.Grade;
                    spt.Igst = item.Igst;
                    spt.Currency = item.Currency;
                    spt.ProductQuality = po.POType == "IMPORT" ? "IMPORTED" : "DOMESTIC";
                    spt.ImportCode = item.ImportCode?.Trim();
                    db.PurchaseOrderProductTables.Add(spt);
                }
                db.SaveChanges();
                db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                {
                    Poid = res.Poid,
                    Status = PMSPurchaseOrderStatus.New,
                    Remark = po.Remarks,
                    AddedBy = GlobalData.loggedInUser,
                    AddedDate = newdate
                });
                db.SaveChanges();
                if (po.Demand != null)
                {
                    foreach (var dnd in po.Demand)
                    {
                        DemandTable dt = db.DemandTables.FirstOrDefault(x => x.DemandId == dnd.DemandId);
                        dt.Poid = res.Poid;
                        dt.Status = PmsCommon.PMSDemandtatus.POCreated;

                        db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                        {
                            Poid = res.Poid,
                            Status = PMSPurchaseOrderStatus.DemandReceived,
                            Remark = "For Product Name: " + db.ProductMasters.Find(dt.ProductId).ProductName,
                            AddedBy = dt.AddedBy,
                            AddedDate = dt.AddedDate.Value
                        });
                    }
                    db.SaveChanges();
                }
                if (po.FabricStock != null)
                {
                    foreach (var dnd in po.FabricStock)
                    {
                        KnittingFabricWeightInputTable kf = db.KnittingFabricWeightInputTables.FirstOrDefault(x => x.KnittingFabricWeightInputId == dnd.KnittingFabricWeightInputId);
                        kf.Poid = res.Poid;
                        kf.IsStockAccepted = true;
                    }
                    db.SaveChanges();
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, povalue);
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                throw;
            }
        }

        public List<PurchaseOrderListVm> GetAllPurchaseOrders()
        {
            List<PurchaseOrderListVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                var highValuePOApprovalLimit = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "HighValuePOApprovalLimit").ConfigValue;
                res = (from a in db.PurchaseOrderTables
                       select new PurchaseOrderListVm
                       {
                           Poid = a.Poid,
                           Ponumber = a.Ponumber,
                           SupplierId = a.SupplierId,
                           Grn = a.Grn,
                           IsPocomplete = a.IsPocomplete,
                           Status = a.Status,
                           IsKnittingStock = db.KnittingFabricWeightInputTables.Any(x => x.Poid == a.Poid)
                       }).OrderByDescending(x => x.Poid).ToList();
            }
            return res;
        }

        public List<PurchaseOrderVm> GetAllPurchaseOrdersForList()
        {
            List<PurchaseOrderVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                var highValuePOApprovalLimit = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "HighValuePOApprovalLimit").ConfigValue;
                res = (from a in db.PurchaseOrderTables
                       join pt in db.PaymentTermMasters on a.PaymentTermId equals pt.PaymentTermId
                       join dt in db.DeliveryTermMasters on a.DeliveryTermId equals dt.DeliveryTermId
                       join s in db.SupplierMasters on a.SupplierId equals s.SupplierId
                       join d in db.DeptMasters on a.DepartmentId equals d.DeptId into dd
                       from d in dd.DefaultIfEmpty()
                       join tr in db.TransportCompanyMasters on a.TransportId equals tr.TransportId into trr
                       from tr in trr.DefaultIfEmpty()
                       join umab in db.UserMasters on a.AddedBy equals umab.Email into umabd
                       from umab in umabd.DefaultIfEmpty()
                       join usac in db.UserMasters on a.ActionBy equals usac.Email into usacr
                       from usac in usacr.DefaultIfEmpty()
                       join usap in db.UserMasters on a.ApprovedBy equals usap.Email into usapr
                       from usap in usapr.DefaultIfEmpty()
                       select new PurchaseOrderVm
                       {
                           Poid = a.Poid,
                           Ponumber = a.Ponumber,
                           PaymentTermId = a.PaymentTermId,
                           PaymentTerm = pt.PaymentTerm,
                           DeliveryTermId = a.DeliveryTermId,
                           DeliveryTerm = dt.DeliveryTerm,
                           PocreationDate = a.PocreationDate,
                           PototalAmount = a.PototalAmount,
                           SupplierId = a.SupplierId,
                           SupplierName = s.SupplierName,
                           SupplierEmail = s.Email,
                           TransportId = a.TransportId,
                           TransportCompanyName = tr.TransportCompanyName,
                           Reference = a.Reference,
                           Grn = a.Grn,
                           IsPocomplete = a.IsPocomplete,
                           AddedBy = new UserMasterVm
                           {
                               Name = umab.Name,
                               Email = umab.Email
                           },
                           AddedDate = a.AddedDate,
                           DeliveryDate = a.DeliveryDate,
                           Status = a.Status,
                           Remarks = a.Remarks,
                           ContactPersonUserId = a.ContactPersonUserId,
                           DepartmentName = d.DeptName,
                           POType = a.Potype,
                           PurchaseOrderProduct = (from p in db.PurchaseOrderProductTables
                                                   join pr in db.ProductMasters on p.ProductId equals pr.ProductId
                                                   where p.Poid == a.Poid
                                                   select new PurchaseOrderProductVm
                                                   {
                                                       PoproductId = p.PoproductId,
                                                       ProductId = p.ProductId,
                                                       ProductName = pr.ProductName,
                                                   }).ToList(),
                           ActionPersonName = usac.Name,
                           ActionPersonEmail = usac.Email,
                           ApprovedDate = a.ApprovedDate,
                           ApprovedPersonEmail = usap.Email,
                           ApprovedPersonName = usap.Name,
                           POHighValue = highValuePOApprovalLimit,
                           IsInvoiceAttached = db.InvoiceMasters.Any(x => x.Poid == a.Poid),

                       }).OrderByDescending(x => x.Poid).ToList();
            }
            return res;
        }
        public List<PurchaseOrderVm> GetAllPurchaseOrdersWithFilters(PurchaseReportRequestVm filter)
        {
            List<PurchaseOrderVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                var highValuePOApprovalLimit = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "HighValuePOApprovalLimit").ConfigValue;
                res = (from a in db.PurchaseOrderTables
                       join pt in db.PaymentTermMasters on a.PaymentTermId equals pt.PaymentTermId
                       join dt in db.DeliveryTermMasters on a.DeliveryTermId equals dt.DeliveryTermId
                       join s in db.SupplierMasters on a.SupplierId equals s.SupplierId
                       join dm in db.DeptMasters on a.DepartmentId equals dm.DeptId into dd
                       from dm in dd.DefaultIfEmpty()
                       join tr in db.TransportCompanyMasters on a.TransportId equals tr.TransportId into trr
                       from tr in trr.DefaultIfEmpty()
                       join umab in db.UserMasters on a.AddedBy equals umab.Email into umabd
                       from umab in umabd.DefaultIfEmpty()
                       where
                        ((((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "podate") && (filter.DateFrom == null || a.PocreationDate >= filter.DateFrom))
                        && ((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "podate") && (filter.DateTo == null || a.PocreationDate <= filter.DateTo)))
                        || (((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "addeddate") && (filter.FromAddedDate == null || a.AddedDate >= filter.FromAddedDate))
                        && ((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "addeddate") && (filter.ToAddedDate == null || a.AddedDate <= filter.ToAddedDate)))
                        || (((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "approveddate") && (filter.FromApprovedDate == null || a.ApprovedDate >= filter.FromApprovedDate))
                        && ((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "approveddate") && (filter.ToApprovedDate == null || a.ApprovedDate <= filter.ToApprovedDate))))
                        && (filter.SupplierId == 0 || filter.SupplierId == null || a.SupplierId == filter.SupplierId)
                        && (string.IsNullOrEmpty(filter.Status) || filter.Status == a.Status)
                        && (string.IsNullOrEmpty(filter.ActionBy) || filter.ActionBy == a.ActionBy)
                        && (string.IsNullOrEmpty(filter.ApprovedBy) || filter.ApprovedBy == a.ApprovedBy)
                        && (string.IsNullOrEmpty(filter.PONumber) || (a.Ponumber == filter.PONumber && a.Status != "New"))
                        && (string.IsNullOrEmpty(filter.POType) || filter.POType == a.Potype)
                        && (filter.DeliveryTermId == 0 || filter.DeliveryTermId == a.DeliveryTermId)
                        && (filter.PaymentTermId == 0 || filter.PaymentTermId == a.PaymentTermId)
                        // Apply delay filtering if specified
                        && (string.IsNullOrEmpty(filter.DelayType) ||
                            (filter.DelayType.ToLowerInvariant() == "payment" &&
                             a.ApprovedDate.HasValue &&
                             DateTime.Now > a.ApprovedDate.Value.AddDays(pt.NumberOfDays ?? 0) &&
                             !db.PurchaseOrderTimelineTables.Any(timeline =>
                                 timeline.Poid == a.Poid
                                 && !string.IsNullOrEmpty(timeline.Status)
                                 && timeline.Status == PmsCommon.PMSPurchaseOrderStatus.FullPaymentCompleted)) ||
                            (filter.DelayType.ToLowerInvariant() == "delivery" &&
                             !string.IsNullOrEmpty(a.Status) &&
                             a.Status.ToLower() != "complete" &&
                             a.ApprovedDate.HasValue &&
                             DateTime.Now > a.ApprovedDate.Value.AddDays(dt.NumberOfDays ?? 0)))
                       select new PurchaseOrderVm
                       {
                           Poid = a.Poid,
                           POType = a.Potype,
                           Ponumber = a.Ponumber,
                           PaymentTermId = a.PaymentTermId,
                           PaymentTerm = pt.PaymentTerm,
                           DeliveryTermId = a.DeliveryTermId,
                           DeliveryTerm = dt.DeliveryTerm,
                           DepartmentName = dm.DeptName,
                           PocreationDate = a.PocreationDate,
                           PototalAmount = a.PototalAmount,
                           SupplierId = a.SupplierId,
                           SupplierName = s.SupplierName,
                           TransportId = a.TransportId,
                           TransportCompanyName = tr.TransportCompanyName,
                           Reference = a.Reference,
                           Grn = a.Grn,
                           IsPocomplete = a.IsPocomplete,
                           AddedBy = new UserMasterVm
                           {
                               Name = umab.Name,
                           },
                           AddedDate = a.AddedDate,
                           DeliveryDate = a.DeliveryDate,
                           Status = a.Status,
                           Remarks = a.Remarks,
                           ContactPersonUserId = a.ContactPersonUserId,
                           ActionBy = (from us in db.UserMasters
                                       where us.Email.ToLower() == a.ActionBy.ToLower()
                                       select new UserMasterVm
                                       {
                                           UserId = us.UserId,
                                           Name = us.Name,
                                           Contact = us.Contact,
                                           Email = us.Email
                                       }).FirstOrDefault(),
                           ApprovedDate = a.ApprovedDate,
                           ApprovedBy = (from us in db.UserMasters
                                         where us.Email.ToLower() == a.ApprovedBy.ToLower()
                                         select new UserMasterVm
                                         {
                                             UserId = us.UserId,
                                             Name = us.Name,
                                             Contact = us.Contact,
                                             Email = us.Email
                                         }).FirstOrDefault(),
                           POHighValue = highValuePOApprovalLimit,
                           IsInvoiceAttached = db.InvoiceMasters.Any(x => x.Poid == a.Poid),
                       }).OrderByDescending(x => x.Poid).ToList();
                var polist = res.Select(x => x.Poid).Distinct().ToList();
                var data = (from p in db.PurchaseOrderProductTables
                            join pr in db.ProductMasters on p.ProductId equals pr.ProductId
                            join prc in db.ProductCategoryMasters on pr.ProductCategoryId equals prc.ProductCategoryId into ps
                            from prc in ps.DefaultIfEmpty()
                            join pf in db.ProductFirstSubCategoryMasters on pr.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on pr.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            where polist.Contains(p.Poid.Value)
                            && (filter.ProductId == 0 || p.ProductId == filter.ProductId)
                            && (string.IsNullOrEmpty(filter.ProductType) || prc.ProductType == filter.ProductType)
                            && (filter.ProductCategoryId == 0 || prc.ProductCategoryId == filter.ProductCategoryId)
                            && (filter.ProductFirstSubCategoryId == 0 || pf.ProductFirstSubCategoryId == filter.ProductFirstSubCategoryId)
                            && (filter.ProductSecSubCategoryId == 0 || psc.ProductSecSubCategoryId == filter.ProductSecSubCategoryId)
                            && (string.IsNullOrEmpty(filter.Unit) || p.Unit == filter.Unit)
                            && (string.IsNullOrEmpty(filter.ImportCode) || p.ImportCode == filter.ImportCode)
                            select new PurchaseOrderProductVm
                            {
                                Poid = p.Poid,
                                PoproductId = p.PoproductId,
                                ProductId = p.ProductId,
                                ProductName = pr.ProductName,
                                Currency = p.Currency

                            }).ToList();

                foreach (var item in res)
                {
                    item.PurchaseOrderProduct = data.Where(x => x.Poid == item.Poid).ToList();
                }
            }
            return res.Where(x => x.PurchaseOrderProduct.Count > 0).ToList();
        }

        public PurchaseOrderVm GetPurchaseOrderById(long id)
        {
            PurchaseOrderVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                var highValuePOApprovalLimit = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "HighValuePOApprovalLimit").ConfigValue;
                res = (from a in db.PurchaseOrderTables
                       join pt in db.PaymentTermMasters on a.PaymentTermId equals pt.PaymentTermId
                       join dt in db.DeliveryTermMasters on a.DeliveryTermId equals dt.DeliveryTermId
                       join s in db.SupplierMasters on a.SupplierId equals s.SupplierId
                       join d in db.DeptMasters on a.DepartmentId equals d.DeptId into dd
                       from d in dd.DefaultIfEmpty()
                       join tr in db.TransportCompanyMasters on a.TransportId equals tr.TransportId into trr
                       from tr in trr.DefaultIfEmpty()
                       where a.Poid == id
                       select new PurchaseOrderVm
                       {
                           Poid = a.Poid,
                           Ponumber = a.Ponumber,
                           PaymentTermId = a.PaymentTermId,
                           PaymentTerm = pt.PaymentTerm,
                           DeliveryTermId = a.DeliveryTermId,
                           DeliveryTerm = dt.DeliveryTerm,
                           PocreationDate = a.PocreationDate,
                           PototalAmount = a.PototalAmount,
                           SupplierId = a.SupplierId,
                           SupplierName = s.SupplierName,
                           Supplier = new SupplierMasterVm
                           {
                               SupplierId = s.SupplierId,
                               SupplierName = s.SupplierName,
                               SupplierContactNumber = s.SupplierContactNumber,
                               Address = s.Address,
                               Email = s.Email,
                               Gst = s.Gst
                           },
                           SupplierEmail = s.Email,
                           TransportId = a.TransportId,
                           TransportCompanyName = tr.TransportCompanyName,
                           Reference = a.Reference,
                           Grn = a.Grn,
                           IsPocomplete = a.IsPocomplete,
                           POType = a.Potype,
                           AddedBy = (from us in db.UserMasters
                                      where us.Email.ToLower() == a.AddedBy.ToLower()
                                      select new UserMasterVm
                                      {
                                          UserId = us.UserId,
                                          Name = us.Name,
                                          Contact = us.Contact,
                                          Email = us.Email
                                      }).FirstOrDefault(),
                           AddedDate = a.AddedDate,
                           DeliveryDate = a.DeliveryDate,
                           Status = a.Status,
                           Remarks = a.Remarks,
                           ContactPersonUserId = a.ContactPersonUserId,
                           DepartmentId = d.DeptId,
                           DepartmentName = d.DeptName,
                           ActionBy = (from us in db.UserMasters
                                       where us.Email.ToLower() == a.ActionBy.ToLower()
                                       select new UserMasterVm
                                       {
                                           UserId = us.UserId,
                                           Name = us.Name,
                                           Contact = us.Contact,
                                           Email = us.Email
                                       }).FirstOrDefault(),
                           ApprovedDate = a.ApprovedDate,
                           ApprovedBy = (from us in db.UserMasters
                                         where us.Email.ToLower() == a.ApprovedBy.ToLower()
                                         select new UserMasterVm
                                         {
                                             UserId = us.UserId,
                                             Name = us.Name,
                                             Contact = us.Contact,
                                             Email = us.Email
                                         }).FirstOrDefault(),
                           IsInvoiceAttached = db.InvoiceMasters.Any(x => x.Poid == a.Poid),
                           UserDetails = (from us in db.UserMasters
                                          where us.UserId == a.ContactPersonUserId
                                          select new UserMasterVm
                                          {
                                              UserId = us.UserId,
                                              Name = us.Name,
                                              Contact = us.Contact,
                                              Email = us.Email
                                          }).FirstOrDefault(),
                           PurchaseOrderProduct = (from p in db.PurchaseOrderProductTables
                                                   join pr in db.ProductMasters on p.ProductId equals pr.ProductId
                                                   join prc in db.ProductCategoryMasters on pr.ProductCategoryId equals prc.ProductCategoryId into ps
                                                   from prc in ps.DefaultIfEmpty()
                                                   join pf in db.ProductFirstSubCategoryMasters on pr.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                                                   from pf in psf.DefaultIfEmpty()
                                                   join psc in db.ProductSecSubCategoryMasters on pr.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                                                   from psc in pssc.DefaultIfEmpty()
                                                   where p.Poid == a.Poid
                                                   select new PurchaseOrderProductVm
                                                   {
                                                       PoproductId = p.PoproductId,
                                                       ProductId = p.ProductId,
                                                       ProductName = pr.ProductName,
                                                       Unit = p.Unit,
                                                       Rate = p.Rate,
                                                       Currency = p.Currency,
                                                       Quantity = p.Quantity,
                                                       Amount = p.Amount,
                                                       Grade = p.Grade,
                                                       Igst = p.Igst,
                                                       ProductQuality = p.ProductQuality,
                                                       ImportCode = p.ImportCode,
                                                       ProductCategoryId = pr.ProductCategoryId,
                                                       ProductCategory = prc.ProductCategory,
                                                       ProductFirstSubCategoryId = pr.ProductFirstSubCategoryId,
                                                       ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                                       ProductSecSubCategoryId = pr.ProductSecSubCategoryId,
                                                       ProductSecSubCategory = psc.ProductSecSubCategory,
                                                       RecievedQuantity = (from ps in db.StockProductTables
                                                                           join s in db.StockMasters on ps.StockId equals s.StockId
                                                                           join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                                                                           where i.Poid == a.Poid && ps.ProductId == p.ProductId
                                                                           select ps.ReceivedQuantity).Sum(),
                                                   }).ToList(),
                           Demand = (from br in db.DemandTables
                                     join pr in db.ProductMasters on br.ProductId equals pr.ProductId
                                     join st in db.StoreMasters on br.StoreId equals st.StoreId
                                     where br.Poid == a.Poid
                                     select new DemandTableVm
                                     {
                                         DemandId = br.DemandId,
                                         StoreId = br.StoreId,
                                         StoreName = st.StoreName,
                                         ProductId = br.ProductId,
                                         ProductName = pr.ProductName,
                                         Quantity = br.Quantity,
                                         Unit = br.Unit,
                                         Status = br.Status,
                                         Poid = br.Poid,
                                         AddedBy = br.AddedBy,
                                         AddedDate = br.AddedDate,
                                     }).OrderByDescending(x => x.DemandId).ToList()
                       }).FirstOrDefault();

                var totalAmountDecimal = decimal.Round(Convert.ToDecimal(res.PototalAmount), 2);
                res.PototalAmount = totalAmountDecimal.ToString();
                res.POHighValue = highValuePOApprovalLimit;
                res.TotalInWords = CommonFunctions.ConvertToIndianWords(decimal.Parse(res.PototalAmount));

                res.PototalTax = res.PurchaseOrderProduct.Sum(x => decimal.Parse(x.Igst));
                res.Pograndtotal = res.PurchaseOrderProduct.Sum(x => decimal.Parse(x.Amount.ToString()) + decimal.Parse(x.Igst));
                // foreach (var item in res.PurchaseOrderProduct)
                // {
                //     var receivedQuantitySum = (from ps in db.StockProductTables
                //                                join s in db.StockMasters on ps.StockId equals s.StockId
                //                                join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                //                                where i.Poid == id && ps.ProductId == item.ProductId
                //                                select ps.ReceivedQuantity).Sum();

                //     item.RecievedQuantity = receivedQuantitySum > 0 ? receivedQuantitySum :
                //                             (from ps in db.StockProductTables
                //                              join s in db.StockMasters on ps.StockId equals s.StockId
                //                              join i in db.InvoiceMasters on s.InvoiceId equals i.InvoiceId
                //                              join ist in (from ist in db.IssueProductTables
                //                                           where ist.ToStore == ist.FromStore
                //                                           select new { ist.ToNewStockProductId, ist.ToStore, ist.FromStore }).Distinct()
                //                                          .Union(from ist in db.IssueProductTables
                //                                                 where ist.ToStore != ist.FromStore
                //                                                 select new { ist.ToNewStockProductId, ist.ToStore, ist.FromStore })
                //                              on ps.StockProductId equals ist.ToNewStockProductId
                //                              where i.Poid == id && ps.ProductId == item.ProductId
                //                              select ps.Quantity).Sum();
                // }
            }
            return res;
        }
        public ApiFunctionResponseVm UpdatePurchaseOrder(PurchaseOrderVm po)
        {
            using (var db = new Models.pmsdbContext())
            {
                PurchaseOrderTable res = db.PurchaseOrderTables.FirstOrDefault(x => x.Poid == po.Poid);
                res.PaymentTermId = po.PaymentTermId;
                res.DeliveryTermId = po.DeliveryTermId;
                res.PocreationDate = po.PocreationDate;
                res.PototalAmount = po.PototalAmount;
                res.SupplierId = po.SupplierId;
                res.TransportId = po.TransportId;
                res.Reference = po.Reference;
                res.DeliveryDate = po.DeliveryDate;
                res.Remarks = po.Remarks;
                // res.AddedBy = GlobalData.loggedInUser;
                // res.AddedDate = System.DateTime.Now;
                res.ContactPersonUserId = po.ContactPersonUserId;
                res.Status = PmsCommon.PMSPurchaseOrderStatus.Revised;
                res.DepartmentId = po.DepartmentId;
                res.Potype = po.POType;
                //db.PurchaseOrderTables.Add(res);
                db.SaveChanges();
                List<PurchaseOrderProductTable> stAllList = db.PurchaseOrderProductTables.Where(x => x.Poid == po.Poid).ToList();
                var deleteRecords = stAllList.Except(stAllList.Where(o => po.PurchaseOrderProduct.Select(s => s.PoproductId).ToList().Contains(o.PoproductId))).ToList();
                var AddRecords = po.PurchaseOrderProduct.Where(x => x.PoproductId == 0).ToList();
                if (AddRecords.Count > 0)
                {
                    foreach (var item in AddRecords)
                    {
                        PurchaseOrderProductTable spt = new PurchaseOrderProductTable();
                        spt.Poid = res.Poid;
                        spt.ProductId = item.ProductId;
                        spt.Unit = item.Unit;
                        spt.Rate = item.Rate;
                        spt.Currency = item.Currency;
                        spt.Quantity = item.Quantity;
                        spt.Amount = item.Amount;
                        spt.Grade = item.Grade;
                        spt.Igst = item.Igst;
                        spt.ProductQuality = po.POType == "IMPORT" ? "IMPORTED" : "DOMESTIC";
                        spt.ImportCode = item.ImportCode?.Trim();
                        db.PurchaseOrderProductTables.Add(spt);
                    }
                }
                var resrec = po.PurchaseOrderProduct.Where(x => x.PoproductId > 0).ToList();
                if (resrec.Count > 0)
                {
                    foreach (var item in AddRecords)
                    {
                        PurchaseOrderProductTable spt = db.PurchaseOrderProductTables.Where(x => x.PoproductId == item.PoproductId).FirstOrDefault();
                        spt.Poid = res.Poid;
                        spt.ProductId = item.ProductId;
                        spt.Unit = item.Unit;
                        spt.Rate = item.Rate;
                        spt.Currency = item.Currency;
                        spt.Quantity = item.Quantity;
                        spt.Amount = item.Amount;
                        spt.Grade = item.Grade;
                        spt.Igst = item.Igst;
                        spt.ProductQuality = po.POType == "IMPORT" ? "IMPORTED" : "DOMESTIC";
                        spt.ImportCode = item.ImportCode?.Trim();
                    }
                }
                db.SaveChanges();
                if (deleteRecords.Count > 0)
                {
                    foreach (var item in deleteRecords)
                    {
                        var dr = db.PurchaseOrderProductTables.SingleOrDefault(x => x.PoproductId == item.PoproductId);
                        if (dr != null)
                            db.PurchaseOrderProductTables.Remove(dr);
                    }
                }
                if (deleteRecords.Count > 0)
                    db.PurchaseOrderProductTables.RemoveRange(deleteRecords);

                db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                {
                    Poid = po.Poid,
                    Status = PMSPurchaseOrderStatus.Revised,
                    Remark = po.Remarks,
                    AddedBy = GlobalData.loggedInUser,
                    AddedDate = DateTime.Now
                });
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm CancelPurchaseOrder(long poid)
        {
            using (var db = new Models.pmsdbContext())
            {
                var dat = db.PurchaseOrderTables.FirstOrDefault(x => x.Poid == poid);
                if (dat != null && dat.IsPocomplete != true)
                {
                    dat.Status = PmsCommon.PMSPurchaseOrderStatus.Cancelled;
                    dat.ActionBy = GlobalData.loggedInUser;
                    //dat.AddedDate = DateTime.Now;
                    db.SaveChanges();
                    db.AuditTables.Add(new AuditTable
                    {
                        RecId = poid,
                        TableName = "PurchaseOrderTable",
                        EntityName = "PurchaseOrderTable",
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = System.DateTime.Now,
                    });
                    db.SaveChanges();
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Purchase order cannot be cancelled");
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public ApiFunctionResponseVm ApprovePurchaseOrder(long poid)
        {
            using (var db = new Models.pmsdbContext())
            {
                var dat = db.PurchaseOrderTables.FirstOrDefault(x => x.Poid == poid);
                if (dat != null && dat.IsPocomplete != true)
                {
                    var highValuePOApprovalLimit = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "HighValuePOApprovalLimit").ConfigValue;
                    var responsibilityCode = "POApproval";
                    if (Convert.ToDecimal(dat.PototalAmount) > Convert.ToDecimal(highValuePOApprovalLimit))
                    {
                        responsibilityCode = "HighValuePOApproval";
                    }
                    bool userinrole = (from userrole in db.UserRoleResponsibilityMappingTables
                                       join resp in db.ResponsibilityMasters on userrole.ResponsibilityId equals resp.ResponsibilityId
                                       join role in db.UserRoleMasters on userrole.UserRoleId equals role.UserRoleId
                                       join user in db.UsernameUserRoleMappingTables on role.UserRoleId equals user.UserRoleId
                                       where user.Username == GlobalData.loggedInUser && resp.ResponsibilityCode == responsibilityCode
                                       select user).Any();
                    if (userinrole)
                    {

                        dat.Status = PmsCommon.PMSPurchaseOrderStatus.Approved;
                        dat.ApprovedBy = GlobalData.loggedInUser;
                        dat.ApprovedDate = DateTime.Now;
                        db.SaveChanges();
                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = poid,
                            TableName = "PurchaseOrderTable",
                            EntityName = "PurchaseOrderTable",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                        });
                        db.SaveChanges();
                        dat.Status = PmsCommon.PMSPurchaseOrderStatus.Active;
                        dat.ActionBy = GlobalData.loggedInUser;
                        dat.AddedDate = DateTime.Now;
                        db.SaveChanges();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Purchase order is Approved");

                    }
                    else
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "User is not authorized to approve");
                    }
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order is not valid");
                }
            }
        }
        public ApiFunctionResponseVm PurchaseOrderEventActions(POActionVm action)
        {
            using var db = new Models.pmsdbContext();

            var dat = db.PurchaseOrderTables.FirstOrDefault(x => x.Poid == action.Poid);
            if (action.Status == PMSPurchaseOrderStatus.Approved)
            {
                if (dat != null && dat.IsPocomplete != true)
                {
                    var highValuePOApprovalLimit = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "HighValuePOApprovalLimit").ConfigValue;
                    var responsibilityCode = "POApproval";
                    if (Convert.ToDecimal(dat.PototalAmount) > Convert.ToDecimal(highValuePOApprovalLimit))
                    {
                        responsibilityCode = "HighValuePOApproval";
                    }
                    bool userinrole = (from userrole in db.UserRoleResponsibilityMappingTables
                                       join resp in db.ResponsibilityMasters on userrole.ResponsibilityId equals resp.ResponsibilityId
                                       join role in db.UserRoleMasters on userrole.UserRoleId equals role.UserRoleId
                                       join user in db.UsernameUserRoleMappingTables on role.UserRoleId equals user.UserRoleId
                                       where user.Username == GlobalData.loggedInUser && resp.ResponsibilityCode == responsibilityCode
                                       select user).Any();
                    var approvedate = DateTime.Now;
                    if (userinrole)
                    {

                        // dat.Status = PmsCommon.PMSPurchaseOrderStatus.Approved;
                        // dat.Remarks = action.Remark;
                        dat.ApprovedBy = GlobalData.loggedInUser;
                        dat.ApprovedDate = approvedate;
                        db.SaveChanges();

                        db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                        {
                            Poid = action.Poid,
                            Status = PMSPurchaseOrderStatus.Approved,
                            Remark = action.Remark,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = approvedate
                        });

                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = action.Poid,
                            TableName = "PurchaseOrderTable",
                            EntityName = "PurchaseOrderTable",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = approvedate,
                        });
                        db.SaveChanges();
                        dat.Status = PmsCommon.PMSPurchaseOrderStatus.Active;
                        dat.ActionBy = GlobalData.loggedInUser;
                        dat.AddedDate = approvedate;
                        db.SaveChanges();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Purchase order is Approved");

                    }
                    else
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "User is not authorized to approve");
                    }
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order is not valid");
                }
            }
            else if (action.Status == PMSPurchaseOrderStatus.Complete)
            {
                if (dat != null && dat.IsPocomplete != true)
                {
                    dat.Status = PmsCommon.PMSPurchaseOrderStatus.Complete;
                    dat.ActionBy = GlobalData.loggedInUser;
                    dat.IsPocomplete = true;

                    db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                    {
                        Poid = action.Poid,
                        Status = PMSPurchaseOrderStatus.Complete,
                        Remark = "Manual PO Completion",
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now
                    });
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Purchase order is closed and marked completed.");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order is not valid");
                }
            }
            else if (action.Status == PMSPurchaseOrderStatus.ReOpen)
            {
                if (dat != null && dat.IsPocomplete == true)
                {
                    dat.Status = PmsCommon.PMSPurchaseOrderStatus.Active;
                    dat.IsPocomplete = false;

                    db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                    {
                        Poid = action.Poid,
                        Status = PMSPurchaseOrderStatus.ReOpen,
                        Remark = action.Remark,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now
                    });
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Purchase order is re-opened");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order is not valid");
                }
            }
            else if (action.Status == PMSPurchaseOrderStatus.Cancelled)
            {
                if (dat != null && dat.IsPocomplete != true)
                {
                    dat.Status = PmsCommon.PMSPurchaseOrderStatus.Cancelled;
                    db.SaveChanges();
                    var cancellDate = DateTime.Now;
                    db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                    {
                        Poid = action.Poid,
                        Status = PMSPurchaseOrderStatus.Cancelled,
                        Remark = action.Remark,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = cancellDate
                    });

                    db.AuditTables.Add(new AuditTable
                    {
                        RecId = action.Poid,
                        TableName = "PurchaseOrderTable",
                        EntityName = "PurchaseOrderTable",
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = cancellDate,
                    });
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Purchase order cancelled successfully");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order cannot be cancelled");
                }
            }
            else if (action.Status == PMSPurchaseOrderStatus.Blocked)
            {
                if (dat.IsPocomplete != true && (dat.Status == PMSPurchaseOrderStatus.New || dat.Status == PMSPurchaseOrderStatus.Revised))
                {
                    dat.Status = PmsCommon.PMSPurchaseOrderStatus.Blocked;
                    db.SaveChanges();
                    var blockedDate = DateTime.Now;
                    db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                    {
                        Poid = action.Poid,
                        Status = PMSPurchaseOrderStatus.Blocked,
                        Remark = action.Remark,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = blockedDate
                    });
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Purchase order is blocked now");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order can only be blocked before approval");
                }
            }
            else if (action.Status == PMSPurchaseOrderStatus.Unblock)
            {
                if (dat != null && dat.IsPocomplete != true && dat.Status == PMSPurchaseOrderStatus.Blocked)
                {
                    var checkpreviousstatus = db.PurchaseOrderTimelineTables.Where(x => x.Poid == action.Poid && x.Status == PMSPurchaseOrderStatus.Revised).ToList();
                    if (checkpreviousstatus.Count > 0)
                    {
                        dat.Status = PmsCommon.PMSPurchaseOrderStatus.Revised;
                    }
                    else
                    {
                        dat.Status = PmsCommon.PMSPurchaseOrderStatus.New;
                    }

                    db.SaveChanges();
                    var unblockDate = DateTime.Now;
                    db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                    {
                        Poid = action.Poid,
                        Status = PMSPurchaseOrderStatus.Unblock,
                        Remark = action.Remark,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = unblockDate
                    });
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Purchase order is un-blocked now");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order can cannot be un-blocked. Please check with administrator.");
                }
            }
            else if (action.Status == PMSPurchaseOrderStatus.InTransit)
            {
                if (dat != null && dat.IsPocomplete != true && dat.Status == PMSPurchaseOrderStatus.Active)
                {
                    // dat.Status = PmsCommon.PMSPurchaseOrderStatus.InTransit;
                    // db.SaveChanges();
                    //Implement Email and WhatsApp Notification to store department personnel.

                    var inTransitDate = DateTime.Now;
                    db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                    {
                        Poid = action.Poid,
                        Status = PMSPurchaseOrderStatus.InTransit,
                        Remark = action.Remark,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = inTransitDate
                    });
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Purchase order in transit is recorded and Store is notified");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Only Active Status Purchase order can be marked as in transit");
                }
            }
            else if (action.Status == PMSPurchaseOrderStatus.PartialPaymentCompleted)
            {
                if (dat.Status == PMSPurchaseOrderStatus.Active || dat.Status == PMSPurchaseOrderStatus.Complete)
                {
                    // dat.Status = PmsCommon.PMSPurchaseOrderStatus.InTransit;
                    // db.SaveChanges();
                    //Implement Email and WhatsApp Notification to purchase department personnel.

                    var ppdate = DateTime.Now;
                    db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                    {
                        Poid = action.Poid,
                        Status = PMSPurchaseOrderStatus.PartialPaymentCompleted,
                        Remark = action.Remark,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = ppdate
                    });
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Partial Payment against Purchase order No: " + dat.Ponumber + " is recorded successfully");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order is invalid");
                }
            }
            else if (action.Status == PMSPurchaseOrderStatus.FullPaymentCompleted)
            {
                if (dat.Status == PMSPurchaseOrderStatus.Active || dat.Status == PMSPurchaseOrderStatus.InTransit || dat.Status == PMSPurchaseOrderStatus.Complete)
                {
                    // dat.Status = PmsCommon.PMSPurchaseOrderStatus.InTransit;
                    // db.SaveChanges();
                    //Implement Email and WhatsApp Notification to purchase department personnel.

                    var ppdate = DateTime.Now;
                    db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                    {
                        Poid = action.Poid,
                        Status = PMSPurchaseOrderStatus.FullPaymentCompleted,
                        Remark = action.Remark,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = ppdate
                    });
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Full Payment against Purchase order No: " + dat.Ponumber + " is recorded successfully");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order is invalid");
                }
            }
            else if (action.Status == PMSPurchaseOrderStatus.StoreAcknowledged)
            {
                if (dat.Status == PMSPurchaseOrderStatus.InTransit)
                {
                    // dat.Status = PmsCommon.PMSPurchaseOrderStatus.InTransit;
                    // db.SaveChanges();

                    //Implement Email and WhatsApp Notification to purchase department personnel.

                    var ppdate = DateTime.Now;
                    db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                    {
                        Poid = action.Poid,
                        Status = PMSPurchaseOrderStatus.StoreAcknowledged,
                        Remark = action.Remark,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = ppdate
                    });
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order in transit is recorded and Store is notified");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Only Active Status Purchase order can be marked as in transit");
                }
            }
            else
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Purchase order status is not valid");
            }

        }

        public List<DemandTableVm> GetAllDemands()
        {
            List<DemandTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from br in db.DemandTables
                       join pr in db.ProductMasters on br.ProductId equals pr.ProductId
                       join po in db.PurchaseOrderTables on br.Poid equals po.Poid into pos
                       from por in pos.DefaultIfEmpty()
                       join st in db.StoreMasters on br.StoreId equals st.StoreId into sto
                       from stor in sto.DefaultIfEmpty()
                       join pcr in db.ProductCategoryMasters on pr.ProductCategoryId equals pcr.ProductCategoryId into ps
                       from pcr in ps.DefaultIfEmpty()
                       join pf in db.ProductFirstSubCategoryMasters on pr.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                       from pf in psf.DefaultIfEmpty()
                       join psc in db.ProductSecSubCategoryMasters on pr.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                       from psc in pssc.DefaultIfEmpty()
                       select new DemandTableVm
                       {
                           DemandId = br.DemandId,
                           StoreId = br.StoreId,
                           StoreName = stor.StoreName,
                           ProductId = br.ProductId,
                           ProductName = pr.ProductName,
                           ProductType = pr.ProductType,
                           ProductCategory = pcr.ProductCategory,
                           ProductCategoryId = pcr.ProductCategoryId,
                           ProductFirstSubCategory = pf.ProductFirstSubCategory,
                           ProductFirstSubCategoryId = pf.ProductFirstSubCategoryId,
                           ProductSecSubCategory = psc.ProductSecSubCategory,
                           ProductSecSubCategoryId = psc.ProductSecSubCategoryId,
                           Quantity = br.Quantity,
                           Unit = br.Unit,
                           Status = br.Status,
                           Poid = br.Poid,
                           PONumber = por.Ponumber,
                           AddedBy = br.AddedBy,
                           AddedDate = br.AddedDate,
                       }).OrderByDescending(x => x.DemandId).ToList();
            }
            return res;
        }

        public List<DemandTableVm> GetAllDemandsWithFilter(DemandFilterRequestVm filter)
        {
            List<DemandTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                var query = from br in db.DemandTables
                            join pr in db.ProductMasters on br.ProductId equals pr.ProductId
                            join po in db.PurchaseOrderTables on br.Poid equals po.Poid into pos
                            from por in pos.DefaultIfEmpty()
                            join st in db.StoreMasters on br.StoreId equals st.StoreId into sto
                            from stor in sto.DefaultIfEmpty()
                            join pcr in db.ProductCategoryMasters on pr.ProductCategoryId equals pcr.ProductCategoryId into ps
                            from pcr in ps.DefaultIfEmpty()
                            join pf in db.ProductFirstSubCategoryMasters on pr.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                            from pf in psf.DefaultIfEmpty()
                            join psc in db.ProductSecSubCategoryMasters on pr.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                            from psc in pssc.DefaultIfEmpty()
                            where (filter.DateFrom == null || br.AddedDate >= filter.DateFrom)
                               && (filter.DateTo == null || br.AddedDate <= filter.DateTo)
                               && (string.IsNullOrEmpty(filter.Status) || br.Status == filter.Status)
                               && (string.IsNullOrEmpty(filter.ProductType) || pr.ProductType == filter.ProductType)
                               && (filter.ProductCategoryId == 0 || pr.ProductCategoryId == filter.ProductCategoryId)
                               && (filter.ProductFirstSubCategoryId == 0 || pr.ProductFirstSubCategoryId == filter.ProductFirstSubCategoryId)
                               && (filter.ProductSecSubCategoryId == 0 || pr.ProductSecSubCategoryId == filter.ProductSecSubCategoryId)
                               && (filter.ProductId == 0 || pr.ProductId == filter.ProductId)
                               && (filter.StoreId == 0 || br.StoreId == filter.StoreId)
                            select new DemandTableVm
                            {
                                DemandId = br.DemandId,
                                StoreId = br.StoreId,
                                StoreName = stor.StoreName,
                                ProductId = br.ProductId,
                                ProductName = pr.ProductName,
                                ProductType = pr.ProductType,
                                ProductCategory = pcr.ProductCategory,
                                ProductCategoryId = pcr.ProductCategoryId,
                                ProductFirstSubCategory = pf.ProductFirstSubCategory,
                                ProductFirstSubCategoryId = pf.ProductFirstSubCategoryId,
                                ProductSecSubCategory = psc.ProductSecSubCategory,
                                ProductSecSubCategoryId = psc.ProductSecSubCategoryId,
                                Quantity = br.Quantity,
                                Unit = br.Unit,
                                Status = br.Status,
                                Poid = br.Poid,
                                PONumber = por.Ponumber,
                                AddedBy = br.AddedBy,
                                AddedDate = br.AddedDate,
                            };

                res = query.OrderByDescending(x => x.DemandId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddDemand(DemandTableVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = new DemandTable()
                {
                    StoreId = br.StoreId,
                    ProductId = br.ProductId,
                    Quantity = br.Quantity,
                    Unit = br.Unit,
                    Status = PmsCommon.PMSDemandtatus.Active,
                    AddedBy = GlobalData.loggedInUser,
                    AddedDate = DateTime.Now
                };
                db.DemandTables.Add(res);
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm ChangeDemandStatus(DemandTableVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = db.DemandTables.Where(x => x.DemandId == br.DemandId).FirstOrDefault();
                res.Status = br.Status;
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm SendPOInEmail(Stream st, long poid, string[] emaillist)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = db.PurchaseOrderTables.Where(x => x.Poid == poid).FirstOrDefault();
                EmailDataFn.SendEmail("Its PMS", st, emaillist, "PurchaseOrder : " + res.Ponumber + ".pdf", "PurchaseOrder-" + res.Ponumber);

                db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                {
                    Poid = poid,
                    Status = PMSPurchaseOrderStatus.SupplierNotifiedViaEmail,
                    Remark = "Email sent to Supplier",
                    AddedBy = GlobalData.loggedInUser,
                    AddedDate = DateTime.Now,
                });
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Email Sent");
            }
        }

        public List<PurchaseOrderTimelineVm> GetPurchaseOrderTimelineById(long Poid)
        {
            List<PurchaseOrderTimelineVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.PurchaseOrderTimelineTables
                       join po in db.PurchaseOrderTables on a.Poid equals po.Poid
                       join s in db.SupplierMasters on po.SupplierId equals s.SupplierId
                       join um in db.UserMasters on a.AddedBy equals um.Email into umd
                       from um in umd.DefaultIfEmpty()
                       where a.Poid == Poid
                       select new PurchaseOrderTimelineVm
                       {
                           Poid = a.Poid,
                           Status = a.Status,
                           AddedBy = um.Name,
                           AddedByUsername = a.AddedBy,
                           AddedDate = a.AddedDate,
                           Remark = a.Remark,
                           PONumber = po.Ponumber,
                           SupplierName = s.SupplierName,
                           PocreationDate = po.PocreationDate

                       }).OrderBy(x => x.AddedDate).ToList();
            }
            return res;
        }
        public List<POProductPriceHistoryVm> GetPOProductPriceHistory(POProductPriceHistoryRequestVm request)
        {
            List<POProductPriceHistoryVm> res = null;
            using var db = new pmsdbContext();

            res = db.PurchaseOrderProductTables
            .Join(db.PurchaseOrderTables, x => x.Poid, y => y.Poid, (x, y) => new { x, y })
            .Join(db.SupplierMasters, x => x.y.SupplierId, y => y.SupplierId, (x, y) => new { x, y })
            .Join(db.ProductMasters, x => x.x.x.ProductId, y => y.ProductId, (x, y) => new { x, y })
            .Where(x => x.x.x.x.ProductId == request.ProductId)
            .Select(x => new POProductPriceHistoryVm
            {
                PONumber = x.x.x.y.Ponumber,
                Poid = x.x.x.y.Poid,
                ProductName = x.y.ProductName,
                Price = x.x.x.x.Rate,
                SupplierName = x.x.y.SupplierName,
                Currency = x.x.x.x.Currency
            }).Take(request.NumberOfRecords).OrderByDescending(x => x.Poid).ToList();

            return res;
        }
        public ApiFunctionResponseVm GetPurchaseOrderPdf(long poid)
        {
            {
                try
                {
                    using (var db = new pmsdbContext())
                    {
                        var po = GetPurchaseOrderById(poid);
                        if (po == null)
                            return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Purchase order not found");

                        var totalInWords = CommonFunctions.ConvertToIndianWords(decimal.Parse(po.Pograndtotal.ToString()));
                        var pdfData = new PurchaseOrderPdfAdapter(po, totalInWords);

                        var currentDate = DateTime.UtcNow.ToString("yyyy/MM/dd");
                        var FormattedPonumber = po.Ponumber.Replace("/", "-");
                        var fileName = $"{currentDate}/PO_{FormattedPonumber}.pdf";

                        var pdfUrl = _pdfService.GeneratePdfAndUploadToStorageAsync(
                            pdfData,
                            fileName
                        );

                        string url = pdfUrl.Result;

                        return new ApiFunctionResponseVm(HttpStatusCode.OK, new { url = url });
                    }
                }
                catch (Exception ex)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, ex.Message);
                }
            }
        }

    }
}
