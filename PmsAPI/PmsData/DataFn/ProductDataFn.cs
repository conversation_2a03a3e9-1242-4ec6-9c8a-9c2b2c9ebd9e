﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class ProductDataFn
    {
        public GlobalDataEntity GlobalData;
        public ProductDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<ProductMasterVm> GetAllProducts()
        {
            List<ProductMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProductMasters
                       join ext in db.ProductMasterExtensions on a.ProductId equals ext.ProductId into aext
                       from ext in aext.DefaultIfEmpty()
                       join p in db.ProductCategoryMasters on a.ProductCategoryId equals p.ProductCategoryId into ps
                       from p in ps.DefaultIfEmpty()
                       join pf in db.ProductFirstSubCategoryMasters on a.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                       from pf in psf.DefaultIfEmpty()
                       join psc in db.ProductSecSubCategoryMasters on a.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                       from psc in pssc.DefaultIfEmpty()
                       where a.Disabled != true
                       select new ProductMasterVm
                       {
                           ProductId = a.ProductId,
                           ProductName = a.ProductName,
                           ProductCode = a.ProductCode,
                           Unit = a.Unit,
                           ProductType = p.ProductType,
                           ProductCategoryId = a.ProductCategoryId,
                           ProductCategory = p.ProductCategory,
                           ProductFirstSubCategoryId = a.ProductFirstSubCategoryId,
                           ProductFirstSubCategory = pf.ProductFirstSubCategory,
                           ProductSecSubCategoryId = a.ProductSecSubCategoryId,
                           ProductSecSubCategory = psc.ProductSecSubCategory,
                           MinimumQuantity = a.MinimumQuantity,
                           AvgGsm = a.AvgGsm,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           WidthInMeter = a.WidthInMeter,
                           ProductDescription = a.ProductDescription,
                           ProductExtension = new ProductMasterExtensionVm
                           {
                               ProductExtId = ext != null ? ext.ProductExtId : 0,
                               ProductId = ext != null ? ext.ProductExtId : 0,
                               ProductStyle = ext.ProductStyle,
                               MarketFor = ext.MarketFor,
                               Width = ext.Width,
                               Length = ext.Length,
                               Gender = ext.Gender,
                               Gsm = ext.Gsm,
                               Shape = ext.Shape,
                               Remark = ext.Remark
                           }
                       }).OrderByDescending(x => x.ProductId).ToList();
            }
            return res;
        }


        public List<ProductMasterVm> GetAllProductsByProductType(string ProductType)
        {
            List<ProductMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProductMasters
                       join ext in db.ProductMasterExtensions on a.ProductId equals ext.ProductId into aext
                       from ext in aext.DefaultIfEmpty()
                       join p in db.ProductCategoryMasters on a.ProductCategoryId equals p.ProductCategoryId into ps
                       from p in ps.DefaultIfEmpty()
                       join pf in db.ProductFirstSubCategoryMasters on a.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                       from pf in psf.DefaultIfEmpty()
                       join psc in db.ProductSecSubCategoryMasters on a.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                       from psc in pssc.DefaultIfEmpty()
                       where a.Disabled != true
                       && (string.IsNullOrEmpty(ProductType) || p.ProductType.ToLower() == ProductType.ToLower())
                       select new ProductMasterVm
                       {
                           ProductId = a.ProductId,
                           ProductName = a.ProductName,
                           ProductCode = a.ProductCode,
                           Unit = a.Unit,
                           ProductType = p.ProductType,
                           ProductCategoryId = a.ProductCategoryId,
                           ProductCategory = p.ProductCategory,
                           ProductFirstSubCategoryId = a.ProductFirstSubCategoryId,
                           ProductFirstSubCategory = pf.ProductFirstSubCategory,
                           ProductSecSubCategoryId = a.ProductSecSubCategoryId,
                           ProductSecSubCategory = psc.ProductSecSubCategory,
                           MinimumQuantity = a.MinimumQuantity,
                           AvgGsm = a.AvgGsm,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           WidthInMeter = a.WidthInMeter,
                           ProductDescription = a.ProductDescription,
                           ProductExtension = new ProductMasterExtensionVm
                           {
                               ProductExtId = ext != null ? ext.ProductExtId : 0,
                               ProductId = ext != null ? ext.ProductExtId : 0,
                               ProductStyle = ext.ProductStyle,
                               MarketFor = ext.MarketFor,
                               Width = ext.Width,
                               Length = ext.Length,
                               Gender = ext.Gender,
                               Gsm = ext.Gsm,
                               Shape = ext.Shape,
                               Remark = ext.Remark
                           }
                       }).OrderByDescending(x => x.ProductId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateProduct(ProductMasterVm product)
        {
            using (var db = new Models.pmsdbContext())
            using (var transaction = db.Database.BeginTransaction())
            {
                try
                {
                    // Validate the product
                    var validationResult = ValidateProduct(db, product);
                    if (validationResult.StatusCode != HttpStatusCode.OK)
                    {
                        return validationResult;
                    }

                    ProductMaster res;
                    if (product.ProductId == 0)
                    {
                        // Add new product
                        res = new ProductMaster
                        {
                            ProductName = product.ProductName.Trim(),
                            ProductType = product.ProductType,
                            ProductCode = product.ProductCode.Trim(),
                            Unit = product.Unit,
                            ProductCategoryId = product.ProductCategoryId,
                            ProductFirstSubCategoryId = product.ProductFirstSubCategoryId,
                            ProductSecSubCategoryId = product.ProductSecSubCategoryId,
                            MinimumQuantity = product.MinimumQuantity,
                            AvgGsm = product.AvgGsm,
                            AddedBy = product.AddedBy,
                            AddedDate = System.DateTime.Now,
                            WidthInMeter = product.WidthInMeter,
                            ProductDescription = product.ProductDescription
                        };
                        db.ProductMasters.Add(res);
                        db.SaveChanges();
                        if (product.ProductExtension != null)
                        {
                            ProductMasterExtension pme = new ProductMasterExtension();
                            pme.ProductId = res.ProductId;
                            pme.ProductStyle = product.ProductExtension.ProductStyle;
                            pme.MarketFor = product.ProductExtension.MarketFor;
                            pme.Width = product.ProductExtension.Width;
                            pme.Length = product.ProductExtension.Length;
                            pme.Gender = product.ProductExtension.Gender;
                            pme.Gsm = product.ProductExtension.Gsm;
                            pme.Shape = product.ProductExtension.Shape;
                            pme.Remark = product.ProductExtension.Remark;
                            db.ProductMasterExtensions.Add(pme);
                        }
                    }
                    else
                    {
                        // Update existing product
                        res = db.ProductMasters.Find(product.ProductId);
                        if (res == null)
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Product not found");
                        }

                        // Update properties
                        res.ProductName = product.ProductName.Trim();
                        res.ProductType = product.ProductType;
                        res.ProductCode = product.ProductCode.Trim();
                        res.Unit = product.Unit;
                        res.ProductCategoryId = product.ProductCategoryId;
                        res.ProductFirstSubCategoryId = product.ProductFirstSubCategoryId;
                        res.ProductSecSubCategoryId = product.ProductSecSubCategoryId;
                        res.MinimumQuantity = product.MinimumQuantity;
                        res.AvgGsm = product.AvgGsm;
                        res.AddedBy = product.AddedBy;
                        res.AddedDate = System.DateTime.Now;
                        res.WidthInMeter = product.WidthInMeter;
                        res.ProductDescription = product.ProductDescription;
                        if (product.ProductExtension != null)
                        {
                            ProductMasterExtension pme = db.ProductMasterExtensions.Where(x => x.ProductId == product.ProductId).FirstOrDefault();
                            if (pme != null)
                            {
                                pme.ProductId = product.ProductExtension.ProductId;
                                pme.ProductStyle = product.ProductExtension.ProductStyle;
                                pme.MarketFor = product.ProductExtension.MarketFor;
                                pme.Width = product.ProductExtension.Width;
                                pme.Length = product.ProductExtension.Length;
                                pme.Gender = product.ProductExtension.Gender;
                                pme.Gsm = product.ProductExtension.Gsm;
                                pme.Shape = product.ProductExtension.Shape;
                                pme.Remark = product.ProductExtension.Remark;
                            }
                            else
                            {
                                pme = new ProductMasterExtension();
                                pme.ProductId = res.ProductId;
                                pme.ProductStyle = product.ProductExtension.ProductStyle;
                                pme.MarketFor = product.ProductExtension.MarketFor;
                                pme.Width = product.ProductExtension.Width;
                                pme.Length = product.ProductExtension.Length;
                                pme.Gender = product.ProductExtension.Gender;
                                pme.Gsm = product.ProductExtension.Gsm;
                                pme.Shape = product.ProductExtension.Shape;
                                pme.Remark = product.ProductExtension.Remark;
                                db.ProductMasterExtensions.Add(pme);
                            }
                        }
                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = res.ProductId,
                            TableName = "ProductMasters",
                            EntityName = "ProductMaster",
                            AddedBy = product.AddedBy,
                            AddedDate = DateTime.Now
                        });

                        db.SaveChanges();
                    }
                    transaction.Commit();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK,
                        product.ProductId == 0 ? "Product added successfully" : "Product updated successfully");
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError,
                        "An error occurred. Please contact administrator. " + ex.Message);
                }
            }
        }

        public ApiFunctionResponseVm DeleteProduct(long productId)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        bool deleteflag = false;
                        var res = db.ProductMasters.Where(x => x.ProductId == productId).FirstOrDefault();
                        if (res != null)
                        {
                            var stockProduct = db.StockProductTables.Where(x => x.ProductId == productId).ToList();
                            if (stockProduct.Count > 0)
                            {
                                foreach (var item in stockProduct)
                                {
                                    var stockProductAllocation = db.StockProductAllocationTables.Where(x => x.StockProductId == item.StockProductId && x.Quantity > 0).ToList();
                                    if (stockProductAllocation.Count > 0)
                                    {

                                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product can not be deleted as there are some stocks available in the store. Please check Stock Availability Report.");
                                    }
                                    else
                                    {
                                        deleteflag = true;
                                    }
                                }
                            }
                            var purchaseOrderProduct = db.PurchaseOrderProductTables.Where(x => x.ProductId == productId).ToList();
                            if (purchaseOrderProduct.Count > 0)
                            {
                                foreach (var item in purchaseOrderProduct)
                                {
                                    var purchaseOrder = db.PurchaseOrderTables.Where(x => x.Poid == item.Poid && (x.Status == "Active" || x.Status == "New")).FirstOrDefault();
                                    if (purchaseOrder != null && purchaseOrder.Status == "Active")
                                    {
                                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product can not be deleted as there is a purchase order created for it and it has been Approved and in Active status. Please search this product on Purchase Order List page.");
                                    }
                                    if (purchaseOrder != null && purchaseOrder.Status == "New")
                                    {
                                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product can not be deleted as there is a New purchase order created for it. Please search this product on Purchase Order List page.");
                                    }
                                    else
                                    {
                                        deleteflag = true;
                                    }
                                }
                            }
                            var formulationFabricProduct = db.SaleFormulationCodeMasters.Where(x => x.FabricProductId == productId).ToList();
                            if (formulationFabricProduct.Count > 0)
                            {
                                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product can not be deleted as it is being used as Fabric Product in one or more Formulation Code. Please search this product on Formulation Code and remove it.");
                            }
                            else
                            {
                                deleteflag = true;
                            }
                            if (deleteflag)
                            {
                                res.Disabled = true;
                                res.DisabledBy = GlobalData.loggedInUser;
                                res.DisabledDate = System.DateTime.Now;
                                db.AuditTables.Add(new AuditTable
                                {
                                    RecId = res.ProductId,
                                    TableName = "ProductMasters",
                                    EntityName = "ProductMaster",
                                    AddedBy = GlobalData.loggedInUser,
                                    AddedDate = System.DateTime.Now,
                                });
                                db.SaveChanges();
                            }
                        }
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product removed successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }

        public decimal? GetProductDefaultPerUnitCost(long productId)
        {
            decimal? perunitcost = 0;
            using (var db = new Models.pmsdbContext())
            {
                bool flag = db.StockProductTables.Any(x => x.ProductId == productId);
                perunitcost = flag ? db.StockProductTables.Where(x => x.ProductId == productId).OrderByDescending(x => x.StockProductId).ToList()[0].PricePerUnit : 0;
            }
            return perunitcost;
        }
        public List<ProductMasterVm> GetProductsByCategories(SearchParamsProductCategoryReportVm filter)
        {
            List<ProductMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProductMasters
                       join p in db.ProductCategoryMasters on a.ProductCategoryId equals p.ProductCategoryId into ps
                       from p in ps.DefaultIfEmpty()
                       join pf in db.ProductFirstSubCategoryMasters on a.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                       from pf in psf.DefaultIfEmpty()
                       join psc in db.ProductSecSubCategoryMasters on a.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                       from psc in pssc.DefaultIfEmpty()
                       where a.Disabled != true && a.ProductType == "Raw"
                       && (filter.ProductCategoryId == 0 || filter.ProductCategoryId == a.ProductCategoryId)
                       && (filter.ProductFirstSubCategoryId == 0 || filter.ProductFirstSubCategoryId == a.ProductFirstSubCategoryId)
                       && (filter.ProductSecSubCategoryId == 0 || filter.ProductSecSubCategoryId == a.ProductSecSubCategoryId)
                       select new ProductMasterVm
                       {
                           ProductId = a.ProductId,
                           ProductName = a.ProductName,
                           ProductCode = a.ProductCode,
                           Unit = a.Unit,
                           ProductType = p.ProductType,
                           ProductCategoryId = a.ProductCategoryId,
                           ProductCategory = p.ProductCategory,
                           ProductFirstSubCategoryId = a.ProductFirstSubCategoryId,
                           ProductFirstSubCategory = pf.ProductFirstSubCategory,
                           ProductSecSubCategoryId = a.ProductSecSubCategoryId,
                           ProductSecSubCategory = psc.ProductSecSubCategory,
                           MinimumQuantity = a.MinimumQuantity,
                           AvgGsm = a.AvgGsm,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           WidthInMeter = a.WidthInMeter,
                           ProductDescription = a.ProductDescription
                       }).OrderByDescending(x => x.ProductId).ToList();
            }
            return res;
        }
        public List<ProductMasterVm> GetProductsByCategoryNames(SearchParamsProductCategoryByNameVm filter)
        {
            List<ProductMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProductMasters
                       join p in db.ProductCategoryMasters on a.ProductCategoryId equals p.ProductCategoryId into ps
                       from p in ps.DefaultIfEmpty()
                       join pf in db.ProductFirstSubCategoryMasters on a.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                       from pf in psf.DefaultIfEmpty()
                       join psc in db.ProductSecSubCategoryMasters on a.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                       from psc in pssc.DefaultIfEmpty()
                       where a.Disabled != true && p.ProductType == "Raw"
                       && (string.IsNullOrEmpty(filter.ProductCategoryName) || filter.ProductCategoryName.Trim().ToLower() == p.ProductCategory.Trim().ToLower())
                       && (string.IsNullOrEmpty(filter.ProductFirstSubCategoryName) || filter.ProductFirstSubCategoryName.Trim().ToLower() == pf.ProductFirstSubCategory.Trim().ToLower())
                       && (string.IsNullOrEmpty(filter.ProductSecSubCategoryName) || filter.ProductSecSubCategoryName.Trim().ToLower() == psc.ProductSecSubCategory.Trim().ToLower())
                       select new ProductMasterVm
                       {
                           ProductId = a.ProductId,
                           ProductName = a.ProductName,
                           ProductCode = a.ProductCode,
                           Unit = a.Unit,
                           ProductType = p.ProductType,
                           ProductCategoryId = a.ProductCategoryId,
                           ProductCategory = p.ProductCategory,
                           ProductFirstSubCategoryId = a.ProductFirstSubCategoryId,
                           ProductFirstSubCategory = pf.ProductFirstSubCategory,
                           ProductSecSubCategoryId = a.ProductSecSubCategoryId,
                           ProductSecSubCategory = psc.ProductSecSubCategory,
                           MinimumQuantity = a.MinimumQuantity,
                           AvgGsm = a.AvgGsm,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           WidthInMeter = a.WidthInMeter,
                           ProductDescription = a.ProductDescription
                       }).OrderByDescending(x => x.ProductId).ToList();
            }
            return res;
        }

        private ApiFunctionResponseVm ValidateProduct(Models.pmsdbContext db, ProductMasterVm product)
        {
            try
            {
                // Basic input validations
                if (string.IsNullOrWhiteSpace(product.ProductName))
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product name cannot be empty.");
                }

                // Trim and normalize the product name
                product.ProductName = product.ProductName.Trim();
                var normalizedNewName = NormalizeProductName(product.ProductName);
                var normalizedCase = product.ProductName.ToUpper();

                // Length validation
                if (product.ProductName.Length < 3 || product.ProductName.Length > 120)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                        "Product name must be between 3 and 120 characters.");
                }

                // Multiple spaces and special characters validation
                if (product.ProductName.Contains("  "))
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                        "Product name cannot contain multiple consecutive spaces.");
                }

                if (ContainsSpecialCharacters(product.ProductName))
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                        "Product name can only contain letters, numbers, spaces, dot (.), hyphens (-) and parentheses ().");
                }



                // Duplicate check query with enhanced normalization
                var duplicateCheck = db.ProductMasters
                        .Where(x => x.ProductId != product.ProductId &&
                                    x.Disabled != true &&
                                    x.ProductCategoryId == product.ProductCategoryId)
                        .Select(x => new
                        {
                            x.ProductName,
                            ExactMatch = x.ProductName.Trim().ToLower()
                        })
                        .AsEnumerable() // Switch to in-memory
                        .Where(x => x.ExactMatch == product.ProductName.Trim().ToLower() ||
                                    NormalizeProductName(x.ProductName) == normalizedNewName)
                        .Select(x => new
                        {
                            x.ProductName,
                            IsExactMatch = x.ExactMatch == product.ProductName.Trim().ToLower()
                        })
                        .FirstOrDefault();

                if (duplicateCheck != null)
                {
                    var errorMessage = duplicateCheck.IsExactMatch
                        ? $"A product with name '{product.ProductName}' already exists in this category (case-insensitive match)."
                        : $"A similar product name '{duplicateCheck.ProductName}' already exists in this category. Names with different spacing or casing are considered duplicates.";

                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, errorMessage);
                }

                // Category-based validation
                if (product.ProductCategoryId <= 0)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product category is required.");
                }

                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Validation successful");
            }
            catch (Exception ex)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError,
                    $"Validation error: {ex.Message}");
            }
        }
        private bool ContainsSpecialCharacters(string input)
        {
            return !System.Text.RegularExpressions.Regex.IsMatch(
                input,
                @"^[a-zA-Z0-9\s\-()\.]+$"
            );
        }
        private string NormalizeProductName(string name)
        {
            if (string.IsNullOrEmpty(name)) return string.Empty;

            // First remove special characters and convert to uppercase
            var cleanName = System.Text.RegularExpressions.Regex.Replace(
                name.ToUpper().Trim(),
                @"[-(){}[\].,]", // Remove hyphens, parentheses, and other special chars
                string.Empty
            );

            // Remove spaces between numbers and letters
            var normalized = System.Text.RegularExpressions.Regex.Replace(
                cleanName,
                @"(\d+)\s+([a-zA-Z])|([a-zA-Z])\s+(\d+)",
                m => m.Groups[1].Success
                    ? m.Groups[1].Value + m.Groups[2].Value
                    : m.Groups[3].Value + m.Groups[4].Value
            );

            // Remove any remaining multiple spaces and trim
            normalized = System.Text.RegularExpressions.Regex.Replace(normalized, @"\s+", " ").Trim();

            return normalized;
        }
    }
}
