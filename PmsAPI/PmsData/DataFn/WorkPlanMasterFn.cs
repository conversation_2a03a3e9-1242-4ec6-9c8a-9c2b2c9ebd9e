using PmsCommon;
using PmsData.Models;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;

namespace PmsData.DataFn
{
    public class WorkPlanMasterFn
    {
        public GlobalDataEntity GlobalData;
        public WorkPlanMasterFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ResultWorkPlanMasterList GetAllWorkPlan()
        {
            ResultWorkPlanMasterList vmModel = new ResultWorkPlanMasterList();
            try
            {
                using (var db = new pmsdbContext())
                {
                    vmModel.Data = (from a in db.WorkPlanMasters
                                    where a.Disabled != true
                                    select new WorkPlanMasterVm
                                    {
                                        BatchNo = a.BatchNo,
                                        LotNo = a.LotNo,
                                        ProductionDetails = a.ProductionDetails,
                                        WorkPlanDate = a.WorkPlanDate,
                                        WorkPlanId = a.WorkPlanId,
                                        WorkPlanNo = a.WorkPlanNo,
                                        AddedBy = a.AddedBy,
                                        AddedDate = a.AddedDate,

                                    }).OrderByDescending(x => x.WorkPlanId).ToList();
                }
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message };
            }
            return vmModel;
        }

        public ResultWorkPlanMasterList GetAllWorkPlanBysaleorderstatus(List<string> saleorderstatus)
        {
            List<ESalesOrderStatus> stsList = new List<ESalesOrderStatus>();

            foreach (var item in saleorderstatus)
            {
                stsList.Add(PMSEnum.ParseEnum<ESalesOrderStatus>(item));

            }
            ResultWorkPlanMasterList vmModel = new ResultWorkPlanMasterList();
            try
            {
                using (var db = new pmsdbContext())
                {
                    vmModel.Data = (from a in db.WorkPlanMasters
                                    join wp in db.WorkPlanOrders on a.WorkPlanId equals wp.WorkplanId
                                    join sot in db.SaleOrderTables on wp.OrderId equals sot.SaleOrderId
                                    where stsList.Contains((ESalesOrderStatus)sot.Status) && a.Disabled != true
                                    select new WorkPlanMasterVm
                                    {
                                        BatchNo = a.BatchNo,
                                        LotNo = a.LotNo,
                                        ProductionDetails = a.ProductionDetails,
                                        WorkPlanDate = a.WorkPlanDate,
                                        WorkPlanId = a.WorkPlanId,
                                        WorkPlanNo = a.WorkPlanNo,
                                        AddedBy = a.AddedBy,
                                        AddedDate = a.AddedDate,
                                    }).Distinct().OrderByDescending(x => x.WorkPlanId).ToList();
                }
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message };
            }
            return vmModel;
        }
        public ResultWorkPlanMasterList GetAllWorkPlanBySaleOrderStatusForConsume(List<string> saleorderstatus)
        {
            List<ESalesOrderStatus> stsList = new List<ESalesOrderStatus>();

            foreach (var item in saleorderstatus)
            {
                stsList.Add(PMSEnum.ParseEnum<ESalesOrderStatus>(item));

            }
            ResultWorkPlanMasterList vmModel = new ResultWorkPlanMasterList();
            try
            {
                using (var db = new pmsdbContext())
                {
                    vmModel.Data = (from a in db.WorkPlanMasters
                                    join wp in db.WorkPlanOrders on a.WorkPlanId equals wp.WorkplanId
                                    join sot in db.SaleOrderTables on wp.OrderId equals sot.SaleOrderId
                                    join cspm in db.ConsumeStockProductMasters on sot.SaleOrderId equals cspm.SaleOrderId into cspmd
                                    from cspm in cspmd.DefaultIfEmpty()
                                    where stsList.Contains((ESalesOrderStatus)sot.Status) && cspm == null && a.Disabled != true
                                    select new WorkPlanMasterVm
                                    {
                                        BatchNo = a.BatchNo,
                                        LotNo = a.LotNo,
                                        ProductionDetails = a.ProductionDetails,
                                        WorkPlanDate = a.WorkPlanDate,
                                        WorkPlanId = a.WorkPlanId,
                                        WorkPlanNo = a.WorkPlanNo,
                                        AddedBy = a.AddedBy,
                                        AddedDate = a.AddedDate,
                                    }).Distinct().OrderByDescending(x => x.WorkPlanId).ToList();
                }
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message };
            }
            return vmModel;
        }
        public ResultWorkPlanMaster GetAllWorkPlanById(long Id)
        {
            ResultWorkPlanMaster vmModel = new ResultWorkPlanMaster();
            try
            {
                using (var db = new pmsdbContext())
                {
                    vmModel.Data = (from a in db.WorkPlanMasters
                                    where a.WorkPlanId == Id
                                    select new WorkPlanMasterVm
                                    {
                                        BatchNo = a.BatchNo,
                                        LotNo = a.LotNo,
                                        ProductionDetails = a.ProductionDetails,
                                        WorkPlanDate = a.WorkPlanDate,
                                        WorkPlanId = a.WorkPlanId,
                                        WorkPlanNo = a.WorkPlanNo,
                                        AddedBy = a.AddedBy,
                                        AddedDate = a.AddedDate,
                                        WorkPlanOrder = (from order in db.WorkPlanOrders
                                                         where order.WorkplanId == a.WorkPlanId
                                                         select new WorkPlanOrderVm
                                                         {
                                                             WorkplanId = order.WorkplanId,
                                                             OrderId = order.OrderId,
                                                             WorkPlanOrdersId = order.WorkPlanOrdersId,
                                                             WorkPlanJumbo = (from model in db.WorkPlanJumboMasters
                                                                              join wpo in db.WorkPlanOrders on model.SaleOrderId equals wpo.OrderId
                                                                              where model.SaleOrderId == order.OrderId
                                                                              select new WorkPlanJumboMasterVm
                                                                              {
                                                                                  WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                                                                                  SaleOrderId = model.SaleOrderId,
                                                                                  JumboRollDate = model.JumboRollDate,
                                                                                  JumboRollStartTime = model.JumboRollStartTime,
                                                                                  JumboRollEndTime = model.JumboRollEndTime,
                                                                                  JumboNo = model.JumboNo,
                                                                                  Rate = model.Rate,
                                                                                  Amount = model.Amount,
                                                                                  JumboRolQty = model.JumboRolQty,
                                                                                  ActualQuantity = model.ActualQuantity,
                                                                                  WastageEmbossing = model.WastageEmbossing,
                                                                                  WastageLacquer = model.WastageLacquer,
                                                                                  WastagePrint = model.WastagePrint,
                                                                                  WastageTumbling = model.WastageTumbling,
                                                                                  WastageVacuum = model.WastageVacuum,
                                                                                  Weight = model.Weight,
                                                                                  RackId = model.RackId,
                                                                                  StoreId = model.StoreId,
                                                                                  RackCode = model.RackCode,
                                                                                  RackName = model.RackName,
                                                                                  StoreCode = model.StoreCode,
                                                                                  StoreName = model.StoreName,
                                                                                  Remark = model.Remark,
                                                                                  Yield = model.Yield,
                                                                                  IsInspectionCompleted = model.IsInspectionCompleted
                                                                              }).OrderByDescending(x => x.WorkPlanJumboMasterId).ToList(),
                                                             SaleOrder = (from s in db.SaleOrderTables
                                                                          join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                                                                          join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                                                                          from pcm in spc.DefaultIfEmpty()
                                                                          join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                                                                          from cust in scu.DefaultIfEmpty()
                                                                          join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                                                                          from c in cagr.DefaultIfEmpty()
                                                                          join pr in db.ProductMasters on a.ProductId equals pr.ProductId into prs
                                                                          from pr in prs.DefaultIfEmpty()
                                                                          join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                                                                          from fcm in fcms.DefaultIfEmpty()
                                                                          join prf in db.ProductMasters on fcm.FabricProductId equals prf.ProductId into prsf
                                                                          from prf in prsf.DefaultIfEmpty()
                                                                          join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                                                                          from thm in thmsa.DefaultIfEmpty()
                                                                          where s.SaleOrderId == order.OrderId
                                                                          select new SaleOrderTableVm
                                                                          {
                                                                              SaleOrderId = s.SaleOrderId,
                                                                              SaleOrderDate = s.SaleOrderDate,
                                                                              SaleOrderNumber = s.SaleOrderNumber,
                                                                              SaleOrderType = s.SaleOrderType,
                                                                              SaleOrderCode = s.SaleOrderCode,
                                                                              CostingAdded = s.CostingAdded,
                                                                              CustomerId = s.CustomerId,
                                                                              CustomerName = cust.CustomerName,
                                                                              CategoryId = s.CategoryId,
                                                                              Category = pcm.ProductCategory,
                                                                              AddedBy = s.AddedBy,
                                                                              AddedDate = s.AddedDate,
                                                                              DeliveryDate = s.DeliveryDate,
                                                                              Remarks = s.Remarks,
                                                                              SaleOrderStatus = s.SaleOrderStatus,
                                                                              ProformaInvoiceId = s.ProformaInvoiceId,
                                                                              IsRawMaterialIssued = s.IsRawMaterialIssued,
                                                                              Status = (PmsCommon.ESalesOrderStatus)s.Status,
                                                                              IsJumboRequired = s.IsJumboRequired,
                                                                              ProductionCompletionRemarks = s.ProductionCompletionRemarks,
                                                                              SaleFormulationCode = fcm.SaleFormulationCode,
                                                                              FormulationFabricProductName = prf.ProductName,
                                                                              SaleOrderProduction = new SaleOrderProductionTableVm
                                                                              {
                                                                                  SaleOrderProductionId = a.SaleOrderProductionId,
                                                                                  ProductId = a.ProductId,
                                                                                  ProductName = pr.ProductName,
                                                                                  ProductCode = pr.ProductCode,
                                                                                  ManufacturingProductName = a.ManufacturingProductName,
                                                                                  ManufacturingProductCode = a.ManufacturingProductCode,
                                                                                  Lot = a.Lot,
                                                                                  Batch = a.Batch,
                                                                                  OrderQuantity = a.OrderQuantity,
                                                                                  ManufacturingQuantity = a.ManufacturingQuantity,
                                                                                  Unit = a.Unit,
                                                                                  ColorId = a.ColorId,
                                                                                  ColorName = c.ColorName,
                                                                                  Barcode = a.Barcode,
                                                                                  ProductionStatus = a.ProductionStatus,
                                                                                  CostingStatus = a.CostingStatus,
                                                                                  SlippagePercent = a.SlippagePercent,
                                                                                  TotalCost = a.TotalCost,
                                                                                  ProcessFormulationCode = a.ProcessFormulationCode,
                                                                                  MixingFormulationCode = a.MixingFormulationCode,
                                                                                  Thick = a.Thick,
                                                                                  ThicknessValue = thm.ThicknessNumber,
                                                                                  AddedBy = a.AddedBy,
                                                                                  AddedDate = a.AddedDate,
                                                                                  FormulationMixing = (from la in db.FormulationCodeMixingTables
                                                                                                       join a in db.MixingMasters on la.MixingId equals a.MixingId
                                                                                                       where la.SaleFormulationCodeId == fcm.SaleFormulationCodeId
                                                                                                       select new FormulationCodeMixingTableVm
                                                                                                       {
                                                                                                           FormulationCodeMixingId = la.FormulationCodeMixingId,
                                                                                                           SaleFormulationCodeId = la.SaleFormulationCodeId,
                                                                                                           AddedDate = la.AddedDate,
                                                                                                           AddedBy = la.AddedBy,
                                                                                                           MixingId = la.MixingId,
                                                                                                           MixingName = a.MixingName,
                                                                                                           MixingRawMaterial = (from op in db.FormulationCodeMixingRawMaterialTables
                                                                                                                                join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                                                                where op.FormulationCodeMixingId == la.FormulationCodeMixingId
                                                                                                                                select new FormulationCodeMixingRawMaterialTableVm
                                                                                                                                {
                                                                                                                                    FormulationCodeMixingRawMaterialId = op.FormulationCodeMixingRawMaterialId,
                                                                                                                                    FormulationCodeMixingId = op.FormulationCodeMixingId,
                                                                                                                                    ProductId = op.ProductId,
                                                                                                                                    ProductName = p.ProductName,
                                                                                                                                    ProductCode = p.ProductCode,
                                                                                                                                    Quantity = op.Quantity,
                                                                                                                                    Scquantity = op.Scquantity,
                                                                                                                                    AvgGsm = p.AvgGsm,
                                                                                                                                    Unit = op.Unit,
                                                                                                                                    Price = op.Price
                                                                                                                                }).ToList()
                                                                                                       }).ToList(),

                                                                              }
                                                                          }).OrderByDescending(x => x.SaleOrderId).FirstOrDefault()

                                                         }).OrderByDescending(x => x.WorkplanId).ToList()
                                    }).FirstOrDefault();
                }
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message };
            }
            return vmModel;
        }
        public ResultWorkPlanMaster AddWorkPlanWithOrders(WorkPlanMasterVm model, pmsdbContext transactiondb = null, bool IsWorkPlanChange = false)
        {
            ResultWorkPlanMaster vmModel = new ResultWorkPlanMaster();

            var isOwnContext = transactiondb == null;
            var db = transactiondb ?? new pmsdbContext();
            var transaction = isOwnContext ? db.Database.BeginTransaction() : null;

            try
            {
                WorkPlanMaster data = new WorkPlanMaster();
                data.BatchNo = model.BatchNo;
                data.LotNo = model.LotNo;
                data.ProductionDetails = model.ProductionDetails;
                data.WorkPlanDate = model.WorkPlanDate;
                data.WorkPlanId = model.WorkPlanId;
                data.ProductionLineNo = model.ProductionLineNo;
                data.WorkShift = model.WorkShift;
                data.AddedBy = GlobalData.loggedInUser;
                data.AddedDate = System.DateTime.Now;
                db.WorkPlanMasters.Add(data);

                db.SaveChanges();

                var date = data.WorkPlanDate;
                var todayWp = db.WorkPlanMasters.Where(e => e.WorkPlanDate == date).Count();
                var workShiftShort = data.WorkShift == "day" ? "D" : "N";

                data.WorkPlanNo = "L" + data.ProductionLineNo + workShiftShort + "/" + date.ToString("dd/MM/yyyy") + "/" + data.WorkPlanId + "/" + todayWp;

                model.WorkPlanNo = data.WorkPlanNo;
                model.BatchNo = todayWp.ToString();
                model.LotNo = data.WorkPlanId.ToString();

                data.BatchNo = data.WorkPlanNo;
                data.LotNo = data.WorkPlanId.ToString();

                db.Entry(data).State = Microsoft.EntityFrameworkCore.EntityState.Modified;

                var enableSOStatusEmail = new List<ConfigTable>();
                if (!IsWorkPlanChange)
                {
                    enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnWorkPlanAdd" && x.ConfigValue == "true")).ToList();
                }
                foreach (var item in model.WorkPlanOrder)
                {
                    var existorder = db.WorkPlanOrders.FirstOrDefault(e => e.OrderId == item.OrderId);
                    if (existorder == null)
                    {
                        var sales = db.SaleOrderTables.FirstOrDefault(e => e.SaleOrderId == item.OrderId);
                        if (sales != null && IsWorkPlanChange == false)
                        {
                            sales.WorkPlanStatus = true;
                            sales.Status = (int)ESalesOrderStatus.WorkPlan;
                            db.Entry(sales).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                        }

                        WorkPlanOrder subData = new WorkPlanOrder();
                        subData.WorkplanId = data.WorkPlanId;
                        subData.OrderId = item.OrderId;

                        db.WorkPlanOrders.Add(subData);
                        if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == item.OrderId.Value && x.Status == (int)ESalesOrderStatus.WorkPlan) && IsWorkPlanChange == false)
                        {
                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                            {
                                SaleOrderId = item.OrderId.Value,
                                Status = (int)ESalesOrderStatus.WorkPlan,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = System.DateTime.Now,
                                WorkPlanId = subData.WorkplanId
                            });
                            db.SaveChanges();
                        }
                    }

                    if (enableSOStatusEmail.Count == 2)
                    {
                        var emailSaleOrderStatus = SaleOrderEmailStatus.InProductionPlanning;
                        _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(item.OrderId.Value, emailSaleOrderStatus);
                    }
                }
                if (isOwnContext)
                {
                    db.SaveChanges();
                    transaction.Commit();
                }
                model.WorkPlanId = data.WorkPlanId;

                vmModel.Data = model;
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                List<string> error = new List<string>();
                if (isOwnContext)
                {
                    transaction?.Rollback();
                    try
                    {
                        error.Add(ex.InnerException.Message);
                        error.Add(ex.InnerException.Source);
                        error.Add(ex.InnerException.StackTrace);
                    }
                    catch
                    {

                    }
                }
                vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message, Errors = error };
            }
            finally
            {
                if (isOwnContext)
                {
                    db.Dispose();
                }
            }
            return vmModel;
        }
        public ResultWorkPlanMaster ChangeWorkPlanForSingleOrder(WorkPlanMasterVm model)
        {
            ResultWorkPlanMaster vmModel = new ResultWorkPlanMaster();
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {

                var saleOrder = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == model.WorkPlanOrder[0].OrderId);
                if (saleOrder == null)
                {
                    vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.NotFound, Message = "Sale Order Not Found" };
                }

                var workPlanOrder = db.WorkPlanOrders.First(x => x.OrderId == model.WorkPlanOrder[0].OrderId);
                var wpm = db.WorkPlanMasters.First(x => x.WorkPlanId == workPlanOrder.WorkplanId);
                if (workPlanOrder != null)
                {
                    db.WorkPlanOrders.Remove(workPlanOrder);

                    var wpo = db.WorkPlanOrders.Where(x => x.WorkplanId == workPlanOrder.WorkplanId).ToList();

                    if (wpo.Count < 1)
                    {
                        wpm.Disabled = true;
                        wpm.DisabledBy = GlobalData.loggedInUser;
                        wpm.DisabledDate = DateTime.Now;
                    }
                }

                var addworkplanresponse = AddWorkPlanWithOrders(model, db, true);
                if (addworkplanresponse.Result.Succeeded == true)
                {
                    db.WorkPlanOrderTrackingTables.Add(new WorkPlanOrderTrackingTable
                    {
                        SaleOrderId = model.WorkPlanOrder[0].OrderId.Value,
                        CurrentWorkPlanId = workPlanOrder.WorkplanId.Value,
                        NewWorkPlanId = addworkplanresponse.Data.WorkPlanId,
                        OrderStatus = saleOrder.Status,
                        ChangedBy = GlobalData.loggedInUser,
                        ChangedDate = DateTime.Now
                    });

                    if (wpm.IsReviewed ?? false)
                    {
                        var newwpm = db.WorkPlanMasters.First(x => x.WorkPlanId == addworkplanresponse.Data.WorkPlanId);
                        newwpm.IsReviewed = true;
                        newwpm.ReviewedBy = wpm.ReviewedBy;
                        newwpm.ReviewedDate = wpm.ReviewedDate;
                    }
                    db.SaveChanges();
                    transaction.Commit();
                    vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Sale Order WorkPlan changed successfully." };
                }
                else
                {
                    transaction.Rollback();
                    vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.BadRequest, Message = "Sale Order WorkPlan change failed. Please contact Administrator for more details." };
                }
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                List<string> error = new List<string>();
                transaction?.Rollback();
                try
                {
                    error.Add(ex.InnerException.Message);
                    error.Add(ex.InnerException.Source);
                    error.Add(ex.InnerException.StackTrace);
                }
                catch
                {

                }
                vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message, Errors = error };
            }
            return vmModel;
        }
        public ResultWorkPlanMasterShortList GetAllWorkPlanReport(SearchParamWorkPlanMasterReportVm param)
        {
            ResultWorkPlanMasterShortList vmModel = new ResultWorkPlanMasterShortList();
            var FilteredResult = new List<WorkPlanMasterShortVm>();
            try
            {
                using (var db = new pmsdbContext())
                {
                    vmModel.Data = (from a in db.WorkPlanMasters
                                    join wpo in db.WorkPlanOrders on a.WorkPlanId equals wpo.WorkplanId into wp
                                    from s in wp.DefaultIfEmpty()
                                    join so in db.SaleOrderTables on s.OrderId equals so.SaleOrderId
                                    where a.Disabled != true && (so.CustomerId == param.CustomerId || param.CustomerId == 0)
                                    && (a.WorkPlanDate >= param.FromDate || param.FromDate == null)
                                    && (a.WorkPlanDate <= param.ToDate || param.ToDate == null)
                                    && (so.SaleFormulationCodeId == param.FormulationCodeId || param.FormulationCodeId == 0)
                                    && (so.Status == (int)param.Status || param.Status == null)
                                    && (a.IsReviewed == param.IsReviewed || param.IsReviewed == null)
                                    && (string.IsNullOrEmpty(param.SaleOrderNumber) || so.SaleOrderNumber == param.SaleOrderNumber)
                                    select new WorkPlanMasterShortVm
                                    {
                                        BatchNo = a.BatchNo,
                                        LotNo = a.LotNo,
                                        ProductionDetails = a.ProductionDetails,
                                        WorkPlanDate = a.WorkPlanDate,
                                        WorkPlanId = a.WorkPlanId,
                                        WorkPlanNo = a.WorkPlanNo,
                                        AddedBy = a.AddedBy,
                                        AddedDate = a.AddedDate,
                                        IsReviewed = a.IsReviewed,
                                        ReviewedBy = a.ReviewedBy,
                                        ReviewedDate = a.ReviewedDate,
                                        WorkPlanOrder = (from order in db.WorkPlanOrders
                                                         where order.WorkplanId == a.WorkPlanId
                                                         select new WorkPlanOrderShortVm
                                                         {
                                                             WorkplanId = order.WorkplanId,
                                                             OrderId = order.OrderId,
                                                             WorkPlanOrdersId = order.WorkPlanOrdersId,
                                                             SaleOrder = (from s in db.SaleOrderTables
                                                                          join fc in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fc.SaleFormulationCodeId into fcd
                                                                          from fc in fcd.DefaultIfEmpty()
                                                                          where s.SaleOrderId == order.OrderId
                                                                          select new SaleOrderShortVm
                                                                          {
                                                                              SaleOrderId = s.SaleOrderId,
                                                                              SaleOrderNumber = s.SaleOrderNumber,
                                                                              SaleOrderType = s.SaleOrderType,
                                                                              SaleOrderCode = s.SaleOrderCode,
                                                                              SaleFormulationCode = fc.SaleFormulationCode,
                                                                              Status = (ESalesOrderStatus)s.Status
                                                                          }).OrderByDescending(x => x.SaleOrderId).FirstOrDefault()

                                                         }).OrderByDescending(x => x.WorkplanId).ToList()

                                    }).OrderByDescending(x => x.WorkPlanId).ToList();

                    foreach (var item in vmModel.Data)
                    {
                        if (FilteredResult.Any(x => x.WorkPlanId == item.WorkPlanId) != true)
                        {
                            FilteredResult.Add(item);
                        }
                    }
                    vmModel.Data = FilteredResult;
                }
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message };
            }
            return vmModel;
        }

        public ApiFunctionResponseVm ReviewWorkplan(ReviewWorkPlanVm obj)
        {
            using (var db = new Models.pmsdbContext())
            {
                foreach (var item in obj.SaleOrderId)
                {
                    var res = db.WorkPlanOrders.FirstOrDefault(x => x.WorkplanId == obj.WorkPlanId && x.OrderId == item);
                    db.WorkPlanOrders.Remove(res);
                    UpdateSaleOrderStatusModified((long)res.OrderId, ESalesOrderStatus.WorkPlan, ESalesOrderStatus.NotYet, GlobalData.loggedInUser, true);
                    var enableSOStatusEmailOnCreate = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnWorkPlanRemove" && x.ConfigValue == "true")).ToList();
                    if (enableSOStatusEmailOnCreate.Count == 2)
                    {
                        var emailSaleOrderStatus = SaleOrderEmailStatus.RemovedPlanning;
                        _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(res.OrderId.Value, emailSaleOrderStatus);
                    }
                }
                db.SaveChanges();
                if (obj.deleteWorkplan)
                {
                    var rec = db.WorkPlanMasters.FirstOrDefault(x => x.WorkPlanId == obj.WorkPlanId);
                    db.WorkPlanMasters.Remove(rec);
                }
                else
                {
                    var wpm = db.WorkPlanMasters.FirstOrDefault(x => x.WorkPlanId == obj.WorkPlanId);
                    wpm.IsReviewed = true;
                    wpm.ReviewedBy = GlobalData.loggedInUser;
                    wpm.ReviewedDate = DateTime.Now;
                    db.SaveChanges();
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm UpdateSaleOrderStatusModified(long saleorderid, PmsCommon.ESalesOrderStatus statusToRemove, PmsCommon.ESalesOrderStatus statusToAdd, string addedby, bool removeWorkPlan)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
                        if (so != null)
                        {
                            so.Status = (int)statusToAdd;
                            if (removeWorkPlan == true)
                            {
                                so.WorkPlanStatus = false;
                            }
                            db.SaveChanges();
                        }
                        var sot = db.SaleOrderTimelineTables.FirstOrDefault(x => x.SaleOrderId == saleorderid && x.Status == (int)statusToRemove);
                        if (sot == null)
                        {
                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                            {
                                SaleOrderId = saleorderid,
                                Status = (int)statusToAdd,
                                AddedBy = addedby,
                                AddedDate = System.DateTime.Now
                            });
                            db.SaveChanges();
                        }
                        else if (sot != null)
                        {
                            db.SaleOrderTimelineTables.RemoveRange(sot);
                            db.SaveChanges();
                        }
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");

                    }
                    catch (System.Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }


        }

        public WorkPlanDetailPlanVm GetWorkPlanBysaleorderid(long SaleorderId)
        {

            WorkPlanDetailPlanVm vmModel = new WorkPlanDetailPlanVm();
            
                using (var db = new pmsdbContext())
                {
                vmModel = (from a in db.WorkPlanMasters
                           join wp in db.WorkPlanOrders on a.WorkPlanId equals wp.WorkplanId
                           join sot in db.SaleOrderTables on wp.OrderId equals sot.SaleOrderId
                           where sot.SaleOrderId == SaleorderId
                           select new WorkPlanDetailPlanVm
                           {
                               BatchNo = a.BatchNo,
                               LotNo = a.LotNo,
                               ProductionDetails = a.ProductionDetails,
                               WorkPlanDate = a.WorkPlanDate,
                               WorkPlanId = a.WorkPlanId,
                               WorkPlanNo = a.WorkPlanNo,
                               AddedBy = a.AddedBy,
                               AddedDate = a.AddedDate,
                               WorkPlanTracking = (from a in db.WorkPlanOrderTrackingTables
                                                   where a.SaleOrderId == SaleorderId
                                                   select new WorkPlanOrderTrackingTableDetailVm
                                                   {
                                                       WorkPlanTrackingId = a.WorkPlanTrackingId,
                                                       SaleOrderId = a.SaleOrderId,
                                                       CurrentWorkPlanId = a.CurrentWorkPlanId,
                                                       NewWorkPlanId = a.NewWorkPlanId,
                                                       OrderStatus = (PmsCommon.ESalesOrderStatus)a.OrderStatus,
                                                       ChangedBy = a.ChangedBy,
                                                       ChangedDate = a.ChangedDate

                                                   }).ToList()

                           }).FirstOrDefault();
                }            
            
            return vmModel;
        }



    }
}
