﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class ColorDataFn
    {
        public List<ColorMasterVm> GetAllColors()
        {
            List<ColorMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ColorMasters
                       where a.Disabled != true
                       select new ColorMasterVm
                       {
                           ColorId = a.ColorId,
                           ColorName = a.ColorName,
                           ColorCode = a.ColorCode,
                           ColorDesc = a.ColorDesc,
                           ColorAddedBy = a.ColorAddedBy,
                           ColorAddedDate = a.ColorAddedDate
                       }).OrderBy(x => x.ColorCode).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateColor(ColorMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.ColorId == 0)
                {
                    var rec = db.ColorMasters.Where(x => x.ColorCode == br.ColorCode && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                ColorMaster res = new ColorMaster();
                if (br.ColorId == 0)
                {
                    res.ColorName = br.ColorName;
                    res.ColorCode = br.ColorCode;
                    res.ColorDesc = br.ColorDesc;
                    res.ColorAddedBy = br.ColorAddedBy;
                    res.ColorAddedDate = System.DateTime.Now;
                    db.ColorMasters.Add(res);
                }
                else
                {
                    res = db.ColorMasters.Where(x => x.ColorId == br.ColorId).FirstOrDefault();
                    if (res != null)
                    {
                        res.ColorName = br.ColorName;
                        res.ColorCode = br.ColorCode;
                        res.ColorDesc = br.ColorDesc;
                        res.ColorAddedBy = br.ColorAddedBy;
                        res.ColorAddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DeleteColor(ColorMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        ColorMaster item = db.ColorMasters.FirstOrDefault(x => x.ColorId == param.ColorId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
