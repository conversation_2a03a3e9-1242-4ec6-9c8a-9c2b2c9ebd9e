using PmsCore.Notifications.Interfaces;
using System.Linq;
using PmsCommon;
using Microsoft.Extensions.Logging;
using System;
using PmsEntity.ViewModel;
using PmsCore.PDFGeneration.Interfaces;
using System.Net;

namespace PmsData.DataFn
{
    public class NotificationProcessorDataFn
    {
        private readonly ILogger<NotificationProcessorDataFn> _logger;
        private readonly GlobalDataEntity _globalData;
        private readonly INotificationService _notificationService;
        private readonly IPdfService _pdfService;

        public NotificationProcessorDataFn(
            ILogger<NotificationProcessorDataFn> logger,
            GlobalDataEntity globalData,
            INotificationService notificationService,
            IPdfService pdfService)
        {
            _logger = logger;
            _globalData = globalData;
            _notificationService = notificationService;
            _pdfService = pdfService;
        }

        /// <summary>
        /// Triggers an on-demand notification for the specified notification group
        /// </summary>
        /// <param name="notificationRequest">The on-demand notification request containing group ID and parameters</param>
        /// <returns>Response indicating success or failure</returns>
        public ApiFunctionResponseVm TriggerOnDemandNotification(PmsCore.Notifications.Models.OnDemandNotificationRequest notificationRequest)
        {
            _logger.LogInformation("Data layer: Processing on-demand notification for type {NotificationType}, report {ReportName}",
                notificationRequest.NotificationType, notificationRequest.ReportName);

            try
            {
                // Verify the notification group exists and is configured for on-demand notifications
                using (var db = new Models.pmsdbContext())
                {
                    var notificationGroup = db.NotificationGroupsTables
                        .FirstOrDefault(ng => ng.NotificationType == notificationRequest.NotificationType
                         && ng.ReportName == notificationRequest.ReportName && ng.Disabled != true && ng.NotificationGroupUserId == notificationRequest.NotificationGroupUserId);

                    if (notificationGroup == null)
                    {
                        _logger.LogWarning("Data layer: Notification type {NotificationType} for report {ReportName} not found or disabled",
                            notificationRequest.NotificationType, notificationRequest.ReportName);
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                            $"Notification group {notificationRequest.NotificationType} for report {notificationRequest.ReportName} not found or is disabled");
                    }

                    // Check if this is an on-demand notification group
                    if (notificationGroup.TriggerType == null ||
                        (!notificationGroup.TriggerType.Contains("OnDemand") && !notificationGroup.NotificationType.Contains("OnDemand")))
                    {
                        _logger.LogWarning("Data layer: Notification type {NotificationType} for report {ReportName} is not configured for on-demand notifications",
                            notificationRequest.NotificationType, notificationRequest.ReportName);
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                            $"Notification group {notificationRequest.NotificationType} for report {notificationRequest.ReportName} is not configured for on-demand notifications");
                    }

                    // Update the notification group's last triggered info
                    notificationGroup.LastTriggeredBy = _globalData.loggedInUser;
                    notificationGroup.LastTriggeredDate = DateTime.Now;
                    db.SaveChanges();
                }

                // Process the notification based on the report type
                if (notificationRequest.ReportName.Equals("YieldReportSummary", StringComparison.OrdinalIgnoreCase))
                {
                    // Extract fromDate and toDate from parameters
                    if (notificationRequest.Parameters == null ||
                        !notificationRequest.Parameters.TryGetValue("fromDate", out string fromDateStr) ||
                        !notificationRequest.Parameters.TryGetValue("toDate", out string toDateStr) ||
                        !DateTime.TryParse(fromDateStr, out DateTime fromDate) ||
                        !DateTime.TryParse(toDateStr, out DateTime toDate))
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                            "Missing or invalid fromDate and toDate parameters for YieldReportSummary");
                    }

                    _logger.LogInformation("Triggering YieldReportSummary for period {FromDate} to {ToDate}",
                        fromDate.ToString("yyyy-MM-dd"), toDate.ToString("yyyy-MM-dd"));

                    // Call the notification service to send the yield report
                    _notificationService.SendYieldReportSummaryWhatsApp(fromDate, toDate, "OnDemand").GetAwaiter().GetResult();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK,
                        $"Yield Report Summary notification has been triggered successfully for period {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                }
                else if (notificationRequest.ReportName.Equals("LowStockReport", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInformation("Triggering LowStockReport notification");

                    // Call the notification service to send the low stock report
                    _notificationService.SendLowStockReportNotification("OnDemand").GetAwaiter().GetResult();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK,
                        "Low Stock Report notification has been triggered successfully");
                }
                else if (notificationRequest.ReportName.Equals("ReturnableOutPassReport", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInformation("Triggering ReturnableOutPassReport notification");

                    // Call the notification service to send the pending returnable outpass report
                    _notificationService.SendPendingReturnableOutPassReportNotification("OnDemand").GetAwaiter().GetResult();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK,
                        "Pending Returnable OutPass Report notification has been triggered successfully");
                }
                else if (notificationRequest.NotificationType.Equals("ReturnableOutPassReminder", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInformation("Triggering ReturnableOutPassReminder notification");

                    // For reminder notifications, we need an OutpassId parameter
                    if (!notificationRequest.Parameters.ContainsKey("OutpassId") ||
                        !long.TryParse(notificationRequest.Parameters["OutpassId"], out long outpassId))
                    {
                        _logger.LogWarning("OutpassId parameter is required for ReturnableOutPassReminder notification");
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                            "OutpassId parameter is required for ReturnableOutPassReminder notification");
                    }

                    // Call the notification service to send the pending returnable outpass reminder
                    _notificationService.SendPendingReturnableOutPassReminderNotification(outpassId, "OnDemand").GetAwaiter().GetResult();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK,
                        $"Pending Returnable OutPass Reminder notification has been triggered successfully for OutPass ID: {outpassId}");
                }
                else if (notificationRequest.ReportName.Equals("ExecutiveCostingReport", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInformation("Triggering ExecutiveCostingReport notification");

                    // Call the notification service to send the executive costing report
                    _notificationService.SendCostingReportPDFNotification(DateTime.Now.AddDays(-30), DateTime.Now, "OnDemand", notificationRequest.NotificationGroupUserId).GetAwaiter().GetResult();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK,
                        "Executive Costing Report notification has been triggered successfully");
                }
                else if (notificationRequest.NotificationType.Equals("OverheadCostUpdateReminder", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInformation("Triggering OverheadCostUpdateReminder notification");

                    // Call the notification service to send the overhead cost update reminder
                    _notificationService.SendScheduledOverheadCostUpdateReminders("OnDemand").GetAwaiter().GetResult();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK,
                        "Overhead Cost Update Reminder notification has been triggered successfully");
                }
                else
                {
                    _logger.LogWarning("Unsupported report type: {ReportName}", notificationRequest.ReportName);
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                        $"Unsupported report type: {notificationRequest.ReportName}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Data layer: Error processing on-demand notification for type {NotificationType}, report {ReportName}",
                    notificationRequest.NotificationType, notificationRequest.ReportName);
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError,
                    "An error occurred while processing the notification: " + ex.Message);
            }
        }
    }
}

