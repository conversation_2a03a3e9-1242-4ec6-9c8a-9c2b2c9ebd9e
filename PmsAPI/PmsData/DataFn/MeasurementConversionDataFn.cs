﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class MeasurementConversionDataFn
    {
        public List<MeasurementConversionMasterVm> GetAllMeasurementConversion()
        {
            List<MeasurementConversionMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.MeasurementConversionMasters
                       select new MeasurementConversionMasterVm
                       {
                           ConversionId = a.ConversionId,
                           FromUnit = a.FromUnit,
                           ToUnit = a.ToUnit,
                           ConversionValue = a.ConversionValue,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderByDescending(x => x.ConversionId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateMeasurementConversion(MeasurementConversionMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.ConversionId == 0)
                {
                    var rec = db.MeasurementConversionMasters.Where(x => x.FromUnit == br.FromUnit && x.ToUnit == br.ToUnit).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                MeasurementConversionMaster res = new MeasurementConversionMaster();
                if (br.ConversionId == 0)
                {
                    res.FromUnit = br.FromUnit;
                    res.ToUnit = br.ToUnit;
                    res.ConversionValue = br.ConversionValue;
                    res.AddedBy = br.AddedBy;
                    res.AddedDate = System.DateTime.Now;
                    db.MeasurementConversionMasters.Add(res);
                }
                else
                {
                    res = db.MeasurementConversionMasters.Where(x => x.ConversionId == br.ConversionId).FirstOrDefault();
                    if (res != null)
                    {
                        res.FromUnit = br.FromUnit;
                        res.ToUnit = br.ToUnit;
                        res.ConversionValue = br.ConversionValue;
                        res.AddedBy = br.AddedBy;
                        res.AddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
    }
}
