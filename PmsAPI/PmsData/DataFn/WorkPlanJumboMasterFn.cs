﻿using Microsoft.Graph.TermStore;
using PmsCommon;
using PmsData.Models;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace PmsData.DataFn
{
    public class WorkPlanJumboMasterFn
    {
        public GlobalDataEntity GlobalData;
        public WorkPlanJumboMasterFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ResultWorkPlanJumboMasterList GetWorkPlanJumboByWorkPlanOrdersId(long saleorderId)
        {
            ResultWorkPlanJumboMasterList vmModel = new ResultWorkPlanJumboMasterList();
            try
            {
                using (var db = new pmsdbContext())
                {
                    vmModel.Data = (from model in db.WorkPlanJumboMasters
                                    join so in db.SaleOrderTables on model.SaleOrderId equals so.SaleOrderId
                                    join wo in db.WorkPlanOrders on model.SaleOrderId equals wo.OrderId
                                    join wp in db.WorkPlanMasters on wo.WorkplanId equals wp.WorkPlanId
                                    join ssw in db.FactoryWorkersMasters on model.ShiftSupervisorWorkerId equals ssw.WorkerId into sswd
                                    from ssw in sswd.DefaultIfEmpty()
                                    where model.SaleOrderId == saleorderId
                                    select new WorkPlanJumboMasterVm
                                    {
                                        WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                                        SaleOrderId = model.SaleOrderId,
                                        SaleOrderNumber = so.SaleOrderNumber,
                                        JumboRollDate = model.JumboRollDate,
                                        JumboRollStartTime = model.JumboRollStartTime,
                                        JumboRollEndTime = model.JumboRollEndTime,
                                        JumboNo = model.JumboNo,
                                        Rate = model.Rate,
                                        Amount = model.Amount,
                                        JumboRolQty = model.JumboRolQty,
                                        ActualQuantity = model.ActualQuantity,
                                        WastageEmbossing = model.WastageEmbossing,
                                        WastageLacquer = model.WastageLacquer,
                                        WastagePrint = model.WastagePrint,
                                        WastageTumbling = model.WastageTumbling,
                                        WastageVacuum = model.WastageVacuum,
                                        Weight = model.Weight,
                                        RackId = model.RackId,
                                        StoreId = model.StoreId,
                                        RackCode = model.RackCode,
                                        RackName = model.RackName,
                                        StoreCode = model.StoreCode,
                                        StoreName = model.StoreName,
                                        Remark = model.Remark,
                                        AddedBy = model.AddedBy,
                                        AddedDate = model.AddedDate,
                                        Yield = model.Yield,
                                        IsInspectionCompleted = model.IsInspectionCompleted,
                                        ShiftSupervisorWorkerName = ssw.Name,
                                        ProductionLineNo = wp.ProductionLineNo,
                                        JumboInspection = (from j in db.JumboInspectionTables
                                                           join spt in db.StockProductTables on j.StockId equals spt.StockId into sptd
                                                           from spt in sptd.DefaultIfEmpty()
                                                           join spat in db.StockProductAllocationTables on spt.StockProductId equals spat.StockProductId
                                                           join rm in db.RackMasters on spat.RackId equals rm.RackId
                                                           join sm in db.StoreMasters on rm.StoreId equals sm.StoreId
                                                           where j.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId
                                                           select new JumboInspectionTableVm
                                                           {
                                                               JumboInspectionId = j.JumboInspectionId,
                                                               WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                                                               Grade = j.Grade,
                                                               Code = j.Code,
                                                               Unit = j.Unit,
                                                               Quantity = j.Quantity,
                                                               Weight = j.Weight,
                                                               AddedBy = j.AddedBy,
                                                               AddedDate = j.AddedDate,
                                                               InspectedBy = j.InspectedBy,
                                                               RollType = j.RollType,
                                                               DispatchStatus = j.DispatchStatus,
                                                               RackId = rm.RackId,
                                                               RackName = rm.RackName,
                                                               StoreId = rm.StoreId,
                                                               StoreName = sm.StoreName
                                                           }).ToList(),
                                    }).OrderByDescending(x => x.WorkPlanJumboMasterId).ToList();
                }
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message };
            }
            return vmModel;
        }

        public ResultWorkPlanJumboMasterList GetAllWorkPlanJumbo()
        {
            ResultWorkPlanJumboMasterList vmModel = new ResultWorkPlanJumboMasterList();
            try
            {
                using (var db = new pmsdbContext())
                {
                    vmModel.Data = (from model in db.WorkPlanJumboMasters
                                    join a in db.SaleOrderTables on model.SaleOrderId equals a.SaleOrderId
                                    join sfcm in db.SaleFormulationCodeMasters on a.SaleFormulationCodeId equals sfcm.SaleFormulationCodeId
                                    join c in db.CustomerMasters on a.CustomerId equals c.CustomerId
                                    join wo in db.WorkPlanOrders on a.SaleOrderId equals wo.OrderId
                                    join wpm in db.WorkPlanMasters on wo.WorkplanId equals wpm.WorkPlanId
                                    where wpm.Disabled != true
                                    select new WorkPlanJumboMasterVm
                                    {
                                        WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                                        WorkPlanId = wpm.WorkPlanId,
                                        WorkPlanNo = wpm.WorkPlanNo,
                                        JumboRollDate = model.JumboRollDate,
                                        JumboRollStartTime = model.JumboRollStartTime,
                                        JumboRollEndTime = model.JumboRollEndTime,
                                        JumboNo = model.JumboNo,
                                        Rate = model.Rate,
                                        Amount = model.Amount,
                                        JumboRolQty = model.JumboRolQty,
                                        ActualQuantity = model.ActualQuantity,
                                        WastageEmbossing = model.WastageEmbossing,
                                        WastageLacquer = model.WastageLacquer,
                                        WastagePrint = model.WastagePrint,
                                        WastageTumbling = model.WastageTumbling,
                                        WastageVacuum = model.WastageVacuum,
                                        Weight = model.Weight,
                                        RackId = model.RackId,
                                        StoreId = model.StoreId,
                                        RackCode = model.RackCode,
                                        RackName = model.RackName,
                                        StoreCode = model.StoreCode,
                                        StoreName = model.StoreName,
                                        Remark = model.Remark,
                                        AddedBy = model.AddedBy,
                                        AddedDate = model.AddedDate,
                                        CustomerId = c.CustomerId,
                                        CustomerName = c.CustomerName,
                                        SaleOrderId = a.SaleOrderId,
                                        SaleOrderNumber = a.SaleOrderNumber,
                                        IsInspectionCompleted = model.IsInspectionCompleted,
                                        Yield = model.Yield,
                                        IsLiningOrder = db.LinkedSaleOrderTables.Where(x => x.LinkedSaleOrder == a.SaleOrderId).FirstOrDefault() != null,
                                        SaleFormulationCodeName = sfcm.SaleFormulationCode,
                                        SaleOrderStatus = (PmsCommon.ESalesOrderStatus)a.Status,
                                        JumboInspection = (from j in db.JumboInspectionTables
                                                           where j.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId
                                                           select new JumboInspectionTableVm
                                                           {
                                                               JumboInspectionId = j.JumboInspectionId,
                                                               WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                                                               Grade = j.Grade,
                                                               Code = j.Code,
                                                               Quantity = j.Quantity,
                                                               Weight = j.Weight,
                                                               AddedBy = j.AddedBy,
                                                               AddedDate = j.AddedDate,
                                                               InspectedBy = j.InspectedBy,
                                                               RollType = j.RollType
                                                           }).ToList(),
                                    }).OrderByDescending(x => x.WorkPlanJumboMasterId).ToList();
                }
                foreach (var item in vmModel.Data)
                {
                    if (item.JumboRollStartTime != null && item.JumboRollEndTime != null)
                    {
                        TimeSpan? TimeDifference = item.JumboRollEndTime - item.JumboRollStartTime;
                        item.JumboRollPrdSpeed = decimal.Round(item.JumboRolQty.Value / (decimal)TimeDifference.Value.TotalMinutes, 2);
                    }
                    else
                        item.JumboRollPrdSpeed = 0;
                }
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                vmModel.Result = new ApiResult
                {
                    Succeeded = false,
                    Code = EMessageCode.Exception,
                    Message = ex.Message
                };
            }
            return vmModel;
        }
        public ResultWorkPlanJumboMasterList GetAllWorkPlanJumboWithFilters(JumboFinalInspectionFilter filter)
        {
            ResultWorkPlanJumboMasterList vmModel = new ResultWorkPlanJumboMasterList();
            try
            {
                using (var db = new pmsdbContext())
                {
                    vmModel.Data = (from model in db.WorkPlanJumboMasters
                                    join a in db.SaleOrderTables on model.SaleOrderId equals a.SaleOrderId
                                    join sop in db.SaleOrderProductionTables on a.SaleOrderId equals sop.SaleOrderId
                                    join sfcm in db.SaleFormulationCodeMasters on a.SaleFormulationCodeId equals sfcm.SaleFormulationCodeId
                                    join c in db.CustomerMasters on a.CustomerId equals c.CustomerId
                                    join wo in db.WorkPlanOrders on a.SaleOrderId equals wo.OrderId
                                    join wpm in db.WorkPlanMasters on wo.WorkplanId equals wpm.WorkPlanId
                                    where wpm.Disabled != true
                                    && (model.IsInspectionCompleted == (filter.IsInspectionCompleted == false ? null : true) || model.IsInspectionCompleted == filter.IsInspectionCompleted)
                                    && (filter.FromDate == null || model.AddedDate >= filter.FromDate)
                                    && (filter.ToDate == null || model.AddedDate <= filter.ToDate)
                                    && (string.IsNullOrEmpty(filter.SaleOrderNumber) || a.SaleOrderNumber.ToLower().Contains(filter.SaleOrderNumber))
                                    && (string.IsNullOrEmpty(filter.JumboNumber) || model.JumboNo.ToLower().Contains(filter.JumboNumber))
                                    && (filter.CustomerId == 0 || a.CustomerId == filter.CustomerId)
                                    && (filter.SaleFormulationCodeId == 0 || a.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                                    && (filter.ColorId == 0 || sop.ColorId == filter.ColorId)
                                    && (filter.GrainId == 0 || sop.GrainId == filter.GrainId)
                                    select new WorkPlanJumboMasterVm
                                    {
                                        WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                                        WorkPlanId = wpm.WorkPlanId,
                                        WorkPlanNo = wpm.WorkPlanNo,
                                        JumboRollDate = model.JumboRollDate,
                                        JumboRollStartTime = model.JumboRollStartTime,
                                        JumboRollEndTime = model.JumboRollEndTime,
                                        JumboNo = model.JumboNo,
                                        Rate = model.Rate,
                                        Amount = model.Amount,
                                        JumboRolQty = model.JumboRolQty,
                                        ActualQuantity = model.ActualQuantity,
                                        WastageEmbossing = model.WastageEmbossing,
                                        WastageLacquer = model.WastageLacquer,
                                        WastagePrint = model.WastagePrint,
                                        WastageTumbling = model.WastageTumbling,
                                        WastageVacuum = model.WastageVacuum,
                                        Weight = model.Weight,
                                        RackId = model.RackId,
                                        StoreId = model.StoreId,
                                        RackCode = model.RackCode,
                                        RackName = model.RackName,
                                        StoreCode = model.StoreCode,
                                        StoreName = model.StoreName,
                                        Remark = model.Remark,
                                        AddedBy = model.AddedBy,
                                        AddedDate = model.AddedDate,
                                        CustomerId = c.CustomerId,
                                        CustomerName = c.CustomerName,
                                        SaleOrderId = a.SaleOrderId,
                                        SaleOrderNumber = a.SaleOrderNumber,
                                        IsInspectionCompleted = model.IsInspectionCompleted,
                                        Yield = model.Yield,
                                        IsLiningOrder = db.LinkedSaleOrderTables.Where(x => x.LinkedSaleOrder == a.SaleOrderId).FirstOrDefault() != null,
                                        SaleFormulationCodeName = sfcm.SaleFormulationCode,
                                        SaleOrderStatus = (PmsCommon.ESalesOrderStatus)a.Status,
                                        JumboInspection = (from j in db.JumboInspectionTables
                                                           where j.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId
                                                           select new JumboInspectionTableVm
                                                           {
                                                               JumboInspectionId = j.JumboInspectionId,
                                                               WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                                                               Grade = j.Grade,
                                                               Code = j.Code,
                                                               Quantity = j.Quantity,
                                                               Weight = j.Weight,
                                                               AddedBy = j.AddedBy,
                                                               AddedDate = j.AddedDate,
                                                               InspectedBy = j.InspectedBy,
                                                               RollType = j.RollType
                                                           }).ToList(),
                                    }).OrderByDescending(x => x.WorkPlanJumboMasterId).ToList();
                }
                foreach (var item in vmModel.Data)
                {
                    if (item.JumboRollStartTime != null && item.JumboRollEndTime != null)
                    {
                        DateTime startTime = item.JumboRollStartTime.Value;
                        DateTime endTime = item.JumboRollEndTime.Value;

                        // Check if the end time is earlier than the start time, indicating a span over midnight
                        if (endTime < startTime)
                        {
                            endTime = endTime.AddDays(1);
                        }

                        TimeSpan TimeDifference = endTime - startTime;
                        item.JumboRollPrdSpeed = decimal.Round(item.JumboRolQty.Value / (decimal)TimeDifference.TotalMinutes, 2);
                    }
                    else
                        item.JumboRollPrdSpeed = 0;
                }
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                vmModel.Result = new ApiResult
                {
                    Succeeded = false,
                    Code = EMessageCode.Exception,
                    Message = ex.Message
                };
            }
            return vmModel;
        }

        public WorkPlanJumboMasterVm GetWorkPlanJumboByJumboNumber(string jumbonumber)
        {
            using (var db = new pmsdbContext())
            {
                var data = (from model in db.WorkPlanJumboMasters
                            join a in db.SaleOrderTables on model.SaleOrderId equals a.SaleOrderId
                            join c in db.CustomerMasters on a.CustomerId equals c.CustomerId
                            join wp in db.WorkPlanOrders on a.SaleOrderId equals wp.OrderId
                            where model.JumboNo.ToLower() == jumbonumber.ToLower()
                            select new WorkPlanJumboMasterVm
                            {
                                WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                                WorkPlanId = wp.WorkplanId,
                                JumboRollDate = model.JumboRollDate,
                                JumboRollStartTime = model.JumboRollStartTime,
                                JumboRollEndTime = model.JumboRollEndTime,
                                JumboNo = model.JumboNo,
                                Rate = model.Rate,
                                Amount = model.Amount,
                                JumboRolQty = model.JumboRolQty,
                                ActualQuantity = model.ActualQuantity,
                                WastageEmbossing = model.WastageEmbossing,
                                WastageLacquer = model.WastageLacquer,
                                WastagePrint = model.WastagePrint,
                                WastageTumbling = model.WastageTumbling,
                                WastageVacuum = model.WastageVacuum,
                                Weight = model.Weight,
                                RackId = model.RackId,
                                StoreId = model.StoreId,
                                RackCode = model.RackCode,
                                RackName = model.RackName,
                                StoreCode = model.StoreCode,
                                StoreName = model.StoreName,
                                Remark = model.Remark,
                                AddedBy = model.AddedBy,
                                AddedDate = model.AddedDate,
                                CustomerId = c.CustomerId,
                                CustomerName = c.CustomerName,
                                SaleOrderId = a.SaleOrderId,
                                SaleOrderNumber = a.SaleOrderNumber,
                                IsInspectionCompleted = model.IsInspectionCompleted,
                                Yield = model.Yield,
                                SaleOrderStatus = (PmsCommon.ESalesOrderStatus)a.Status,
                                JumboInspection = (from j in db.JumboInspectionTables
                                                   where j.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId
                                                   select new JumboInspectionTableVm
                                                   {
                                                       JumboInspectionId = j.JumboInspectionId,
                                                       WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                                                       Grade = j.Grade,
                                                       Code = j.Code,
                                                       Quantity = j.Quantity,
                                                       Weight = j.Weight,
                                                       AddedBy = j.AddedBy,
                                                       AddedDate = j.AddedDate,
                                                       InspectedBy = j.InspectedBy,
                                                       RollType = j.RollType
                                                   }).ToList(),
                            }).FirstOrDefault();
                return data;
            }
        }


        public ResultWorkPlanJumboMaster AddWorkPlanJumbo(List<WorkPlanJumboMasterVm> modellist)
        {
            ResultWorkPlanJumboMaster vmModel = new ResultWorkPlanJumboMaster();
            try
            {
                using (var db = new pmsdbContext())
                {
                    var qty = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == modellist[0].SaleOrderId).ManufacturingQuantity;
                    var qtysum = modellist.Sum(x => x.JumboRolQty);
                    if (qtysum > qty)
                    {
                        vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = "Jumbo quantity exceeds manufacturing quantity", Errors = new List<string>() };
                        return vmModel;
                    }

                    foreach (var model in modellist)
                    {
                        var rackDetails = (from rack in db.RackMasters
                                           join store in db.StoreMasters on rack.StoreId equals store.StoreId
                                           where rack.RackId == model.RackId
                                           select new WorkPlanJumboMasterVm
                                           {
                                               RackId = rack.RackId,
                                               StoreId = store.StoreId,
                                               RackCode = rack.RackCode,
                                               RackName = rack.RackName,
                                               StoreCode = store.StoreCode,
                                               StoreName = store.StoreName,

                                           }).FirstOrDefault();

                        model.RackId = rackDetails.RackId;
                        model.StoreId = rackDetails.StoreId;
                        model.RackCode = rackDetails.RackCode;
                        model.RackName = rackDetails.RackName;
                        model.StoreCode = rackDetails.StoreCode;
                        model.StoreName = rackDetails.StoreName;

                        WorkPlanJumboMaster data = new WorkPlanJumboMaster();
                        data.Remark = model.Remark;

                        data.SaleOrderId = model.SaleOrderId;
                        data.JumboRollDate = model.JumboRollDate;
                        data.JumboRollStartTime = model.JumboRollStartTime;
                        data.JumboRollEndTime = model.JumboRollEndTime;
                        //data.JumboNo = model.JumboNo;
                        data.Rate = model.Rate;
                        data.Amount = model.Amount;
                        data.JumboRolQty = model.JumboRolQty;
                        data.ActualQuantity = model.JumboRolQty;
                        data.Weight = model.Weight;
                        data.RackId = model.RackId;
                        data.AddedBy = GlobalData.loggedInUser;
                        data.AddedDate = System.DateTime.Now;
                        db.WorkPlanJumboMasters.Add(data);
                        db.SaveChanges();
                        long ar = data.WorkPlanJumboMasterId + 50000;
                        data.JumboNo = "ZJ-" + ar;
                        db.SaveChanges();
                    }
                    var sopid = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == modellist[0].OrderId).SaleOrderProductionId;

                    var IsPrintRequired = db.SaleOrderProductionPrintMasters.Any(x => x.SaleOrderProductionId == sopid && x.Removed != true);
                    var IsEmbossingRequired = db.SaleOrderProductionEmbossingMasters.Any(x => x.SaleOrderProductionId == sopid && x.Removed != true);
                    var IsLacquerRequired = db.SaleOrderProductionLacquerRawMaterialTables.Any(x => x.SaleOrderProductionId == sopid && x.Removed != true);
                    var IsTumblingRequired = db.SaleOrderProductionTumblingMasters.Any(x => x.SaleOrderProductionId == sopid && x.Removed != true);
                    var IsVacuumRequired = db.SaleOrderProductionVacuumMasters.Any(x => x.SaleOrderProductionId == sopid && x.Removed != true);
                    var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == modellist[0].OrderId);
                    if (!IsPrintRequired && !IsEmbossingRequired && !IsLacquerRequired && !IsTumblingRequired && !IsVacuumRequired)
                    {
                        so.Status = (int)ESalesOrderStatus.JumboInspection;
                        if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.JumboInspection))
                        {
                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                            {
                                SaleOrderId = so.SaleOrderId,
                                Status = (int)ESalesOrderStatus.JumboInspection,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = System.DateTime.Now,
                                WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                            });
                            db.SaveChanges();
                            var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnProductionCompleted" && x.ConfigValue == "true")).ToList();
                            if (enableSOStatusEmail.Count == 2)
                            {
                                var emailSaleOrderStatus = SaleOrderEmailStatus.ProductionCompleted;
                                _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(modellist[0].OrderId.Value, emailSaleOrderStatus);
                            }
                        }
                    }
                    else
                    {
                        so.Status = (int)ESalesOrderStatus.MoveToPostProcess;
                        if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.MoveToPostProcess))
                        {
                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                            {
                                SaleOrderId = so.SaleOrderId,
                                Status = (int)ESalesOrderStatus.MoveToPostProcess,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = System.DateTime.Now,
                                WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                            });
                            db.SaveChanges();
                        }
                    }
                    db.SaveChanges();


                }

                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };

            }
            catch (Exception ex)
            {
                List<string> error = new List<string>();
                error.Add(ex.InnerException.Message);
                error.Add(ex.InnerException.Source);
                error.Add(ex.InnerException.StackTrace);
                vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message, Errors = error };
            }
            return vmModel;
        }

        public ResultWorkPlanJumboMaster AddWorkPlanJumboSingleObj(WorkPlanJumboMasterVm model)
        {
            ResultWorkPlanJumboMaster vmModel = new ResultWorkPlanJumboMaster();

            using (var db = new pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var qty = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == model.SaleOrderId).OrderQuantity;
                        var existJumboQty = db.WorkPlanJumboMasters.Where(x => x.SaleOrderId == model.SaleOrderId).Sum(x => x.JumboRolQty);
                        var qtysum = model.JumboRolQty + existJumboQty;
                        if ((qtysum - 4000) > qty)
                        {
                            vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = "Jumbo quantity cannot exceed order quantity", Errors = new List<string>() };
                            return vmModel;
                        }

                        WorkPlanJumboMaster data = new WorkPlanJumboMaster();
                        data.Remark = model.Remark;

                        data.SaleOrderId = model.SaleOrderId;
                        data.JumboRollDate = TimeZoneHelper.ConvertToTimeZone(model.JumboRollStartTime.Value, TimeZoneId.IndiaStandardTime).Date;
                        data.JumboRollStartTime = model.JumboRollStartTime;
                        data.JumboRollEndTime = model.JumboRollEndTime;
                        //data.JumboNo = model.JumboNo;
                        data.Rate = model.Rate;
                        data.Amount = model.Amount;
                        data.JumboRolQty = model.JumboRolQty;
                        data.ActualQuantity = model.JumboRolQty;
                        data.Weight = model.Weight;
                        data.RackId = model.RackId;
                        data.AddedBy = GlobalData.loggedInUser;
                        data.AddedDate = System.DateTime.Now;
                        data.ShiftSupervisorWorkerId = model.ShiftSupervisorWorkerId;
                        db.WorkPlanJumboMasters.Add(data);
                        db.SaveChanges();
                        long ar = data.WorkPlanJumboMasterId + 50000;
                        data.JumboNo = "ZJ-" + ar;
                        db.SaveChanges();

                        var res = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == model.SaleOrderId);
                        res.ManufacturingQuantity = qtysum;
                        db.SaveChanges();

                        var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == model.SaleOrderId);
                        if (so != null && so.Status != (int)ESalesOrderStatus.InJumbo)
                        {
                            so.Status = (int)ESalesOrderStatus.InJumbo;
                            db.SaveChanges();
                            if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.InJumbo))
                            {
                                db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                {
                                    SaleOrderId = so.SaleOrderId,
                                    Status = (int)ESalesOrderStatus.InJumbo,
                                    AddedBy = GlobalData.loggedInUser,
                                    AddedDate = System.DateTime.Now,
                                    WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                                });
                                db.SaveChanges();
                            }
                        }

                        transaction.Commit();

                        vmModel.Data = new WorkPlanJumboSingleObjResponseVm
                        {
                            WorkPlanJumboMasterId = data.WorkPlanJumboMasterId,
                            JumboNo = data.JumboNo,
                            JumboRollDate = data.JumboRollDate,
                            JumboRollStartTime = data.JumboRollStartTime,
                            JumboRollEndTime = data.JumboRollEndTime,
                            Rate = data.Rate,
                            Amount = data.Amount,
                            JumboRolQty = data.JumboRolQty,
                            Weight = data.Weight,
                            RackId = data.RackId
                        };
                        vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        List<string> error = new List<string>();
                        error.Add(ex.InnerException.Message);
                        error.Add(ex.InnerException.Source);
                        error.Add(ex.InnerException.StackTrace);
                        vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message, Errors = error };
                    }
                }
            }
            return vmModel;
        }

        public List<WorkPlanJumboMasterVm> GetJumboListWithInspectionCount()
        {
            using (var db = new pmsdbContext())
            {
                var res = (from model in db.WorkPlanJumboMasters
                           join a in db.SaleOrderTables on model.SaleOrderId equals a.SaleOrderId
                           join c in db.CustomerMasters on a.CustomerId equals c.CustomerId
                           join sfcm in db.SaleFormulationCodeMasters on a.SaleFormulationCodeId equals sfcm.SaleFormulationCodeId
                           join p in db.ProductMasters on sfcm.FabricProductId equals p.ProductId
                           where model.IsInspectionCompleted == true
                           select new WorkPlanJumboMasterVm
                           {
                               WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                               JumboRollDate = model.JumboRollDate,
                               JumboRollStartTime = model.JumboRollStartTime,
                               JumboRollEndTime = model.JumboRollEndTime,
                               JumboNo = model.JumboNo,
                               FabricName = p.ProductName,
                               Rate = model.Rate,
                               Amount = model.Amount,
                               JumboRolQty = model.JumboRolQty,
                               ActualQuantity = model.ActualQuantity,
                               WastageEmbossing = model.WastageEmbossing,
                               WastageLacquer = model.WastageLacquer,
                               WastagePrint = model.WastagePrint,
                               WastageTumbling = model.WastageTumbling,
                               WastageVacuum = model.WastageVacuum,
                               Weight = model.Weight,
                               RackId = model.RackId,
                               StoreId = model.StoreId,
                               RackCode = model.RackCode,
                               RackName = model.RackName,
                               StoreCode = model.StoreCode,
                               StoreName = model.StoreName,
                               Remark = model.Remark,
                               AddedBy = model.AddedBy,
                               AddedDate = model.AddedDate,
                               CustomerId = c.CustomerId,
                               CustomerName = c.CustomerName,
                               SaleOrderId = a.SaleOrderId,
                               SaleOrderNumber = a.SaleOrderNumber,
                               IsInspectionCompleted = model.IsInspectionCompleted,
                               Yield = model.Yield,
                               SaleOrderCode = a.SaleOrderCode,
                               SaleOrderStatus = (PmsCommon.ESalesOrderStatus)a.Status,
                               //JumboInspection = (from j in db.JumboInspectionTables
                               //                   where j.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId
                               //                   select new JumboInspectionTableVm
                               //                   {
                               //                       JumboInspectionId = j.JumboInspectionId,
                               //                       WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                               //                       Grade = j.Grade,
                               //                       Code = j.Code,
                               //                       Quantity = j.Quantity,
                               //                       AddedBy = j.AddedBy,
                               //                       AddedDate = j.AddedDate,
                               //                       InspectedBy = j.InspectedBy
                               //                   }).ToList(),
                               FirstGrade = db.JumboInspectionTables.FirstOrDefault(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.Grade == "1st").Quantity ?? 0,
                               AGrade = db.JumboInspectionTables.FirstOrDefault(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.Grade == "A").Quantity ?? 0,
                               CUTPCGrade = db.JumboInspectionTables.FirstOrDefault(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.Grade == "CUT-PC").Quantity ?? 0,
                               FILMGrade = db.JumboInspectionTables.FirstOrDefault(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.Grade == "FILM").Quantity ?? 0,
                               LOTGrade = db.JumboInspectionTables.FirstOrDefault(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.Grade == "LOT").Quantity ?? 0,
                               NSGrade = db.JumboInspectionTables.FirstOrDefault(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.Grade == "NS").Quantity ?? 0,
                               WASTEGrade = db.JumboInspectionTables.FirstOrDefault(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.Grade == "WASTE").Quantity ?? 0,
                               SampleQuantity = db.JumboInspectionTables.FirstOrDefault(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.RollType == "SAMPLE").Quantity ?? 0,
                           }).OrderByDescending(x => x.WorkPlanJumboMasterId).ToList().Distinct().ToList();
                return res;
            }
        }

        public List<WorkPlanJumboMasterVm> GetWorkPlanJumboForFinalInspection(JumboFinalInspectionFilter filter)
        {
            List<ESalesOrderStatus> stsList = new()
            {
                ESalesOrderStatus.JumboInspection,
                ESalesOrderStatus.MoveToDispatch,
                ESalesOrderStatus.PartialDispatchReady,
                ESalesOrderStatus.PartialDispatchCompleted
            };
            List<WorkPlanJumboMasterVm> vmModel = new List<WorkPlanJumboMasterVm>();
            using (var db = new pmsdbContext())
            {
                vmModel = (from model in db.WorkPlanJumboMasters
                           join a in db.SaleOrderTables on model.SaleOrderId equals a.SaleOrderId
                           join sop in db.SaleOrderProductionTables on a.SaleOrderId equals sop.SaleOrderId
                           join st in db.SaleOrderTimelineTables on model.SaleOrderId equals st.SaleOrderId
                           join c in db.CustomerMasters on a.CustomerId equals c.CustomerId
                           join wo in db.WorkPlanOrders on a.SaleOrderId equals wo.OrderId
                           join wpm in db.WorkPlanMasters on wo.WorkplanId equals wpm.WorkPlanId
                           where stsList.Contains((ESalesOrderStatus)a.Status) && st.Status == (int)ESalesOrderStatus.JumboInspection && wpm.Disabled != true
                           && (model.IsInspectionCompleted == (filter.IsInspectionCompleted == false ? null : true) || model.IsInspectionCompleted == filter.IsInspectionCompleted)
                           && (filter.FromDate == null || st.AddedDate >= filter.FromDate)
                           && (filter.ToDate == null || st.AddedDate <= filter.ToDate)
                           && (String.IsNullOrEmpty(filter.SaleOrderNumber) || a.SaleOrderNumber.ToLower().Contains(filter.SaleOrderNumber))
                           && (String.IsNullOrEmpty(filter.JumboNumber) || model.JumboNo.ToLower().Contains(filter.JumboNumber))
                           && (filter.CustomerId == 0 || a.CustomerId == filter.CustomerId)
                           && (filter.SaleFormulationCodeId == 0 || a.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                           && (filter.ColorId == 0 || sop.ColorId == filter.ColorId)
                           && (filter.GrainId == 0 || sop.GrainId == filter.GrainId)
                           select new WorkPlanJumboMasterVm
                           {
                               AddedDate = model.AddedDate,
                               AddedBy = model.AddedBy,
                               WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                               WorkPlanId = wpm.WorkPlanId,
                               WorkPlanNo = wpm.WorkPlanNo,
                               JumboRollDate = model.JumboRollDate,
                               JumboRollStartTime = model.JumboRollStartTime,
                               JumboRollEndTime = model.JumboRollEndTime,
                               JumboNo = model.JumboNo,
                               Rate = model.Rate,
                               Amount = model.Amount,
                               JumboRolQty = model.JumboRolQty,
                               ActualQuantity = model.ActualQuantity,
                               Weight = model.Weight,
                               CustomerId = c.CustomerId,
                               CustomerName = c.CustomerName,
                               SaleOrderId = a.SaleOrderId,
                               SaleOrderNumber = a.SaleOrderNumber,
                               IsInspectionCompleted = model.IsInspectionCompleted,
                               Yield = model.Yield,
                               SaleOrderStatus = (PmsCommon.ESalesOrderStatus)a.Status,
                               JumboInspection = (from j in db.JumboInspectionTables
                                                  where j.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId
                                                  select new JumboInspectionTableVm
                                                  {
                                                      JumboInspectionId = j.JumboInspectionId,
                                                      WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                                                      Grade = j.Grade,
                                                      Code = j.Code,
                                                      Quantity = j.Quantity,
                                                      Unit = j.Unit,
                                                      Weight = j.Weight,
                                                      AddedBy = j.AddedBy,
                                                      AddedDate = j.AddedDate,
                                                      InspectedBy = j.InspectedBy,
                                                      RollType = j.RollType
                                                  }).ToList(),
                               QuantityFromJumboInspection = db.JumboInspectionTables.Where(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId).Sum(x => x.Quantity),
                           }).OrderByDescending(x => x.WorkPlanJumboMasterId).ToList();
            }

            return vmModel;
        }
        public ResultJumboPrintData GetJumboPrintByOrderIdJumboId(long saleOrderId, long jumboId)
        {
            ResultJumboPrintData vmModel = new ResultJumboPrintData();
            try
            {
                SaleOrderTableVm res = null;
                using (var db = new Models.pmsdbContext())
                {
                    var linksaleOrder = db.LinkedSaleOrderTables.Where(x => x.LinkedSaleOrder == saleOrderId).ToList();
                    string thicknessPattern = @"[\d.]+";
                    var grain = "";
                    var grainName = "";
                    double thickness = 0.0;
                    long parentColorId = 0;
                    var parentColorCode = "";
                    var parentColorName = "";
                    var parentFinishCode = "";
                    var parentSaleOrderNumber = "";
                    foreach (var item in linksaleOrder)
                    {
                        var ParentOrderData = (from s in db.SaleOrderTables
                                               join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                                               join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                                               from c in cagr.DefaultIfEmpty()
                                               join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                                               from g in agr.DefaultIfEmpty()
                                               join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                                               from thm in thmsa.DefaultIfEmpty()
                                               where a.SaleOrderId == item.ParentSaleOrder
                                               select new
                                               {
                                                   g.GrainCode,
                                                   g.GrainName,
                                                   thm.ThicknessNumber,
                                                   a.ColorId,
                                                   c.ColorCode,
                                                   c.ColorName,
                                                   s.FinishCode,
                                                   s.SaleOrderNumber
                                               }).FirstOrDefault();
                        if (ParentOrderData != null)
                        {
                            grain = grain + "-L / " + ParentOrderData.GrainCode + "-U";
                            grainName = grainName + "-L / " + ParentOrderData.GrainName + "-U";
                            double th1val = Convert.ToDouble(Regex.Match(ParentOrderData.ThicknessNumber, thicknessPattern).Value);
                            thickness = thickness + th1val;
                            parentColorId = ParentOrderData.ColorId.Value;
                            parentColorCode = ParentOrderData.ColorCode;
                            parentColorName = ParentOrderData.ColorName;
                            parentFinishCode = ParentOrderData.FinishCode;
                            parentSaleOrderNumber = ParentOrderData.SaleOrderNumber;
                        }
                    }

                    vmModel.Data = (from wpjm in db.WorkPlanJumboMasters
                                    join s in db.SaleOrderTables on wpjm.SaleOrderId equals s.SaleOrderId
                                    join a in db.SaleOrderProductionTables on wpjm.SaleOrderId equals a.SaleOrderId
                                    join wo in db.WorkPlanOrders on wpjm.SaleOrderId equals wo.OrderId
                                    join wp in db.WorkPlanMasters on wo.WorkplanId equals wp.WorkPlanId
                                    join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                                    from c in cagr.DefaultIfEmpty()
                                    join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                                    from fcm in fcms.DefaultIfEmpty()
                                    join cm in db.CustomerMasters on s.CustomerId equals cm.CustomerId
                                    join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                                    from g in agr.DefaultIfEmpty()
                                    join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                                    from thm in thmsa.DefaultIfEmpty()
                                    join wdm in db.WidthMasters on a.Width equals wdm.WidthId into wdmsa
                                    from wdm in wdmsa.DefaultIfEmpty()
                                    join fwm in db.FactoryWorkersMasters on wpjm.ShiftSupervisorWorkerId equals fwm.WorkerId into fwmd
                                    from fwm in fwmd.DefaultIfEmpty()
                                    where wpjm.WorkPlanJumboMasterId == jumboId && wpjm.SaleOrderId == saleOrderId
                                    select new JumboPrintDataVm
                                    {
                                        SaleOrderId = s.SaleOrderId,
                                        SaleOrderNumber = s.SaleOrderNumber,
                                        CustomerName = cm.CustomerName,
                                        ArticleName = a.ManufacturingProductName,
                                        WorkPlanJumboMasterId = wpjm.WorkPlanJumboMasterId,
                                        Length = wpjm.JumboRolQty,
                                        Weight = wpjm.Weight,
                                        JumboNo = wpjm.JumboNo,
                                        ProductionLineNo = wp.ProductionLineNo,
                                        JumboRollDate = wpjm.JumboRollDate,
                                        SaleFormulationCode = fcm.SaleFormulationCode,
                                        FinishCode = string.IsNullOrEmpty(parentFinishCode) ? s.FinishCode : parentFinishCode,
                                        ColorCode = c.ColorCode,
                                        ColorId = a.ColorId,
                                        ColorName = c.ColorName,
                                        GrainCode = g.GrainCode + grain,
                                        GrainName = g.GrainName + grainName,
                                        Thickness = thm.ThicknessNumber,
                                        WidthNumber = wdm.WidthNumber,
                                        SupervisorName = fwm.Name

                                    }).FirstOrDefault();
                    double thval = Convert.ToDouble(Regex.Match(vmModel.Data.Thickness, thicknessPattern).Value);
                    double TotalThickness = thval + thickness;
                    vmModel.Data.Thickness = TotalThickness.ToString("0.00 MM");
                    if (linksaleOrder.Count > 0)
                    {
                        vmModel.Data.SaleFormulationCode = vmModel.Data.SaleFormulationCode + " / SW";
                        vmModel.Data.ParentSaleOrderNumber = parentSaleOrderNumber;
                        vmModel.Data.UpperFinishCode = parentFinishCode;
                    }
                    if (parentColorId != vmModel.Data.ColorId && parentColorId > 0)
                    {
                        vmModel.Data.ColorCode = vmModel.Data.ColorCode + "-L / " + parentColorCode + "-U";
                        vmModel.Data.ColorName = vmModel.Data.ColorName + "-L / " + parentColorName + "-U";
                    }
                }
                vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
            }
            catch (Exception ex)
            {
                vmModel.Result = new ApiResult
                {
                    Succeeded = false,
                    Code = EMessageCode.Exception,
                    Message = ex.Message
                };
            }
            return vmModel;
        }
    }
}
