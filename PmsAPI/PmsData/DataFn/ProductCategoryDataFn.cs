﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class ProductCategoryDataFn
    {
        public GlobalDataEntity GlobalData;
        public ProductCategoryDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<ProductCategoryMasterVm> GetAllProductCategories()
        {
            List<ProductCategoryMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProductCategoryMasters
                       where a.Disabled != true
                       select new ProductCategoryMasterVm
                       {
                           ProductCategoryId = a.ProductCategoryId,
                           ProductCategory = a.ProductCategory,
                           ProductCategoryDesc = a.ProductCategoryDesc,
                           ProductCategoryAddedBy = a.ProductCategoryAddedBy,
                           ProductCategoryAddedDate = a.ProductCategoryAddedDate,
                           ProductType = a.ProductType,
                           ProductFirstSubCategoryMaster = (from af in db.ProductFirstSubCategoryMasters
                                                            where af.Disabled != true
                                                            select new ProductFirstSubCategoryMasterVm
                                                            {
                                                                ProductFirstSubCategoryId = af.ProductFirstSubCategoryId,
                                                                ProductCategoryId = af.ProductFirstSubCategoryId,
                                                                ProductFirstSubCategory = af.ProductFirstSubCategory,
                                                                ProductFirstSubCategoryDesc = af.ProductFirstSubCategoryDesc,
                                                                ProductFirstSubCategoryAddedBy = af.ProductFirstSubCategoryAddedBy,
                                                                ProductFirstSubCategoryAddedDate = af.ProductFirstSubCategoryAddedDate,
                                                                ProductSecSubCategoryMaster = (from afs in db.ProductSecSubCategoryMasters
                                                                                               where afs.Disabled != true
                                                                                               select new ProductSecSubCategoryMasterVm
                                                                                               {
                                                                                                   ProductFirstSubCategoryId = afs.ProductFirstSubCategoryId,
                                                                                                   ProductSecSubCategoryId = afs.ProductSecSubCategoryId,
                                                                                                   ProductSecSubCategory = afs.ProductSecSubCategory,
                                                                                                   ProductSecSubCategoryDesc = afs.ProductSecSubCategoryDesc,
                                                                                                   ProductSecSubCategoryAddedBy = afs.ProductSecSubCategoryAddedBy,
                                                                                                   ProductSecSubCategoryAddedDate = afs.ProductSecSubCategoryAddedDate,
                                                                                               }).ToList()
                                                            }).ToList()
                       }).OrderBy(x => x.ProductCategory).ToList();
            }
            return res;
        }
        public List<ProductCategoryListingVm> GetAllProductCategoriesForListing()
        {
            List<ProductCategoryListingVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProductCategoryMasters
                       where a.Disabled != true
                       select new ProductCategoryListingVm
                       {
                           ProductCategoryId = a.ProductCategoryId,
                           ProductCategory = a.ProductCategory,
                           ProductType = a.ProductType
                       }).OrderBy(x => x.ProductCategory).ToList();
            }
            return res;
        }

        public List<ProductFirstSubCategoryMasterVm> GetAllProductFirstSubCategories()
        {
            List<ProductFirstSubCategoryMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from af in db.ProductFirstSubCategoryMasters
                       join i in db.ProductCategoryMasters on af.ProductCategoryId equals i.ProductCategoryId
                       where af.Disabled != true
                       select new ProductFirstSubCategoryMasterVm
                       {
                           ProductFirstSubCategoryId = af.ProductFirstSubCategoryId,
                           ProductCategoryId = af.ProductCategoryId,
                           ProductCategory = i.ProductCategory,
                           ProductFirstSubCategory = af.ProductFirstSubCategory,
                           ProductFirstSubCategoryDesc = af.ProductFirstSubCategoryDesc,
                           ProductFirstSubCategoryAddedBy = af.ProductFirstSubCategoryAddedBy,
                           ProductFirstSubCategoryAddedDate = af.ProductFirstSubCategoryAddedDate,
                       }).OrderBy(x => x.ProductFirstSubCategory).ToList();
            }
            return res;
        }

        public List<ProductSecSubCategoryMasterVm> GetAllProducSecSubCategories()
        {
            List<ProductSecSubCategoryMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from afs in db.ProductSecSubCategoryMasters
                       join ic in db.ProductFirstSubCategoryMasters on afs.ProductFirstSubCategoryId equals ic.ProductFirstSubCategoryId
                       join i in db.ProductCategoryMasters on ic.ProductCategoryId equals i.ProductCategoryId
                       where afs.Disabled != true
                       select new ProductSecSubCategoryMasterVm
                       {
                           ProductFirstSubCategoryId = afs.ProductFirstSubCategoryId,
                           ProductSecSubCategoryId = afs.ProductSecSubCategoryId,
                           ProductCategory = i.ProductCategory,
                           ProductFirstSubCategory = ic.ProductFirstSubCategory,
                           ProductSecSubCategory = afs.ProductSecSubCategory,
                           ProductSecSubCategoryDesc = afs.ProductSecSubCategoryDesc,
                           ProductSecSubCategoryAddedBy = afs.ProductSecSubCategoryAddedBy,
                           ProductSecSubCategoryAddedDate = afs.ProductSecSubCategoryAddedDate,
                       }).OrderBy(x => x.ProductSecSubCategory).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateProductCategory(ProductCategoryMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.ProductCategoryId == 0)
                {
                    var rec = db.ProductCategoryMasters.Where(x => x.ProductCategory == br.ProductCategory && x.ProductType == br.ProductType && x.ProductCategoryId != br.ProductCategoryId && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product Category already exist with same Name and Type");
                }
                ProductCategoryMaster res = new ProductCategoryMaster();
                if (br.ProductCategoryId == 0)
                {
                    res.ProductCategory = br.ProductCategory;
                    res.ProductCategoryDesc = br.ProductCategoryDesc;
                    res.ProductType = br.ProductType;
                    res.ProductCategoryAddedBy = GlobalData.loggedInUser;
                    res.ProductCategoryAddedDate = System.DateTime.Now;
                    db.ProductCategoryMasters.Add(res);
                }
                else
                {
                    res = db.ProductCategoryMasters.Where(x => x.ProductCategoryId == br.ProductCategoryId).FirstOrDefault();
                    if (res != null)
                    {
                        res.ProductCategory = br.ProductCategory;
                        res.ProductCategoryDesc = br.ProductCategoryDesc;
                        res.ProductType = br.ProductType;
                        res.ProductCategoryAddedBy = GlobalData.loggedInUser;
                        res.ProductCategoryAddedDate = System.DateTime.Now;

                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = br.ProductCategoryId,
                            TableName = "ProductCategoryMaster",
                            EntityName = "ProductCategoryMaster",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                        });
                        db.SaveChanges();
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product Category Added Successfully");
            }
        }

        public ApiFunctionResponseVm AddUpdateProductFirstSubCategory(ProductFirstSubCategoryMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                var rec = db.ProductFirstSubCategoryMasters.Where(x => x.ProductFirstSubCategory == br.ProductFirstSubCategory && x.ProductFirstSubCategoryId != br.ProductFirstSubCategoryId && x.Disabled != true).FirstOrDefault();
                if (rec != null)
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                ProductFirstSubCategoryMaster res = new ProductFirstSubCategoryMaster();
                if (br.ProductFirstSubCategoryId == 0)
                {
                    res.ProductFirstSubCategory = br.ProductFirstSubCategory;
                    res.ProductCategoryId = br.ProductCategoryId;
                    res.ProductFirstSubCategoryDesc = br.ProductFirstSubCategoryDesc;
                    res.ProductFirstSubCategoryAddedBy = GlobalData.loggedInUser;
                    res.ProductFirstSubCategoryAddedDate = System.DateTime.Now;
                    db.ProductFirstSubCategoryMasters.Add(res);
                }
                else
                {
                    res = db.ProductFirstSubCategoryMasters.Where(x => x.ProductFirstSubCategoryId == br.ProductFirstSubCategoryId).FirstOrDefault();
                    if (res != null)
                    {
                        res.ProductFirstSubCategory = br.ProductFirstSubCategory;
                        res.ProductCategoryId = br.ProductCategoryId;
                        res.ProductFirstSubCategoryDesc = br.ProductFirstSubCategoryDesc;
                        res.ProductFirstSubCategoryAddedBy = GlobalData.loggedInUser;
                        res.ProductFirstSubCategoryAddedDate = System.DateTime.Now;
                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = br.ProductFirstSubCategoryId,
                            TableName = "ProductFirstSubCategoryMaster",
                            EntityName = "ProductFirstSubCategoryMaster",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                        });
                        db.SaveChanges();
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm AddUpdateProductSecSubCategory(ProductSecSubCategoryMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                var rec = db.ProductSecSubCategoryMasters.Where(x => x.ProductSecSubCategory == br.ProductSecSubCategory && x.ProductSecSubCategoryId != br.ProductSecSubCategoryId && x.Disabled != true).FirstOrDefault();
                if (rec != null)
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                ProductSecSubCategoryMaster res = new ProductSecSubCategoryMaster();
                if (br.ProductSecSubCategoryId == 0)
                {
                    res.ProductSecSubCategory = br.ProductSecSubCategory;
                    res.ProductFirstSubCategoryId = br.ProductFirstSubCategoryId;
                    res.ProductSecSubCategoryDesc = br.ProductSecSubCategoryDesc;
                    res.ProductSecSubCategoryAddedBy = GlobalData.loggedInUser;
                    res.ProductSecSubCategoryAddedDate = System.DateTime.Now;
                    db.ProductSecSubCategoryMasters.Add(res);
                }
                else
                {
                    res = db.ProductSecSubCategoryMasters.Where(x => x.ProductSecSubCategoryId == br.ProductSecSubCategoryId).FirstOrDefault();
                    if (res != null)
                    {
                        res.ProductSecSubCategory = br.ProductSecSubCategory;
                        res.ProductFirstSubCategoryId = br.ProductFirstSubCategoryId;
                        res.ProductSecSubCategoryDesc = br.ProductSecSubCategoryDesc;
                        res.ProductSecSubCategoryAddedBy = GlobalData.loggedInUser;
                        res.ProductSecSubCategoryAddedDate = System.DateTime.Now;
                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = br.ProductSecSubCategoryId,
                            TableName = "ProductSecSubCategoryMaster",
                            EntityName = "ProductSecSubCategoryMaster",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                        });
                        db.SaveChanges();
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        //Create three functions to delete productcategory, productfirstsubcategory and productSecSubCategory by checking No product and any dependent category is not mapped with any of the categories

        public ApiFunctionResponseVm DeleteProductCategory(long id)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        bool productcheckflag = false;
                        bool deleteflag = false;
                        var products = db.ProductMasters.Where(x => x.ProductCategoryId == id).ToList();
                        if (products.Count > 0)
                        {
                            productcheckflag = true;
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product Category is mapped with one or more Product. Either delete the product or move it to a correct category.");
                        }
                        else
                        {
                            productcheckflag = false;
                        }
                        var rec1 = db.ProductFirstSubCategoryMasters.Where(x => x.ProductCategoryId == id).FirstOrDefault();
                        if (rec1 != null)
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product Category is mapped with Product First Sub Category. First remove the dependent category to proceed.");
                        else
                        {
                            deleteflag = true;
                        }

                        if (productcheckflag == false && deleteflag == true)
                        {
                            var rec = db.ProductCategoryMasters.Where(x => x.ProductCategoryId == id).FirstOrDefault();
                            rec.Disabled = true;
                            rec.DisabledBy = GlobalData.loggedInUser;
                            rec.DisabledDate = System.DateTime.Now;
                            db.AuditTables.Add(new AuditTable
                            {
                                RecId = id,
                                TableName = "ProductCategoryMaster",
                                EntityName = "ProductCategoryMaster",
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = System.DateTime.Now,
                            });
                            db.SaveChanges();
                            transaction.Commit();
                        }
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product Category Deleted Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }

        public ApiFunctionResponseVm DeleteProductFirstSubCategory(long id)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        bool productcheckflag = false;
                        bool deleteflag = false;
                        var products = db.ProductMasters.Where(x => x.ProductFirstSubCategoryId == id).ToList();
                        if (products.Count > 0)
                        {
                            productcheckflag = true;
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product First Sub Category is mapped with one or more Product. Either delete the product or move it to a correct category.");
                        }
                        else
                        {
                            productcheckflag = false;
                        }
                        var rec1 = db.ProductSecSubCategoryMasters.Where(x => x.ProductFirstSubCategoryId == id).FirstOrDefault();
                        if (rec1 != null)
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product First Sub Category is mapped with Product Second Sub Category");
                        else
                        {
                            deleteflag = true;
                        }
                        if (productcheckflag == false && deleteflag == true)
                        {
                            var rec = db.ProductFirstSubCategoryMasters.Where(x => x.ProductFirstSubCategoryId == id).FirstOrDefault();
                            rec.Disabled = true;
                            rec.DisabledBy = GlobalData.loggedInUser;
                            rec.DisabledDate = System.DateTime.Now;
                            db.AuditTables.Add(new AuditTable
                            {
                                RecId = id,
                                TableName = "ProductFirstSubCategoryMasters",
                                EntityName = "ProductFirstSubCategoryMasters",
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = System.DateTime.Now,
                            });
                            db.SaveChanges();
                            transaction.Commit();
                        }
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product Category Deleted Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }

        public ApiFunctionResponseVm DeleteProductSecSubCategory(long id)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var products = db.ProductMasters.Where(x => x.ProductSecSubCategoryId == id).ToList();
                        if (products.Count > 0)
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Product Second Sub Category is mapped with one or more Product. Either delete the product or move it to a correct category.");
                        }
                        else
                        {

                            var rec = db.ProductSecSubCategoryMasters.Where(x => x.ProductSecSubCategoryId == id).FirstOrDefault();
                            if (rec != null)
                            {
                                rec.Disabled = true;
                                rec.DisabledBy = GlobalData.loggedInUser;
                                rec.DisabledDate = System.DateTime.Now;
                                db.AuditTables.Add(new AuditTable
                                {
                                    RecId = id,
                                    TableName = "ProductSecSubCategoryMasters",
                                    EntityName = "ProductSecSubCategoryMasters",
                                    AddedBy = GlobalData.loggedInUser,
                                    AddedDate = System.DateTime.Now,
                                });
                                db.SaveChanges();
                                transaction.Commit();
                            }
                        }
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product Category Deleted Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;

                    }
                }

            }
        }
    }
}
