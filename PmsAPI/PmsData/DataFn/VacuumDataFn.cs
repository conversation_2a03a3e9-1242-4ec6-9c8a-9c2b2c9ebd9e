﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class VacuumDataFn
    {
        public List<VacuumMasterVm> GetAllVacuums()
        {
            List<VacuumMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.VacuumMasters
                       where a.Disabled != true
                       select new VacuumMasterVm
                       {
                           VacuumMasterId = a.VacuumMasterId,
                           Name = a.Name,
                           Code = a.Code,
                           Description = a.Description,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderBy(x => x.Code).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateVacuum(VacuumMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.VacuumMasterId == 0)
                {
                    var rec = db.VacuumMasters.Where(x => x.Code == br.Code && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                VacuumMaster res = new VacuumMaster();
                if (br.VacuumMasterId == 0)
                {

                    res.Name = br.Name;
                    res.Code = br.Code;
                    res.Description = br.Description;
                    res.AddedBy = br.AddedBy;
                    res.AddedDate = System.DateTime.Now;
                    db.VacuumMasters.Add(res);
                }
                else
                {
                    res = db.VacuumMasters.Where(x => x.VacuumMasterId == br.VacuumMasterId).FirstOrDefault();
                    if (res != null)
                    {
                        res.Name = br.Name;
                        res.Code = br.Code;
                        res.Description = br.Description;
                        res.AddedBy = br.AddedBy;
                        res.AddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DeleteVacuum(VacuumMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        VacuumMaster item = db.VacuumMasters.FirstOrDefault(x => x.VacuumMasterId == param.VacuumMasterId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
