﻿using System.Collections.Generic;
using System.Linq;
using System.Net;
using PmsEntity.ViewModel;
using PmsData.Models;
using PmsCommon;


namespace PmsData.DataFn
{
    public class FormulationCodeDataFn
    {
        public GlobalDataEntity GlobalData;
        public FormulationCodeDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }

        public List<SaleFormulationCodeMasterVm> GetSaleFormulationCodeList()
        {
            List<SaleFormulationCodeMasterVm> FormulationCodeList = null;
            using (var db = new Models.pmsdbContext())
            {
                FormulationCodeList = (from s in db.SaleFormulationCodeMasters
                                       join c in db.ProductCategoryMasters on s.CategoryId equals c.ProductCategoryId
                                       join ab in db.ProductMasters on s.FabricProductId equals ab.ProductId into pmd
                                       from ab in pmd.DefaultIfEmpty()
                                       join th in db.ThicknessMasters on s.ThicknessId equals th.ThicknessId into psf
                                       from th in psf.DefaultIfEmpty()
                                       where s.Disabled != true
                                       select new SaleFormulationCodeMasterVm
                                       {
                                           SaleFormulationCodeId = s.SaleFormulationCodeId,
                                           SaleFormulationCode = s.SaleFormulationCode,
                                           FabricProductId = s.FabricProductId,
                                           FabricProductName = s.FabricProductId == 0 ? "N/A" : ab.ProductName,
                                           AddedBy = s.AddedBy,
                                           AddedDate = s.AddedDate,
                                           PreSkinGsm = s.PreSkinGsm,
                                           SkinGsm = s.SkinGsm,
                                           FoamGsm = s.FoamGsm,
                                           AdhesiveGsm = s.AdhesiveGsm,
                                           FabricGsm = s.FabricGsm,
                                           TotalGsm = s.TotalGsm,
                                           MinSpeed = s.MinSpeed,
                                           MaxSpeed = s.MaxSpeed,
                                           ThicknessId = s.ThicknessId,
                                           ThicknessNumber = th.ThicknessNumber,
                                           CategoryId = s.CategoryId,
                                           CategoryName = c.ProductCategory,
                                           IsOrderLinkingRequired = s.IsOrderLinkingRequired,
                                           MixingData = (from la in db.FormulationCodeMixingTables
                                                         join a in db.MixingMasters on la.MixingId equals a.MixingId
                                                         where la.SaleFormulationCodeId == s.SaleFormulationCodeId
                                                         select new FormulationCodeMixingTableVm
                                                         {
                                                             FormulationCodeMixingId = la.FormulationCodeMixingId,
                                                             SaleFormulationCodeId = la.SaleFormulationCodeId,
                                                             AddedDate = la.AddedDate,
                                                             AddedBy = la.AddedBy,
                                                             MixingId = la.MixingId,
                                                             MixingName = a.MixingName,
                                                             MixingRawMaterial = (from op in db.FormulationCodeMixingRawMaterialTables
                                                                                  join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                  where op.FormulationCodeMixingId == la.FormulationCodeMixingId
                                                                                  select new FormulationCodeMixingRawMaterialTableVm
                                                                                  {
                                                                                      FormulationCodeMixingRawMaterialId = op.FormulationCodeMixingRawMaterialId,
                                                                                      FormulationCodeMixingId = op.FormulationCodeMixingId,
                                                                                      ProductId = op.ProductId,
                                                                                      ProductName = p.ProductName,
                                                                                      ProductCode = p.ProductCode,
                                                                                      Quantity = op.Quantity,
                                                                                      Scquantity = op.Scquantity,
                                                                                      AvgGsm = p.AvgGsm,
                                                                                      Unit = op.Unit,
                                                                                      Price = op.Price
                                                                                  }).ToList()
                                                         }).ToList(),
                                       }).OrderByDescending(x => x.SaleFormulationCodeId).ToList();
            }
            return FormulationCodeList;
        }
        public List<SaleFormulationCodeMasterVm> GetSaleFormulationCodeForFilter()
        {
            List<SaleFormulationCodeMasterVm> FormulationCodeList = null;
            using (var db = new Models.pmsdbContext())
            {
                FormulationCodeList = (from s in db.SaleFormulationCodeMasters
                                       join c in db.ProductCategoryMasters on s.CategoryId equals c.ProductCategoryId
                                       join ab in db.ProductMasters on s.FabricProductId equals ab.ProductId into pmd
                                       from ab in pmd.DefaultIfEmpty()
                                       join th in db.ThicknessMasters on s.ThicknessId equals th.ThicknessId into psf
                                       from th in psf.DefaultIfEmpty()
                                       where s.Disabled != true
                                       select new SaleFormulationCodeMasterVm
                                       {
                                           SaleFormulationCodeId = s.SaleFormulationCodeId,
                                           SaleFormulationCode = s.SaleFormulationCode,
                                       }).OrderByDescending(x => x.SaleFormulationCodeId).ToList();
            }
            return FormulationCodeList;
        }
        public ApiFunctionResponseVm AddFormulationCode(SaleFormulationCodeMasterVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                SaleFormulationCodeMaster mm = new SaleFormulationCodeMaster();
                mm.SaleFormulationCode = mix.SaleFormulationCode;
                mm.AddedBy = GlobalData.loggedInUser;
                mm.AddedDate = System.DateTime.Now;
                mm.FabricProductId = mix.FabricProductId != null ? mix.FabricProductId : 0;
                mm.PreSkinGsm = mix.PreSkinGsm;
                mm.SkinGsm = mix.SkinGsm;
                mm.FoamGsm = mix.FoamGsm;
                mm.AdhesiveGsm = mix.AdhesiveGsm;
                mm.FabricGsm = mix.FabricGsm;
                mm.TotalGsm = mix.TotalGsm;
                mm.ThicknessId = mix.ThicknessId;
                mm.CategoryId = mix.CategoryId;
                mm.MinSpeed = mix.MinSpeed;
                mm.MaxSpeed = mix.MaxSpeed;
                mm.IsOrderLinkingRequired = mix.IsOrderLinkingRequired;
                db.SaleFormulationCodeMasters.Add(mm);
                db.SaveChanges();
                foreach (var item in mix.MixingData)
                {
                    FormulationCodeMixingTable spt = new FormulationCodeMixingTable();
                    spt.SaleFormulationCodeId = mm.SaleFormulationCodeId;
                    spt.MixingId = item.MixingId;
                    spt.AddedBy = GlobalData.loggedInUser;
                    spt.AddedDate = System.DateTime.Now;
                    db.FormulationCodeMixingTables.Add(spt);
                    db.SaveChanges();
                    foreach (var mrm in item.MixingRawMaterial)
                    {
                        FormulationCodeMixingRawMaterialTable sptraw = new FormulationCodeMixingRawMaterialTable();
                        sptraw.FormulationCodeMixingId = spt.FormulationCodeMixingId;
                        sptraw.ProductId = mrm.ProductId;
                        sptraw.Quantity = mrm.Quantity;
                        sptraw.Unit = mrm.Unit;
                        sptraw.Price = mrm.Price;
                        sptraw.Scquantity = mrm.Quantity;
                        db.FormulationCodeMixingRawMaterialTables.Add(sptraw);
                        db.SaveChanges();
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public ApiFunctionResponseVm UpdateFormulationCode(SaleFormulationCodeMasterVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = db.SaleOrderTables.Any(x => x.SaleFormulationCodeId == mix.SaleFormulationCodeId);
                var configres = db.ConfigTables.Where(x => x.ConfigItem == "FormulationCodeEditable").ToList();
                if (res == true && configres[0].ConfigValue.ToLowerInvariant() != "true")
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Cannot update as formulation code is linked with a saleorder");
                }
                //var saleFormulationCodeMaster = db.SaleFormulationCodeMasters.FirstOrDefault(x => x.SaleFormulationCodeId == mix.SaleFormulationCodeId);
                var formulationCodeMixingTable = db.FormulationCodeMixingTables.Where(x => x.SaleFormulationCodeId == mix.SaleFormulationCodeId).ToList();
                foreach (var item in formulationCodeMixingTable)
                {
                    var sptraw = db.FormulationCodeMixingRawMaterialTables.Where(x => x.FormulationCodeMixingId == item.FormulationCodeMixingId).ToList();
                    db.FormulationCodeMixingRawMaterialTables.RemoveRange(sptraw);
                    db.SaveChanges();
                }
                db.FormulationCodeMixingTables.RemoveRange(formulationCodeMixingTable);
                db.SaveChanges();
                //db.SaleFormulationCodeMasters.Remove(saleFormulationCodeMaster);
                //db.SaveChanges();


                SaleFormulationCodeMaster mm = db.SaleFormulationCodeMasters.FirstOrDefault(x => x.SaleFormulationCodeId == mix.SaleFormulationCodeId);
                mm.SaleFormulationCode = mix.SaleFormulationCode;
                mm.AddedBy = GlobalData.loggedInUser;
                mm.AddedDate = System.DateTime.Now;
                mm.FabricProductId = mix.FabricProductId != null ? mix.FabricProductId : 0;
                mm.PreSkinGsm = mix.PreSkinGsm;
                mm.SkinGsm = mix.SkinGsm;
                mm.FoamGsm = mix.FoamGsm;
                mm.AdhesiveGsm = mix.AdhesiveGsm;
                mm.FabricGsm = mix.FabricGsm;
                mm.TotalGsm = mix.TotalGsm;
                mm.ThicknessId = mix.ThicknessId;
                mm.CategoryId = mix.CategoryId;
                mm.MinSpeed = mix.MinSpeed;
                mm.MaxSpeed = mix.MaxSpeed;
                mm.IsOrderLinkingRequired = mix.IsOrderLinkingRequired;
                db.SaveChanges();
                db.AuditTables.Add(new AuditTable
                {
                    RecId = mix.SaleFormulationCodeId,
                    TableName = "SaleFormulationCodeMaster",
                    EntityName = "SaleFormulationCodeMasters",
                    AddedBy = mix.AddedBy,
                    AddedDate = System.DateTime.Now,
                });
                db.SaveChanges();
                foreach (var item in mix.MixingData)
                {
                    FormulationCodeMixingTable spt = new FormulationCodeMixingTable();
                    spt.SaleFormulationCodeId = mix.SaleFormulationCodeId;
                    spt.MixingId = item.MixingId;
                    spt.AddedBy = GlobalData.loggedInUser;
                    spt.AddedDate = System.DateTime.Now;
                    db.FormulationCodeMixingTables.Add(spt);

                    db.SaveChanges();
                    foreach (var mrm in item.MixingRawMaterial)
                    {
                        FormulationCodeMixingRawMaterialTable sptraw = new FormulationCodeMixingRawMaterialTable();
                        sptraw.FormulationCodeMixingId = spt.FormulationCodeMixingId;
                        sptraw.ProductId = mrm.ProductId;
                        sptraw.Quantity = mrm.Quantity;
                        sptraw.Unit = mrm.Unit;
                        sptraw.Price = mrm.Price;
                        sptraw.Scquantity = mrm.Quantity;
                        db.FormulationCodeMixingRawMaterialTables.Add(sptraw);
                        db.SaveChanges();
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Formulation code updated successfully");
        }
        public ApiFunctionResponseVm DisableFormulationCode(long saleFormulationCodeId)
        {
            using (var db = new Models.pmsdbContext())
            {
                SaleFormulationCodeMaster mm = db.SaleFormulationCodeMasters.FirstOrDefault(x => x.SaleFormulationCodeId == saleFormulationCodeId);
                mm.Disabled = true;
                mm.DisabledBy = GlobalData.loggedInUser;
                mm.DisabledDate = System.DateTime.Now;

                db.SaveChanges();
                db.AuditTables.Add(new AuditTable
                {
                    RecId = saleFormulationCodeId,
                    TableName = "SaleFormulationCodeMaster",
                    EntityName = "SaleFormulationCodeMasters",
                    AddedBy = GlobalData.loggedInUser,
                    AddedDate = System.DateTime.Now,
                });
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Formulation code disabled successfully.");
        }

        public SaleFormulationCodeMasterVm GetFormulationCodeById(long saleFormulationCodeId)
        {
            SaleFormulationCodeMasterVm FormulationCodeList = null;
            using (var db = new Models.pmsdbContext())
            {
                var AllFormulationCodeList = (from s in db.SaleFormulationCodeMasters
                                              join c in db.ProductCategoryMasters on s.CategoryId equals c.ProductCategoryId
                                              join ab in db.ProductMasters on s.FabricProductId equals ab.ProductId into pmd
                                              from ab in pmd.DefaultIfEmpty()
                                              join th in db.ThicknessMasters on s.ThicknessId equals th.ThicknessId into psf
                                              from th in psf.DefaultIfEmpty()
                                              join um in db.UserMasters on s.AddedBy equals um.Email
                                              where s.Disabled != true
                                              select new SaleFormulationCodeMasterVm
                                              {
                                                  SaleFormulationCodeId = s.SaleFormulationCodeId,
                                                  SaleFormulationCode = s.SaleFormulationCode,
                                                  FabricProductId = s.FabricProductId,
                                                  FabricProductName = s.FabricProductId == 0 ? "N/A" : ab.ProductName,
                                                  AddedBy = um.Name,
                                                  AddedDate = s.AddedDate,
                                                  PreSkinGsm = s.PreSkinGsm,
                                                  SkinGsm = s.SkinGsm,
                                                  FoamGsm = s.FoamGsm,
                                                  AdhesiveGsm = s.AdhesiveGsm,
                                                  FabricGsm = s.FabricGsm,
                                                  TotalGsm = s.TotalGsm,
                                                  MinSpeed = s.MinSpeed,
                                                  MaxSpeed = s.MaxSpeed,
                                                  ThicknessId = s.ThicknessId,
                                                  ThicknessNumber = th.ThicknessNumber,
                                                  CategoryId = s.CategoryId,
                                                  CategoryName = c.ProductCategory,
                                                  IsOrderLinkingRequired = s.IsOrderLinkingRequired,
                                                  MixingData = (from la in db.FormulationCodeMixingTables
                                                                join a in db.MixingMasters on la.MixingId equals a.MixingId
                                                                where la.SaleFormulationCodeId == s.SaleFormulationCodeId
                                                                select new FormulationCodeMixingTableVm
                                                                {
                                                                    FormulationCodeMixingId = la.FormulationCodeMixingId,
                                                                    SaleFormulationCodeId = la.SaleFormulationCodeId,
                                                                    AddedDate = la.AddedDate,
                                                                    AddedBy = la.AddedBy,
                                                                    MixingId = la.MixingId,
                                                                    MixingName = a.MixingName,
                                                                    MixingRawMaterial = (from op in db.FormulationCodeMixingRawMaterialTables
                                                                                         join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                         where op.FormulationCodeMixingId == la.FormulationCodeMixingId
                                                                                         select new FormulationCodeMixingRawMaterialTableVm
                                                                                         {
                                                                                             FormulationCodeMixingRawMaterialId = op.FormulationCodeMixingRawMaterialId,
                                                                                             FormulationCodeMixingId = op.FormulationCodeMixingId,
                                                                                             ProductId = op.ProductId,
                                                                                             ProductName = p.ProductName,
                                                                                             ProductCode = p.ProductCode,
                                                                                             Quantity = op.Quantity,
                                                                                             Scquantity = op.Scquantity,
                                                                                             AvgGsm = p.AvgGsm,
                                                                                             Unit = op.Unit,
                                                                                             Price = op.Price
                                                                                         }).ToList()
                                                                }).ToList(),
                                              }).OrderByDescending(x => x.SaleFormulationCodeId).ToList();

                FormulationCodeList = AllFormulationCodeList.FirstOrDefault(x => x.SaleFormulationCodeId == saleFormulationCodeId);

            }
            return FormulationCodeList;
        }
    }
}
