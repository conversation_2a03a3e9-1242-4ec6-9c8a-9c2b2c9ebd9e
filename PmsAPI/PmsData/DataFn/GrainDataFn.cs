﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class GrainDataFn
    {
        public List<GrainMasterVm> GetAllGrains()
        {
            List<GrainMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.GrainMasters
                       where a.Disabled != true
                       select new GrainMasterVm
                       {
                           GrainId = a.GrainId,
                           GrainName = a.GrainName,
                           GrainCode = a.GrainCode,
                           GrainDesc = a.GrainDesc,
                           Price = a.<PERSON>,
                           GrainAddedBy = a.GrainAddedBy,
                           GrainAddedDate = a.GrainAddedDate
                       }).OrderByDescending(x => x.GrainId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateGrain(GrainMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.GrainId == 0)
                {
                    var rec = db.GrainMasters.Where(x => x.GrainCode == br.GrainCode && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                GrainMaster res = new GrainMaster();
                if (br.GrainId == 0)
                {
                    res.GrainName = br.GrainName;
                    res.GrainCode = br.GrainCode;
                    res.GrainDesc = br.GrainDesc;
                    res.Price = br.Price;
                    res.GrainAddedBy = br.GrainAddedBy;
                    res.GrainAddedDate = System.DateTime.Now;
                    db.GrainMasters.Add(res);
                }
                else
                {
                    res = db.GrainMasters.Where(x => x.GrainId == br.GrainId).FirstOrDefault();
                    if (res != null)
                    {
                        res.GrainName = br.GrainName;
                        res.GrainCode = br.GrainCode;
                        res.GrainDesc = br.GrainDesc;
                        res.Price = br.Price;
                        res.GrainAddedBy = br.GrainAddedBy;
                        res.GrainAddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DeleteGrain(GrainMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        GrainMaster item = db.GrainMasters.FirstOrDefault(x => x.GrainId == param.GrainId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
