﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class WorkersDesignationDataFn
    {
        public GlobalDataEntity GlobalData;
        public WorkersDesignationDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<WorkerDesignationMasterVm> GetAllWorkersDesignations()
        {
            List<WorkerDesignationMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.WorkerDesignationMasters
                       where a.Disabled != true
                       select new WorkerDesignationMasterVm
                       {
                           DesignationId = a.DesignationId,
                           Name = a.Name,
                           ShortName = a.ShortName,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                       }).OrderByDescending(x => x.Name).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateWorkerDesignation(WorkerDesignationMasterVm item)
        {
            using (var db = new Models.pmsdbContext())
            {
                WorkerDesignationMaster res = new WorkerDesignationMaster();
                if (item.DesignationId == 0)
                {
                    res.Name = item.Name;
                    res.ShortName = item.ShortName;
                    res.AddedBy = GlobalData.loggedInUser;
                    res.AddedDate = System.DateTime.Now;
                    db.WorkerDesignationMasters.Add(res);
                    db.SaveChanges();
                }
                else
                {
                    res = db.WorkerDesignationMasters.Where(x => x.DesignationId == item.DesignationId).FirstOrDefault();

                    if (res != null)
                    {
                        res.Name = item.Name;
                        res.ShortName = item.ShortName;
                        res.DesignationId = item.DesignationId;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = res.DesignationId,
                            TableName = "WorkerDesignationMasters",
                            EntityName = "WorkerDesignationMaster",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                        });
                        db.SaveChanges();
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm DeleteWorkerDesignation(long itemid)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var res = db.WorkerDesignationMasters.Where(x => x.DesignationId == itemid).FirstOrDefault();
                        if (res != null)
                        {
                            res.Disabled = true;
                            res.DisabledBy = GlobalData.loggedInUser;
                            res.DisabledDate = System.DateTime.Now;
                            db.SaveChanges();

                        }
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Worker Designation Removed Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }

    }
}
