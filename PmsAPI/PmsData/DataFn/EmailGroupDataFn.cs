﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using PmsData.ViewModel;

namespace PmsData.DataFn
{
    public class EmailGroupDataFn
    {
        public List<EmailGroupTableVm> GetAllEmailGroups()
        {
            List<EmailGroupTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.EmailGroupTables
                       select new EmailGroupTableVm
                       {
                           Id = a.Id,
                           EmailGroupName = a.EmailGroupName,
                           EmailId = a.EmailId,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                       }).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddEmailGroup(EmailGroupTableVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                var rec = db.EmailGroupTables.Where(x => x.EmailId == br.EmailId && x.EmailGroupName == br.EmailGroupName).FirstOrDefault();
                if (rec != null)
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                EmailGroupTable res = new EmailGroupTable
                {
                    EmailGroupName = br.EmailGroupName,
                    EmailId = br.EmailId,
                    AddedBy = br.AddedBy,
                    AddedDate = System.DateTime.Now,
                };
                db.EmailGroupTables.Add(res);
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm DeleteEmailGroup(EmailGroupTableVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                var rec = db.EmailGroupTables.Where(x => x.EmailId == br.EmailId && x.EmailGroupName == br.EmailGroupName).FirstOrDefault();
                if (rec == null)
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                db.EmailGroupTables.Remove(rec);
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "FUnction Executed Successfully");
            }
        }

    }
}
