﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class TumblingDataFn
    {
        public List<TumblingMasterVm> GetAllTumblings()
        {
            List<TumblingMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.TumblingMasters
                       select new TumblingMasterVm
                       {
                           TumblingMasterId = a.TumblingMasterId,
                           Name = a.Name,
                           Code = a.Code,
                           Description = a.Description,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderBy(x => x.Code).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateTumbling(TumblingMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.TumblingMasterId == 0)
                {
                    var rec = db.TumblingMasters.Where(x => x.Code == br.Code && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                TumblingMaster res = new TumblingMaster();
                if (br.TumblingMasterId == 0)
                {

                    res.Name = br.Name;
                    res.Code = br.Code;
                    res.Description = br.Description;
                    res.AddedBy = br.AddedBy;
                    res.AddedDate = System.DateTime.Now;
                    db.TumblingMasters.Add(res);
                }
                else
                {
                    res = db.TumblingMasters.Where(x => x.TumblingMasterId == br.TumblingMasterId).FirstOrDefault();
                    if (res != null)
                    {
                        res.Name = br.Name;
                        res.Code = br.Code;
                        res.Description = br.Description;
                        res.AddedBy = br.AddedBy;
                        res.AddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
    }
}
