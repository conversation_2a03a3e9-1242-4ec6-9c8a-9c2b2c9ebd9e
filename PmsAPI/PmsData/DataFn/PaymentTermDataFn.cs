﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class PaymentTermDataFn
    {
        public List<PaymentTermMasterVm> GetAllPaymentTerms()
        {
            List<PaymentTermMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.PaymentTermMasters
                       where a.Disabled != true
                       select new PaymentTermMasterVm
                       {
                           PaymentTermId = a.PaymentTermId,
                           PaymentTerm = a.PaymentTerm,
                           PaymentTermDesc = a.PaymentTermDesc,
                           PaymentTermAddedBy = a.PaymentTermAddedBy,
                           PaymentTermAddedDate = a.PaymentTermAddedDate,
                           NumberOfDays = a.NumberOfDays
                       }).OrderBy(x => x.PaymentTerm).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdatePaymentTerm(PaymentTermMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.PaymentTermId == 0)
                {
                    var rec = db.PaymentTermMasters.Where(x => x.PaymentTerm == br.PaymentTerm && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                PaymentTermMaster res = new PaymentTermMaster();
                if (br.PaymentTermId == 0)
                {
                    res.PaymentTerm = br.PaymentTerm;
                    res.PaymentTermDesc = br.PaymentTermDesc;
                    res.PaymentTermAddedBy = br.PaymentTermAddedBy;
                    res.PaymentTermAddedDate = System.DateTime.Now;
                    res.NumberOfDays = br.NumberOfDays;
                    db.PaymentTermMasters.Add(res);
                }
                else
                {
                    res = db.PaymentTermMasters.Where(x => x.PaymentTermId == br.PaymentTermId).FirstOrDefault();
                    if (res != null)
                    {
                        res.PaymentTerm = br.PaymentTerm;
                        res.PaymentTermDesc = br.PaymentTermDesc;
                        res.PaymentTermAddedBy = br.PaymentTermAddedBy;
                        res.PaymentTermAddedDate = System.DateTime.Now;
                        res.NumberOfDays = br.NumberOfDays;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DeletePaymentTerm(PaymentTermMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        PaymentTermMaster item = db.PaymentTermMasters.FirstOrDefault(x => x.PaymentTermId == param.PaymentTermId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
