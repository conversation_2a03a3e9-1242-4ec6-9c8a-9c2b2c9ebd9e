using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using PmsData.Models;
using PmsCommon;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace PmsData.DataFn
{
    public class InvoiceDataFn
    {
        public GlobalDataEntity GlobalData;
        private readonly ILogger<InvoiceDataFn> _logger;

        public InvoiceDataFn(GlobalDataEntity gd, ILogger<InvoiceDataFn> logger = null)
        {
            GlobalData = gd;
            _logger = logger;
        }

        public PaginatedInvoiceResult GetAllInvoices(InvoiceRequestVm request = null)
        {
            try
            {
                _logger?.LogInformation("Starting GetAllInvoices operation for user: {User}", GlobalData.loggedInUser);

                if (request == null)
                {
                    request = new InvoiceRequestVm { PageNumber = 1, PageSize = 20 };
                }

                using (var db = new Models.pmsdbContext())
                {
                    var baseQuery = from inv in db.InvoiceMasters.AsNoTracking()
                                    join s in db.SupplierMasters on inv.SupplierId equals s.SupplierId into suppliers
                                    from s in suppliers.DefaultIfEmpty()
                                    join po in db.PurchaseOrderTables on inv.Poid equals po.Poid into purchaseOrders
                                    from po in purchaseOrders.DefaultIfEmpty()
                                    join updatedBy in db.UserMasters on inv.UpdatedById equals updatedBy.UserId into updatedUsers
                                    from updatedBy in updatedUsers.DefaultIfEmpty()
                                    join disabledBy in db.UserMasters on inv.DisabledById equals disabledBy.UserId into disabledUsers
                                    from disabledBy in disabledUsers.DefaultIfEmpty()
                                    where inv.Active == true
                                    select new { inv, s, po, updatedBy, disabledBy };

                    // Get total count
                    var totalCount = baseQuery.Count();
                    _logger?.LogInformation("Found {TotalCount} total invoices", totalCount);

                    // Apply pagination
                    var skip = (request.PageNumber - 1) * request.PageSize;
                    var invoices = baseQuery
                        .OrderByDescending(x => x.inv.InvoiceId)
                        .Skip(skip)
                        .Take(request.PageSize)
                        .Select(x => new InvoiceMasterVm
                        {
                            InvoiceId = x.inv.InvoiceId,
                            InvoiceNumber = x.inv.InvoiceNumber,
                            InvoiceDate = x.inv.InvoiceDate,
                            InvoiceFile = x.inv.InvoiceFile,
                            InvoiceTotalPrice = x.inv.InvoiceTotalPrice,
                            EwayBill = x.inv.EwayBill,
                            EwayBillDate = x.inv.EwayBillDate,
                            SupplierId = x.inv.SupplierId,
                            SupplierName = x.s != null ? x.s.SupplierName : "",
                            Grn = x.inv.Grn,
                            Poid = x.inv.Poid,
                            PONumber = x.po != null ? x.po.Ponumber : "",
                            FreightInsurance = x.inv.FreightInsurance,
                            ShippingHandling = x.inv.ShippingHandling,
                            OtherCharges = x.inv.OtherCharges,
                            InvoiceTotal = x.inv.InvoiceTotal,
                            UpdatedById = x.inv.UpdatedById,
                            UpdatedDate = x.inv.UpdatedDate,
                            UpdatedByName = x.updatedBy != null ? x.updatedBy.Name : "",
                            Active = x.inv.Active,
                            DisabledById = x.inv.DisabledById,
                            DisabledDate = x.inv.DisabledDate,
                            DisabledByName = x.disabledBy != null ? x.disabledBy.Name : ""
                        }).ToList();

                    _logger?.LogInformation("Retrieved {Count} invoices for page {PageNumber} (page size: {PageSize})",
                        invoices.Count, request.PageNumber, request.PageSize);

                    return new PaginatedInvoiceResult
                    {
                        Items = invoices,
                        TotalCount = totalCount,
                        PageNumber = request.PageNumber,
                        PageSize = request.PageSize
                    };
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error occurred while retrieving invoices for user: {User}", GlobalData.loggedInUser);
                throw;
            }
        }

        public PaginatedInvoiceResult GetAllInvoicesWithFilters(InvoiceRequestVm filters)
        {
            try
            {
                _logger?.LogInformation("Starting GetAllInvoicesWithFilters operation for user: {User} with filters: SupplierId={SupplierId}, POId={POId}, SearchText={SearchText}",
                    GlobalData.loggedInUser, filters.SupplierId, filters.POId, filters.SearchText);

                using (var db = new Models.pmsdbContext())
                {
                    var query = from inv in db.InvoiceMasters.AsNoTracking()
                                join gateMap in db.GateInInvoiceMappingTables on inv.InvoiceId equals gateMap.InvoiceId into gateInvoices
                                from gateMap in gateInvoices.DefaultIfEmpty()
                                join gate in db.GateInTables on gateMap.GateInId equals gate.GateInId into gateIn
                                from gate in gateIn.DefaultIfEmpty()
                                join s in db.SupplierMasters on inv.SupplierId equals s.SupplierId into suppliers
                                from s in suppliers.DefaultIfEmpty()
                                join po in db.PurchaseOrderTables on inv.Poid equals po.Poid into purchaseOrders
                                from po in purchaseOrders.DefaultIfEmpty()
                                join updatedBy in db.UserMasters on inv.UpdatedById equals updatedBy.UserId into updatedUsers
                                from updatedBy in updatedUsers.DefaultIfEmpty()
                                join disabledBy in db.UserMasters on inv.DisabledById equals disabledBy.UserId into disabledUsers
                                from disabledBy in disabledUsers.DefaultIfEmpty()
                                where filters.Active == null || inv.Active == filters.Active
                                select new { inv, s, po, updatedBy, disabledBy, gate };

                    //Remove Internal and Opening Stock Invoices from the list
                    query = query.Where(x => !x.inv.InvoiceNumber.Contains("Internal_") && !x.inv.InvoiceNumber.Contains("OpeningStock_") && !x.inv.InvoiceNumber.Contains("ProductionStock_"));

                    // Apply filters
                    if (filters.ProductId > 0)
                    {
                        query = query.Join(db.StockMasters, x => x.inv.InvoiceId, s => s.InvoiceId, (inv, s) => new { inv, s })
                        .Join(db.StockProductTables, x => x.s.StockId, sp => sp.StockId, (x, sp) => new { x.inv, x.s, sp })
                        .Where(x => x.sp.ProductId == filters.ProductId).Select(x => x.inv);
                    }

                    if (filters.SupplierId.HasValue && filters.SupplierId > 0)
                    {
                        query = query.Where(x => x.inv.SupplierId == filters.SupplierId);
                    }

                    if (filters.POId.HasValue && filters.POId > 0)
                    {
                        query = query.Where(x => x.inv.Poid == filters.POId);
                    }

                    if (filters.FromDate.HasValue)
                    {
                        query = query.Where(x => x.inv.InvoiceDate >= filters.FromDate);
                    }

                    if (filters.ToDate.HasValue)
                    {
                        query = query.Where(x => x.inv.InvoiceDate <= filters.ToDate);
                    }
                    if (!string.IsNullOrEmpty(filters.InvoiceNumber))
                    {
                        query = query.Where(x => x.inv.InvoiceNumber == filters.InvoiceNumber);
                    }

                    if (!string.IsNullOrEmpty(filters.SearchText))
                    {
                        var searchLower = filters.SearchText.ToLower();
                        query = query.Where(x =>
                            x.inv.InvoiceNumber.ToLower().Contains(searchLower) ||
                            (x.s != null && x.s.SupplierName.ToLower().Contains(searchLower)) ||
                            (x.po != null && x.po.Ponumber.ToLower().Contains(searchLower)) ||
                            x.inv.Grn.ToLower().Contains(searchLower));
                        _logger?.LogDebug("Applied search text filter: {SearchText}", filters.SearchText);
                    }

                    // Filter for invoices without PO mapping
                    if (filters.ShowInvoicesWithoutPOMapping)
                    {
                        query = query.Where(x => x.inv.Poid == null || x.inv.Poid == 0);
                        _logger?.LogDebug("Applied filter for invoices without PO mapping");
                    }

                    // Filter for invoices pending stock receipt
                    if (filters.ShowInvoicesPendingStockReceipt)
                    {
                        var invoicesWithStock = db.StockMasters
                            .Select(s => s.InvoiceId)
                            .Distinct();

                        query = query.Where(x => !invoicesWithStock.Contains(x.inv.InvoiceId));
                        _logger?.LogDebug("Applied filter for invoices pending stock receipt");
                    }

                    // Get total count after filters
                    var totalCount = query.Count();
                    _logger?.LogInformation("Found {TotalCount} invoices after applying filters", totalCount);

                    // Apply pagination
                    var skip = (filters.PageNumber - 1) * filters.PageSize;
                    var invoices = query
                        .OrderByDescending(x => x.inv.InvoiceId)
                        .Skip(skip)
                        .Take(filters.PageSize)
                        .Select(x => new InvoiceMasterVm
                        {
                            InvoiceId = x.inv.InvoiceId,
                            InvoiceNumber = x.inv.InvoiceNumber,
                            InvoiceDate = x.inv.InvoiceDate,
                            InvoiceFile = x.inv.InvoiceFile,
                            InvoiceTotalPrice = x.inv.InvoiceTotalPrice,
                            EwayBill = x.inv.EwayBill,
                            EwayBillDate = x.inv.EwayBillDate,
                            SupplierId = x.inv.SupplierId,
                            SupplierName = x.s != null ? x.s.SupplierName : "",
                            Grn = x.inv.Grn,
                            Poid = x.inv.Poid,
                            PONumber = x.po != null ? x.po.Ponumber : "",
                            FreightInsurance = x.inv.FreightInsurance,
                            ShippingHandling = x.inv.ShippingHandling,
                            OtherCharges = x.inv.OtherCharges,
                            InvoiceTotal = x.inv.InvoiceTotal,
                            InvoiceReceivedDate = x.gate != null ? x.gate.GateInDate : null,
                            InvoiceReceivedBy = x.gate != null ? x.gate.AddedBy : "",
                            UpdatedById = x.inv.UpdatedById,
                            UpdatedDate = x.inv.UpdatedDate,
                            UpdatedByName = x.updatedBy != null ? x.updatedBy.Name : "",
                            Active = x.inv.Active,
                            DisabledById = x.inv.DisabledById,
                            DisabledDate = x.inv.DisabledDate,
                            DisabledByName = x.disabledBy != null ? x.disabledBy.Name : ""
                        }).ToList();

                    // Check if stock is received for each invoice
                    foreach (var invoice in invoices)
                    {
                        invoice.IsStockReceived = db.StockMasters.Any(x => x.InvoiceId == invoice.InvoiceId);
                    }

                    _logger?.LogInformation("Retrieved {Count} invoices for page {PageNumber} (page size: {PageSize}) after filtering",
                        invoices.Count, filters.PageNumber, filters.PageSize);

                    return new PaginatedInvoiceResult
                    {
                        Items = invoices,
                        TotalCount = totalCount,
                        PageNumber = filters.PageNumber,
                        PageSize = filters.PageSize
                    };
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error occurred while retrieving filtered invoices for user: {User}", GlobalData.loggedInUser);
                throw;
            }
        }

        public InvoiceMasterVm GetInvoiceById(long invoiceId)
        {
            try
            {
                _logger?.LogInformation("Getting invoice by ID: {InvoiceId} for user: {User}", invoiceId, GlobalData.loggedInUser);

                using (var db = new Models.pmsdbContext())
                {
                    var res = (from inv in db.InvoiceMasters.AsNoTracking()
                               join s in db.SupplierMasters on inv.SupplierId equals s.SupplierId into suppliers
                               from s in suppliers.DefaultIfEmpty()
                               join po in db.PurchaseOrderTables on inv.Poid equals po.Poid into purchaseOrders
                               from po in purchaseOrders.DefaultIfEmpty()
                               join updatedBy in db.UserMasters on inv.UpdatedById equals updatedBy.UserId into updatedUsers
                               from updatedBy in updatedUsers.DefaultIfEmpty()
                               join disabledBy in db.UserMasters on inv.DisabledById equals disabledBy.UserId into disabledUsers
                               from disabledBy in disabledUsers.DefaultIfEmpty()
                               where inv.InvoiceId == invoiceId
                               select new InvoiceMasterVm
                               {
                                   InvoiceId = inv.InvoiceId,
                                   InvoiceNumber = inv.InvoiceNumber,
                                   InvoiceDate = inv.InvoiceDate,
                                   InvoiceFile = inv.InvoiceFile,
                                   InvoiceTotalPrice = inv.InvoiceTotalPrice,
                                   EwayBill = inv.EwayBill,
                                   EwayBillDate = inv.EwayBillDate,
                                   SupplierId = inv.SupplierId,
                                   SupplierName = s != null ? s.SupplierName : "",
                                   Grn = inv.Grn,
                                   Poid = inv.Poid,
                                   PONumber = po != null ? po.Ponumber : "",
                                   FreightInsurance = inv.FreightInsurance,
                                   ShippingHandling = inv.ShippingHandling,
                                   OtherCharges = inv.OtherCharges,
                                   InvoiceTotal = inv.InvoiceTotal,
                                   UpdatedById = inv.UpdatedById,
                                   UpdatedDate = inv.UpdatedDate,
                                   UpdatedByName = updatedBy != null ? updatedBy.Name : "",
                                   Active = inv.Active,
                                   DisabledById = inv.DisabledById,
                                   DisabledDate = inv.DisabledDate,
                                   DisabledByName = disabledBy != null ? disabledBy.Name : ""
                               }).FirstOrDefault();

                    if (res != null)
                    {
                        _logger?.LogInformation("Successfully retrieved invoice {InvoiceId} ({InvoiceNumber})", invoiceId, res.InvoiceNumber);
                    }
                    else
                    {
                        _logger?.LogWarning("Invoice with ID {InvoiceId} not found", invoiceId);
                    }

                    return res;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error occurred while retrieving invoice {InvoiceId} for user: {User}", invoiceId, GlobalData.loggedInUser);
                throw;
            }
        }

        public ApiFunctionResponseVm UpdateInvoice(InvoiceMasterVm invoice)
        {
            try
            {
                _logger?.LogInformation("Updating invoice {InvoiceId} for user: {User}", invoice.InvoiceId, GlobalData.loggedInUser);

                using (var db = new Models.pmsdbContext())
                {
                    var existingInvoice = db.InvoiceMasters.FirstOrDefault(x => x.InvoiceId == invoice.InvoiceId);
                    if (existingInvoice == null)
                    {
                        _logger?.LogWarning("Invoice {InvoiceId} not found for update", invoice.InvoiceId);
                        return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Invoice not found");
                    }

                    _logger?.LogDebug("Updating invoice {InvoiceId} fields: PO={POId}, EwayBill={EwayBill}, GRN={GRN}",
                        invoice.InvoiceId, invoice.Poid, invoice.EwayBill, invoice.Grn);

                    // Update editable fields
                    existingInvoice.Poid = invoice.Poid;
                    existingInvoice.EwayBill = invoice.EwayBill;
                    existingInvoice.EwayBillDate = invoice.EwayBillDate;
                    existingInvoice.FreightInsurance = invoice.FreightInsurance;
                    existingInvoice.ShippingHandling = invoice.ShippingHandling;
                    existingInvoice.OtherCharges = invoice.OtherCharges;
                    existingInvoice.InvoiceTotal = invoice.InvoiceTotal;

                    // Update audit fields
                    existingInvoice.UpdatedById = db.UserMasters.FirstOrDefault(x => x.Email == GlobalData.loggedInUser)?.UserId;
                    existingInvoice.UpdatedDate = DateTime.Now;

                    db.SaveChanges();

                    // Add audit trail
                    db.AuditTables.Add(new AuditTable
                    {
                        RecId = invoice.InvoiceId,
                        TableName = "InvoiceMaster",
                        EntityName = "InvoiceMaster",
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now,
                    });
                    db.SaveChanges();

                    _logger?.LogInformation("Successfully updated invoice {InvoiceId} ({InvoiceNumber})",
                        invoice.InvoiceId, existingInvoice.InvoiceNumber);

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Invoice updated successfully");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error occurred while updating invoice {InvoiceId} for user: {User}",
                    invoice.InvoiceId, GlobalData.loggedInUser);
                throw;
            }
        }

        public ApiFunctionResponseVm DeleteInvoice(long invoiceId)
        {
            try
            {
                _logger?.LogInformation("Deleting invoice {InvoiceId} for user: {User}", invoiceId, GlobalData.loggedInUser);

                using (var db = new Models.pmsdbContext())
                {
                    var invoice = db.InvoiceMasters.FirstOrDefault(x => x.InvoiceId == invoiceId);
                    if (invoice == null)
                    {
                        _logger?.LogWarning("Invoice {InvoiceId} not found for deletion", invoiceId);
                        return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Invoice not found");
                    }

                    // Check if invoice is linked to stock
                    var linkedStock = db.StockMasters.Any(x => x.InvoiceId == invoiceId);
                    if (linkedStock)
                    {
                        _logger?.LogWarning("Cannot delete invoice {InvoiceId} ({InvoiceNumber}) - linked to stock records",
                            invoiceId, invoice.InvoiceNumber);
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Cannot delete invoice that is linked to stock records");
                    }

                    _logger?.LogDebug("Performing soft delete for invoice {InvoiceId} ({InvoiceNumber})",
                        invoiceId, invoice.InvoiceNumber);

                    // Soft delete
                    invoice.Active = false;
                    invoice.DisabledById = db.UserMasters.FirstOrDefault(x => x.Email == GlobalData.loggedInUser)?.UserId;
                    invoice.DisabledDate = DateTime.Now;

                    db.SaveChanges();

                    // Add audit trail
                    db.AuditTables.Add(new AuditTable
                    {
                        RecId = invoiceId,
                        TableName = "InvoiceMaster",
                        EntityName = "InvoiceMaster",
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now,
                    });
                    db.SaveChanges();

                    _logger?.LogInformation("Successfully deleted invoice {InvoiceId} ({InvoiceNumber})",
                        invoiceId, invoice.InvoiceNumber);

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Invoice deleted successfully");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error occurred while deleting invoice {InvoiceId} for user: {User}",
                    invoiceId, GlobalData.loggedInUser);
                throw;
            }
        }

        public InvoiceProductDetailsVm GetInvoiceProductDetails(long invoiceId)
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    var invoice = db.InvoiceMasters.AsNoTracking()
                        .Join(db.SupplierMasters, i => i.SupplierId, s => s.SupplierId, (i, s) => new { i, s })
                        .Where(i => i.i.InvoiceId == invoiceId && i.i.Active == true)
                        .Select(i => new
                        {
                            i.i.InvoiceId,
                            i.i.InvoiceNumber,
                            i.i.InvoiceDate,
                            i.s.SupplierName
                        })
                        .FirstOrDefault();

                    if (invoice == null)
                    {
                        throw new Exception("Invoice not found");
                    }

                    var stockProducts = (from spt in db.StockProductTables
                                         join sm in db.StockMasters on spt.StockId equals sm.StockId
                                         join pm in db.ProductMasters on spt.ProductId equals pm.ProductId
                                         where sm.InvoiceId == invoiceId
                                         select new InvoiceProductItemVm
                                         {
                                             ProductId = spt.ProductId,
                                             InternalProductName = pm.ProductName,
                                             SupplierProductName = spt.SupplierProductName,
                                             Quantity = spt.Quantity,
                                             Unit = pm.Unit,
                                             PricePerUnit = spt.InvoicePricePerUnit,
                                             TotalAmount = spt.Quantity * spt.InvoicePricePerUnit,
                                             Grade = spt.Grade,
                                             ProductSupplierMappingId = spt.ProductSupplierMappingId
                                         }).ToList();

                    return new InvoiceProductDetailsVm
                    {
                        InvoiceId = invoice.InvoiceId,
                        InvoiceNumber = invoice.InvoiceNumber,
                        InvoiceDate = invoice.InvoiceDate,
                        SupplierName = invoice.SupplierName,
                        Products = stockProducts
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error retrieving invoice product details: " + ex.Message);
            }
        }
    }
}
