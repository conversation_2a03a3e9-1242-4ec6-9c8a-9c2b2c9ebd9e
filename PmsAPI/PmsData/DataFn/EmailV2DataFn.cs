﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using System.Net.Mail;
using System.IO;
using Amazon.SimpleEmail;
using Amazon.SimpleEmail.Model;
using Amazon.Runtime;
using MimeKit;
using System.Threading.Tasks;
using PmsCommon;

namespace PmsData.DataFn
{
    public class EmailV2DataFn
    {
        public static async void SendEmailUsingAmazonSES(string ModuleName, long ModuleId, string EmailInitiatedBy, string htmlString, Stream qwe, string[] emaillist, string[] CCEmailList, string[] BCCEmailList, bool setReplyTo, string[] ReplyToEmail, string filename, string subject)
        {
            try
            {
                using var db = new pmsdbContext();
                var res = new EmailConfigTable();
                res = (from a in db.EmailConfigTables
                       where a.EmailConfigName.ToLower() == "primary"
                       select a).FirstOrDefault();
                EmailTrackingTable ett = new()
                {
                    ModuleName = ModuleName,
                    ModuleId = ModuleId,
                    ToEmailList = string.Join(",", emaillist),
                    EmailInitiatedBy = EmailInitiatedBy,
                    EmailInitiatedDate = DateTime.Now
                };
                db.Add(ett);
                db.SaveChanges();
                var awsCreds = new BasicAWSCredentials(res.EmailConfigFromEmailId, res.EmailConfigPassword);
                var client = new AmazonSimpleEmailServiceClient(awsCreds, Amazon.RegionEndpoint.APSouth1);
                var FromAddressWithName = new MailAddress(res.EmailConfigAccountId, res.EmailConfigFromEmailDisplayName);

                var rawMessage = ConstructRawMessage(htmlString, subject, FromAddressWithName, emaillist, CCEmailList, BCCEmailList, ReplyToEmail, qwe, filename);
                var sendRawEmailRequest = new SendRawEmailRequest
                {
                    RawMessage = rawMessage,
                    Destinations = emaillist.ToList(),
                    Source = FromAddressWithName.ToString()
                };

                var response = await client.SendRawEmailAsync(sendRawEmailRequest);
                if (!string.IsNullOrEmpty(response.MessageId) && response.HttpStatusCode == HttpStatusCode.OK)
                {
                    ett.SesmessageId = response.MessageId;
                    ett.Status = "Accepted";
                }
                else
                {
                    ett.Status = "FailedToSend";
                }
                db.SaveChanges();

            }
            catch (Exception ex)
            {
                throw;
            }
        }
        private static RawMessage ConstructRawMessage(string htmlString, string subject, MailAddress fromAddress, string[] toAddresses, string[] ccAddresses, string[] bccAddresses, string[] replyToAddresses, Stream attachmentStream, string filename)
        {
            var mimeMessage = new MimeMessage();
            mimeMessage.From.Add(new MailboxAddress(fromAddress.DisplayName, fromAddress.Address));
            if (toAddresses != null)
            {
                mimeMessage.To.AddRange(toAddresses.Select(address => MailboxAddress.Parse(address)));
            }
            if (ccAddresses != null)
            {
                mimeMessage.Cc.AddRange(ccAddresses.Select(address => MailboxAddress.Parse(address)));
            }
            if (bccAddresses != null)
            {
                mimeMessage.Bcc.AddRange(bccAddresses.Select(address => MailboxAddress.Parse(address)));
            }
            mimeMessage.Subject = subject;

            // Add Reply-To header if specified
            if (replyToAddresses != null && replyToAddresses.Length > 0)
            {
                mimeMessage.ReplyTo.AddRange(replyToAddresses.Select(address => MailboxAddress.Parse(address)));
            }

            var bodyBuilder = new BodyBuilder
            {
                HtmlBody = htmlString
            };

            if (attachmentStream != null)
            {
                bodyBuilder.Attachments.Add(filename, attachmentStream);
            }

            mimeMessage.Body = bodyBuilder.ToMessageBody();

            using var memoryStream = new MemoryStream();
            mimeMessage.WriteTo(memoryStream);
            return new RawMessage { Data = memoryStream };
        }
        public static void SendEmail(string ModuleName, long ModuleId, string EmailInitiatedBy, string htmlString, Stream qwe, string[] emaillist, string[] CCEmailList, string[] BCCEmailList, bool setReplyTo, string[] ReplyToEmail, string filename, string subject)
        {
            var res = new EmailConfigTable();
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.EmailConfigTables
                       where a.EmailConfigName.ToLower() == "secondary"
                       select a).FirstOrDefault();

                EmailTrackingTable ett = new()
                {
                    ModuleName = ModuleName,
                    ModuleId = ModuleId,
                    ToEmailList = string.Join(",", emaillist),
                    EmailInitiatedBy = EmailInitiatedBy,
                    EmailInitiatedDate = DateTime.Now
                };
                db.Add(ett);
                db.SaveChanges();


                MailMessage message = new MailMessage();
                SmtpClient smtp = new SmtpClient();
                message.From = new MailAddress(res.EmailConfigAccountId, res.EmailConfigFromEmailDisplayName);
                foreach (var email in emaillist)
                {
                    message.To.Add(new MailAddress(email));
                }
                if (CCEmailList != null)
                {
                    foreach (var email in CCEmailList)
                    {
                        message.CC.Add(new MailAddress(email));
                    }
                }
                if (BCCEmailList != null)
                {
                    foreach (var email in BCCEmailList)
                    {
                        message.Bcc.Add(new MailAddress(email));
                    }
                }
                if (setReplyTo)
                {
                    foreach (var email in ReplyToEmail)
                    {
                        message.ReplyToList.Add(new MailAddress(email));
                    }
                }
                message.Subject = subject;
                message.IsBodyHtml = true;
                message.Priority = MailPriority.High;
                message.Body = htmlString;
                if (qwe != null)
                {
                    Attachment attach = new Attachment(qwe, filename);
                    message.Attachments.Add(attach);
                }
                smtp.Port = Convert.ToInt32(res.EmailConfigPort);
                smtp.Host = res.EmailConfigSmtp;
                smtp.EnableSsl = true;
                smtp.UseDefaultCredentials = false;
                smtp.Credentials = new NetworkCredential(res.EmailConfigFromEmailId, res.EmailConfigPassword);
                smtp.DeliveryMethod = SmtpDeliveryMethod.Network;
                try
                {
                    smtp.Send(message);
                    ett.Status = "Accepted";
                }
                catch (Exception)
                {
                    ett.Status = "FailedToSend";
                }
                db.SaveChanges();
            }
        }
    }
}
