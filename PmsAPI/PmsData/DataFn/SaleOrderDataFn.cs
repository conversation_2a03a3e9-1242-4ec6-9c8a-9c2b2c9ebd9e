using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using System.Text.RegularExpressions;
using Microsoft.Graph.TermStore;
using Microsoft.EntityFrameworkCore.Storage;
using System.IO;
using System.Collections;

namespace PmsData.DataFn
{
    public class SaleOrderDataFn
    {
        public GlobalDataEntity GlobalData;
        public SaleOrderDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<SaleOrderTableVm> GetAllSaleOrderData()
        {
            List<SaleOrderTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from s in db.SaleOrderTables
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                       from pcm in spc.DefaultIfEmpty()
                       join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                       from cust in scu.DefaultIfEmpty()
                       join gr in db.GrainMasters on a.GrainId equals gr.GrainId into grn
                       from gr in grn.DefaultIfEmpty()
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId into apr
                       from pr in apr.DefaultIfEmpty()
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           SaleOrderCode = s.SaleOrderCode,
                           CostingAdded = s.CostingAdded,
                           CustomerId = s.CustomerId,
                           CustomerName = cust.CustomerName,
                           CategoryId = s.CategoryId,
                           Category = pcm.ProductCategory,
                           AddedBy = s.AddedBy,
                           AddedDate = s.AddedDate,
                           DeliveryDate = s.DeliveryDate,
                           Remarks = s.Remarks,
                           SaleOrderStatus = s.SaleOrderStatus,
                           WorkPlanStatus = s.WorkPlanStatus,
                           WorkPlanNumber = s.WorkPlanStatus == true ? db.WorkPlanMasters.Where(y => y.WorkPlanId == (db.WorkPlanOrders.Where(x => x.OrderId == s.SaleOrderId).FirstOrDefault().WorkplanId)).FirstOrDefault().WorkPlanNo : "Not Created",
                           IsRawMaterialIssued = s.IsRawMaterialIssued,
                           SaleFormulationCode = fcm.SaleFormulationCode,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,
                           IsJumboRequired = s.IsJumboRequired,
                           ProformaInvoiceId = s.ProformaInvoiceId,
                           ProductionCompletionRemarks = s.ProductionCompletionRemarks,
                           IsPrintRequired = db.SaleOrderProductionPrintMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsEmbossingRequired = db.SaleOrderProductionEmbossingMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsLacquerRequired = db.SaleOrderProductionLacquerRawMaterialTables.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsTumblingRequired = db.SaleOrderProductionTumblingMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsVacuumRequired = db.SaleOrderProductionVacuumMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               ProductId = a.ProductId,
                               ProductName = pr.ProductName,
                               ProductCode = pr.ProductCode,
                               ManufacturingProductName = a.ManufacturingProductName,
                               ManufacturingProductCode = a.ManufacturingProductCode,
                               Lot = a.Lot,
                               Batch = a.Batch,
                               OrderQuantity = a.OrderQuantity,
                               ManufacturingQuantity = a.ManufacturingQuantity,
                               Unit = a.Unit,
                               ColorId = a.ColorId,
                               ColorName = c.ColorName,
                               GrainName = gr.GrainName,
                               GrainId = gr.GrainId,
                               Barcode = a.Barcode,
                               ProductionStatus = a.ProductionStatus,
                               CostingStatus = a.CostingStatus,
                               SlippagePercent = a.SlippagePercent,
                               TotalCost = a.TotalCost,
                               ProcessFormulationCode = a.ProcessFormulationCode,
                               MixingFormulationCode = a.MixingFormulationCode,
                               SalePrice = a.SalePrice,
                               PreSkinGsm = a.PreSkinGsm,
                               SkinGsm = a.SkinGsm,
                               FoamGsm = a.FoamGsm,
                               AdhesiveGsm = a.AdhesiveGsm,
                               FabricGsm = a.FabricGsm,
                               TotalGsm = a.TotalGsm,
                               AddedBy = a.AddedBy,
                               AddedDate = a.AddedDate
                           }
                       }).OrderByDescending(x => x.SaleOrderId).ToList();
            }
            return res;
        }

        public List<SaleOrderTableVm> GetAllSaleOrderWithFilter(SaleOrderRequestFilter filter)
        {
            List<SaleOrderTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from s in db.SaleOrderTables
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                       from pcm in spc.DefaultIfEmpty()
                       join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                       from cust in scu.DefaultIfEmpty()
                       join gr in db.GrainMasters on a.GrainId equals gr.GrainId into grn
                       from gr in grn.DefaultIfEmpty()
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId into apr
                       from pr in apr.DefaultIfEmpty()
                       where ((((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "addeddate") && (filter.FromAddedDate == null || s.AddedDate >= filter.FromAddedDate))
                        && ((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "addeddate") && (filter.ToAddedDate == null || s.AddedDate <= filter.ToAddedDate)))
                        || (((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "saleorderdate") && (filter.FromSaleOrderDate == null || s.SaleOrderDate >= filter.FromSaleOrderDate))
                        && ((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "saleorderdate") && (filter.ToSaleOrderDate == null || s.SaleOrderDate <= filter.ToSaleOrderDate)))
                        || (((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "deliverydate") && (filter.FromDeliveryDate == null || s.DeliveryDate >= filter.FromDeliveryDate))
                        && ((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "deliverydate") && (filter.ToDeliveryDate == null || s.DeliveryDate <= filter.ToDeliveryDate))))
                        && (filter.SaleFormulationCodeId == 0 || filter.SaleFormulationCodeId == null || s.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                        && (String.IsNullOrEmpty(filter.SaleOrderNumber) || s.SaleOrderNumber.Contains(filter.SaleOrderNumber))
                        && (filter.Status == null || s.Status == (int)filter.Status)
                        && (String.IsNullOrEmpty(filter.AddedBy) || s.AddedBy.ToLower().Contains(filter.AddedBy))
                        && (String.IsNullOrEmpty(filter.ArticleName) || a.ManufacturingProductName.ToLower().Contains(filter.ArticleName.ToLower()))
                        && (filter.ColorId == 0 || filter.ColorId == null || a.ColorId == filter.ColorId)
                        && (filter.GrainId == 0 || filter.GrainId == null || a.GrainId == filter.GrainId)
                        && (filter.CustomerId == 0 || filter.CustomerId == null || s.CustomerId == filter.CustomerId)
                        && (String.IsNullOrEmpty(filter.ProductType) || fcm.SaleFormulationCode.ToLower().StartsWith(filter.ProductType.ToLower()))
                        && (String.IsNullOrEmpty(filter.OrderType) || s.SaleOrderType.ToLower().Contains(filter.OrderType.ToLower()))
                        && (string.IsNullOrEmpty(filter.SaleOrderStatus) || s.SaleOrderStatus == filter.SaleOrderStatus)
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           SaleOrderCode = s.SaleOrderCode,
                           CostingAdded = s.CostingAdded,
                           CustomerId = s.CustomerId,
                           CustomerName = cust.CustomerName,
                           CategoryId = s.CategoryId,
                           Category = pcm.ProductCategory,
                           AddedBy = s.AddedBy,
                           AddedDate = s.AddedDate,
                           DeliveryDate = s.DeliveryDate,
                           Remarks = s.Remarks,
                           SaleOrderStatus = s.SaleOrderStatus,
                           OrderStatusActionBy = s.SaleOrderStatus == PMSSaleOrderStatus.Hold ? "Hold By: " + db.UserMasters.Where(x => x.Email == s.HoldBy).FirstOrDefault().Name : s.SaleOrderStatus == PMSSaleOrderStatus.Active && s.ApprovedBy != null ? "Approved By: " + db.UserMasters.Where(x => x.Email == s.ApprovedBy).FirstOrDefault().Name : s.SaleOrderStatus == PMSSaleOrderStatus.Active && s.ApprovedBy == null ? "N/A" : "Pending",
                           OrderStatusActionDate = s.SaleOrderStatus == PMSSaleOrderStatus.Hold ? s.HoldDate : s.SaleOrderStatus == PMSSaleOrderStatus.Active ? s.ApprovedDate : null,
                           WorkPlanStatus = s.WorkPlanStatus,
                           WorkPlanNumber = s.WorkPlanStatus == true ? db.WorkPlanMasters.Where(y => y.WorkPlanId == (db.WorkPlanOrders.Where(x => x.OrderId == s.SaleOrderId).FirstOrDefault().WorkplanId)).FirstOrDefault().WorkPlanNo : "Not Created",
                           IsRawMaterialIssued = s.IsRawMaterialIssued,
                           SaleFormulationCode = fcm.SaleFormulationCode,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,
                           IsJumboRequired = s.IsJumboRequired,
                           ProformaInvoiceId = s.ProformaInvoiceId,
                           ProductionCompletionRemarks = s.ProductionCompletionRemarks,
                           IsPrintRequired = db.SaleOrderProductionPrintMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsEmbossingRequired = db.SaleOrderProductionEmbossingMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsLacquerRequired = db.SaleOrderProductionLacquerRawMaterialTables.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsTumblingRequired = db.SaleOrderProductionTumblingMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsVacuumRequired = db.SaleOrderProductionVacuumMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           LinkedSaleOrder = db.LinkedSaleOrderTables.Where(x => x.LinkedSaleOrder == a.SaleOrderId).Select(x => new LinkedSaleOrderTableVm
                           {
                               LinkedId = x.LinkedId,
                               ParentSaleOrder = x.ParentSaleOrder,
                               ParentSaleOrderNumber = db.SaleOrderTables.FirstOrDefault(y => y.SaleOrderId == x.ParentSaleOrder).SaleOrderNumber,
                               LinkedSaleOrder = x.LinkedSaleOrder,
                               LinkedSaleOrderNumber = s.SaleOrderNumber
                           }).ToList(),
                           ParentSaleOrder = db.LinkedSaleOrderTables.Where(x => x.ParentSaleOrder == a.SaleOrderId).Select(x => new LinkedSaleOrderTableVm
                           {
                               LinkedId = x.LinkedId,
                               ParentSaleOrder = x.ParentSaleOrder,
                               ParentSaleOrderNumber = s.SaleOrderNumber,
                               LinkedSaleOrder = x.LinkedSaleOrder,
                               LinkedSaleOrderNumber = db.SaleOrderTables.FirstOrDefault(y => y.SaleOrderId == x.LinkedSaleOrder).SaleOrderNumber
                           }).ToList(),
                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               ProductId = a.ProductId,
                               ProductName = pr.ProductName,
                               ProductCode = pr.ProductCode,
                               ManufacturingProductName = a.ManufacturingProductName,
                               ManufacturingProductCode = a.ManufacturingProductCode,
                               Lot = a.Lot,
                               Batch = a.Batch,
                               OrderQuantity = a.OrderQuantity,
                               ManufacturingQuantity = a.ManufacturingQuantity,
                               Unit = a.Unit,
                               ColorId = a.ColorId,
                               ColorName = c.ColorName,
                               GrainName = gr.GrainName,
                               GrainId = gr.GrainId,
                               Barcode = a.Barcode,
                               ProductionStatus = a.ProductionStatus,
                               CostingStatus = a.CostingStatus,
                               SlippagePercent = a.SlippagePercent,
                               TotalCost = a.TotalCost,
                               ProcessFormulationCode = a.ProcessFormulationCode,
                               MixingFormulationCode = a.MixingFormulationCode,
                               SalePrice = a.SalePrice,
                               PreSkinGsm = a.PreSkinGsm,
                               SkinGsm = a.SkinGsm,
                               FoamGsm = a.FoamGsm,
                               AdhesiveGsm = a.AdhesiveGsm,
                               FabricGsm = a.FabricGsm,
                               TotalGsm = a.TotalGsm,
                               AddedBy = a.AddedBy,
                               AddedDate = a.AddedDate
                           }
                       }).OrderByDescending(x => x.SaleOrderId).ToList();
            }
            return res;
        }
        public List<SaleOrderTableVm> GetAllSaleOrderForFilterByStatus(List<string> saleorderstatus)
        {
            List<ESalesOrderStatus> stsList = new List<ESalesOrderStatus>();

            foreach (var item in saleorderstatus)
            {
                stsList.Add(PMSEnum.ParseEnum<ESalesOrderStatus>(item));

            }
            List<SaleOrderTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from s in db.SaleOrderTables
                       where stsList.Contains((ESalesOrderStatus)s.Status)
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderNumber = s.SaleOrderNumber
                       }).OrderByDescending(x => x.SaleOrderId).ToList();
            }
            return res;
        }

        public List<SaleOrderTableVm> GetAllSaleOrderDataByStatus(string status)
        {
            ESalesOrderStatus sts = PMSEnum.ParseEnum<ESalesOrderStatus>(status);
            List<SaleOrderTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from s in db.SaleOrderTables
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                       from pcm in spc.DefaultIfEmpty()
                       join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                       from cust in scu.DefaultIfEmpty()
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId into apr
                       from pr in apr.DefaultIfEmpty()
                       where s.Status == (int)sts
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           SaleOrderCode = s.SaleOrderCode,
                           CostingAdded = s.CostingAdded,
                           CustomerId = s.CustomerId,
                           CustomerName = cust.CustomerName,
                           CategoryId = s.CategoryId,
                           Category = pcm.ProductCategory,
                           AddedBy = s.AddedBy,
                           AddedDate = s.AddedDate,
                           UpdatedBy = s.UpdatedBy,
                           UpdatedDate = s.UpdatedDate,
                           DeliveryDate = s.DeliveryDate,
                           Remarks = s.Remarks,
                           SaleOrderStatus = s.SaleOrderStatus,
                           WorkPlanStatus = s.WorkPlanStatus,
                           IsRawMaterialIssued = s.IsRawMaterialIssued,
                           SaleFormulationCode = fcm.SaleFormulationCode,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,
                           IsJumboRequired = s.IsJumboRequired,
                           ProformaInvoiceId = s.ProformaInvoiceId,
                           ProductionCompletionRemarks = s.ProductionCompletionRemarks,
                           IsPrintRequired = db.SaleOrderProductionPrintMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsEmbossingRequired = db.SaleOrderProductionEmbossingMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsLacquerRequired = db.SaleOrderProductionLacquerRawMaterialTables.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsTumblingRequired = db.SaleOrderProductionTumblingMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsVacuumRequired = db.SaleOrderProductionVacuumMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               ProductId = a.ProductId,
                               ProductName = pr.ProductName,
                               ProductCode = pr.ProductCode,
                               ManufacturingProductName = a.ManufacturingProductName,
                               ManufacturingProductCode = a.ManufacturingProductCode,
                               Lot = a.Lot,
                               Batch = a.Batch,
                               OrderQuantity = a.OrderQuantity,
                               ManufacturingQuantity = a.ManufacturingQuantity,
                               Unit = a.Unit,
                               ColorId = a.ColorId,
                               ColorName = c.ColorName,
                               Barcode = a.Barcode,
                               ProductionStatus = a.ProductionStatus,
                               CostingStatus = a.CostingStatus,
                               SlippagePercent = a.SlippagePercent,
                               TotalCost = a.TotalCost,
                               ProcessFormulationCode = a.ProcessFormulationCode,
                               MixingFormulationCode = a.MixingFormulationCode,
                               SalePrice = a.SalePrice,
                               PreSkinGsm = a.PreSkinGsm,
                               SkinGsm = a.SkinGsm,
                               FoamGsm = a.FoamGsm,
                               AdhesiveGsm = a.AdhesiveGsm,
                               FabricGsm = a.FabricGsm,
                               TotalGsm = a.TotalGsm,
                               AddedBy = a.AddedBy,
                               AddedDate = a.AddedDate
                           }
                       }).OrderByDescending(x => x.SaleOrderId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddSaleOrder(SaleOrderTableVm saleorder)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var (financialYearStart, financialYearEnd) = CommonFunctions.GetFinancialYearDates();

                        var PreviousSaleOrder = db.SaleOrderTables
                                          .Where(order => order.AddedDate.Value.Date >= financialYearStart && order.AddedDate.Value.Date <= financialYearEnd)
                                          .OrderByDescending(order => order.AddedDate)
                                          .Select(order => new { order.SaleOrderId, order.SaleOrderNumber })
                                          .FirstOrDefault();


                        var sofc = db.SaleFormulationCodeMasters.FirstOrDefault(x => x.SaleFormulationCode == saleorder.SaleFormulationCode && x.Disabled != true);

                        SaleOrderTable sot = new SaleOrderTable();
                        //sot.SaleOrderNumber = saleorder.SaleOrderNumber;
                        sot.SaleOrderDate = saleorder.SaleOrderDate;
                        sot.CustomerId = saleorder.CustomerId;
                        sot.SaleOrderType = saleorder.SaleOrderType;
                        sot.DeliveryDate = saleorder.DeliveryDate;
                        sot.Remarks = saleorder.Remarks;
                        sot.CostingAdded = saleorder.CostingAdded;
                        sot.Bornumber = saleorder.BORNumber;
                        sot.SaleOrderStatus = PMSSaleOrderStatus.ApprovalPending;
                        sot.CategoryId = saleorder.CategoryId;
                        sot.AddedBy = GlobalData.loggedInUser;
                        sot.AddedDate = System.DateTime.Now;
                        sot.ProformaInvoiceId = saleorder.ProformaInvoiceId;
                        sot.WorkPlanStatus = false;
                        sot.Status = (int)ESalesOrderStatus.NotYet;
                        sot.SaleFormulationCodeId = sofc.SaleFormulationCodeId;
                        db.SaleOrderTables.Add(sot);
                        db.SaveChanges();

                        var newsaleOrderId = sot.SaleOrderId;
                        sot.SaleOrderNumber = CommonFunctions.FinancialYearUniqueNumber(newsaleOrderId, PreviousSaleOrder?.SaleOrderId, PreviousSaleOrder?.SaleOrderNumber, true);
                        db.SaveChanges();


                        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                        {
                            SaleOrderId = sot.SaleOrderId,
                            Status = sot.Status,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now
                        });
                        db.SaveChanges();

                        SaleOrderProductionTable pt = new SaleOrderProductionTable();
                        pt.SaleOrderId = sot.SaleOrderId;
                        pt.ProductId = saleorder.SaleOrderProduction.ProductId;
                        pt.ManufacturingProductName = saleorder.SaleOrderProduction.ManufacturingProductName;
                        pt.ManufacturingProductCode = saleorder.SaleOrderProduction.ManufacturingProductCode;
                        pt.Lot = saleorder.SaleOrderProduction.Lot;
                        pt.Batch = saleorder.SaleOrderProduction.Batch;
                        pt.OrderQuantity = saleorder.SaleOrderProduction.OrderQuantity;
                        //pt.ManufacturingQuantity = saleorder.SaleOrderProduction.ManufacturingQuantity;
                        pt.Barcode = saleorder.SaleOrderProduction.Barcode;
                        pt.ColorId = saleorder.SaleOrderProduction.ColorId;
                        pt.ColorPrice = saleorder.SaleOrderProduction.ColorPrice;
                        pt.Thick = saleorder.SaleOrderProduction.Thick;
                        pt.ThickPrice = saleorder.SaleOrderProduction.ThickPrice;
                        pt.Width = saleorder.SaleOrderProduction.Width;
                        pt.WidthPrice = saleorder.SaleOrderProduction.WidthPrice;
                        pt.ProductionStatus = PmsCommon.PMSStatus.Planning;
                        pt.SlippagePercent = saleorder.SaleOrderProduction.SlippagePercent;
                        pt.TotalCost = saleorder.SaleOrderProduction.TotalCost;
                        pt.CostingStatus = CostingStatus.Pending;
                        pt.AddedBy = GlobalData.loggedInUser;
                        pt.AddedDate = System.DateTime.Now;
                        pt.GrainId = saleorder.SaleOrderProduction.GrainId;
                        pt.GrainPrice = saleorder.SaleOrderProduction.GrainPrice;
                        pt.MixingFormulationCode = saleorder.SaleOrderProduction.MixingFormulationCode;
                        pt.SalePrice = saleorder.SaleOrderProduction.SalePrice;
                        pt.PreSkinGsm = saleorder.SaleOrderProduction.PreSkinGsm;
                        pt.SkinGsm = saleorder.SaleOrderProduction.SkinGsm;
                        pt.FoamGsm = saleorder.SaleOrderProduction.FoamGsm;
                        pt.AdhesiveGsm = saleorder.SaleOrderProduction.AdhesiveGsm;
                        pt.FabricGsm = saleorder.SaleOrderProduction.FabricGsm;
                        pt.TotalCost = saleorder.SaleOrderProduction.TotalCost;
                        pt.FabricColorId = saleorder.SaleOrderProduction.FabricColorId;
                        pt.FabricProductId = saleorder.SaleOrderProduction.FabricProductId;
                        db.SaleOrderProductionTables.Add(pt);
                        db.SaveChanges();

                        if (sofc.IsOrderLinkingRequired.Value)
                        {
                            db.LinkedSaleOrderTables.Add(new LinkedSaleOrderTable
                            {
                                ParentSaleOrder = saleorder.OrderIdForLinking,
                                LinkedSaleOrder = sot.SaleOrderId,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                IsLinkingComplete = false
                            });
                            db.SaveChanges();
                        }

                        foreach (var sopp in saleorder.SaleOrderPostProcessOrder.OrderBy(x => x.Rank))
                        {
                            SaleOrderPostProcessOrderTable ordertb = new SaleOrderPostProcessOrderTable()
                            {
                                SaleOrderId = sot.SaleOrderId,
                                Rank = sopp.Rank.Value,
                                PostProcessName = sopp.PostProcessName
                            };
                            db.SaleOrderPostProcessOrderTables.Add(ordertb);
                            db.SaveChanges();
                        }
                        foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionEmbossing.OrderBy(x => x.Rank))
                        {
                            SaleOrderProductionEmbossingMaster spt = new SaleOrderProductionEmbossingMaster();
                            spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                            spt.EmbossingMasterId = item.EmbossingMasterId;
                            spt.Price = item.Price;
                            spt.Quantity = item.Quantity;
                            spt.Total = item.Total;
                            spt.Rank = item.Rank;
                            db.SaleOrderProductionEmbossingMasters.Add(spt);

                            var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                            if (ppt != null)
                            {
                                ppt.ProcessMasterId = item.EmbossingMasterId;
                                db.SaveChanges();
                            }
                        }
                        var EmbossingQry = (from e in db.EmbossingMasters
                                            join i in db.SaleOrderProductionEmbossingMasters on e.EmbossingMasterId equals i.EmbossingMasterId
                                            where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                            select new KeyValue
                                            {
                                                Key = i.Rank,
                                                Value = e.Code
                                            }).Distinct().ToList();

                        foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionPrint.OrderBy(x => x.Rank))
                        {
                            SaleOrderProductionPrintMaster spt = new SaleOrderProductionPrintMaster();
                            spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                            spt.PrintMasterId = item.PrintMasterId;
                            spt.Price = item.Price;
                            spt.Quantity = item.Quantity;
                            spt.Total = item.Total;
                            spt.Rank = item.Rank;
                            db.SaleOrderProductionPrintMasters.Add(spt);

                            var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                            if (ppt != null)
                            {
                                ppt.ProcessMasterId = item.PrintMasterId;
                                db.SaveChanges();
                            }
                        }
                        var PrintQry = (from e in db.PrintMasters
                                        join i in db.SaleOrderProductionPrintMasters on e.PrintMasterId equals i.PrintMasterId
                                        where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                        select new KeyValue
                                        {
                                            Key = i.Rank,
                                            Value = e.Code
                                        }).Distinct().ToList();

                        foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionTumbling)
                        {
                            SaleOrderProductionTumblingMaster spt = new SaleOrderProductionTumblingMaster();
                            spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                            spt.TumblingMasterId = item.TumblingMasterId;
                            spt.Price = item.Price;
                            spt.Quantity = item.Quantity;
                            spt.Total = item.Total;
                            spt.Rank = item.Rank;
                            db.SaleOrderProductionTumblingMasters.Add(spt);

                            var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                            if (ppt != null)
                            {
                                ppt.ProcessMasterId = item.TumblingMasterId;
                                db.SaveChanges();
                            }
                        }
                        var TumblingQry = (from e in db.TumblingMasters
                                           join i in db.SaleOrderProductionTumblingMasters on e.TumblingMasterId equals i.TumblingMasterId
                                           where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                           select new KeyValue
                                           {
                                               Key = i.Rank,
                                               Value = e.Code
                                           }).Distinct().ToList();
                        foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionVacuum)
                        {
                            SaleOrderProductionVacuumMaster spt = new SaleOrderProductionVacuumMaster();
                            spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                            spt.VacuumMasterId = item.VacuumMasterId;
                            spt.Price = item.Price;
                            spt.Quantity = item.Quantity;
                            spt.Total = item.Total;
                            spt.Rank = item.Rank;
                            db.SaleOrderProductionVacuumMasters.Add(spt);

                            var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                            if (ppt != null)
                            {
                                ppt.ProcessMasterId = item.VacuumMasterId;
                                db.SaveChanges();
                            }

                        }
                        var VacuumQry = (from e in db.VacuumMasters
                                         join i in db.SaleOrderProductionVacuumMasters on e.VacuumMasterId equals i.VacuumMasterId
                                         where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                         select new KeyValue
                                         {
                                             Key = i.Rank,
                                             Value = e.Code
                                         }).Distinct().ToList();
                        //foreach (var item in production.Process)
                        //{
                        //    ProductionProcessTable mm = new ProductionProcessTable();
                        //    mm.ProductionProcessName = item.ProductionProcessName;
                        //    mm.Temperature = item.Temperature;
                        //    mm.ProductionId = pt.ProductionId;
                        //    mm.WeightGsm = item.WeightGsm;
                        //    mm.WeightPressure = item.WeightPressure;
                        //    mm.AddedBy = GlobalData.loggedInUser;
                        //    mm.AddedDate = System.DateTime.Now;
                        //    db.ProductionProcessTables.Add(mm);
                        //    db.SaveChanges();
                        //    foreach (var itm in item.ProductionProcessRawMaterial)
                        //    {
                        //        ProductionProcessRawMaterialTable spt = new ProductionProcessRawMaterialTable();
                        //        spt.ProductionProcessId = mm.ProductionProcessId;
                        //        spt.ProductId = itm.ProductId;
                        //        spt.Quantity = itm.Quantity;
                        //        spt.Unit = itm.Unit;
                        //        spt.Price = itm.Price;
                        //        db.ProductionProcessRawMaterialTables.Add(spt);
                        //    }
                        //    db.SaveChanges();
                        //}
                        foreach (var item in saleorder.SaleOrderProduction.Mixing)
                        {
                            SaleOrderProductionMixingTable mm = new SaleOrderProductionMixingTable();
                            mm.ProductionMixingName = item.ProductionMixingName;
                            mm.SaleOrderProductionId = pt.SaleOrderProductionId;
                            mm.Wastage = item.Wastage;
                            mm.WastageType = item.WastageType;
                            mm.Total = item.Total;
                            mm.WeightGsm = item.WeightGsm;
                            mm.AddedBy = GlobalData.loggedInUser;
                            mm.AddedDate = System.DateTime.Now;
                            db.SaleOrderProductionMixingTables.Add(mm);
                            db.SaveChanges();
                            foreach (var itm in item.MixingRawMaterial)
                            {
                                SaleOrderProductionMixingRawMaterialTable spt = new SaleOrderProductionMixingRawMaterialTable();
                                spt.SaleOrderProductionMixingId = mm.SaleOrderProductionMixingId;
                                spt.ProductId = itm.ProductId;
                                spt.Quantity = itm.Quantity;
                                spt.Unit = itm.Unit;
                                spt.Price = itm.Price;
                                db.SaleOrderProductionMixingRawMaterialTables.Add(spt);
                            }
                            db.SaveChanges();
                        }
                        foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionRawMaterial)
                        {
                            bool flag = db.StockProductTables.Any(x => x.ProductId == item.ProductId);
                            SaleOrderProductionRawMaterialTable spt = new SaleOrderProductionRawMaterialTable();
                            spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                            spt.ProductId = item.ProductId;
                            spt.Unit = item.Unit;
                            spt.Quantity = item.Quantity;
                            spt.PerUnitCost = flag ? db.StockProductTables.Where(x => x.ProductId == item.ProductId).OrderByDescending(x => x.StockProductId).ToList()[0].PricePerUnit : 0;
                            spt.TotalCost = item.Quantity * spt.PerUnitCost;
                            db.SaleOrderProductionRawMaterialTables.Add(spt);
                        }
                        foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionElement)
                        {
                            SaleOrderProductionElementTable spt = new SaleOrderProductionElementTable();
                            spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                            spt.ElementId = item.ElementId;
                            spt.PerUnitCost = item.PerUnitCost;
                            spt.TotalCost = item.TotalCost;
                            spt.Value = item.Value;
                            db.SaleOrderProductionElementTables.Add(spt);
                        }

                        // foreach (var item in saleorder.SaleOrderProduction.Lacquer)
                        // {
                        //     var lacqdata = db.LacquerRawMaterialTables.Where(x => x.LacquerMasterId == item.LacquerMasterId).ToList();
                        //     foreach (var itemraw in lacqdata)
                        //     {
                        //         SaleOrderProductionLacquerRawMaterialTable spt = new SaleOrderProductionLacquerRawMaterialTable();
                        //         spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                        //         spt.LacquerMasterId = item.LacquerMasterId;
                        //         //spt.ProductId = itemraw.ProductId;
                        //         spt.Quantity = itemraw.Quantity;
                        //         spt.Unit = itemraw.Unit;
                        //         spt.Price = itemraw.Price;
                        //         spt.Rank = item.Rank;
                        //         db.SaleOrderProductionLacquerRawMaterialTables.Add(spt);
                        //     }
                        //     var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                        //     if (ppt != null)
                        //     {
                        //         ppt.ProcessMasterId = item.LacquerMasterId;
                        //         db.SaveChanges();
                        //     }
                        // }

                        foreach (var item in saleorder.SaleOrderProduction.Lacquer)
                        {

                            SaleOrderProductionLacquerRawMaterialTable spt = new SaleOrderProductionLacquerRawMaterialTable();
                            spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                            spt.LacquerMasterId = item.LacquerMasterId;
                            //spt.ProductId = itemraw.ProductId;
                            // spt.Quantity = itemraw.Quantity;
                            // spt.Unit = itemraw.Unit;
                            // spt.Price = itemraw.Price;
                            spt.Rank = item.Rank;
                            db.SaleOrderProductionLacquerRawMaterialTables.Add(spt);

                            var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                            if (ppt != null)
                            {
                                ppt.ProcessMasterId = item.LacquerMasterId;
                                db.SaveChanges();
                            }
                        }
                        var LacquerQry = (from e in db.LacquerMasters
                                          join i in db.SaleOrderProductionLacquerRawMaterialTables on e.LacquerMasterId equals i.LacquerMasterId
                                          where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                          select new KeyValue
                                          {
                                              Key = i.Rank,
                                              Value = e.Code
                                          }).Distinct().ToList();
                        db.SaveChanges();

                        var graincode = SanitizeCodeComponent(db.GrainMasters.FirstOrDefault(x => x.GrainId == saleorder.SaleOrderProduction.GrainId)?.GrainCode);
                        var colorcode = SanitizeCodeComponent(db.ColorMasters.FirstOrDefault(x => x.ColorId == saleorder.SaleOrderProduction.ColorId)?.ColorCode);
                        var thicknesscode = SanitizeCodeComponent(db.ThicknessMasters.FirstOrDefault(x => x.ThicknessId == saleorder.SaleOrderProduction.Thick)?.Code);

                        // Build components list with validation
                        var saleCodeComponents = new List<string>();

                        // Add formulation code
                        if (!string.IsNullOrWhiteSpace(saleorder.SaleFormulationCode))
                            saleCodeComponents.Add(SanitizeCodeComponent(saleorder.SaleFormulationCode));

                        // Add grain code
                        if (!string.IsNullOrWhiteSpace(graincode))
                            saleCodeComponents.Add(graincode);

                        // Add color code
                        if (!string.IsNullOrWhiteSpace(colorcode))
                            saleCodeComponents.Add(colorcode);

                        // Get process codes
                        var RankCodes = new List<KeyValue>();
                        if (PrintQry != null)
                            RankCodes.AddRange(PrintQry);
                        if (EmbossingQry != null)
                            RankCodes.AddRange(EmbossingQry);
                        if (TumblingQry != null)
                            RankCodes.AddRange(TumblingQry);
                        if (VacuumQry != null)
                            RankCodes.AddRange(VacuumQry);
                        if (LacquerQry != null)
                            RankCodes.AddRange(LacquerQry);

                        var newsaleCode = GetSaleOrderProcessCode(RankCodes);
                        var finishcode = newsaleCode;

                        // Add process codes only if they exist
                        if (!string.IsNullOrWhiteSpace(newsaleCode))
                            saleCodeComponents.Add(newsaleCode);

                        // Add thickness code
                        if (!string.IsNullOrWhiteSpace(thicknesscode))
                            saleCodeComponents.Add(thicknesscode);

                        // Join all components with single slash
                        var salecode = string.Join("/", saleCodeComponents);
                        sot.SaleOrderCode = salecode;
                        sot.FinishCode = string.IsNullOrEmpty(finishcode) == true ? "DIRECT" : finishcode;
                        db.SaveChanges();
                        transaction.Commit();
                        var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnCreate" && x.ConfigValue == "true")).ToList();
                        if (enableSOStatusEmail.Count == 2)
                        {
                            var emailSaleOrderStatus = SaleOrderEmailStatus.OrderReceived;
                            _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(sot.SaleOrderId, emailSaleOrderStatus);
                        }

                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }

        }
        public string GetSaleOrderProcessCode(List<KeyValue> InputList)
        {
            if (InputList == null || !InputList.Any())
                return string.Empty;

            // Filter out null/empty values and trim whitespace
            var validCodes = InputList
                .Where(x => !string.IsNullOrWhiteSpace(x.Value))
                .OrderBy(x => x.Key)
                .Select(x => x.Value.Trim())
                .ToArray();

            return validCodes.Any() ? string.Join("/", validCodes) : string.Empty;
        }

        private string SanitizeCodeComponent(string component)
        {
            if (string.IsNullOrWhiteSpace(component))
                return string.Empty;

            return component.Trim()
                .Replace("//", "/")  // Remove double slashes
                .Replace("  ", " ")  // Remove double spaces
                .Trim();
        }
        public ApiFunctionResponseVm EditPostProcess(SaleOrderTableVm saleOrder)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        SaleOrderTable objSot = db.SaleOrderTables.FirstOrDefault(S => S.SaleOrderId == saleOrder.SaleOrderId);
                        var SaleOrderProductionRecord = db.SaleOrderProductionTables.FirstOrDefault(I => I.SaleOrderId == saleOrder.SaleOrderId);
                        var existingPPO = db.SaleOrderPostProcessOrderTables.Where(x => x.SaleOrderId == saleOrder.SaleOrderId).ToList();
                        if (objSot.Status >= 8 && objSot.Status <= 23 && !StructuralComparisons.StructuralEqualityComparer.Equals(saleOrder.SaleOrderPostProcessOrder, existingPPO))
                        {
                            foreach (var sopp in saleOrder.SaleOrderPostProcessOrder)
                            {
                                SaleOrderPostProcessOrderTable ordertb = new SaleOrderPostProcessOrderTable()
                                {
                                    SaleOrderId = saleOrder.SaleOrderId,
                                    Rank = sopp.Rank.Value,
                                    PostProcessName = sopp.PostProcessName,
                                    ProcessMasterId = sopp.ProcessMasterId
                                };

                                db.SaleOrderPostProcessOrderTables.Add(ordertb);
                                db.SaveChanges();
                            }

                            var EmbossingCode = "";

                            foreach (var item in saleOrder.SaleOrderProduction.SaleOrderProductionEmbossing)
                            {
                                SaleOrderProductionEmbossingMaster spt = new SaleOrderProductionEmbossingMaster();
                                spt.SaleOrderProductionId = SaleOrderProductionRecord.SaleOrderProductionId;
                                spt.EmbossingMasterId = item.EmbossingMasterId;
                                spt.Price = item.Price;
                                spt.Quantity = item.Quantity;
                                spt.Total = item.Total;
                                spt.Rank = item.Rank;
                                db.SaleOrderProductionEmbossingMasters.Add(spt);

                                var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == saleOrder.SaleOrderId).FirstOrDefault();
                                if (ppt != null)
                                {
                                    ppt.ProcessMasterId = item.EmbossingMasterId;
                                }

                                if (string.IsNullOrEmpty(EmbossingCode))
                                {
                                    EmbossingCode = db.EmbossingMasters.FirstOrDefault(x => x.EmbossingMasterId == item.EmbossingMasterId).Code;
                                }
                                else
                                {
                                    EmbossingCode = EmbossingCode + ":" + (db.EmbossingMasters.FirstOrDefault(x => x.EmbossingMasterId == item.EmbossingMasterId).Code);
                                }
                            }

                            var printCode = "";
                            foreach (var item in saleOrder.SaleOrderProduction.SaleOrderProductionPrint)
                            {
                                SaleOrderProductionPrintMaster spt = new SaleOrderProductionPrintMaster();
                                spt.SaleOrderProductionId = SaleOrderProductionRecord.SaleOrderProductionId;
                                spt.PrintMasterId = item.PrintMasterId;
                                spt.Price = item.Price;
                                spt.Quantity = item.Quantity;
                                spt.Total = item.Total;
                                spt.Rank = item.Rank;
                                db.SaleOrderProductionPrintMasters.Add(spt);

                                var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == saleOrder.SaleOrderId).FirstOrDefault();
                                if (ppt != null)
                                {
                                    ppt.ProcessMasterId = item.PrintMasterId;
                                }

                                if (string.IsNullOrEmpty(printCode))
                                {
                                    printCode = db.PrintMasters.FirstOrDefault(x => x.PrintMasterId == item.PrintMasterId).Code;
                                }
                                else
                                {
                                    printCode = printCode + ":" + (db.PrintMasters.FirstOrDefault(x => x.PrintMasterId == item.PrintMasterId).Code);
                                }
                            }

                            var tumblingCode = "";
                            foreach (var item in saleOrder.SaleOrderProduction.SaleOrderProductionTumbling)
                            {
                                SaleOrderProductionTumblingMaster spt = new SaleOrderProductionTumblingMaster();
                                spt.SaleOrderProductionId = SaleOrderProductionRecord.SaleOrderProductionId;
                                spt.TumblingMasterId = item.TumblingMasterId;
                                spt.Price = item.Price;
                                spt.Quantity = item.Quantity;
                                spt.Total = item.Total;
                                spt.Rank = item.Rank;
                                db.SaleOrderProductionTumblingMasters.Add(spt);

                                var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == saleOrder.SaleOrderId).FirstOrDefault();
                                if (ppt != null)
                                {
                                    ppt.ProcessMasterId = item.TumblingMasterId;
                                }

                                if (string.IsNullOrEmpty(tumblingCode))
                                {
                                    tumblingCode = db.TumblingMasters.FirstOrDefault(x => x.TumblingMasterId == item.TumblingMasterId).Code;
                                }
                                else
                                {
                                    tumblingCode = tumblingCode + ":" + (db.TumblingMasters.FirstOrDefault(x => x.TumblingMasterId == item.TumblingMasterId).Code);
                                }
                            }

                            var vacuumCode = "";
                            foreach (var item in saleOrder.SaleOrderProduction.SaleOrderProductionVacuum)
                            {
                                SaleOrderProductionVacuumMaster spt = new SaleOrderProductionVacuumMaster();
                                spt.SaleOrderProductionId = SaleOrderProductionRecord.SaleOrderProductionId;
                                spt.VacuumMasterId = item.VacuumMasterId;
                                spt.Price = item.Price;
                                spt.Quantity = item.Quantity;
                                spt.Total = item.Total;
                                spt.Rank = item.Rank;
                                db.SaleOrderProductionVacuumMasters.Add(spt);

                                var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == saleOrder.SaleOrderId).FirstOrDefault();
                                if (ppt != null)
                                {
                                    ppt.ProcessMasterId = item.VacuumMasterId;
                                }

                                if (string.IsNullOrEmpty(vacuumCode))
                                {
                                    vacuumCode = db.VacuumMasters.FirstOrDefault(x => x.VacuumMasterId == item.VacuumMasterId).Code;
                                }
                                else
                                {
                                    vacuumCode = vacuumCode + ":" + (db.VacuumMasters.FirstOrDefault(x => x.VacuumMasterId == item.VacuumMasterId).Code);
                                }
                            }

                            var lacquercode = "";
                            foreach (var item in saleOrder.SaleOrderProduction.Lacquer)
                            {
                                var lacqdata = db.LacquerRawMaterialTables.Where(x => x.LacquerMasterId == item.LacquerMasterId).ToList();
                                foreach (var itemraw in lacqdata)
                                {
                                    SaleOrderProductionLacquerRawMaterialTable spt = new SaleOrderProductionLacquerRawMaterialTable();
                                    spt.SaleOrderProductionId = SaleOrderProductionRecord.SaleOrderProductionId;
                                    spt.LacquerMasterId = item.LacquerMasterId;
                                    //spt.ProductId = itemraw.ProductId;
                                    spt.Quantity = itemraw.Quantity;
                                    spt.Unit = itemraw.Unit;
                                    spt.Price = itemraw.Price;
                                    spt.Rank = item.Rank;
                                    db.SaleOrderProductionLacquerRawMaterialTables.Add(spt);
                                }

                                var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == saleOrder.SaleOrderId).FirstOrDefault();
                                if (ppt != null)
                                {
                                    ppt.ProcessMasterId = item.LacquerMasterId;
                                }

                                if (string.IsNullOrEmpty(lacquercode))
                                {
                                    lacquercode = db.LacquerMasters.FirstOrDefault(x => x.LacquerMasterId == item.LacquerMasterId).Code;
                                }
                                else
                                {
                                    lacquercode = lacquercode + ":" + (db.LacquerMasters.FirstOrDefault(x => x.LacquerMasterId == item.LacquerMasterId).Code);
                                }
                            }
                            db.SaveChanges();

                            var graincode = SanitizeCodeComponent(db.GrainMasters.FirstOrDefault(x => x.GrainId == SaleOrderProductionRecord.GrainId)?.GrainCode);
                            var colorcode = SanitizeCodeComponent(db.ColorMasters.FirstOrDefault(x => x.ColorId == SaleOrderProductionRecord.ColorId)?.ColorCode);
                            var thicknesscode = SanitizeCodeComponent(db.ThicknessMasters.FirstOrDefault(x => x.ThicknessId == SaleOrderProductionRecord.Thick)?.Code);

                            // Build initial salecode components
                            var initialComponents = new List<string>();
                            if (!string.IsNullOrWhiteSpace(saleOrder.SaleFormulationCode))
                                initialComponents.Add(SanitizeCodeComponent(saleOrder.SaleFormulationCode));
                            if (!string.IsNullOrWhiteSpace(graincode))
                                initialComponents.Add(graincode);
                            if (!string.IsNullOrWhiteSpace(colorcode))
                                initialComponents.Add(colorcode);

                            var salecode = string.Join("/", initialComponents);
                            var finishcode = "";
                            var sequence = db.SaleOrderPostProcessOrderTables.Where(x => x.SaleOrderId == saleOrder.SaleOrderId && x.Removed != true).OrderBy(x => x.Rank).ToList();

                            for (int i = 0; i < sequence.Count(); i++)
                            {
                                if (sequence[i].PostProcessName == "Print" && !string.IsNullOrEmpty(printCode))
                                {
                                    var sanitizedPrintCode = SanitizeCodeComponent(printCode);
                                    salecode = salecode + "/" + sanitizedPrintCode;
                                    if (i == 0)
                                    {
                                        finishcode = sanitizedPrintCode;
                                    }
                                    else
                                    {
                                        finishcode += "/" + sanitizedPrintCode;
                                    }
                                }
                                if (sequence[i].PostProcessName == "Embossing" && !string.IsNullOrEmpty(EmbossingCode))
                                {
                                    var sanitizedEmbossingCode = SanitizeCodeComponent(EmbossingCode);
                                    salecode = salecode + "/" + sanitizedEmbossingCode;
                                    if (i == 0)
                                    {
                                        finishcode = sanitizedEmbossingCode;
                                    }
                                    else
                                    {
                                        finishcode += "/" + sanitizedEmbossingCode;
                                    }
                                }
                                if (sequence[i].PostProcessName == "Lacquar" && !string.IsNullOrEmpty(lacquercode))
                                {
                                    var sanitizedLacquerCode = SanitizeCodeComponent(lacquercode);
                                    salecode = salecode + "/" + sanitizedLacquerCode;
                                    if (i == 0)
                                    {
                                        finishcode = sanitizedLacquerCode;
                                    }
                                    else
                                    {
                                        finishcode += "/" + sanitizedLacquerCode;
                                    }
                                }
                                if (sequence[i].PostProcessName == "Vaccum" && !string.IsNullOrEmpty(vacuumCode))
                                {
                                    var sanitizedVacuumCode = SanitizeCodeComponent(vacuumCode);
                                    salecode = salecode + "/" + sanitizedVacuumCode;
                                    if (i == 0)
                                    {
                                        finishcode = sanitizedVacuumCode;
                                    }
                                    else
                                    {
                                        finishcode += "/" + sanitizedVacuumCode;
                                    }
                                }
                                if (sequence[i].PostProcessName == "Tumbling" && !string.IsNullOrEmpty(tumblingCode))
                                {
                                    var sanitizedTumblingCode = SanitizeCodeComponent(tumblingCode);
                                    salecode = salecode + "/" + sanitizedTumblingCode;
                                    if (i == 0)
                                    {
                                        finishcode = sanitizedTumblingCode;
                                    }
                                    else
                                    {
                                        finishcode += "/" + sanitizedTumblingCode;
                                    }
                                }
                            }
                            // Add thickness code only if it's not empty
                            if (!string.IsNullOrWhiteSpace(thicknesscode))
                                salecode = salecode + "/" + SanitizeCodeComponent(thicknesscode);

                            objSot.SaleOrderCode = salecode;
                            objSot.FinishCode = string.IsNullOrEmpty(finishcode) == true ? "DIRECT" : finishcode;

                            db.SaveChanges();
                            transaction.Commit();

                            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                        }
                        else
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Order Status Out of Range.");
                        }
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();

                        throw;
                    }
                }
            }
        }

        public ApiFunctionResponseVm UpdateSaleOrder(SaleOrderTableVm saleorder)
        {
            using var db = new Models.pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                SaleOrderTable sot = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorder.SaleOrderId);
                if (sot.Status == (int)ESalesOrderStatus.NotYet || sot.Status == (int)ESalesOrderStatus.WorkPlan || sot.Status == (int)ESalesOrderStatus.Inspection)
                {
                    var sofc = db.SaleFormulationCodeMasters.FirstOrDefault(x => x.SaleFormulationCode == saleorder.SaleFormulationCode && x.Disabled != true);
                    //sot.SaleOrderNumber = saleorder.SaleOrderNumber;
                    sot.SaleOrderDate = saleorder.SaleOrderDate;
                    sot.CustomerId = saleorder.CustomerId;
                    sot.SaleOrderType = saleorder.SaleOrderType;
                    sot.DeliveryDate = saleorder.DeliveryDate;
                    sot.Remarks = saleorder.Remarks;
                    sot.CostingAdded = saleorder.CostingAdded;
                    sot.Bornumber = saleorder.BORNumber;
                    sot.SaleOrderStatus = PmsCommon.PMSSaleOrderStatus.ApprovalPending;
                    sot.CategoryId = saleorder.CategoryId;
                    sot.UpdatedBy = GlobalData.loggedInUser;
                    sot.UpdatedDate = System.DateTime.Now;
                    sot.ProformaInvoiceId = saleorder.ProformaInvoiceId;
                    sot.WorkPlanStatus = false;
                    sot.Status = (int)ESalesOrderStatus.NotYet;
                    sot.SaleFormulationCodeId = sofc.SaleFormulationCodeId;
                    //db.SaleOrderTables.Add(sot);
                    db.SaveChanges();
                    //sot.SaleOrderNumber = System.DateTime.Now.Year + "-" + System.DateTime.Now.Month + "/" + sot.SaleOrderId;
                    db.SaveChanges();


                    SaleOrderProductionTable pt = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == saleorder.SaleOrderId);
                    pt.SaleOrderId = sot.SaleOrderId;
                    pt.ProductId = saleorder.SaleOrderProduction.ProductId;
                    pt.ManufacturingProductName = saleorder.SaleOrderProduction.ManufacturingProductName;
                    pt.ManufacturingProductCode = saleorder.SaleOrderProduction.ManufacturingProductCode;
                    pt.Lot = saleorder.SaleOrderProduction.Lot;
                    pt.Batch = saleorder.SaleOrderProduction.Batch;
                    pt.OrderQuantity = saleorder.SaleOrderProduction.OrderQuantity;
                    //pt.ManufacturingQuantity = saleorder.SaleOrderProduction.ManufacturingQuantity;
                    pt.Barcode = saleorder.SaleOrderProduction.Barcode;
                    pt.ColorId = saleorder.SaleOrderProduction.ColorId;
                    pt.ColorPrice = saleorder.SaleOrderProduction.ColorPrice;
                    pt.Thick = saleorder.SaleOrderProduction.Thick;
                    pt.ThickPrice = saleorder.SaleOrderProduction.ThickPrice;
                    pt.Width = saleorder.SaleOrderProduction.Width;
                    pt.WidthPrice = saleorder.SaleOrderProduction.WidthPrice;
                    pt.ProductionStatus = PmsCommon.PMSStatus.Planning;
                    pt.SlippagePercent = saleorder.SaleOrderProduction.SlippagePercent;
                    pt.TotalCost = saleorder.SaleOrderProduction.TotalCost;
                    pt.CostingStatus = PmsCommon.PMSStatus.Pending;
                    pt.AddedBy = GlobalData.loggedInUser;
                    pt.AddedDate = System.DateTime.Now;
                    pt.GrainId = saleorder.SaleOrderProduction.GrainId;
                    pt.GrainPrice = saleorder.SaleOrderProduction.GrainPrice;
                    pt.MixingFormulationCode = saleorder.SaleOrderProduction.MixingFormulationCode;
                    pt.SalePrice = saleorder.SaleOrderProduction.SalePrice;
                    pt.PreSkinGsm = saleorder.SaleOrderProduction.PreSkinGsm;
                    pt.SkinGsm = saleorder.SaleOrderProduction.SkinGsm;
                    pt.FoamGsm = saleorder.SaleOrderProduction.FoamGsm;
                    pt.AdhesiveGsm = saleorder.SaleOrderProduction.AdhesiveGsm;
                    pt.FabricGsm = saleorder.SaleOrderProduction.FabricGsm;
                    pt.TotalCost = saleorder.SaleOrderProduction.TotalCost;
                    pt.FabricColorId = saleorder.SaleOrderProduction.FabricColorId;
                    pt.FabricProductId = saleorder.SaleOrderProduction.FabricProductId;
                    //db.SaleOrderProductionTables.Add(pt);
                    db.SaveChanges();


                    if (sofc.IsOrderLinkingRequired.Value)
                    {
                        var checkExistingLink = db.LinkedSaleOrderTables.Where(t => t.LinkedSaleOrder == sot.SaleOrderId).ToList();
                        if (checkExistingLink.Count > 0)
                        {
                            db.LinkedSaleOrderTables.RemoveRange(checkExistingLink);
                            db.SaveChanges();
                        }

                        db.LinkedSaleOrderTables.Add(new LinkedSaleOrderTable
                        {
                            ParentSaleOrder = saleorder.OrderIdForLinking,
                            LinkedSaleOrder = sot.SaleOrderId,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now,
                            IsLinkingComplete = false
                        });
                        db.SaveChanges();
                    }

                    var spm = db.SaleOrderPostProcessOrderTables.Where(x => x.SaleOrderId == sot.SaleOrderId && x.Removed != true).ToList();
                    db.SaleOrderPostProcessOrderTables.RemoveRange(spm);
                    db.SaveChanges();

                    foreach (var sopp in saleorder.SaleOrderPostProcessOrder)
                    {
                        SaleOrderPostProcessOrderTable ordertb = new SaleOrderPostProcessOrderTable()
                        {
                            SaleOrderId = sot.SaleOrderId,
                            Rank = sopp.Rank.Value,
                            PostProcessName = sopp.PostProcessName
                        };
                        db.SaleOrderPostProcessOrderTables.Add(ordertb);
                        db.SaveChanges();
                    }

                    var em = db.SaleOrderProductionEmbossingMasters.Where(x => x.SaleOrderProductionId == pt.SaleOrderProductionId && x.Removed != true).ToList();
                    db.SaleOrderProductionEmbossingMasters.RemoveRange(em);
                    db.SaveChanges();

                    foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionEmbossing)
                    {
                        SaleOrderProductionEmbossingMaster spt = new SaleOrderProductionEmbossingMaster();
                        spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                        spt.EmbossingMasterId = item.EmbossingMasterId;
                        spt.Price = item.Price;
                        spt.Quantity = item.Quantity;
                        spt.Total = item.Total;
                        spt.Rank = item.Rank;
                        db.SaleOrderProductionEmbossingMasters.Add(spt);

                        var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                        if (ppt != null)
                        {
                            ppt.ProcessMasterId = item.EmbossingMasterId;
                            db.SaveChanges();
                        }

                    }
                    var EmbossingQry = (from e in db.EmbossingMasters
                                        join i in db.SaleOrderProductionEmbossingMasters on e.EmbossingMasterId equals i.EmbossingMasterId
                                        where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                        select new KeyValue
                                        {
                                            Key = i.Rank,
                                            Value = e.Code
                                        }).Distinct().ToList();

                    var print = db.SaleOrderProductionPrintMasters.Where(x => x.SaleOrderProductionId == pt.SaleOrderProductionId && x.Removed != true).ToList();
                    db.SaleOrderProductionPrintMasters.RemoveRange(print);
                    db.SaveChanges();

                    foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionPrint)
                    {
                        SaleOrderProductionPrintMaster spt = new SaleOrderProductionPrintMaster();
                        spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                        spt.PrintMasterId = item.PrintMasterId;
                        spt.Price = item.Price;
                        spt.Quantity = item.Quantity;
                        spt.Total = item.Total;
                        spt.Rank = item.Rank;
                        db.SaleOrderProductionPrintMasters.Add(spt);

                        var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                        if (ppt != null)
                        {
                            ppt.ProcessMasterId = item.PrintMasterId;
                            db.SaveChanges();
                        }
                    }
                    var PrintQry = (from e in db.PrintMasters
                                    join i in db.SaleOrderProductionPrintMasters on e.PrintMasterId equals i.PrintMasterId
                                    where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                    select new KeyValue
                                    {
                                        Key = i.Rank,
                                        Value = e.Code
                                    }).Distinct().ToList();

                    var tum = db.SaleOrderProductionTumblingMasters.Where(x => x.SaleOrderProductionId == pt.SaleOrderProductionId && x.Removed != true).ToList();
                    db.SaleOrderProductionTumblingMasters.RemoveRange(tum);
                    db.SaveChanges();

                    foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionTumbling)
                    {
                        SaleOrderProductionTumblingMaster spt = new SaleOrderProductionTumblingMaster();
                        spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                        spt.TumblingMasterId = item.TumblingMasterId;
                        spt.Price = item.Price;
                        spt.Quantity = item.Quantity;
                        spt.Total = item.Total;
                        spt.Rank = item.Rank;
                        db.SaleOrderProductionTumblingMasters.Add(spt);

                        var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                        if (ppt != null)
                        {
                            ppt.ProcessMasterId = item.TumblingMasterId;
                            db.SaveChanges();
                        }
                    }
                    var TumblingQry = (from e in db.TumblingMasters
                                       join i in db.SaleOrderProductionTumblingMasters on e.TumblingMasterId equals i.TumblingMasterId
                                       where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                       select new KeyValue
                                       {
                                           Key = i.Rank,
                                           Value = e.Code
                                       }).Distinct().ToList();

                    var vac = db.SaleOrderProductionVacuumMasters.Where(x => x.SaleOrderProductionId == pt.SaleOrderProductionId && x.Removed != true).ToList();
                    db.SaleOrderProductionVacuumMasters.RemoveRange(vac);
                    db.SaveChanges();
                    foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionVacuum)
                    {
                        SaleOrderProductionVacuumMaster spt = new SaleOrderProductionVacuumMaster();
                        spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                        spt.VacuumMasterId = item.VacuumMasterId;
                        spt.Price = item.Price;
                        spt.Quantity = item.Quantity;
                        spt.Total = item.Total;
                        spt.Rank = item.Rank;
                        db.SaleOrderProductionVacuumMasters.Add(spt);

                        var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                        if (ppt != null)
                        {
                            ppt.ProcessMasterId = item.VacuumMasterId;
                            db.SaveChanges();
                        }
                    }
                    var VacuumQry = (from e in db.VacuumMasters
                                     join i in db.SaleOrderProductionVacuumMasters on e.VacuumMasterId equals i.VacuumMasterId
                                     where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                     select new KeyValue
                                     {
                                         Key = i.Rank,
                                         Value = e.Code
                                     }).Distinct().ToList();

                    var mixing = db.SaleOrderProductionMixingTables.Where(x => x.SaleOrderProductionId == pt.SaleOrderProductionId).ToList();
                    foreach (var item in mixing)
                    {
                        var mrm = db.SaleOrderProductionMixingRawMaterialTables.Where(x => x.SaleOrderProductionMixingId == item.SaleOrderProductionMixingId).ToList();
                        db.SaleOrderProductionMixingRawMaterialTables.RemoveRange(mrm);
                        db.SaleOrderProductionMixingTables.Remove(item);
                    }
                    db.SaveChanges();

                    foreach (var item in saleorder.SaleOrderProduction.Mixing)
                    {
                        SaleOrderProductionMixingTable mm = new SaleOrderProductionMixingTable();
                        mm.ProductionMixingName = item.ProductionMixingName;
                        mm.SaleOrderProductionId = pt.SaleOrderProductionId;
                        mm.Wastage = item.Wastage;
                        mm.WastageType = item.WastageType;
                        mm.Total = item.Total;
                        mm.WeightGsm = item.WeightGsm;
                        mm.AddedBy = GlobalData.loggedInUser;
                        mm.AddedDate = System.DateTime.Now;
                        db.SaleOrderProductionMixingTables.Add(mm);
                        db.SaveChanges();
                        foreach (var itm in item.MixingRawMaterial)
                        {
                            SaleOrderProductionMixingRawMaterialTable spt = new SaleOrderProductionMixingRawMaterialTable();
                            spt.SaleOrderProductionMixingId = mm.SaleOrderProductionMixingId;
                            spt.ProductId = itm.ProductId;
                            spt.Quantity = itm.Quantity;
                            spt.Unit = itm.Unit;
                            spt.Price = itm.Price;
                            db.SaleOrderProductionMixingRawMaterialTables.Add(spt);
                        }
                        db.SaveChanges();
                    }

                    var srm = db.SaleOrderProductionRawMaterialTables.Where(x => x.SaleOrderProductionId == pt.SaleOrderProductionId).ToList();
                    db.SaleOrderProductionRawMaterialTables.RemoveRange(srm);
                    db.SaveChanges();
                    foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionRawMaterial)
                    {
                        bool flag = db.StockProductTables.Any(x => x.ProductId == item.ProductId);
                        SaleOrderProductionRawMaterialTable spt = new SaleOrderProductionRawMaterialTable();
                        spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                        spt.ProductId = item.ProductId;
                        spt.Unit = item.Unit;
                        spt.Quantity = item.Quantity;
                        spt.PerUnitCost = flag ? db.StockProductTables.Where(x => x.ProductId == item.ProductId).OrderByDescending(x => x.StockProductId).ToList()[0].PricePerUnit : 0;
                        spt.TotalCost = item.Quantity * spt.PerUnitCost;
                        db.SaleOrderProductionRawMaterialTables.Add(spt);
                    }

                    var smt = db.SaleOrderProductionElementTables.Where(x => x.SaleOrderProductionId == pt.SaleOrderProductionId).ToList();
                    db.SaleOrderProductionElementTables.RemoveRange(smt);
                    db.SaveChanges();
                    foreach (var item in saleorder.SaleOrderProduction.SaleOrderProductionElement)
                    {
                        SaleOrderProductionElementTable spt = new SaleOrderProductionElementTable();
                        spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                        spt.ElementId = item.ElementId;
                        spt.PerUnitCost = item.PerUnitCost;
                        spt.TotalCost = item.TotalCost;
                        spt.Value = item.Value;
                        db.SaleOrderProductionElementTables.Add(spt);
                    }

                    var lmt = db.SaleOrderProductionLacquerRawMaterialTables.Where(x => x.SaleOrderProductionId == pt.SaleOrderProductionId && x.Removed != true).ToList();
                    db.SaleOrderProductionLacquerRawMaterialTables.RemoveRange(lmt);
                    db.SaveChanges();
                    foreach (var item in saleorder.SaleOrderProduction.Lacquer)
                    {
                        SaleOrderProductionLacquerRawMaterialTable spt = new SaleOrderProductionLacquerRawMaterialTable();
                        spt.SaleOrderProductionId = pt.SaleOrderProductionId;
                        spt.LacquerMasterId = item.LacquerMasterId;
                        spt.Rank = item.Rank;
                        db.SaleOrderProductionLacquerRawMaterialTables.Add(spt);

                        var ppt = db.SaleOrderPostProcessOrderTables.Where(x => x.Rank == item.Rank && x.SaleOrderId == sot.SaleOrderId).FirstOrDefault();
                        if (ppt != null)
                        {
                            ppt.ProcessMasterId = item.LacquerMasterId;
                            db.SaveChanges();
                        }
                    }
                    var LacquerQry = (from e in db.LacquerMasters
                                      join i in db.SaleOrderProductionLacquerRawMaterialTables on e.LacquerMasterId equals i.LacquerMasterId
                                      where e.Code != null && i.SaleOrderProductionId == pt.SaleOrderProductionId
                                      select new KeyValue
                                      {
                                          Key = i.Rank,
                                          Value = e.Code
                                      }).Distinct().ToList();
                    db.SaveChanges();
                    var graincode = SanitizeCodeComponent(db.GrainMasters.FirstOrDefault(x => x.GrainId == saleorder.SaleOrderProduction.GrainId)?.GrainCode);
                    var colorcode = SanitizeCodeComponent(db.ColorMasters.FirstOrDefault(x => x.ColorId == saleorder.SaleOrderProduction.ColorId)?.ColorCode);
                    var thicknesscode = SanitizeCodeComponent(db.ThicknessMasters.FirstOrDefault(x => x.ThicknessId == saleorder.SaleOrderProduction.Thick)?.Code);

                    // Build components list with validation
                    var saleCodeComponents = new List<string>();

                    // Add formulation code
                    if (!string.IsNullOrWhiteSpace(saleorder.SaleFormulationCode))
                        saleCodeComponents.Add(SanitizeCodeComponent(saleorder.SaleFormulationCode));

                    // Add grain code
                    if (!string.IsNullOrWhiteSpace(graincode))
                        saleCodeComponents.Add(graincode);

                    // Add color code
                    if (!string.IsNullOrWhiteSpace(colorcode))
                        saleCodeComponents.Add(colorcode);

                    // Get process codes
                    var RankCodes = new List<KeyValue>();
                    if (PrintQry != null)
                        RankCodes.AddRange(PrintQry);
                    if (EmbossingQry != null)
                        RankCodes.AddRange(EmbossingQry);
                    if (TumblingQry != null)
                        RankCodes.AddRange(TumblingQry);
                    if (VacuumQry != null)
                        RankCodes.AddRange(VacuumQry);
                    if (LacquerQry != null)
                        RankCodes.AddRange(LacquerQry);

                    var newsaleCode = GetSaleOrderProcessCode(RankCodes);
                    var finishcode = newsaleCode;

                    // Add process codes only if they exist
                    if (!string.IsNullOrWhiteSpace(newsaleCode))
                        saleCodeComponents.Add(newsaleCode);

                    // Add thickness code
                    if (!string.IsNullOrWhiteSpace(thicknesscode))
                        saleCodeComponents.Add(thicknesscode);

                    // Join all components with single slash
                    var salecode = string.Join("/", saleCodeComponents);
                    sot.SaleOrderCode = salecode;
                    sot.FinishCode = string.IsNullOrEmpty(finishcode) == true ? "DIRECT" : finishcode;
                    db.SaveChanges();

                    var wpo = db.WorkPlanOrders.Where(x => x.OrderId == sot.SaleOrderId).ToList();
                    db.WorkPlanOrders.RemoveRange(wpo);

                    if (wpo.Count() > 0)
                    {
                        var wpm = db.WorkPlanOrders.Where(x => x.WorkplanId == wpo[0].WorkplanId).ToList();

                        if (wpm.Count() == 1)
                        {
                            var wsocount = db.WorkPlanMasters.Where(x => x.WorkPlanId == wpo[0].WorkplanId).ToList();
                            db.WorkPlanMasters.RemoveRange(wsocount);
                        }
                    }
                    var stl = db.SaleOrderTimelineTables.Where(x => x.SaleOrderId == sot.SaleOrderId && x.Status == (int)ESalesOrderStatus.WorkPlan).ToList();
                    db.SaleOrderTimelineTables.RemoveRange(stl);
                    db.SaveChanges();
                    transaction.Commit();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Unable to delete the Saleorder as it is in under process");
                }
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw;
            }
        }
        public ApiFunctionResponseVm UpdateSaleOrderGSM(SaleOrderProductionTableVm request)
        {
            using var db = new Models.pmsdbContext();
            SaleOrderTable sot = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == request.SaleOrderId);
            if (sot.Status == (int)ESalesOrderStatus.NotYet || sot.Status == (int)ESalesOrderStatus.WorkPlan)
            {
                SaleOrderProductionTable pt = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == request.SaleOrderId);

                pt.PreSkinGsm = request.PreSkinGsm;
                pt.SkinGsm = request.SkinGsm;
                pt.FoamGsm = request.FoamGsm;
                pt.AdhesiveGsm = request.AdhesiveGsm;
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "GSM Updated Successfully");
            }
            else
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "GSM cannot be updated at this stage");
            }

        }

        public ApiFunctionResponseVm DeleteSaleOrder(long soid)
        {

            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == soid);
                        if (so.Status == (int)ESalesOrderStatus.NotYet || so.Status == (int)ESalesOrderStatus.WorkPlan || so.Status == (int)ESalesOrderStatus.Inspection)
                        {
                            var saleorderProduction = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == soid);

                            var saleorderId = so.SaleOrderId;
                            var saleorderProductionId = saleorderProduction.SaleOrderProductionId;

                            var lso = db.LinkedSaleOrderTables.FirstOrDefault(x => x.LinkedSaleOrder == saleorderId);
                            if (lso != null)
                            {
                                db.LinkedSaleOrderTables.Remove(lso);
                            }

                            var em = db.SaleOrderProductionEmbossingMasters.Where(x => x.SaleOrderProductionId == saleorderProductionId).ToList();
                            db.SaleOrderProductionEmbossingMasters.RemoveRange(em);

                            var lrm = db.SaleOrderProductionLacquerRawMaterialTables.Where(x => x.SaleOrderProductionId == saleorderProductionId).ToList();
                            db.SaleOrderProductionLacquerRawMaterialTables.RemoveRange(lrm);

                            var pe = db.SaleOrderProductionElementTables.Where(x => x.SaleOrderProductionId == saleorderProductionId).ToList();
                            db.SaleOrderProductionElementTables.RemoveRange(pe);

                            var prm = db.SaleOrderProductionRawMaterialTables.Where(x => x.SaleOrderProductionId == saleorderProductionId).ToList();
                            db.SaleOrderProductionRawMaterialTables.RemoveRange(prm);

                            var pvm = db.SaleOrderProductionVacuumMasters.Where(x => x.SaleOrderProductionId == saleorderProductionId).ToList();
                            db.SaleOrderProductionVacuumMasters.RemoveRange(pvm);

                            var tm = db.SaleOrderProductionTumblingMasters.Where(x => x.SaleOrderProductionId == saleorderProductionId).ToList();
                            db.SaleOrderProductionTumblingMasters.RemoveRange(tm);

                            var print = db.SaleOrderProductionPrintMasters.Where(x => x.SaleOrderProductionId == saleorderProductionId).ToList();
                            db.SaleOrderProductionPrintMasters.RemoveRange(print);

                            var mixing = db.SaleOrderProductionMixingTables.Where(x => x.SaleOrderProductionId == saleorderProductionId).ToList();
                            foreach (var item in mixing)
                            {
                                var mrm = db.SaleOrderProductionMixingRawMaterialTables.Where(x => x.SaleOrderProductionMixingId == item.SaleOrderProductionMixingId).ToList();
                                db.SaleOrderProductionMixingRawMaterialTables.RemoveRange(mrm);
                                db.SaleOrderProductionMixingTables.Remove(item);
                            }

                            var spm = db.SaleOrderPostProcessOrderTables.Where(x => x.SaleOrderId == saleorderId).ToList();
                            db.SaleOrderPostProcessOrderTables.RemoveRange(spm);

                            var wpo = db.WorkPlanOrders.Where(x => x.OrderId == so.SaleOrderId).ToList();
                            db.WorkPlanOrders.RemoveRange(wpo);

                            if (wpo.Count() > 0)
                            {
                                var wpm = db.WorkPlanOrders.Where(x => x.WorkplanId == wpo[0].WorkplanId).ToList();

                                if (wpm.Count() == 1)
                                {
                                    var wsocount = db.WorkPlanMasters.Where(x => x.WorkPlanId == wpo[0].WorkplanId).ToList();
                                    db.WorkPlanMasters.RemoveRange(wsocount);
                                }
                            }

                            var stl = db.SaleOrderTimelineTables.Where(x => x.SaleOrderId == saleorderId).ToList();
                            db.SaleOrderTimelineTables.RemoveRange(stl);

                            db.SaleOrderProductionTables.Remove(saleorderProduction);

                            db.SaleOrderTables.Remove(so);

                            db.SaveChanges();
                            transaction.Commit();

                            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                        }
                        else
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Unable to delete the Saleorder as it is in under process");
                        }
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                    }
                }
            }
        }

        public SaleOrderTableVm GetSaleOrderDataById(long pid)
        {
            SaleOrderTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                var saleprodid = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == pid).SaleOrderProductionId;
                var insform = (from a in db.InspectionSaleFormulationCodeMasters
                               join la in db.InspectionFormulationCodeMixingTables on a.InspectionSaleFormulationCodeId equals la.InspectionSaleFormulationCodeId
                               join p in db.ProductMasters on a.FabricProductId equals p.ProductId into pd
                               from p in pd.DefaultIfEmpty()
                               where la.SaleOrderId == pid
                               select new InspectionSaleFormulationCodeMasterVm
                               {
                                   FabricProductId = a.FabricProductId,
                                   FabricProductName = p.ProductName,
                                   FabricProductQty = a.FabricProductQty,
                                   FabricProductUnit = p.Unit,
                                   AddedBy = a.AddedBy,
                                   AddedDate = a.AddedDate
                               }).FirstOrDefault();
                var wpo = db.WorkPlanOrders.Where(x => x.OrderId == pid).FirstOrDefault();
                long? wpid = 0;
                string wpnumber = "";
                if (wpo != null)
                    wpid = wpo.WorkplanId;
                if (wpid > 0)
                    wpnumber = db.WorkPlanMasters.Where(y => y.WorkPlanId == wpid).FirstOrDefault().WorkPlanNo;
                var costdata = (from cs in db.ConsumeStockProductMasters
                                join sp in db.StockProductTables on cs.StockProductId equals sp.StockProductId
                                where cs.SaleOrderId == pid
                                select new
                                {
                                    cs.ProductId,
                                    sp.PricePerUnit,
                                    cs.Unit
                                }).ToList();

                res = (from s in db.SaleOrderTables
                       join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                       from pcm in spc.DefaultIfEmpty()
                       join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                       from cust in scu.DefaultIfEmpty()
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join lso in db.LinkedSaleOrderTables on s.SaleOrderId equals lso.ParentSaleOrder into lsod
                       from lso in lsod.DefaultIfEmpty()
                       join prf in db.ProductMasters on a.FabricProductId equals prf.ProductId into prsf
                       from prf in prsf.DefaultIfEmpty()
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join cf in db.ColorMasters on a.FabricColorId equals cf.ColorId into cagrf
                       from cf in cagrf.DefaultIfEmpty()
                       join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                       from g in agr.DefaultIfEmpty()
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId into pra
                       from pr in pra.DefaultIfEmpty()
                       join p in db.ProductCategoryMasters on pr.ProductCategoryId equals p.ProductCategoryId into ps
                       from p in ps.DefaultIfEmpty()
                       join pf in db.ProductFirstSubCategoryMasters on pr.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                       from pf in psf.DefaultIfEmpty()
                       join ext in db.ProductMasterExtensions on a.ProductId equals ext.ProductId into aext
                       from ext in aext.DefaultIfEmpty()
                       join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                       from thm in thmsa.DefaultIfEmpty()
                       join wdm in db.WidthMasters on a.Width equals wdm.WidthId into wdmsa
                       from wdm in wdmsa.DefaultIfEmpty()
                       where a.SaleOrderId == pid
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           SaleOrderCode = s.SaleOrderCode,
                           FinishCode = s.FinishCode,
                           CostingAdded = s.CostingAdded,
                           CustomerId = s.CustomerId,
                           CustomerName = cust.CustomerName,
                           CategoryId = s.CategoryId,
                           Category = pcm.ProductCategory,
                           AddedBy = s.AddedBy,
                           AddedDate = s.AddedDate,
                           DeliveryDate = s.DeliveryDate,
                           Remarks = s.Remarks,
                           BORNumber = s.Bornumber,
                           SaleOrderStatus = s.SaleOrderStatus,
                           WorkPlanStatus = s.WorkPlanStatus,
                           WorkPlanNumber = s.WorkPlanStatus == true ? wpnumber : "Not Created",
                           IsRawMaterialIssued = s.IsRawMaterialIssued,
                           ProformaInvoiceId = s.ProformaInvoiceId,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,
                           IsJumboRequired = s.IsJumboRequired,
                           ProductionCompletionRemarks = s.ProductionCompletionRemarks,
                           SaleFormulationCode = fcm.SaleFormulationCode,
                           SaleFormulationCodeId = fcm.SaleFormulationCodeId,
                           FormulationFabricProductName = insform == null ? prf.ProductName : insform.FabricProductName,
                           FormulationFabricProductId = insform == null ? prf.ProductId : insform.FabricProductId.Value,
                           FormulationFabricProductQty = insform == null ? 0 : insform.FabricProductQty.Value,
                           FormulationFabricProducWidthInMeter = insform == null ? prf.WidthInMeter : insform.FabricWidthInMeter,
                           FormulationFabricProductUnit = insform == null ? prf.Unit : insform.FabricProductUnit,
                           FormulationFabricProductPrice = 0,
                           OrderIdForLinking = lso.LinkedSaleOrder,


                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               ProductId = a.ProductId,
                               ProductName = pr.ProductName,
                               ProductCode = pr.ProductCode,
                               ProductCategoryId = pr.ProductCategoryId,
                               ProductCategory = p.ProductCategory,
                               ProductFirstSubCategoryId = pr.ProductFirstSubCategoryId,
                               ProductFirstSubCategory = pf.ProductFirstSubCategory,
                               ManufacturingProductName = a.ManufacturingProductName,
                               ManufacturingProductCode = a.ManufacturingProductCode,
                               Lot = a.Lot,
                               Batch = a.Batch,
                               OrderQuantity = a.OrderQuantity,
                               LMConstant = a.Lmconstant,
                               ExtraProduction = a.ExtraProduction,
                               ManufacturingQuantity = a.ManufacturingQuantity,
                               Unit = a.Unit,
                               ColorId = a.ColorId,
                               ColorName = c.ColorName,
                               ColorCode = c.ColorCode,
                               FabricColorId = a.FabricColorId,
                               FabricColorName = cf.ColorName,
                               FabricProductId = insform == null ? a.FabricProductId : insform.FabricProductId,
                               FabricProductUnit = insform == null ? prf.Unit : insform.FabricProductUnit,
                               FabricProductName = insform == null ? prf.ProductName : insform.FabricProductName,
                               FabricProductWidthInMeter = insform == null ? prf.WidthInMeter : insform.FabricWidthInMeter,
                               ColorPrice = a.ColorPrice,
                               GrainId = a.GrainId,
                               GrainName = g.GrainName,
                               GrainCode = g.GrainCode,
                               GrainPrice = a.GrainPrice,
                               Thick = a.Thick,
                               ThickPrice = a.ThickPrice,
                               ThicknessValue = thm.ThicknessNumber,
                               Width = a.Width,
                               WidthNumber = wdm.WidthNumber,
                               WidthPrice = a.WidthPrice,
                               Barcode = a.Barcode,
                               ProductionStatus = a.ProductionStatus,
                               CostingStatus = a.CostingStatus,
                               SlippagePercent = a.SlippagePercent,
                               TotalCost = a.TotalCost,
                               AddedBy = a.AddedBy,
                               AddedDate = a.AddedDate,
                               ProcessFormulationCode = a.ProcessFormulationCode,
                               MixingFormulationCode = a.MixingFormulationCode,
                               SalePrice = a.SalePrice,
                               PreSkinGsm = a.PreSkinGsm,
                               SkinGsm = a.SkinGsm,
                               FoamGsm = a.FoamGsm,
                               AdhesiveGsm = a.AdhesiveGsm,
                               FabricGsm = a.FabricGsm,
                               TotalGsm = a.TotalGsm,
                           }
                       }).FirstOrDefault();

                res.SaleOrderPostProcessOrder = (from sopp in db.SaleOrderPostProcessOrderTables
                                                 where sopp.SaleOrderId == pid && sopp.Removed != true
                                                 select new SaleOrderPostProcessOrderTableVm
                                                 {
                                                     SaleOrderId = sopp.SaleOrderId,
                                                     Rank = sopp.Rank,
                                                     PostProcessName = sopp.PostProcessName,
                                                     ProcessMasterId = sopp.ProcessMasterId
                                                 }).OrderBy(x => x.Rank).ToList();

                res.SaleOrderProduction.SaleOrderProductionMiscellaneousRawMaterial = (from op in db.SaleOrderProductionMiscellaneousRawMaterialTables
                                                                                       join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                       where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId
                                                                                       select new SaleOrderProductionMiscellaneousRawMaterialTableVm
                                                                                       {
                                                                                           SaleOrderProductionMiscellaneousRawMaterialId = op.SaleOrderProductionMiscellaneousRawMaterialId,
                                                                                           SaleOrderProductionId = op.SaleOrderProductionId,
                                                                                           ProductId = op.ProductId,
                                                                                           ProductCode = p.ProductCode,
                                                                                           ProductName = p.ProductName,
                                                                                           Unit = op.Unit,
                                                                                           Quantity = op.Quantity,
                                                                                           MaterialCategory = op.MaterialCategory
                                                                                       }).ToList();

                res.SaleOrderProduction.SaleOrderProductionRawMaterial = (from op in db.SaleOrderProductionRawMaterialTables
                                                                          join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                          where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId
                                                                          select new SaleOrderProductionRawMaterialTableVm
                                                                          {
                                                                              SaleOrderProductionRawMaterialId = op.SaleOrderProductionRawMaterialId,
                                                                              SaleOrderProductionId = op.SaleOrderProductionId,
                                                                              ProductId = op.ProductId,
                                                                              ProductCode = p.ProductCode,
                                                                              ProductName = p.ProductName,
                                                                              Unit = op.Unit,
                                                                              Quantity = op.Quantity,
                                                                              AvgGsm = p.AvgGsm,
                                                                              TotalCost = op.TotalCost,
                                                                              PerUnitCost = op.PerUnitCost,
                                                                          }).ToList();
                res.SaleOrderProduction.SaleOrderProductionElement = (from op in db.SaleOrderProductionElementTables
                                                                      join p in db.ElementMasters on op.ElementId equals p.ElementId
                                                                      where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId
                                                                      select new SaleOrderProductionElementTableVm
                                                                      {
                                                                          SaleOrderProductionElementId = op.SaleOrderProductionElementId,
                                                                          SaleOrderProductionId = op.SaleOrderProductionId,
                                                                          ElementId = op.ElementId,
                                                                          ElementName = p.ElementName,
                                                                          Value = op.Value,
                                                                          TotalCost = op.TotalCost,
                                                                          PerUnitCost = op.PerUnitCost,
                                                                      }).ToList();
                res.SaleOrderProduction.SaleOrderProductionEmbossing = (from op in db.SaleOrderProductionEmbossingMasters
                                                                        join p in db.EmbossingMasters on op.EmbossingMasterId equals p.EmbossingMasterId
                                                                        where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId && op.Removed != true
                                                                        select new SaleOrderProductionEmbossingMasterVm
                                                                        {
                                                                            SaleOrderProductionEmbossingMasterId = op.SaleOrderProductionEmbossingMasterId,
                                                                            SaleOrderProductionId = op.SaleOrderProductionId,
                                                                            EmbossingMasterId = op.EmbossingMasterId,
                                                                            Name = p.Name,
                                                                            ImageName = p.ImageName,
                                                                            Code = p.Code,
                                                                            Description = p.Description,
                                                                            Price = op.Price,
                                                                            Quantity = op.Quantity,
                                                                            Total = op.Total,
                                                                            Rank = op.Rank,
                                                                        }).ToList();
                res.SaleOrderProduction.SaleOrderProductionPrint = (from op in db.SaleOrderProductionPrintMasters
                                                                    join p in db.PrintMasters on op.PrintMasterId equals p.PrintMasterId
                                                                    where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId && op.Removed != true
                                                                    select new SaleOrderProductionPrintMasterVm
                                                                    {
                                                                        SaleOrderProductionPrintMasterId = op.SaleOrderProductionPrintMasterId,
                                                                        SaleOrderProductionId = op.SaleOrderProductionId,
                                                                        PrintMasterId = op.PrintMasterId,
                                                                        Name = p.Name,
                                                                        ImageName = p.ImageName,
                                                                        Code = p.Code,
                                                                        Description = p.Description,
                                                                        Price = op.Price,
                                                                        Quantity = op.Quantity,
                                                                        Total = op.Total,
                                                                        Rank = op.Rank,
                                                                    }).ToList();
                res.SaleOrderProduction.SaleOrderProductionTumbling = (from op in db.SaleOrderProductionTumblingMasters
                                                                       join p in db.TumblingMasters on op.TumblingMasterId equals p.TumblingMasterId
                                                                       where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId && op.Removed != true
                                                                       select new SaleOrderProductionTumblingMasterVm
                                                                       {
                                                                           SaleOrderProductionTumblingMasterId = op.SaleOrderProductionTumblingMasterId,
                                                                           SaleOrderProductionId = op.SaleOrderProductionId,
                                                                           TumblingMasterId = op.TumblingMasterId,
                                                                           Name = p.Name,
                                                                           Code = p.Code,
                                                                           Description = p.Description,
                                                                           Price = op.Price,
                                                                           Quantity = op.Quantity,
                                                                           Total = op.Total,
                                                                           Rank = op.Rank,
                                                                       }).ToList();
                res.SaleOrderProduction.SaleOrderProductionVacuum = (from op in db.SaleOrderProductionVacuumMasters
                                                                     join p in db.VacuumMasters on op.VacuumMasterId equals p.VacuumMasterId
                                                                     where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId && op.Removed != true
                                                                     select new SaleOrderProductionVacuumMasterVm
                                                                     {
                                                                         SaleOrderProductionVacuumMasterId = op.SaleOrderProductionVacuumMasterId,
                                                                         SaleOrderProductionId = op.SaleOrderProductionId,
                                                                         VacuumMasterId = op.VacuumMasterId,
                                                                         Name = p.Name,
                                                                         Code = p.Code,
                                                                         Description = p.Description,
                                                                         Price = op.Price,
                                                                         Quantity = op.Quantity,
                                                                         Total = op.Total,
                                                                         Rank = op.Rank,
                                                                     }).ToList();

                res.SaleOrderProduction.Lacquer = (from am in db.LacquerMasters
                                                   join lrm in db.SaleOrderProductionLacquerRawMaterialTables on am.LacquerMasterId equals lrm.LacquerMasterId
                                                   where lrm.SaleOrderProductionId == saleprodid && lrm.Removed != true
                                                   select new LacquerMasterVm
                                                   {
                                                       LacquerMasterId = am.LacquerMasterId,
                                                       Name = am.Name,
                                                       Code = am.Code,
                                                       Description = am.Description,
                                                       AddedBy = am.AddedBy,
                                                       AddedDate = am.AddedDate,
                                                       Rank = lrm.Rank,
                                                       LacquerRawMaterial = (from op in db.LacquerRawMaterialTables
                                                                             join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                             where op.LacquerMasterId == am.LacquerMasterId
                                                                             select new LacquerRawMaterialTableVm
                                                                             {
                                                                                 LacquerRawMaterialId = op.LacquerRawMaterialId,
                                                                                 LacquerMasterId = op.LacquerMasterId,
                                                                                 ProductId = op.ProductId,
                                                                                 ProductName = p.ProductName,
                                                                                 ProductCode = p.ProductCode,
                                                                                 Quantity = lrm.Quantity,
                                                                                 Unit = op.Unit,
                                                                                 Price = 0
                                                                             }).ToList()
                                                   }).OrderByDescending(x => x.LacquerMasterId).ToList();

                res.SaleOrderProduction.SaleOrderPostProcessPrint = db.SaleOrderPostProcessPrintTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(a => new SaleOrderPostProcessPrintTableVm
                {
                    SaleOrderPostProcessPrintId = a.SaleOrderPostProcessPrintId,
                    SaleOrderId = a.SaleOrderId,
                    ReceivedQuantity = a.ReceivedQuantity,
                    PrintCompletedQuantity = a.PrintCompletedQuantity,
                    PrintMeasurementUnit = a.PrintMeasurementUnit,
                    Remark = a.Remark,
                    PrintRack = a.PrintRack,
                    PrintWastageQuantity = a.PrintWastageQuantity,
                    PrintStatus = a.PrintStatus,
                    AddedBy = a.AddedBy,
                    AddedDate = a.AddedDate,
                }).FirstOrDefault();
                res.SaleOrderProduction.SaleOrderPostProcessEmbossing = db.SaleOrderPostProcessEmbossingTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(a => new SaleOrderPostProcessEmbossingTableVm
                {
                    SaleOrderPostProcessEmbossingId = a.SaleOrderPostProcessEmbossingId,
                    SaleOrderId = a.SaleOrderId,
                    ReceivedQuantity = a.ReceivedQuantity,
                    EmbossingCompletedQuantity = a.EmbossingCompletedQuantity,
                    EmbossingMeasurementUnit = a.EmbossingMeasurementUnit,
                    Remark = a.Remark,
                    EmbossingRack = a.EmbossingRack,
                    EmbossingWastageQuantity = a.EmbossingWastageQuantity,
                    EmbossingStatus = a.EmbossingStatus,
                    AddedBy = a.AddedBy,
                    AddedDate = a.AddedDate,
                }).FirstOrDefault();
                res.SaleOrderProduction.SaleOrderPostProcessVacuum = db.SaleOrderPostProcessVacuumTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(slpv => new SaleOrderPostProcessVacuumTableVm
                {
                    SaleOrderPostProcessVacuumId = slpv.SaleOrderPostProcessVacuumId,
                    SaleOrderId = slpv.SaleOrderId,
                    ReceivedQuantity = slpv.ReceivedQuantity,
                    VacuumCompletedQuantity = slpv.VacuumCompletedQuantity,
                    VacuumMeasurementUnit = slpv.VacuumMeasurementUnit,
                    Remark = slpv.Remark,
                    VacuumRack = slpv.VacuumRack,
                    VacuumWastageQuantity = slpv.VacuumWastageQuantity,
                    VacuumStatus = slpv.VacuumStatus,
                    AddedBy = slpv.AddedBy,
                    AddedDate = slpv.AddedDate,
                }).FirstOrDefault();
                res.SaleOrderProduction.SaleOrderPostProcessLacquer = db.SaleOrderPostProcessLacquerTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(slpl => new SaleOrderPostProcessLacquerTableVm
                {
                    SaleOrderPostProcessLacquerId = slpl.SaleOrderPostProcessLacquerId,
                    SaleOrderId = slpl.SaleOrderId,
                    ReceivedQuantity = slpl.ReceivedQuantity,
                    LacquerCompletedQuantity = slpl.LacquerCompletedQuantity,
                    LacquerMeasurementUnit = slpl.LacquerMeasurementUnit,
                    Remark = slpl.Remark,
                    LacquerRack = slpl.LacquerRack,
                    LacquerWastageQuantity = slpl.LacquerWastageQuantity,
                    LacquerStatus = slpl.LacquerStatus,
                    AddedBy = slpl.AddedBy,
                    AddedDate = slpl.AddedDate,
                }).FirstOrDefault();
                res.SaleOrderProduction.SaleOrderPostProcessTumbling = db.SaleOrderPostProcessTumblingTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(slpt => new SaleOrderPostProcessTumblingTableVm
                {
                    SaleOrderPostProcessTumblingId = slpt.SaleOrderPostProcessTumblingId,
                    SaleOrderId = slpt.SaleOrderId,
                    ReceivedQuantity = slpt.ReceivedQuantity,
                    TumblingCompletedQuantity = slpt.TumblingCompletedQuantity,
                    TumblingMeasurementUnit = slpt.TumblingMeasurementUnit,
                    Remark = slpt.Remark,
                    TumblingRack = slpt.TumblingRack,
                    TumblingWastageQuantity = slpt.TumblingWastageQuantity,
                    TumblingStatus = slpt.TumblingStatus,
                    AddedBy = slpt.AddedBy,
                    AddedDate = slpt.AddedDate,
                }).FirstOrDefault();


                res.PreInspectionCompletedBy = res.Status >= ESalesOrderStatus.Inspection ? (insform == null ? null : insform.AddedBy) : null;
                res.PreInspectionCompletedOn = res.Status >= ESalesOrderStatus.Inspection ? (insform == null ? null : insform.AddedDate) : null;
                decimal prefor = (decimal)1.45 / 1000;
                res.SaleOrderProduction.FormulationMixing = (from la in db.FormulationCodeMixingTables
                                                             join a in db.MixingMasters on la.MixingId equals a.MixingId
                                                             join sl in db.SaleFormulationCodeMasters on la.SaleFormulationCodeId equals sl.SaleFormulationCodeId
                                                             where la.SaleFormulationCodeId == res.SaleFormulationCodeId
                                                             select new FormulationCodeMixingTableVm
                                                             {
                                                                 FormulationCodeMixingId = la.FormulationCodeMixingId,
                                                                 SaleFormulationCodeId = la.SaleFormulationCodeId,
                                                                 PreSkinGsm = sl.PreSkinGsm,
                                                                 SkinGsm = sl.SkinGsm,
                                                                 FoamGsm = sl.FoamGsm,
                                                                 AdhesiveGsm = sl.AdhesiveGsm,
                                                                 AddedDate = la.AddedDate,
                                                                 AddedBy = la.AddedBy,
                                                                 MixingId = la.MixingId,
                                                                 MixingName = a.MixingName,
                                                                 MixingRawMaterial = (from op in db.FormulationCodeMixingRawMaterialTables
                                                                                      join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                      where op.FormulationCodeMixingId == la.FormulationCodeMixingId
                                                                                      select new FormulationCodeMixingRawMaterialTableVm
                                                                                      {
                                                                                          FormulationCodeMixingRawMaterialId = op.FormulationCodeMixingRawMaterialId,
                                                                                          FormulationCodeMixingId = op.FormulationCodeMixingId,
                                                                                          ProductId = op.ProductId,
                                                                                          ProductName = p.ProductName,
                                                                                          ProductCode = p.ProductCode,
                                                                                          Quantity = op.Quantity,
                                                                                          Scquantity = op.Scquantity,
                                                                                          AvgGsm = p.AvgGsm,
                                                                                          Unit = op.Unit,
                                                                                          Price = op.Price
                                                                                      }).ToList()
                                                             }).ToList();
                foreach (var item in res.SaleOrderProduction.FormulationMixing)
                {
                    item.PreSkinGsmPasteReq = item.PreSkinGsmSCPasteReq = item.PreSkinGsm * res.SaleOrderProduction.OrderQuantity * prefor;
                    item.SkinGsmPasteReq = item.SkinGsmSCPasteReq = item.SkinGsm * res.SaleOrderProduction.OrderQuantity * prefor;
                    item.FoamGsmPasteReq = item.FoamGsmSCPasteReq = item.FoamGsm * res.SaleOrderProduction.OrderQuantity * prefor;
                    item.AdhesiveGsmPasteReq = item.AdhesiveGsmSCPasteReq = item.AdhesiveGsm * res.SaleOrderProduction.OrderQuantity * prefor;
                }

                res.SaleOrderProductionComplete = (from sopc in db.SaleOrderProductionCompleteTables
                                                   where sopc.SaleOrderId == res.SaleOrderId
                                                   select new SaleOrderProductionCompleteTableVm
                                                   {
                                                       SaleOrderProductionCompleteId = sopc.SaleOrderProductionCompleteId,
                                                       SaleOrderId = sopc.SaleOrderId,
                                                       ManufacturedQuantity = sopc.ManufacturedQuantity,
                                                       PreSkinGsm = sopc.PreSkinGsm,
                                                       SkinGsm = sopc.SkinGsm,
                                                       FoamGsm = sopc.FoamGsm,
                                                       AdhesiveGsm = sopc.AdhesiveGsm,
                                                       FabricGsm = sopc.FabricGsm,
                                                       PreSkinActualPasteQty = sopc.PreSkinActualPasteQty,
                                                       PreSkinRemainingPasteQty = sopc.PreSkinRemainingPasteQty,
                                                       SkinActualPasteQty = sopc.SkinActualPasteQty,
                                                       SkinRemainingPasteQty = sopc.SkinRemainingPasteQty,
                                                       FoamActualPasteQty = sopc.FoamActualPasteQty,
                                                       FoamRemainingPasteQty = sopc.FoamRemainingPasteQty,
                                                       AdhesiveActualPasteQty = sopc.AdhesiveActualPasteQty,
                                                       AdhesiveRemainingPasteQty = sopc.AdhesiveRemainingPasteQty,
                                                       Addedby = sopc.Addedby,
                                                       AddedDate = sopc.AddedDate
                                                   }).FirstOrDefault();

                if (res.SaleOrderProductionComplete != null)
                {
                    var calres = new CalculationsDataFn();
                    decimal LineSpeed = 0;
                    LineSpeed = calres.GetProductionLineSpeedIncAllJumbo(res.SaleOrderId);

                    // decimal CurrentMonthOverhead = decimal.Parse(db.ConfigTables.Where(x => x.ConfigItem == "CurrentMonthOverhead").FirstOrDefault().ConfigValue);
                    decimal PerHourAvgOverheadCost = db.OverheadCostingTables.OrderByDescending(x => x.ApplicableOn).FirstOrDefault().OverheadCost.Value;
                    decimal OverHeadwithLineSpeed = 0;
                    if (PerHourAvgOverheadCost > 0 && LineSpeed > 0)
                    {
                        // OverHeadwithLineSpeed = decimal.Round(PerHourAvgOverheadCost / 60 / LineSpeed, 2);
                        OverHeadwithLineSpeed = decimal.Round(calres.GetOverheadforAllJumboPerSaleOrder(res.SaleOrderId), 2);
                    }

                    res.SaleOrderProductionComplete.PrdLineSpeed = LineSpeed;
                    res.SaleOrderProductionComplete.Overhead = OverHeadwithLineSpeed;
                }

                res.SaleOrderProduction.InspectionSaleFormulationCode = (from ifcm in db.InspectionFormulationCodeMixingTables
                                                                         join isfc in db.InspectionSaleFormulationCodeMasters on ifcm.InspectionSaleFormulationCodeId equals isfc.InspectionSaleFormulationCodeId
                                                                         join pr in db.ProductMasters on isfc.FabricProductId equals pr.ProductId into prd
                                                                         from pr in prd.DefaultIfEmpty()
                                                                         join fw in db.FactoryWorkersMasters on isfc.ShiftSupervisorWorkerId equals fw.WorkerId into fwd
                                                                         from fw in fwd.DefaultIfEmpty()
                                                                         where ifcm.SaleOrderId == res.SaleOrderId
                                                                         select new InspectionSaleFormulationCodeMasterVm
                                                                         {
                                                                             FabricProductId = isfc.FabricProductId,
                                                                             FabricProductName = pr.ProductName,
                                                                             FabricProductQty = isfc.FabricProductQty,
                                                                             FabricGsm = isfc.FabricGsm,
                                                                             FabricProductUnit = pr.Unit,
                                                                             FabricWidthInMeter = isfc.FabricWidthInMeter,
                                                                             ShiftSupervisorWorkerId = isfc.ShiftSupervisorWorkerId,
                                                                             ShiftSupervisorWorkerName = fw.Name
                                                                         }).FirstOrDefault();

                res.SaleOrderProduction.InspectionFormulationMixing = (from la in db.InspectionFormulationCodeMixingTables
                                                                       join a in db.MixingMasters on la.MixingId equals a.MixingId
                                                                       where la.SaleOrderId == res.SaleOrderId
                                                                       select new InspectionFormulationCodeMixingTableVm
                                                                       {
                                                                           FormulationCodeMixingId = la.FormulationCodeMixingId,
                                                                           SaleFormulationCodeId = la.SaleFormulationCodeId,
                                                                           GSM = (a.MixingName == "PRE SKIN" ? res.SaleOrderProduction.PreSkinGsm :
                                                                           (a.MixingName == "ADHESIVE" ? res.SaleOrderProduction.AdhesiveGsm :
                                                                           (a.MixingName == "FOAM" ? res.SaleOrderProduction.FoamGsm :
                                                                           (a.MixingName == "SKIN" ? res.SaleOrderProduction.SkinGsm :
                                                                           0)))),
                                                                           FinalGSM = (a.MixingName == "PRE SKIN" && res.SaleOrderProductionComplete != null ? res.SaleOrderProductionComplete.PreSkinGsm :
                                                                           (a.MixingName == "ADHESIVE" && res.SaleOrderProductionComplete != null ? res.SaleOrderProductionComplete.AdhesiveGsm :
                                                                           (a.MixingName == "FOAM" && res.SaleOrderProductionComplete != null ? res.SaleOrderProductionComplete.FoamGsm :
                                                                           (a.MixingName == "SKIN" && res.SaleOrderProductionComplete != null ? res.SaleOrderProductionComplete.SkinGsm :
                                                                           0)))),
                                                                           AddedDate = la.AddedDate,
                                                                           AddedBy = la.AddedBy,
                                                                           MixingId = la.MixingId,
                                                                           MixingName = a.MixingName,
                                                                           MixingRawMaterial = (from op in db.InspectionFormulationCodeMixingRawMaterialTables
                                                                                                join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                                where op.FormulationCodeMixingId == la.FormulationCodeMixingId
                                                                                                select new InspectionFormulationCodeMixingRawMaterialTableVm
                                                                                                {
                                                                                                    FormulationCodeMixingRawMaterialId = op.FormulationCodeMixingRawMaterialId,
                                                                                                    FormulationCodeMixingId = op.FormulationCodeMixingId,
                                                                                                    ProductId = op.ProductId,
                                                                                                    ProductName = p.ProductName,
                                                                                                    ProductCode = p.ProductCode,
                                                                                                    BaseQuantity = op.BaseQuantity,
                                                                                                    IsBaseMaterial = op.IsBaseMaterial,
                                                                                                    Quantity = op.Quantity,
                                                                                                    Scquantity = op.Scquantity,
                                                                                                    AvgGsm = p.AvgGsm,
                                                                                                    Unit = op.Unit,
                                                                                                    Price = 0,
                                                                                                }).ToList()
                                                                       }).ToList();
                foreach (var item in res.SaleOrderProduction.InspectionFormulationMixing)
                {
                    var totalqtywithExtra = res.SaleOrderProduction.OrderQuantity + (res.SaleOrderProduction.OrderQuantity * res.SaleOrderProduction.ExtraProduction) / 100;
                    item.PreSkinGsmPasteReq = res.SaleOrderProduction.PreSkinGsm * totalqtywithExtra * prefor;
                    item.SkinGsmPasteReq = res.SaleOrderProduction.SkinGsm * totalqtywithExtra * prefor;
                    item.FoamGsmPasteReq = res.SaleOrderProduction.FoamGsm * totalqtywithExtra * prefor;
                    item.AdhesiveGsmPasteReq = res.SaleOrderProduction.AdhesiveGsm * totalqtywithExtra * prefor;
                }

                var insdata = (from jit in db.JumboInspectionTables
                               join wpjm in db.WorkPlanJumboMasters on jit.WorkPlanJumboMasterId equals wpjm.WorkPlanJumboMasterId
                               join so in db.SaleOrderTables on wpjm.SaleOrderId equals so.SaleOrderId
                               where so.SaleOrderId == pid
                               select new JumboInspectionTable
                               {
                                   Quantity = jit.Quantity,
                                   Grade = jit.Grade
                               }
                               ).ToList();

                var filteredGradeData = insdata.Where(x => x.Grade == "LOT" || x.Grade == "NS" || x.Grade == "CUT-PC" || x.Grade == "FILM" || x.Grade == "WASTE").ToList();
                var filteredGradeSum = filteredGradeData.Sum(x => x.Quantity);
                if (filteredGradeData.Count > 0)
                {
                    var autoCalculatedRejection = (filteredGradeSum / res.SaleOrderProduction.ManufacturingQuantity) * 100;

                    res.SaleOrderProduction.RejectionPercentAutoCalculated = decimal.Round(autoCalculatedRejection.Value, 2);
                }
                else
                {
                    res.SaleOrderProduction.RejectionPercentAutoCalculated = 0;
                }

                res.SaleOrderCosting = (from soc in db.SaleOrderCostingTables
                                        where soc.SaleOrderId == res.SaleOrderId
                                        select new SaleOrderCostingTableVm
                                        {
                                            SaleOrderId = soc.SaleOrderId,
                                            FabricCost = soc.FabricCost,
                                            CoatingCost = soc.CoatingCost,
                                            FabricCostLm = soc.FabricCostLm,
                                            PasteCostLm = soc.PasteCostLm,
                                            GrainCostLm = soc.GrainCostLm,
                                            FinishingCostLm = soc.FinishingCostLm,
                                            RmcostLm = soc.RmcostLm,
                                            Rejection = soc.Rejection,
                                            ProductionCostLm = soc.Rejection,
                                            PerLmconstant = soc.PerLmconstant,
                                            OverheadCost = soc.OverheadCost,
                                            InlineScraping = soc.InlineScraping,
                                            PrintCostPerUnit = soc.PrintCostPerUnit,
                                            EmbossingCostPerUnit = soc.EmbossingCostPerUnit,
                                            TumblingCostPerUnit = soc.TumblingCostPerUnit,
                                            VacuumCostPerUnit = soc.VacuumCostPerUnit,
                                            LacquerCostPerUnit = soc.LacquerCostPerUnit,
                                            SaleOrderMaterialType = soc.SaleOrderMaterialType,
                                            AddedBy = soc.AddedBy,
                                            AddedDate = soc.AddedDate,
                                        }).FirstOrDefault();

                if (costdata != null && costdata.Count() > 0)
                {
                    res.FormulationFabricProductPrice = (insform == null) ? 0 : (costdata.Any(x => x.ProductId == insform.FabricProductId) ? costdata.FirstOrDefault(x => x.ProductId == insform.FabricProductId).PricePerUnit : 0);
                    res.FormulationFabricProductUnit = (insform == null) ? null : (costdata.Any(x => x.ProductId == insform.FabricProductId) ? costdata.FirstOrDefault(x => x.ProductId == insform.FabricProductId).Unit : res.FormulationFabricProductUnit);
                    foreach (var item in res.SaleOrderProduction.Lacquer)
                    {
                        foreach (var itemrm in item.LacquerRawMaterial)
                        {
                            itemrm.Price = costdata.Any(x => x.ProductId == itemrm.ProductId) ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId).PricePerUnit : 0;
                        }
                    }
                    foreach (var item in res.SaleOrderProduction.InspectionFormulationMixing)
                    {
                        foreach (var itemrm in item.MixingRawMaterial)
                        {
                            itemrm.Price = costdata.Any(x => x.ProductId == itemrm.ProductId) ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId).PricePerUnit : 0;
                        }
                    }
                    foreach (var itemrm in res.SaleOrderProduction.SaleOrderProductionMiscellaneousRawMaterial)
                    {
                        itemrm.Price = costdata.Any(x => x.ProductId == itemrm.ProductId) ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId).PricePerUnit : 0;

                    }
                }
            }
            return res;
        }
        public SaleOrderCostingViewTableVm GetSaleOrderCostingViewDataById(long pid)
        {
            SaleOrderCostingViewTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                var saleprodid = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == pid).SaleOrderProductionId;

                var wpo = db.WorkPlanOrders.Where(x => x.OrderId == pid).FirstOrDefault();
                long? wpid = 0;
                string wpnumber = "";
                if (wpo != null)
                    wpid = wpo.WorkplanId;
                if (wpid > 0)
                    wpnumber = db.WorkPlanMasters.Where(y => y.WorkPlanId == wpid).FirstOrDefault().WorkPlanNo;
                var costdata = (from cs in db.ConsumeStockProductMasters
                                join sp in db.StockProductTables on cs.StockProductId equals sp.StockProductId
                                join st in db.StockMasters on sp.StockId equals st.StockId
                                where cs.SaleOrderId == pid
                                select new
                                {
                                    cs.ProductId,
                                    sp.PricePerUnit,
                                    cs.Unit,
                                    cs.Quantity,
                                    cs.Scquantity,
                                    cs.MaterialCategory,
                                    st.Batch
                                }).ToList();

                res = (from s in db.SaleOrderTables
                       join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                       from pcm in spc.DefaultIfEmpty()
                       join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                       from cust in scu.DefaultIfEmpty()
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join prf in db.ProductMasters on a.FabricProductId equals prf.ProductId into prsf
                       from prf in prsf.DefaultIfEmpty()
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join cf in db.ColorMasters on a.FabricColorId equals cf.ColorId into cagrf
                       from cf in cagrf.DefaultIfEmpty()
                       join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                       from g in agr.DefaultIfEmpty()
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId into pra
                       from pr in pra.DefaultIfEmpty()
                       join p in db.ProductCategoryMasters on pr.ProductCategoryId equals p.ProductCategoryId into ps
                       from p in ps.DefaultIfEmpty()
                       join pf in db.ProductFirstSubCategoryMasters on pr.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                       from pf in psf.DefaultIfEmpty()
                       join ext in db.ProductMasterExtensions on a.ProductId equals ext.ProductId into aext
                       from ext in aext.DefaultIfEmpty()
                       join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                       from thm in thmsa.DefaultIfEmpty()
                       join wdm in db.WidthMasters on a.Width equals wdm.WidthId into wdmsa
                       from wdm in wdmsa.DefaultIfEmpty()
                       where a.SaleOrderId == pid
                       select new SaleOrderCostingViewTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           SaleOrderCode = s.SaleOrderCode,
                           FinishCode = s.FinishCode,
                           CostingAdded = s.CostingAdded,
                           CustomerId = s.CustomerId,
                           CustomerName = cust.CustomerName,
                           CategoryId = s.CategoryId,
                           Category = pcm.ProductCategory,
                           AddedBy = s.AddedBy,
                           AddedDate = s.AddedDate,
                           DeliveryDate = s.DeliveryDate,
                           Remarks = s.Remarks,
                           BORNumber = s.Bornumber,
                           SaleOrderStatus = s.SaleOrderStatus,
                           WorkPlanStatus = s.WorkPlanStatus,
                           WorkPlanNumber = s.WorkPlanStatus == true ? wpnumber : "Not Created",
                           ProformaInvoiceId = s.ProformaInvoiceId,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,
                           SaleFormulationCode = fcm.SaleFormulationCode,
                           SaleFormulationCodeId = fcm.SaleFormulationCodeId,
                           IsLiningOrder = db.LinkedSaleOrderTables.Where(x => x.LinkedSaleOrder == s.SaleOrderId).FirstOrDefault() != null,
                           IsUpperOrder = db.LinkedSaleOrderTables.Where(x => x.ParentSaleOrder == s.SaleOrderId).FirstOrDefault() != null,
                           SaleOrderProduction = new SaleOrderProductionCostingTableVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               ProductId = a.ProductId,
                               ProductName = pr.ProductName,
                               ProductCode = pr.ProductCode,
                               ProductCategoryId = pr.ProductCategoryId,
                               ProductCategory = p.ProductCategory,
                               ProductFirstSubCategoryId = pr.ProductFirstSubCategoryId,
                               ProductFirstSubCategory = pf.ProductFirstSubCategory,
                               ManufacturingProductName = a.ManufacturingProductName,
                               ManufacturingProductCode = a.ManufacturingProductCode,
                               Lot = a.Lot,
                               Batch = a.Batch,
                               OrderQuantity = a.OrderQuantity,
                               LMConstant = a.Lmconstant,
                               ExtraProduction = a.ExtraProduction,
                               ManufacturingQuantity = a.ManufacturingQuantity,
                               Unit = a.Unit,
                               ColorId = a.ColorId,
                               ColorName = c.ColorName,
                               ColorCode = c.ColorCode,
                               FabricColorId = a.FabricColorId,
                               FabricColorName = cf.ColorName,
                               ColorPrice = a.ColorPrice,
                               GrainId = a.GrainId,
                               GrainName = g.GrainName,
                               GrainCode = g.GrainCode,
                               GrainPrice = a.GrainPrice,
                               Thick = a.Thick,
                               ThickPrice = a.ThickPrice,
                               ThicknessValue = thm.ThicknessNumber,
                               Width = a.Width,
                               WidthNumber = wdm.WidthNumber,
                               WidthPrice = a.WidthPrice,
                               Barcode = a.Barcode,
                               TotalCost = a.TotalCost,
                               AddedBy = a.AddedBy,
                               AddedDate = a.AddedDate,
                               ProcessFormulationCode = a.ProcessFormulationCode,
                               MixingFormulationCode = a.MixingFormulationCode,
                               SalePrice = a.SalePrice,
                               PreSkinGsm = a.PreSkinGsm,
                               SkinGsm = a.SkinGsm,
                               FoamGsm = a.FoamGsm,
                               AdhesiveGsm = a.AdhesiveGsm,
                               FabricGsm = a.FabricGsm,
                               TotalGsm = a.TotalGsm,
                           }
                       }).FirstOrDefault();
                res.SaleOrderProduction.PackagingRawMaterial = (from op in db.SaleOrderProductionMiscellaneousRawMaterialTables
                                                                join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId
                                                                && op.MaterialCategory.ToLower() == "packaging"
                                                                select new SaleOrderProductionMiscellaneousRawMaterialTableVm
                                                                {
                                                                    SaleOrderProductionMiscellaneousRawMaterialId = op.SaleOrderProductionMiscellaneousRawMaterialId,
                                                                    SaleOrderProductionId = op.SaleOrderProductionId,
                                                                    ProductId = op.ProductId,
                                                                    ProductCode = p.ProductCode,
                                                                    ProductName = p.ProductName,
                                                                    Unit = op.Unit,
                                                                    Quantity = op.Quantity,
                                                                    MaterialCategory = op.MaterialCategory
                                                                }).ToList();
                res.SaleOrderProduction.SaleOrderProductionMiscellaneousRawMaterial = (from op in db.SaleOrderProductionMiscellaneousRawMaterialTables
                                                                                       join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                       where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId
                                                                                       && op.MaterialCategory.ToLower() != "packaging"
                                                                                       select new SaleOrderProductionMiscellaneousRawMaterialTableVm
                                                                                       {
                                                                                           SaleOrderProductionMiscellaneousRawMaterialId = op.SaleOrderProductionMiscellaneousRawMaterialId,
                                                                                           SaleOrderProductionId = op.SaleOrderProductionId,
                                                                                           ProductId = op.ProductId,
                                                                                           ProductCode = p.ProductCode,
                                                                                           ProductName = p.ProductName,
                                                                                           Unit = op.Unit,
                                                                                           Quantity = op.Quantity,
                                                                                           MaterialCategory = op.MaterialCategory
                                                                                       }).ToList();

                res.SaleOrderProduction.SaleOrderProductionRawMaterial = (from op in db.SaleOrderProductionRawMaterialTables
                                                                          join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                          where op.SaleOrderProductionId == res.SaleOrderProduction.SaleOrderProductionId
                                                                          select new SaleOrderProductionRawMaterialTableVm
                                                                          {
                                                                              SaleOrderProductionRawMaterialId = op.SaleOrderProductionRawMaterialId,
                                                                              SaleOrderProductionId = op.SaleOrderProductionId,
                                                                              ProductId = op.ProductId,
                                                                              ProductCode = p.ProductCode,
                                                                              ProductName = p.ProductName,
                                                                              Unit = op.Unit,
                                                                              Quantity = op.Quantity,
                                                                              AvgGsm = p.AvgGsm,
                                                                              TotalCost = op.TotalCost,
                                                                              PerUnitCost = op.PerUnitCost,
                                                                          }).ToList();
                res.SaleOrderProduction.SaleOrderPostProcessPrint = db.SaleOrderPostProcessPrintTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(a => new SaleOrderPostProcessPrintTableVm
                {
                    SaleOrderPostProcessPrintId = a.SaleOrderPostProcessPrintId,
                    SaleOrderId = a.SaleOrderId,
                    ReceivedQuantity = a.ReceivedQuantity,
                    PrintCompletedQuantity = a.PrintCompletedQuantity,
                    PrintMeasurementUnit = a.PrintMeasurementUnit,
                    Remark = a.Remark,
                    PrintRack = a.PrintRack,
                    PrintWastageQuantity = a.PrintWastageQuantity,
                    PrintStatus = a.PrintStatus,
                    AddedBy = a.AddedBy,
                    AddedDate = a.AddedDate,
                    PricePerUnit = a.PricePerUnit
                }).ToList();
                res.SaleOrderProduction.SaleOrderPostProcessEmbossing = db.SaleOrderPostProcessEmbossingTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(a => new SaleOrderPostProcessEmbossingTableVm
                {
                    SaleOrderPostProcessEmbossingId = a.SaleOrderPostProcessEmbossingId,
                    SaleOrderId = a.SaleOrderId,
                    ReceivedQuantity = a.ReceivedQuantity,
                    EmbossingCompletedQuantity = a.EmbossingCompletedQuantity,
                    EmbossingMeasurementUnit = a.EmbossingMeasurementUnit,
                    Remark = a.Remark,
                    EmbossingRack = a.EmbossingRack,
                    EmbossingWastageQuantity = a.EmbossingWastageQuantity,
                    EmbossingStatus = a.EmbossingStatus,
                    AddedBy = a.AddedBy,
                    AddedDate = a.AddedDate,
                    PricePerUnit = a.PricePerUnit
                }).ToList();
                res.SaleOrderProduction.SaleOrderPostProcessVacuum = db.SaleOrderPostProcessVacuumTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(slpv => new SaleOrderPostProcessVacuumTableVm
                {
                    SaleOrderPostProcessVacuumId = slpv.SaleOrderPostProcessVacuumId,
                    SaleOrderId = slpv.SaleOrderId,
                    ReceivedQuantity = slpv.ReceivedQuantity,
                    VacuumCompletedQuantity = slpv.VacuumCompletedQuantity,
                    VacuumMeasurementUnit = slpv.VacuumMeasurementUnit,
                    Remark = slpv.Remark,
                    VacuumRack = slpv.VacuumRack,
                    VacuumWastageQuantity = slpv.VacuumWastageQuantity,
                    VacuumStatus = slpv.VacuumStatus,
                    AddedBy = slpv.AddedBy,
                    AddedDate = slpv.AddedDate,
                    PricePerUnit = slpv.PricePerUnit
                }).ToList();
                res.SaleOrderProduction.SaleOrderPostProcessLacquer = db.SaleOrderPostProcessLacquerTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(slpl => new SaleOrderPostProcessLacquerTableVm
                {
                    SaleOrderPostProcessLacquerId = slpl.SaleOrderPostProcessLacquerId,
                    SaleOrderId = slpl.SaleOrderId,
                    ReceivedQuantity = slpl.ReceivedQuantity,
                    LacquerCompletedQuantity = slpl.LacquerCompletedQuantity,
                    LacquerMeasurementUnit = slpl.LacquerMeasurementUnit,
                    Remark = slpl.Remark,
                    LacquerRack = slpl.LacquerRack,
                    LacquerWastageQuantity = slpl.LacquerWastageQuantity,
                    LacquerStatus = slpl.LacquerStatus,
                    AddedBy = slpl.AddedBy,
                    AddedDate = slpl.AddedDate,
                    PricePerUnit = slpl.PricePerUnit
                }).ToList();
                res.SaleOrderProduction.SaleOrderPostProcessTumbling = db.SaleOrderPostProcessTumblingTables.Where(x => x.SaleOrderId == res.SaleOrderId).Select(slpt => new SaleOrderPostProcessTumblingTableVm
                {
                    SaleOrderPostProcessTumblingId = slpt.SaleOrderPostProcessTumblingId,
                    SaleOrderId = slpt.SaleOrderId,
                    ReceivedQuantity = slpt.ReceivedQuantity,
                    TumblingCompletedQuantity = slpt.TumblingCompletedQuantity,
                    TumblingMeasurementUnit = slpt.TumblingMeasurementUnit,
                    Remark = slpt.Remark,
                    TumblingRack = slpt.TumblingRack,
                    TumblingWastageQuantity = slpt.TumblingWastageQuantity,
                    TumblingStatus = slpt.TumblingStatus,
                    AddedBy = slpt.AddedBy,
                    AddedDate = slpt.AddedDate,
                    PricePerUnit = slpt.PricePerUnit
                }).ToList();

                var totalPostProcessPricePerUnit =
                 (res.SaleOrderProduction.SaleOrderPostProcessPrint?.Sum(x => x.PricePerUnit ?? 0) ?? 0)
                + (res.SaleOrderProduction.SaleOrderPostProcessEmbossing?.Sum(x => x.PricePerUnit ?? 0) ?? 0)
                + (res.SaleOrderProduction.SaleOrderPostProcessVacuum?.Sum(x => x.PricePerUnit ?? 0) ?? 0)
                + (res.SaleOrderProduction.SaleOrderPostProcessLacquer?.Sum(x => x.PricePerUnit ?? 0) ?? 0)
                + (res.SaleOrderProduction.SaleOrderPostProcessTumbling?.Sum(x => x.PricePerUnit ?? 0) ?? 0);

                res.SaleOrderProduction.TotalPostProcessPricePerUnit = decimal.Round(totalPostProcessPricePerUnit, 2);
                decimal prefor = (decimal)1.45 / 1000;


                res.SaleOrderProductionComplete = (from sopc in db.SaleOrderProductionCompleteTables
                                                   where sopc.SaleOrderId == res.SaleOrderId
                                                   select new SaleOrderProductionCompleteTableVm
                                                   {
                                                       SaleOrderProductionCompleteId = sopc.SaleOrderProductionCompleteId,
                                                       SaleOrderId = sopc.SaleOrderId,
                                                       ManufacturedQuantity = sopc.ManufacturedQuantity,
                                                       PreSkinGsm = sopc.PreSkinGsm,
                                                       SkinGsm = sopc.SkinGsm,
                                                       FoamGsm = sopc.FoamGsm,
                                                       AdhesiveGsm = sopc.AdhesiveGsm,
                                                       FabricGsm = sopc.FabricGsm,
                                                       PreSkinActualPasteQty = sopc.PreSkinActualPasteQty,
                                                       PreSkinRemainingPasteQty = sopc.PreSkinRemainingPasteQty,
                                                       SkinActualPasteQty = sopc.SkinActualPasteQty,
                                                       SkinRemainingPasteQty = sopc.SkinRemainingPasteQty,
                                                       FoamActualPasteQty = sopc.FoamActualPasteQty,
                                                       FoamRemainingPasteQty = sopc.FoamRemainingPasteQty,
                                                       AdhesiveActualPasteQty = sopc.AdhesiveActualPasteQty,
                                                       AdhesiveRemainingPasteQty = sopc.AdhesiveRemainingPasteQty,
                                                       Addedby = sopc.Addedby,
                                                       AddedDate = sopc.AddedDate
                                                   }).FirstOrDefault();

                if (res.SaleOrderProductionComplete != null)
                {
                    var calres = new CalculationsDataFn();
                    decimal LineSpeed = 0;
                    LineSpeed = calres.GetProductionLineSpeedIncAllJumbo(res.SaleOrderId);

                    // decimal CurrentMonthOverhead = decimal.Parse(db.ConfigTables.Where(x => x.ConfigItem == "CurrentMonthOverhead").FirstOrDefault().ConfigValue);
                    decimal PerHourAvgOverheadCost = db.OverheadCostingTables.OrderByDescending(x => x.ApplicableOn).FirstOrDefault().OverheadCost.Value;
                    decimal OverHeadwithLineSpeed = 0;
                    if (PerHourAvgOverheadCost > 0 && LineSpeed > 0)
                    {
                        // OverHeadwithLineSpeed = decimal.Round(PerHourAvgOverheadCost / 60 / LineSpeed, 2);
                        OverHeadwithLineSpeed = decimal.Round(calres.GetOverheadforAllJumboPerSaleOrder(res.SaleOrderId), 2);
                    }

                    res.SaleOrderProductionComplete.PrdLineSpeed = LineSpeed;
                    res.SaleOrderProductionComplete.Overhead = OverHeadwithLineSpeed;
                }

                res.SaleOrderProduction.InspectionSaleFormulationCode = (from ifcm in db.InspectionFormulationCodeMixingTables
                                                                         join isfc in db.InspectionSaleFormulationCodeMasters on ifcm.InspectionSaleFormulationCodeId equals isfc.InspectionSaleFormulationCodeId
                                                                         join pr in db.ProductMasters on isfc.FabricProductId equals pr.ProductId
                                                                         where ifcm.SaleOrderId == res.SaleOrderId
                                                                         select new InspectionSaleFormulationCodeMasterVm
                                                                         {
                                                                             FabricProductId = isfc.FabricProductId,
                                                                             FabricProductName = pr.ProductName,
                                                                             FabricProductQty = isfc.FabricProductQty,
                                                                             FabricGsm = isfc.FabricGsm,
                                                                             FabricProductUnit = pr.Unit,
                                                                             FabricWidthInMeter = isfc.FabricWidthInMeter
                                                                         }).FirstOrDefault();

                res.SaleOrderProduction.FinalFormulationMixing = (from la in db.InspectionFormulationCodeMixingTables
                                                                  join a in db.MixingMasters on la.MixingId equals a.MixingId
                                                                  where la.SaleOrderId == res.SaleOrderId
                                                                  select new InspectionFormulationCodeMixingTableVm
                                                                  {
                                                                      FormulationCodeMixingId = la.FormulationCodeMixingId,
                                                                      SaleFormulationCodeId = la.SaleFormulationCodeId,
                                                                      GSM = (a.MixingName == "PRE SKIN" ? res.SaleOrderProduction.PreSkinGsm :
                                                                      (a.MixingName == "ADHESIVE" ? res.SaleOrderProduction.AdhesiveGsm :
                                                                      (a.MixingName == "FOAM" ? res.SaleOrderProduction.FoamGsm :
                                                                      (a.MixingName == "SKIN" ? res.SaleOrderProduction.SkinGsm :
                                                                      0)))),
                                                                      FinalGSM = (a.MixingName == "PRE SKIN" && res.SaleOrderProductionComplete != null ? res.SaleOrderProductionComplete.PreSkinGsm :
                                                                      (a.MixingName == "ADHESIVE" && res.SaleOrderProductionComplete != null ? res.SaleOrderProductionComplete.AdhesiveGsm :
                                                                      (a.MixingName == "FOAM" && res.SaleOrderProductionComplete != null ? res.SaleOrderProductionComplete.FoamGsm :
                                                                      (a.MixingName == "SKIN" && res.SaleOrderProductionComplete != null ? res.SaleOrderProductionComplete.SkinGsm :
                                                                      0)))),
                                                                      AddedDate = la.AddedDate,
                                                                      AddedBy = la.AddedBy,
                                                                      MixingId = la.MixingId,
                                                                      MixingName = a.MixingName,
                                                                      MixingRawMaterial = (from op in db.InspectionFormulationCodeMixingRawMaterialTables
                                                                                           join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                           where op.FormulationCodeMixingId == la.FormulationCodeMixingId
                                                                                           select new InspectionFormulationCodeMixingRawMaterialTableVm
                                                                                           {
                                                                                               FormulationCodeMixingRawMaterialId = op.FormulationCodeMixingRawMaterialId,
                                                                                               FormulationCodeMixingId = op.FormulationCodeMixingId,
                                                                                               ProductId = op.ProductId,
                                                                                               ProductName = p.ProductName,
                                                                                               ProductCode = p.ProductCode,
                                                                                               BaseQuantity = op.BaseQuantity,
                                                                                               IsBaseMaterial = op.IsBaseMaterial,
                                                                                               Quantity = op.Quantity,
                                                                                               Scquantity = op.Scquantity,
                                                                                               AvgGsm = p.AvgGsm,
                                                                                               Unit = op.Unit,
                                                                                               Price = 0,
                                                                                               MixingName = a.MixingName
                                                                                           }).ToList()
                                                                  }).ToList();
                var inscode = db.InspectionFormulationCodeMixingTables.Where(s => s.SaleOrderId == res.SaleOrderId).FirstOrDefault().InspectionSaleFormulationCodeId;
                var fabricExist = db.InspectionSaleFormulationCodeMasters.FirstOrDefault(x => x.InspectionSaleFormulationCodeId == inscode);
                if (fabricExist.FabricProductId != null && fabricExist.FabricProductId > 0)
                {
                    var MixingDataFabric = new List<FinalFabricRawMaterialTableVm>();
                    MixingDataFabric = (from op in db.InspectionSaleFormulationCodeMasters
                                        join FabPro in db.ProductMasters on op.FabricProductId equals FabPro.ProductId
                                        join consume in db.ConsumeStockProductMasters on "Fabric" equals consume.MaterialCategory
                                        where op.InspectionSaleFormulationCodeId == inscode
                                        select new FinalFabricRawMaterialTableVm
                                        {
                                            ProductId = FabPro.ProductId,
                                            ProductName = FabPro.ProductName,
                                            ProductCode = FabPro.ProductCode,
                                            Quantity = op.FabricProductQty,
                                            Unit = FabPro.Unit,
                                            AvgGsm = op.FabricGsm,
                                            WidthInMeter = op.FabricWidthInMeter,
                                            MixingName = "Fabric"
                                        }).Distinct().ToList();

                    res.SaleOrderProduction.FinalFabricData = new List<FinalFabricDataMixingTableVm>
                    {
                        new() {
                            MixingName = "Fabric",
                            MixingRawMaterial = MixingDataFabric
                        }
                    };
                }
                var insdata = (from jit in db.JumboInspectionTables
                               join wpjm in db.WorkPlanJumboMasters on jit.WorkPlanJumboMasterId equals wpjm.WorkPlanJumboMasterId
                               join so in db.SaleOrderTables on wpjm.SaleOrderId equals so.SaleOrderId
                               where so.SaleOrderId == pid
                               select new JumboInspectionTable
                               {
                                   Quantity = jit.Quantity,
                                   Grade = jit.Grade
                               }
                               ).ToList();

                var filteredGradeData = insdata.Where(x => x.Grade == "LOT" || x.Grade == "NS" || x.Grade == "CUT-PC" || x.Grade == "FILM" || x.Grade == "WASTE").ToList();
                var filteredGradeSum = filteredGradeData.Sum(x => x.Quantity);
                if (filteredGradeData.Count > 0)
                {
                    var autoCalculatedRejection = (filteredGradeSum / res.SaleOrderProduction.ManufacturingQuantity) * 100;

                    res.SaleOrderProduction.RejectionPercentAutoCalculated = decimal.Round(autoCalculatedRejection.Value, 2);
                }
                else
                {
                    res.SaleOrderProduction.RejectionPercentAutoCalculated = 0;
                }

                res.SaleOrderCosting = (from soc in db.SaleOrderCostingTables
                                        where soc.SaleOrderId == res.SaleOrderId
                                        select new SaleOrderCostingTableVm
                                        {
                                            SaleOrderId = soc.SaleOrderId,
                                            FabricCost = soc.FabricCost,
                                            CoatingCost = soc.CoatingCost,
                                            FabricCostLm = soc.FabricCostLm,
                                            PasteCostLm = soc.PasteCostLm,
                                            GrainCostLm = soc.GrainCostLm,
                                            FinishingCostLm = soc.FinishingCostLm,
                                            RmcostLm = soc.RmcostLm,
                                            Rejection = soc.Rejection,
                                            ProductionCostLm = soc.Rejection,
                                            PerLmconstant = soc.PerLmconstant,
                                            OverheadCost = soc.OverheadCost,
                                            InlineScraping = soc.InlineScraping,
                                            PrintCostPerUnit = soc.PrintCostPerUnit,
                                            EmbossingCostPerUnit = soc.EmbossingCostPerUnit,
                                            TumblingCostPerUnit = soc.TumblingCostPerUnit,
                                            VacuumCostPerUnit = soc.VacuumCostPerUnit,
                                            LacquerCostPerUnit = soc.LacquerCostPerUnit,
                                            SaleOrderMaterialType = soc.SaleOrderMaterialType,
                                            PackagingCostPerUnit = soc.PackagingCostPerUnit,
                                            MiscellaneousCostPerUnit = soc.MiscellaneousCostPerUnit,
                                            AddedBy = soc.AddedBy,
                                            AddedDate = soc.AddedDate,
                                        }).FirstOrDefault();
                if (res.IsUpperOrder && res.SaleOrderCosting != null)
                {
                    var rejectionCostPerLm = res.SaleOrderCosting.Rejection;
                    var linkedSaleOrderId = db.LinkedSaleOrderTables.Where(x => x.ParentSaleOrder == res.SaleOrderId).FirstOrDefault().LinkedSaleOrder;
                    res.LinkedSaleOrderCosting = (from soc in db.SaleOrderCostingTables
                                                  join s in db.SaleOrderTables on soc.SaleOrderId equals s.SaleOrderId
                                                  where soc.SaleOrderId == linkedSaleOrderId
                                                  select new SaleOrderCostingTableVm
                                                  {
                                                      SaleOrderId = soc.SaleOrderId,
                                                      SaleOrderNumber = s.SaleOrderNumber,
                                                      TotalCostPerLm = soc.ProductionCostLm + soc.OverheadCost + soc.PackagingCostPerUnit + soc.MiscellaneousCostPerUnit,
                                                  }).FirstOrDefault();
                    res.LinkedSaleOrderCosting.Rejection = res.SaleOrderCosting.Rejection;
                    res.LinkedSaleOrderCosting.RejectionCostLm = res.LinkedSaleOrderCosting.TotalCostPerLm * (res.LinkedSaleOrderCosting.Rejection / 100);
                }

                if (costdata != null && costdata.Count() > 0)
                {
                    foreach (var item in res.SaleOrderProduction.FinalFormulationMixing)
                    {
                        foreach (var itemrm in item.MixingRawMaterial)
                        {
                            itemrm.BatchNo = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MixingName.ToLower())
                             ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MixingName.ToLower()).Batch
                              : "";

                            itemrm.Price = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MixingName.ToLower() && x.Batch == itemrm.BatchNo)
                               ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MixingName.ToLower() && x.Batch == itemrm.BatchNo).PricePerUnit
                                : 0;

                            var FilteredByBatch = costdata.Where(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MixingName.ToLower() && x.Batch == itemrm.BatchNo).ToList();
                            if (FilteredByBatch.Count > 0 && FilteredByBatch.Any(x => x.Quantity == 0))
                            {
                                var totalQty = costdata.Where(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MixingName.ToLower() && x.Batch == itemrm.BatchNo).Sum(x => x.Quantity);
                                itemrm.Quantity = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MixingName.ToLower() && x.Batch == itemrm.BatchNo) ? totalQty : 0;

                                var totalSCQty = costdata.Where(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MixingName.ToLower() && x.Batch == itemrm.BatchNo).Sum(x => x.Scquantity);
                                itemrm.Scquantity = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MixingName.ToLower() && x.Batch == itemrm.BatchNo) ? totalSCQty : 0;
                            }
                        }
                    }
                    if (res.SaleOrderProduction.FinalFabricData != null)
                    {
                        foreach (var item in res.SaleOrderProduction.FinalFabricData)
                        {
                            foreach (var itemrm in item.MixingRawMaterial)
                            {
                                itemrm.BatchNo = costdata.Any(x => x.ProductId == itemrm.ProductId)
                                 ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId).Batch
                                  : "";

                                itemrm.Price = costdata.Any(x => x.ProductId == itemrm.ProductId && x.Batch == itemrm.BatchNo)
                                 ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId && x.Batch == itemrm.BatchNo).PricePerUnit
                                  : 0;

                                var FilteredByBatch = costdata.Where(x => x.ProductId == itemrm.ProductId && x.Batch == itemrm.BatchNo).ToList();
                                if (FilteredByBatch.Count > 0 && FilteredByBatch.Any(x => x.Quantity == 0))
                                {
                                    var totalQty = costdata.Where(x => x.ProductId == itemrm.ProductId && x.Batch == itemrm.BatchNo).Sum(x => x.Quantity);
                                    itemrm.Quantity = costdata.Any(x => x.ProductId == itemrm.ProductId && x.Batch == itemrm.BatchNo) ? totalQty : 0;
                                }
                                if (itemrm.Unit == "Kgs" && itemrm.Quantity > 0)
                                {
                                    itemrm.ConvertedQtyKgsToMtrs = ConvertQtyKgstoMtrs(itemrm.Quantity, itemrm.AvgGsm, itemrm.WidthInMeter);
                                }
                            }
                            item.TotalQtyInMtrs = item.MixingRawMaterial.Where(x => x.Unit == "Mtrs").Sum(x => x.Quantity);
                            item.TotalQtyInKgs = item.MixingRawMaterial.Where(x => x.Unit == "Kgs").Sum(x => x.Quantity);
                            item.QtyInMeterPerKg = item.TotalQtyInKgs > 0 ? item.MixingRawMaterial.Where(x => x.ConvertedQtyKgsToMtrs > 0).Sum(x => x.ConvertedQtyKgsToMtrs) / item.TotalQtyInKgs : 0;
                            decimal totalConvertedQtyKgsToMtrs = item.MixingRawMaterial.Where(x => x.ConvertedQtyKgsToMtrs > 0).Sum(x => x.ConvertedQtyKgsToMtrs.Value);

                            item.TotalQuantity = item.TotalQtyInMtrs + (item.QtyInMeterPerKg * item.TotalQtyInKgs);

                            decimal totalPriceInKgs = item.MixingRawMaterial.Where(x => x.Unit == "Kgs").Sum(x => x.Price.Value * x.Quantity.Value);
                            decimal totalPriceInMtrs = item.MixingRawMaterial.Where(x => x.Unit == "Mtrs").Sum(x => x.Price.Value * x.Quantity.Value);

                            decimal totalPriceInMtrsFromKgs = item.QtyInMeterPerKg > 0 ? totalPriceInKgs / totalConvertedQtyKgsToMtrs : 0;
                            decimal SinglePerMtrPrice = item.TotalQtyInMtrs > 0 ? totalPriceInMtrs / item.TotalQtyInMtrs.Value : 0;

                            decimal totalUnifiedPriceInMtrs = totalPriceInMtrs + totalPriceInMtrsFromKgs;

                            item.PricePerMtr = item.QtyInMeterPerKg > 0 && item.TotalQtyInMtrs > 0 ? totalUnifiedPriceInMtrs / item.TotalQuantity : item.QtyInMeterPerKg > 0 ? totalUnifiedPriceInMtrs : SinglePerMtrPrice;

                            item.TotalCost = item.PricePerMtr * item.TotalQuantity;
                            // item.FabricCostPerLm = 

                            item.TotalCost = decimal.Round(item.TotalCost.Value, 4);
                            item.QtyInMeterPerKg = decimal.Round(item.QtyInMeterPerKg.Value, 4);
                            item.TotalQuantity = decimal.Round(item.TotalQuantity.Value, 4);
                            item.PricePerMtr = decimal.Round(item.PricePerMtr.Value, 4);
                        }
                    }
                    foreach (var itemrm in res.SaleOrderProduction.SaleOrderProductionMiscellaneousRawMaterial)
                    {
                        itemrm.BatchNo = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory)
                         ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory).Batch
                          : "";

                        itemrm.Price = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory && x.Batch == itemrm.BatchNo)
                         ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory && x.Batch == itemrm.BatchNo).PricePerUnit
                          : 0;

                        var FilteredByBatch = costdata.Where(x => x.ProductId == itemrm.ProductId && x.MaterialCategory.ToLower() == itemrm.MaterialCategory.ToLower() && x.Batch == itemrm.BatchNo).ToList();
                        if (FilteredByBatch.Count > 0 && FilteredByBatch.Any(x => x.Quantity == 0))
                        {
                            var totalQty = costdata.Where(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory).Sum(x => x.Quantity);
                            itemrm.Quantity = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory) ? totalQty : 0;
                        }
                    }
                    foreach (var itemrm in res.SaleOrderProduction.PackagingRawMaterial)
                    {
                        itemrm.Price = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory)
                         ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory).PricePerUnit
                          : 0;

                        itemrm.BatchNo = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory)
                         ? costdata.FirstOrDefault(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory).Batch
                          : "";

                        // var totalQty = costdata.Where(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory).Sum(x => x.Quantity);
                        // itemrm.Quantity = costdata.Any(x => x.ProductId == itemrm.ProductId && x.MaterialCategory == itemrm.MaterialCategory) ? totalQty : 0;
                    }
                }
            }
            return res;
        }

        public decimal ConvertQtyKgstoMtrs(decimal? QtyInKgs, decimal? GSM, decimal? WidthInMeter)
        {
            decimal ConvertedQty = 0;
            if (QtyInKgs > 0 && GSM > 0 && WidthInMeter > 0)
                ConvertedQty = decimal.Round(QtyInKgs.Value * 1000 / (GSM.Value * WidthInMeter.Value), 2);

            return ConvertedQty;
        }

        public SaleOrderTableVm GetSaleOrderDataByIdForGradePrint(long pid)
        {
            SaleOrderTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                var linksaleOrder = db.LinkedSaleOrderTables.Where(x => x.ParentSaleOrder == pid).ToList();
                string thicknessPattern = @"[\d.]+";
                var grain = "";
                double thickness = 0.0;
                long liningColorId = 0;
                var liningColorCode = "";
                foreach (var item in linksaleOrder)
                {
                    var graincode = (from s in db.SaleOrderTables
                                     join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                                     join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                                     from c in cagr.DefaultIfEmpty()
                                     join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                                     from g in agr.DefaultIfEmpty()
                                     join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                                     from thm in thmsa.DefaultIfEmpty()
                                     where a.SaleOrderId == item.LinkedSaleOrder
                                     select new
                                     {
                                         g.GrainCode,
                                         thm.ThicknessNumber,
                                         a.ColorId,
                                         c.ColorCode
                                     }).FirstOrDefault();
                    if (graincode != null)
                    {
                        grain = grain + " / " + graincode.GrainCode;
                        double th1val = Convert.ToDouble(Regex.Match(graincode.ThicknessNumber, thicknessPattern).Value);
                        thickness = thickness + th1val;
                        liningColorId = graincode.ColorId.Value;
                        liningColorCode = graincode.ColorCode;
                    }
                }
                res = (from s in db.SaleOrderTables
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                       from g in agr.DefaultIfEmpty()
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId into pra
                       from pr in pra.DefaultIfEmpty()
                       join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                       from thm in thmsa.DefaultIfEmpty()
                       join wdm in db.WidthMasters on a.Width equals wdm.WidthId into wdmsa
                       from wdm in wdmsa.DefaultIfEmpty()
                       where a.SaleOrderId == pid
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           SaleOrderCode = s.SaleOrderCode,
                           FinishCode = s.FinishCode,
                           WorkPlanStatus = s.WorkPlanStatus,
                           SaleFormulationCode = fcm.SaleFormulationCode,
                           SaleFormulationCodeId = fcm.SaleFormulationCodeId,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,

                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               ProductId = a.ProductId,
                               ProductName = pr.ProductName,
                               ProductCode = pr.ProductCode,
                               ManufacturingProductName = a.ManufacturingProductName,
                               ManufacturingProductCode = a.ManufacturingProductCode,
                               Unit = a.Unit,
                               ColorId = a.ColorId,
                               ColorName = c.ColorName,
                               ColorCode = c.ColorCode,
                               GrainId = a.GrainId,
                               GrainName = g.GrainName,
                               GrainCode = g.GrainCode + grain,
                               Thick = a.Thick,
                               ThickPrice = a.ThickPrice,
                               ThicknessValue = thm.ThicknessNumber,
                               Width = a.Width,
                               WidthNumber = wdm.WidthNumber,
                               WidthPrice = a.WidthPrice,
                           }
                       }).FirstOrDefault();
                double thval = Convert.ToDouble(Regex.Match(res.SaleOrderProduction.ThicknessValue, thicknessPattern).Value);
                double TotalThickness = thval + thickness;
                res.SaleOrderProduction.ThicknessValue = TotalThickness.ToString("0.00 MM");
                if (linksaleOrder != null)
                {
                    res.SaleFormulationCode = res.SaleFormulationCode.Replace("U", "SW", true, System.Globalization.CultureInfo.InvariantCulture);
                }
                if (liningColorId != res.SaleOrderProduction.ColorId)
                {
                    res.SaleOrderProduction.ColorCode = res.SaleOrderProduction.ColorCode + " / " + liningColorCode;
                }
                return res;
            }
        }
        public ApiFunctionResponseVm SendDispatchPackagingEmail(Stream st, long dispatchId, string[] emaillist)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = db.JumboDispatchTables.Where(x => x.JumboDispatchId == dispatchId).FirstOrDefault();
                var orderdata = (from sot in db.SaleOrderTables
                                 join wjm in db.WorkPlanJumboMasters on sot.SaleOrderId equals wjm.SaleOrderId
                                 join ji in db.JumboInspectionTables on wjm.WorkPlanJumboMasterId equals ji.WorkPlanJumboMasterId
                                 where ji.JumboDispatchId == dispatchId
                                 select new SaleOrderTableVm
                                 {
                                     SaleOrderNumber = sot.SaleOrderNumber,
                                     SaleOrderId = sot.SaleOrderId
                                 }).Distinct().ToList();
                var ActiveEmailConfigName = db.ConfigTables.Where(x => x.ConfigItem == "ActiveEmailConfigName").First().ConfigValue;
                var replyToList = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.SaleOrderStatusReplyTo && x.Enabled == "true").Select(x => x.EmailId).ToArray();

                var customer = db.CustomerMasters.FirstOrDefault(x => x.CustomerId == res.CustomerId);

                var emailSubject = "Order Dispatch Details | Packaging Number - " + res.PackingNumber + " | " + customer.CustomerName;
                string messageBody = "";

                messageBody = "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 15px;'>Dear Sir / Madam, </span><br><br>";
                messageBody = messageBody + "<span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>Here is your Order status.</span>";
                messageBody = messageBody + @"<br><br>
                                                <span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'><b> Update: Your order is ready for Dispatch. Please find attached dispatch packaging details.</b></span>";

                messageBody = messageBody + "<br><br><span style='font-family: Arial, Helvetica, sans-serif; font-size: 12px;'>The details are as follows:</span><br><br>";

                var reports = new ReportDataFn(GlobalData);
                foreach (var item in orderdata)
                {
                    var dataset = GetSaleOrderDataForEmailById(item.SaleOrderId);
                    messageBody = messageBody + reports.GetOrderDetailsHtmlWithoutCustomerForEmail(dataset, SaleOrderEmailStatus.ReadyToDispatch);
                    if (orderdata.Count > 1)
                    {
                        messageBody = messageBody + @"<br>------------------------------------Next Order----------------------------------------------------<br>";
                    }
                }

                messageBody = messageBody + reports.GetSaleOrderDetailsHtmlFooterEmail();
                if (ActiveEmailConfigName.ToLower() == "primary")
                {
                    EmailV2DataFn.SendEmailUsingAmazonSES(EmailTrackingModules.DispatchPackaging, res.JumboDispatchId, GlobalData.loggedInUser, messageBody, st, emaillist, null, null, true, replyToList, "DispatchPackaging-" + res.JumboDispatchId + ".pdf", emailSubject);
                }
                else if (ActiveEmailConfigName.ToLower() == "secondary")
                {
                    EmailV2DataFn.SendEmail(EmailTrackingModules.DispatchPackaging, res.JumboDispatchId, GlobalData.loggedInUser, messageBody, st, emaillist, null, null, true, replyToList, "DispatchPackaging-" + res.JumboDispatchId + ".pdf", emailSubject);
                }
                var emailstatus = db.EmailTrackingTables.Where(x => x.ModuleId == res.JumboDispatchId).FirstOrDefault();
                if (emailstatus.Status == "Accepted")
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Email Sent Successfully.");
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "Email Sending Failed.");
                }
            }
        }

        public SaleOrderTableVm GetSaleOrderPostProcessDataById(long pid)
        {
            SaleOrderTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                var saleprodid = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == pid).SaleOrderProductionId;
                var insform = (from a in db.InspectionSaleFormulationCodeMasters
                               join la in db.InspectionFormulationCodeMixingTables on a.InspectionSaleFormulationCodeId equals la.InspectionSaleFormulationCodeId
                               join p in db.ProductMasters on a.FabricProductId equals p.ProductId
                               where la.SaleOrderId == pid
                               select new InspectionSaleFormulationCodeMasterVm
                               {
                                   FabricProductId = a.FabricProductId,
                                   FabricProductName = p.ProductName
                               }).FirstOrDefault();

                res = (from s in db.SaleOrderTables
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId
                       join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                       from pcm in spc.DefaultIfEmpty()
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join prf in db.ProductMasters on a.FabricProductId equals prf.ProductId into prsf
                       from prf in prsf.DefaultIfEmpty()
                       join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                       from thm in thmsa.DefaultIfEmpty()
                       join wdm in db.WidthMasters on a.Width equals wdm.WidthId into wdmsa
                       from wdm in wdmsa.DefaultIfEmpty()
                       join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                       from g in agr.DefaultIfEmpty()
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join cf in db.ColorMasters on a.FabricColorId equals cf.ColorId into cagrf
                       from cf in cagrf.DefaultIfEmpty()
                       where s.SaleOrderId == pid
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           CustomerName = cust.CustomerName,
                           SaleOrderDate = s.SaleOrderDate,
                           CategoryId = s.CategoryId,
                           Category = pcm.ProductCategory,
                           Remarks = s.Remarks,
                           SaleOrderStatus = s.SaleOrderStatus,
                           SaleFormulationCode = fcm.SaleFormulationCode,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,
                           FormulationFabricProductName = insform == null ? prf.ProductName : insform.FabricProductName,
                           FormulationFabricProductId = insform == null ? prf.ProductId : insform.FabricProductId.Value,
                           SaleOrderProductionPostProcess = new SaleOrderProductionPostProcessVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               ManufacturingProductName = a.ManufacturingProductName,
                               OrderQuantity = a.OrderQuantity,
                               ManufacturingQuantity = a.ManufacturingQuantity,
                               ColorName = c.ColorName,
                               FabricColorName = cf.ColorName,
                               GrainCode = g.GrainCode,
                               ThicknessValue = thm.ThicknessNumber,
                               WidthNumber = wdm.WidthNumber,
                           }
                       }).FirstOrDefault();
                res.SaleOrderPostProcessOrder = (from sopp in db.SaleOrderPostProcessOrderTables
                                                 where sopp.SaleOrderId == pid && sopp.Removed != true
                                                 select new SaleOrderPostProcessOrderTableVm
                                                 {
                                                     SaleOrderId = sopp.SaleOrderId,
                                                     Rank = sopp.Rank,
                                                     PostProcessName = sopp.PostProcessName,
                                                     ProcessMasterId = sopp.ProcessMasterId
                                                 }).OrderBy(x => x.Rank).ToList();
                var formulationPrefix = res.SaleFormulationCode.Split('-')[0];
                var productType = formulationPrefix == "GZ" ? "PVC" : formulationPrefix == "GZY" ? "PU" : null;
                var PostProcessCostList = db.PostProcessCostingMasters.Where(x => x.SaleOrderType == productType).ToList();
                res.SaleOrderProductionPostProcess.SaleOrderProductionEmbossing = (from op in db.SaleOrderProductionEmbossingMasters
                                                                                   join p in db.EmbossingMasters on op.EmbossingMasterId equals p.EmbossingMasterId
                                                                                   where op.SaleOrderProductionId == saleprodid && op.Removed != true
                                                                                   select new SaleOrderProductionEmbossingMasterVm
                                                                                   {
                                                                                       SaleOrderProductionEmbossingMasterId = op.SaleOrderProductionEmbossingMasterId,
                                                                                       SaleOrderProductionId = op.SaleOrderProductionId,
                                                                                       EmbossingMasterId = op.EmbossingMasterId,
                                                                                       Name = p.Name,
                                                                                       ImageName = p.ImageName,
                                                                                       Code = p.Code,
                                                                                       Description = p.Description,
                                                                                       Quantity = op.Quantity,
                                                                                       Total = op.Total,
                                                                                       Rank = op.Rank
                                                                                   }).ToList();
                res.SaleOrderProductionPostProcess.SaleOrderProductionEmbossing.ForEach(x => x.Price = PostProcessCostList.Count > 0 ? PostProcessCostList.FirstOrDefault(y => y.PostProcessName == "Embossing").Cost : 0);
                res.SaleOrderProductionPostProcess.SaleOrderProductionPrint = (from op in db.SaleOrderProductionPrintMasters
                                                                               join p in db.PrintMasters on op.PrintMasterId equals p.PrintMasterId
                                                                               where op.SaleOrderProductionId == saleprodid && op.Removed != true
                                                                               select new SaleOrderProductionPrintMasterVm
                                                                               {
                                                                                   SaleOrderProductionPrintMasterId = op.SaleOrderProductionPrintMasterId,
                                                                                   SaleOrderProductionId = op.SaleOrderProductionId,
                                                                                   PrintMasterId = op.PrintMasterId,
                                                                                   Name = p.Name,
                                                                                   ImageName = p.ImageName,
                                                                                   Code = p.Code,
                                                                                   Description = p.Description,
                                                                                   Quantity = op.Quantity,
                                                                                   Total = op.Total,
                                                                                   Rank = op.Rank
                                                                               }).ToList();
                res.SaleOrderProductionPostProcess.SaleOrderProductionPrint.ForEach(x => x.Price = PostProcessCostList.Count > 0 ? PostProcessCostList.FirstOrDefault(y => y.PostProcessName == "Print").Cost : 0);
                res.SaleOrderProductionPostProcess.SaleOrderProductionTumbling = (from op in db.SaleOrderProductionTumblingMasters
                                                                                  join p in db.TumblingMasters on op.TumblingMasterId equals p.TumblingMasterId
                                                                                  where op.SaleOrderProductionId == saleprodid && op.Removed != true
                                                                                  select new SaleOrderProductionTumblingMasterVm
                                                                                  {
                                                                                      SaleOrderProductionTumblingMasterId = op.SaleOrderProductionTumblingMasterId,
                                                                                      SaleOrderProductionId = op.SaleOrderProductionId,
                                                                                      TumblingMasterId = op.TumblingMasterId,
                                                                                      Name = p.Name,
                                                                                      Code = p.Code,
                                                                                      Description = p.Description,
                                                                                      Quantity = op.Quantity,
                                                                                      Total = op.Total,
                                                                                      Rank = op.Rank
                                                                                  }).ToList();
                res.SaleOrderProductionPostProcess.SaleOrderProductionTumbling.ForEach(x => x.Price = PostProcessCostList.Count > 0 ? PostProcessCostList.FirstOrDefault(y => y.PostProcessName == "Tumbling").Cost : 0);
                res.SaleOrderProductionPostProcess.SaleOrderProductionVacuum = (from op in db.SaleOrderProductionVacuumMasters
                                                                                join p in db.VacuumMasters on op.VacuumMasterId equals p.VacuumMasterId
                                                                                where op.SaleOrderProductionId == saleprodid && op.Removed != true
                                                                                select new SaleOrderProductionVacuumMasterVm
                                                                                {
                                                                                    SaleOrderProductionVacuumMasterId = op.SaleOrderProductionVacuumMasterId,
                                                                                    SaleOrderProductionId = op.SaleOrderProductionId,
                                                                                    VacuumMasterId = op.VacuumMasterId,
                                                                                    Name = p.Name,
                                                                                    Code = p.Code,
                                                                                    Description = p.Description,
                                                                                    Quantity = op.Quantity,
                                                                                    Total = op.Total,
                                                                                    Rank = op.Rank
                                                                                }).ToList();
                res.SaleOrderProductionPostProcess.SaleOrderProductionVacuum.ForEach(x => x.Price = PostProcessCostList.Count > 0 ? PostProcessCostList.FirstOrDefault(y => y.PostProcessName == "Vacuum").Cost : 0);
                res.SaleOrderProductionPostProcess.Lacquer = (from am in db.LacquerMasters
                                                              join lrm in db.SaleOrderProductionLacquerRawMaterialTables on am.LacquerMasterId equals lrm.LacquerMasterId
                                                              where lrm.SaleOrderProductionId == saleprodid && lrm.Removed != true
                                                              select new LacquerMasterVm
                                                              {
                                                                  LacquerMasterId = am.LacquerMasterId,
                                                                  Name = am.Name,
                                                                  Code = am.Code,
                                                                  Description = am.Description,
                                                                  AddedBy = am.AddedBy,
                                                                  AddedDate = am.AddedDate,
                                                                  Rank = lrm.Rank,
                                                              }).OrderByDescending(x => x.LacquerMasterId).ToList();
                res.SaleOrderProductionPostProcess.Lacquer.ForEach(x => x.Price = PostProcessCostList.Count > 0 ? PostProcessCostList.FirstOrDefault(y => y.PostProcessName == "Lacquer").Cost : 0);
                res.SaleOrderProductionPostProcess.SaleOrderPostProcessPrint = (from slpp in db.SaleOrderPostProcessPrintTables
                                                                                join rm in db.RackMasters on slpp.PrintRack equals rm.RackId
                                                                                where slpp.SaleOrderId == pid
                                                                                select new SaleOrderPostProcessPrintTableVm
                                                                                {
                                                                                    SaleOrderPostProcessPrintId = slpp.SaleOrderPostProcessPrintId,
                                                                                    SaleOrderId = slpp.SaleOrderId,
                                                                                    ReceivedQuantity = slpp.ReceivedQuantity,
                                                                                    PrintCompletedQuantity = slpp.PrintCompletedQuantity,
                                                                                    PrintMeasurementUnit = slpp.PrintMeasurementUnit,
                                                                                    Remark = slpp.Remark,
                                                                                    PrintRack = slpp.PrintRack,
                                                                                    PrintWastageQuantity = slpp.PrintWastageQuantity,
                                                                                    PrintStatus = slpp.PrintStatus,
                                                                                    AddedBy = slpp.AddedBy,
                                                                                    AddedDate = slpp.AddedDate,
                                                                                    Rank = slpp.Rank,
                                                                                    StoreId = rm.StoreId,
                                                                                    ShiftSupervisorWorkerId = slpp.ShiftSupervisorWorkerId,
                                                                                    LineNo = slpp.LineNo,
                                                                                    StartDateTime = slpp.StartDateTime,
                                                                                    EndDateTime = slpp.EndDateTime,
                                                                                    PricePerUnit = slpp.PricePerUnit,
                                                                                }).ToList();

                res.SaleOrderProductionPostProcess.SaleOrderPostProcessEmbossing = (from slpe in db.SaleOrderPostProcessEmbossingTables
                                                                                    join rm in db.RackMasters on slpe.EmbossingRack equals rm.RackId
                                                                                    where slpe.SaleOrderId == pid
                                                                                    select new SaleOrderPostProcessEmbossingTableVm
                                                                                    {
                                                                                        SaleOrderPostProcessEmbossingId = slpe.SaleOrderPostProcessEmbossingId,
                                                                                        SaleOrderId = slpe.SaleOrderId,
                                                                                        ReceivedQuantity = slpe.ReceivedQuantity,
                                                                                        EmbossingCompletedQuantity = slpe.EmbossingCompletedQuantity,
                                                                                        EmbossingMeasurementUnit = slpe.EmbossingMeasurementUnit,
                                                                                        Remark = slpe.Remark,
                                                                                        EmbossingRack = slpe.EmbossingRack,
                                                                                        EmbossingWastageQuantity = slpe.EmbossingWastageQuantity,
                                                                                        EmbossingStatus = slpe.EmbossingStatus,
                                                                                        AddedBy = slpe.AddedBy,
                                                                                        AddedDate = slpe.AddedDate,
                                                                                        Rank = slpe.Rank,
                                                                                        StoreId = rm.StoreId,
                                                                                        ShiftSupervisorWorkerId = slpe.ShiftSupervisorWorkerId,
                                                                                        LineNo = slpe.LineNo,
                                                                                        StartDateTime = slpe.StartDateTime,
                                                                                        EndDateTime = slpe.EndDateTime,
                                                                                        PricePerUnit = slpe.PricePerUnit,
                                                                                    }).ToList();
                res.SaleOrderProductionPostProcess.SaleOrderPostProcessVacuum = (from slpv in db.SaleOrderPostProcessVacuumTables
                                                                                 join rm in db.RackMasters on slpv.VacuumRack equals rm.RackId
                                                                                 where slpv.SaleOrderId == pid
                                                                                 select new SaleOrderPostProcessVacuumTableVm
                                                                                 {
                                                                                     SaleOrderPostProcessVacuumId = slpv.SaleOrderPostProcessVacuumId,
                                                                                     SaleOrderId = slpv.SaleOrderId,
                                                                                     ReceivedQuantity = slpv.ReceivedQuantity,
                                                                                     VacuumCompletedQuantity = slpv.VacuumCompletedQuantity,
                                                                                     VacuumMeasurementUnit = slpv.VacuumMeasurementUnit,
                                                                                     Remark = slpv.Remark,
                                                                                     VacuumRack = slpv.VacuumRack,
                                                                                     VacuumWastageQuantity = slpv.VacuumWastageQuantity,
                                                                                     VacuumStatus = slpv.VacuumStatus,
                                                                                     AddedBy = slpv.AddedBy,
                                                                                     AddedDate = slpv.AddedDate,
                                                                                     Rank = slpv.Rank,
                                                                                     StoreId = rm.StoreId,
                                                                                     ShiftSupervisorWorkerId = slpv.ShiftSupervisorWorkerId,
                                                                                     LineNo = slpv.LineNo,
                                                                                     StartDateTime = slpv.StartDateTime,
                                                                                     EndDateTime = slpv.EndDateTime,
                                                                                     PricePerUnit = slpv.PricePerUnit,
                                                                                 }).ToList();
                res.SaleOrderProductionPostProcess.SaleOrderPostProcessTumbling = (from slpt in db.SaleOrderPostProcessTumblingTables
                                                                                   join rm in db.RackMasters on slpt.TumblingRack equals rm.RackId
                                                                                   where slpt.SaleOrderId == pid
                                                                                   select new SaleOrderPostProcessTumblingTableVm
                                                                                   {
                                                                                       SaleOrderPostProcessTumblingId = slpt.SaleOrderPostProcessTumblingId,
                                                                                       SaleOrderId = slpt.SaleOrderId,
                                                                                       ReceivedQuantity = slpt.ReceivedQuantity,
                                                                                       TumblingCompletedQuantity = slpt.TumblingCompletedQuantity,
                                                                                       TumblingMeasurementUnit = slpt.TumblingMeasurementUnit,
                                                                                       Remark = slpt.Remark,
                                                                                       TumblingRack = slpt.TumblingRack,
                                                                                       TumblingWastageQuantity = slpt.TumblingWastageQuantity,
                                                                                       TumblingStatus = slpt.TumblingStatus,
                                                                                       AddedBy = slpt.AddedBy,
                                                                                       AddedDate = slpt.AddedDate,
                                                                                       Rank = slpt.Rank,
                                                                                       StoreId = rm.StoreId,
                                                                                       ShiftSupervisorWorkerId = slpt.ShiftSupervisorWorkerId,
                                                                                       LineNo = slpt.LineNo,
                                                                                       StartDateTime = slpt.StartDateTime,
                                                                                       EndDateTime = slpt.EndDateTime,
                                                                                       PricePerUnit = slpt.PricePerUnit,
                                                                                   }).ToList();
                res.SaleOrderProductionPostProcess.SaleOrderPostProcessLacquer = (from slpl in db.SaleOrderPostProcessLacquerTables
                                                                                  join rm in db.RackMasters on slpl.LacquerRack equals rm.RackId
                                                                                  where slpl.SaleOrderId == pid
                                                                                  select new SaleOrderPostProcessLacquerTableVm
                                                                                  {
                                                                                      SaleOrderPostProcessLacquerId = slpl.SaleOrderPostProcessLacquerId,
                                                                                      SaleOrderId = slpl.SaleOrderId,
                                                                                      ReceivedQuantity = slpl.ReceivedQuantity,
                                                                                      LacquerCompletedQuantity = slpl.LacquerCompletedQuantity,
                                                                                      LacquerMeasurementUnit = slpl.LacquerMeasurementUnit,
                                                                                      Remark = slpl.Remark,
                                                                                      LacquerRack = slpl.LacquerRack,
                                                                                      LacquerWastageQuantity = slpl.LacquerWastageQuantity,
                                                                                      LacquerStatus = slpl.LacquerStatus,
                                                                                      AddedBy = slpl.AddedBy,
                                                                                      AddedDate = slpl.AddedDate,
                                                                                      Rank = slpl.Rank,
                                                                                      StoreId = rm.StoreId,
                                                                                      ShiftSupervisorWorkerId = slpl.ShiftSupervisorWorkerId,
                                                                                      LineNo = slpl.LineNo,
                                                                                      StartDateTime = slpl.StartDateTime,
                                                                                      EndDateTime = slpl.EndDateTime,
                                                                                      PricePerUnit = slpl.PricePerUnit,
                                                                                  }).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm UpdateSaleOrderStatus(pmsdbContext dbContext, IDbContextTransaction dbtransaction, long saleorderid, PmsCommon.ESalesOrderStatus status, string addedby)
        {
            var db = dbContext;
            var transaction = dbtransaction;

            var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
            if (so != null)
            {
                so.Status = (int)status;
                db.SaveChanges();
            }
            var workPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId;
            if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == saleorderid && x.Status == (int)status))
            {
                db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                {
                    SaleOrderId = saleorderid,
                    Status = (int)status,
                    AddedBy = addedby,
                    AddedDate = System.DateTime.Now,
                    WorkPlanId = workPlanId != null ? workPlanId : null
                });
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public ApiFunctionResponseVm UpdateSaleOrderStatusModified(pmsdbContext dbContext, IDbContextTransaction dbtransaction, long saleorderid, PmsCommon.ESalesOrderStatus statusToRemove, PmsCommon.ESalesOrderStatus statusToAdd, string addedby)
        {
            try
            {
                var db = dbContext;
                var transaction = dbtransaction;
                var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
                if (so != null)
                {
                    so.Status = (int)statusToAdd;
                    db.SaveChanges();
                }
                var sot = db.SaleOrderTimelineTables.FirstOrDefault(x => x.SaleOrderId == saleorderid && x.Status == (int)statusToRemove);
                if (sot == null)
                {
                    db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                    {
                        SaleOrderId = saleorderid,
                        Status = (int)statusToAdd,
                        AddedBy = addedby,
                        AddedDate = System.DateTime.Now,
                        WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == saleorderid).WorkplanId
                    });
                    db.SaveChanges();
                }
                else if (sot != null)
                {
                    db.SaleOrderTimelineTables.RemoveRange(sot);
                    db.SaveChanges();
                }
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
            }
            catch (Exception)
            {
                throw;
            }
        }

        public ApiFunctionResponseVm StartSaleOrderProduction(List<SaleOrderTableVm> saleorder)
        {
            using (var db = new Models.pmsdbContext())
            {
                foreach (var item in saleorder)
                {
                    var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == item.SaleOrderId);
                    if (so != null)
                    {
                        so.Status = (int)ESalesOrderStatus.ProductionStarted;
                        if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.ProductionStarted))
                        {
                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                            {
                                SaleOrderId = so.SaleOrderId,
                                Status = (int)ESalesOrderStatus.ProductionStarted,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = System.DateTime.Now,
                                WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                            });
                            db.SaveChanges();
                        }
                        // so.Status = (int)ESalesOrderStatus.InJumbo;
                        // db.SaveChanges();
                        // if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.InJumbo))
                        // {
                        //     db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                        //     {
                        //         SaleOrderId = so.SaleOrderId,
                        //         Status = (int)ESalesOrderStatus.InJumbo,
                        //         AddedBy = GlobalData.loggedInUser,
                        //         AddedDate = System.DateTime.Now
                        //     });
                        //     db.SaveChanges();
                        // }
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Sale order production started successfully");
        }
        public ResultSaleOrder RemoveSaleOrderJumbo(SaleOrderJumboRemoveVm items)
        {
            ResultSaleOrder vmModel = new ResultSaleOrder();

            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var res = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == items.WorkPlanJumboMasterId);
                        db.WorkPlanJumboMasters.Remove(res);
                        db.SaveChanges();
                        var so = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == res.SaleOrderId);
                        so.ManufacturingQuantity = items.ManufacturingQuantity;
                        db.SaveChanges();
                        transaction.Commit();
                        vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        List<string> error = new List<string>();
                        error.Add(ex.InnerException.Message);
                        error.Add(ex.InnerException.Source);
                        error.Add(ex.InnerException.StackTrace);
                        vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message, Errors = error };
                    }
                }
            }
            return vmModel;
        }
        public ResultSaleOrder CompleteSaleOrderProduction(SaleOrderCompleteProductionVm item)
        {
            ResultSaleOrder vmModel = new ResultSaleOrder();

            using (var db = new pmsdbContext())
            {
                using var transaction = db.Database.BeginTransaction();
                try
                {
                    var sop = db.SaleOrderProductionTables.Where(x => x.SaleOrderId == item.SaleOrderId).FirstOrDefault();

                    CalculationsDataFn fcd = new();
                    FormulationProductCalculatePasteReqRequest request = new()
                    {
                        TotalProductionQty = sop.ManufacturingQuantity
                    };
                    var soprequest = new SaleOrderProductionTableVm
                    {
                        PreSkinGsm = (item.PreSkinGsm ?? 0) > 0 ? item.PreSkinGsm : 0,
                        SkinGsm = (item.SkinGsm ?? 0) > 0 ? item.SkinGsm : 0,
                        FoamGsm = (item.FoamGsm ?? 0) > 0 ? item.FoamGsm : 0,
                        AdhesiveGsm = (item.AdhesiveGsm ?? 0) > 0 ? item.AdhesiveGsm : 0,
                        LMConstant = sop.Lmconstant
                    };
                    request.SaleOrderProduction = soprequest;

                    var ActualPasteReq = fcd.GetFormulationProductCalculatePasteReqQuantity(request);

                    var sopc = new SaleOrderProductionCompleteTable
                    {
                        SaleOrderId = item.SaleOrderId,
                        PreSkinGsm = item.PreSkinGsm,
                        PreSkinScGsm = item.PreSkinScGsm,
                        PreSkinRemainingPasteQty = item.PreSkinRemainingPasteQty,
                        PreSkinActualPasteQty = item.PreSkinActualPasteQty,
                        SkinGsm = item.SkinGsm,
                        SkinScGsm = item.SkinScGsm,
                        SkinRemainingPasteQty = item.SkinRemainingPasteQty,
                        SkinActualPasteQty = item.SkinActualPasteQty,
                        FoamGsm = item.FoamGsm,
                        FoamScGsm = item.FoamScGsm,
                        FoamRemainingPasteQty = item.FoamRemainingPasteQty,
                        FoamActualPasteQty = item.FoamActualPasteQty,
                        AdhesiveGsm = item.AdhesiveGsm,
                        AdhesiveScGsm = item.AdhesiveScGsm,
                        AdhesiveRemainingPasteQty = item.AdhesiveRemainingPasteQty,
                        AdhesiveActualPasteQty = item.AdhesiveActualPasteQty,
                        FabricGsm = item.SaleOrderProduction.FabricGsm,
                        ManufacturedQuantity = item.SaleOrderProduction.ManufacturingQuantity,
                        Addedby = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now
                    };
                    db.SaleOrderProductionCompleteTables.Add(sopc);
                    db.SaveChanges();

                    var sopid = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == item.SaleOrderId);

                    var IsPrintRequired = db.SaleOrderProductionPrintMasters.Any(x => x.SaleOrderProductionId == sopid.SaleOrderProductionId && x.Removed != true);
                    var IsEmbossingRequired = db.SaleOrderProductionEmbossingMasters.Any(x => x.SaleOrderProductionId == sopid.SaleOrderProductionId && x.Removed != true);
                    var IsLacquerRequired = db.SaleOrderProductionLacquerRawMaterialTables.Any(x => x.SaleOrderProductionId == sopid.SaleOrderProductionId && x.Removed != true);
                    var IsTumblingRequired = db.SaleOrderProductionTumblingMasters.Any(x => x.SaleOrderProductionId == sopid.SaleOrderProductionId && x.Removed != true);
                    var IsVacuumRequired = db.SaleOrderProductionVacuumMasters.Any(x => x.SaleOrderProductionId == sopid.SaleOrderProductionId && x.Removed != true);
                    var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == item.SaleOrderId);

                    var AutoLinkingItem = db.LinkedSaleOrderTables.FirstOrDefault(x => x.LinkedSaleOrder == item.SaleOrderId);
                    var IsOrderLinkingStatus = AutoLinkingItem?.IsLinkingComplete == false;
                    if (IsOrderLinkingStatus)
                    {
                        so.Status = (int)ESalesOrderStatus.LiningOrderMerged;

                        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                        {
                            SaleOrderId = so.SaleOrderId,
                            Status = (int)ESalesOrderStatus.LiningOrderMerged,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now,
                            WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                        });
                        AutoLinkingItem.IsLinkingComplete = true;
                        db.SaveChanges();
                    }
                    if (AutoLinkingItem == null)
                    {
                        if (!IsPrintRequired && !IsEmbossingRequired && !IsLacquerRequired && !IsTumblingRequired && !IsVacuumRequired)
                        {
                            so.ProductionCompletionRemarks = item.ProductionCompletionRemarks;
                            so.Status = (int)ESalesOrderStatus.JumboInspection;
                            if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.JumboInspection))
                            {
                                db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                {
                                    SaleOrderId = so.SaleOrderId,
                                    Status = (int)ESalesOrderStatus.JumboInspection,
                                    AddedBy = GlobalData.loggedInUser,
                                    AddedDate = System.DateTime.Now,
                                    WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                                });
                                db.SaveChanges();
                                var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnProductionCompleted" && x.ConfigValue == "true")).ToList();
                                if (enableSOStatusEmail.Count == 2)
                                {
                                    var emailSaleOrderStatus = SaleOrderEmailStatus.ProductionCompleted;
                                    _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(item.SaleOrderId, emailSaleOrderStatus);
                                }
                            }
                        }
                        else
                        {
                            so.Status = (int)ESalesOrderStatus.MoveToPostProcess;
                            if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.MoveToPostProcess))
                            {
                                db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                {
                                    SaleOrderId = so.SaleOrderId,
                                    Status = (int)ESalesOrderStatus.MoveToPostProcess,
                                    AddedBy = GlobalData.loggedInUser,
                                    AddedDate = System.DateTime.Now,
                                    WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                                });
                                db.SaveChanges();
                            }
                        }
                    }
                    db.SaveChanges();
                    transaction.Commit();
                    using var innerTransaction = db.Database.BeginTransaction();
                    try
                    {
                        if (AutoLinkingItem.ParentSaleOrder != AutoLinkingItem.LinkedSaleOrder)
                        {
                            UpdateSaleorderCode(db, transaction, AutoLinkingItem.ParentSaleOrder.Value);
                        }

                        innerTransaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        innerTransaction.Rollback();
                        throw;
                    }
                    vmModel.Result = new ApiResult { Succeeded = true, Code = EMessageCode.Success, Message = "Success" };
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    List<string> error = new List<string>();
                    error.Add(ex.InnerException.Message);
                    error.Add(ex.InnerException.Source);
                    error.Add(ex.InnerException.StackTrace);
                    vmModel.Result = new ApiResult { Succeeded = false, Code = EMessageCode.Exception, Message = ex.Message, Errors = error };
                }
            }
            return vmModel;
        }

        public ApiFunctionResponseVm AddPostProcessPrint(SaleOrderPostProcessPrintTableVm item)
        {
            if (item.JumboActualList.Any(x => x.ActualQuantity < 1))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Submitted Request do not have Jumbo QTY for either one or all Jumbo Rolls");
            }
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var ExistingRecordCheck = db.SaleOrderPostProcessPrintTables.Any(x => x.SaleOrderId == item.SaleOrderId && x.PrintStatus == item.PrintStatus && x.Rank == item.Rank);
                if (ExistingRecordCheck)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Duplicate record found. Refresh the page and try again.");
                }

                long id = item.SaleOrderPostProcessPrintId;
                if (item.PrintStatus != PostProcessItemsStatus.Remove)
                {
                    if (item.SaleOrderPostProcessPrintId == 0)
                    {
                        SaleOrderPostProcessPrintTable mm = new SaleOrderPostProcessPrintTable();
                        mm.SaleOrderId = item.SaleOrderId;
                        mm.PrintRack = item.PrintRack;
                        mm.ReceivedQuantity = item.ReceivedQuantity;
                        mm.PrintMeasurementUnit = item.PrintMeasurementUnit;
                        mm.Remark = item.Remark;
                        mm.PrintStatus = PostProcessItemsStatus.Assigned;
                        mm.AddedBy = GlobalData.loggedInUser;
                        mm.AddedDate = System.DateTime.Now;
                        mm.Rank = item.Rank;
                        mm.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaleOrderPostProcessPrintTables.Add(mm);
                        db.SaveChanges();
                        id = mm.SaleOrderPostProcessPrintId;
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastagePrint = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.PrintAssigned, GlobalData.loggedInUser);
                    }
                    if (item.PrintStatus == PostProcessItemsStatus.Completed)
                    {
                        var res = db.SaleOrderPostProcessPrintTables.FirstOrDefault(x => x.SaleOrderPostProcessPrintId == id && x.Rank == item.Rank);
                        res.PrintCompletedQuantity = item.PrintCompletedQuantity;
                        res.PrintWastageQuantity = item.PrintWastageQuantity;
                        res.PrintStatus = item.PrintStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.StartDateTime = item.StartDateTime;
                        res.EndDateTime = item.EndDateTime;
                        res.PricePerUnit = item.PricePerUnit;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastagePrint = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.PrintCompleted, GlobalData.loggedInUser);
                    }
                    if (item.PrintStatus == PostProcessItemsStatus.InProcess)
                    {
                        var res = db.SaleOrderPostProcessPrintTables.FirstOrDefault(x => x.SaleOrderPostProcessPrintId == id && x.Rank == item.Rank);
                        res.PrintStatus = item.PrintStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastagePrint = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.PrintInProcess, GlobalData.loggedInUser);
                    }

                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                }
                else
                {
                    var res = (from s in db.SaleOrderProductionPrintMasters
                               join a in db.SaleOrderProductionTables on s.SaleOrderProductionId equals a.SaleOrderProductionId
                               where a.SaleOrderId == item.SaleOrderId && s.Rank == item.Rank && s.Removed != true
                               select s).ToList();
                    //db.SaleOrderProductionPrintMasters.RemoveRange(res);
                    foreach (var rec in res)
                    {
                        rec.Removed = true;
                        rec.RemovedBy = GlobalData.loggedInUser;
                        rec.RemovedDate = DateTime.Now;
                    }
                    db.SaveChanges();
                    var orderRec = db.SaleOrderPostProcessOrderTables.FirstOrDefault(x => x.PostProcessName == "Print" && x.SaleOrderId == item.SaleOrderId && x.Rank == item.Rank && x.Removed != true);
                    orderRec.Removed = true;
                    db.SaveChanges();
                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                    UpdateSaleorderCode(db, transaction, item.SaleOrderId);
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Post Process Request Submitted Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                throw;
            }
        }

        public ApiFunctionResponseVm AddPostProcessEmbossing(SaleOrderPostProcessEmbossingTableVm item)
        {
            if (item.JumboActualList.Any(x => x.ActualQuantity < 1))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Submitted Request do not have Jumbo QTY for either one or all Jumbo Rolls");
            }
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var ExistingRecordCheck = db.SaleOrderPostProcessEmbossingTables.Any(x => x.SaleOrderId == item.SaleOrderId && x.EmbossingStatus == item.EmbossingStatus && x.Rank == item.Rank);
                if (ExistingRecordCheck)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Duplicate record found. Refresh the page and try again.");
                }

                long id = item.SaleOrderPostProcessEmbossingId;
                if (item.EmbossingStatus != PostProcessItemsStatus.Remove)
                {
                    if (item.SaleOrderPostProcessEmbossingId == 0)
                    {
                        SaleOrderPostProcessEmbossingTable mm = new SaleOrderPostProcessEmbossingTable();
                        mm.SaleOrderId = item.SaleOrderId;
                        mm.EmbossingRack = item.EmbossingRack;
                        mm.ReceivedQuantity = item.ReceivedQuantity;
                        mm.EmbossingMeasurementUnit = item.EmbossingMeasurementUnit;
                        mm.Remark = item.Remark;
                        mm.EmbossingStatus = PostProcessItemsStatus.Assigned;
                        mm.AddedBy = GlobalData.loggedInUser;
                        mm.AddedDate = System.DateTime.Now;
                        mm.Rank = item.Rank;
                        mm.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaleOrderPostProcessEmbossingTables.Add(mm);
                        db.SaveChanges();
                        id = mm.SaleOrderPostProcessEmbossingId;
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageEmbossing = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.EmbossingAssigned, GlobalData.loggedInUser);
                    }
                    if (item.EmbossingStatus == PostProcessItemsStatus.Completed)
                    {
                        var res = db.SaleOrderPostProcessEmbossingTables.FirstOrDefault(x => x.SaleOrderPostProcessEmbossingId == id && x.Rank == item.Rank);
                        res.EmbossingCompletedQuantity = item.EmbossingCompletedQuantity;
                        res.EmbossingWastageQuantity = item.EmbossingWastageQuantity;
                        res.EmbossingStatus = item.EmbossingStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.StartDateTime = item.StartDateTime;
                        res.EndDateTime = item.EndDateTime;
                        res.PricePerUnit = item.PricePerUnit;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageEmbossing = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.EmbossingCompleted, GlobalData.loggedInUser);
                    }
                    if (item.EmbossingStatus == PostProcessItemsStatus.InProcess)
                    {
                        var res = db.SaleOrderPostProcessEmbossingTables.FirstOrDefault(x => x.SaleOrderPostProcessEmbossingId == id && x.Rank == item.Rank);
                        res.EmbossingStatus = item.EmbossingStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageEmbossing = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.EmbossingInProcess, GlobalData.loggedInUser);
                    }

                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                }
                else
                {
                    var res = (from s in db.SaleOrderProductionEmbossingMasters
                               join a in db.SaleOrderProductionTables on s.SaleOrderProductionId equals a.SaleOrderProductionId
                               where a.SaleOrderId == item.SaleOrderId && s.Rank == item.Rank && s.Removed != true
                               select s).ToList();
                    //db.SaleOrderProductionEmbossingMasters.RemoveRange(res);
                    foreach (var rec in res)
                    {
                        rec.Removed = true;
                        rec.RemovedBy = GlobalData.loggedInUser;
                        rec.RemovedDate = DateTime.Now;
                    }

                    db.SaveChanges();
                    var orderRec = db.SaleOrderPostProcessOrderTables.FirstOrDefault(x => x.PostProcessName == "Embossing" && x.SaleOrderId == item.SaleOrderId && x.Rank == item.Rank && x.Removed != true);
                    orderRec.Removed = true;
                    db.SaveChanges();
                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                    UpdateSaleorderCode(db, transaction, item.SaleOrderId);
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Post Process Request Submitted Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                throw;
            }
        }

        public ApiFunctionResponseVm AddPostProcessVacuum(SaleOrderPostProcessVacuumTableVm item)
        {
            if (item.JumboActualList.Any(x => x.ActualQuantity < 1))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Submitted Request do not have Jumbo QTY for either one or all Jumbo Rolls");
            }
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var ExistingRecordCheck = db.SaleOrderPostProcessVacuumTables.Any(x => x.SaleOrderId == item.SaleOrderId && x.VacuumStatus == item.VacuumStatus && x.Rank == item.Rank);
                if (ExistingRecordCheck)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Duplicate record found. Refresh the page and try again.");
                }

                long id = item.SaleOrderPostProcessVacuumId;
                if (item.VacuumStatus != PostProcessItemsStatus.Remove)
                {
                    if (item.SaleOrderPostProcessVacuumId == 0)
                    {
                        SaleOrderPostProcessVacuumTable mm = new SaleOrderPostProcessVacuumTable();
                        mm.SaleOrderId = item.SaleOrderId;
                        mm.VacuumRack = item.VacuumRack;
                        mm.ReceivedQuantity = item.ReceivedQuantity;
                        mm.VacuumMeasurementUnit = item.VacuumMeasurementUnit;
                        mm.Remark = item.Remark;
                        mm.VacuumStatus = PostProcessItemsStatus.Assigned;
                        mm.AddedBy = GlobalData.loggedInUser;
                        mm.AddedDate = System.DateTime.Now;
                        mm.Rank = item.Rank;
                        mm.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaleOrderPostProcessVacuumTables.Add(mm);
                        db.SaveChanges();
                        id = mm.SaleOrderPostProcessVacuumId;
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageVacuum = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.VacuumAssigned, GlobalData.loggedInUser);
                    }
                    if (item.VacuumStatus == PostProcessItemsStatus.Completed)
                    {
                        var res = db.SaleOrderPostProcessVacuumTables.FirstOrDefault(x => x.SaleOrderPostProcessVacuumId == id && x.Rank == item.Rank);
                        res.VacuumCompletedQuantity = item.VacuumCompletedQuantity;
                        res.VacuumWastageQuantity = item.VacuumWastageQuantity;

                        res.VacuumStatus = item.VacuumStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.StartDateTime = item.StartDateTime;
                        res.EndDateTime = item.EndDateTime;
                        res.PricePerUnit = item.PricePerUnit;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageVacuum = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.VacuumCompleted, GlobalData.loggedInUser);
                    }
                    if (item.VacuumStatus == PostProcessItemsStatus.InProcess)
                    {
                        var res = db.SaleOrderPostProcessVacuumTables.FirstOrDefault(x => x.SaleOrderPostProcessVacuumId == id && x.Rank == item.Rank);
                        res.VacuumStatus = item.VacuumStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageVacuum = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.VacuumInProcess, GlobalData.loggedInUser);
                    }
                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                }
                else
                {
                    var res = (from s in db.SaleOrderProductionVacuumMasters
                               join a in db.SaleOrderProductionTables on s.SaleOrderProductionId equals a.SaleOrderProductionId
                               where a.SaleOrderId == item.SaleOrderId && s.Rank == item.Rank && s.Removed != true
                               select s).ToList();
                    //db.SaleOrderProductionVacuumMasters.RemoveRange(res);
                    foreach (var rec in res)
                    {
                        rec.Removed = true;
                        rec.RemovedBy = GlobalData.loggedInUser;
                        rec.RemovedDate = DateTime.Now;
                    }
                    db.SaveChanges();
                    var orderRec = db.SaleOrderPostProcessOrderTables.FirstOrDefault(x => x.PostProcessName == "Vaccum" && x.SaleOrderId == item.SaleOrderId && x.Rank == item.Rank && x.Removed != true);
                    orderRec.Removed = true;
                    db.SaveChanges();
                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                    UpdateSaleorderCode(db, transaction, item.SaleOrderId);
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Post Process Request Submitted Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                throw;
            }
        }

        public ApiFunctionResponseVm AddPostProcessLacquer(SaleOrderPostProcessLacquerTableVm item)
        {
            if (item.JumboActualList.Any(x => x.ActualQuantity < 1))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Submitted Request do not have Jumbo QTY for either one or all Jumbo Rolls");
            }
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var ExistingRecordCheck = db.SaleOrderPostProcessLacquerTables.Any(x => x.SaleOrderId == item.SaleOrderId && x.LacquerStatus == item.LacquerStatus && x.Rank == item.Rank);
                if (ExistingRecordCheck)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Duplicate record found. Refresh the page and try again.");
                }

                long id = item.SaleOrderPostProcessLacquerId;
                if (item.LacquerStatus != PostProcessItemsStatus.Remove)
                {
                    if (item.SaleOrderPostProcessLacquerId == 0)
                    {
                        SaleOrderPostProcessLacquerTable mm = new SaleOrderPostProcessLacquerTable();
                        mm.SaleOrderId = item.SaleOrderId;
                        mm.LacquerRack = item.LacquerRack;
                        mm.ReceivedQuantity = item.ReceivedQuantity;
                        mm.LacquerMeasurementUnit = item.LacquerMeasurementUnit;
                        mm.Remark = item.Remark;
                        mm.LacquerStatus = PostProcessItemsStatus.Assigned;
                        mm.AddedBy = GlobalData.loggedInUser;
                        mm.AddedDate = System.DateTime.Now;
                        mm.Rank = item.Rank;
                        mm.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaleOrderPostProcessLacquerTables.Add(mm);
                        db.SaveChanges();
                        id = mm.SaleOrderPostProcessLacquerId;
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageLacquer = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.LacquerAssigned, GlobalData.loggedInUser);
                    }
                    if (item.LacquerStatus == PostProcessItemsStatus.Completed)
                    {
                        var res = db.SaleOrderPostProcessLacquerTables.FirstOrDefault(x => x.SaleOrderPostProcessLacquerId == id && x.Rank == item.Rank);
                        res.LacquerCompletedQuantity = item.LacquerCompletedQuantity;
                        res.LacquerWastageQuantity = item.LacquerWastageQuantity;

                        res.LacquerStatus = item.LacquerStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.StartDateTime = item.StartDateTime;
                        res.EndDateTime = item.EndDateTime;
                        res.PricePerUnit = item.PricePerUnit;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageLacquer = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.LacquerCompleted, GlobalData.loggedInUser);
                    }
                    if (item.LacquerStatus == PostProcessItemsStatus.InProcess)
                    {
                        var res = db.SaleOrderPostProcessLacquerTables.FirstOrDefault(x => x.SaleOrderPostProcessLacquerId == id && x.Rank == item.Rank);
                        res.LacquerStatus = item.LacquerStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageLacquer = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.LacquerInProcess, GlobalData.loggedInUser);
                    }
                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                }
                else
                {
                    var res = (from s in db.SaleOrderProductionLacquerRawMaterialTables
                               join a in db.SaleOrderProductionTables on s.SaleOrderProductionId equals a.SaleOrderProductionId
                               where a.SaleOrderId == item.SaleOrderId && s.Rank == item.Rank && s.Removed != true
                               select s).ToList();
                    //db.SaleOrderProductionLacquerRawMaterialTables.RemoveRange(res);
                    foreach (var rec in res)
                    {
                        rec.Removed = true;
                        rec.RemovedBy = GlobalData.loggedInUser;
                        rec.RemovedDate = DateTime.Now;
                    }
                    db.SaveChanges();
                    var orderRec = db.SaleOrderPostProcessOrderTables.FirstOrDefault(x => x.PostProcessName == "Lacquar" && x.SaleOrderId == item.SaleOrderId && x.Rank == item.Rank && x.Removed != true);
                    orderRec.Removed = true;
                    db.SaveChanges();
                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                    UpdateSaleorderCode(db, transaction, item.SaleOrderId);
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Post Process Request Submitted Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                throw;
            }
        }

        public ApiFunctionResponseVm AddPostProcessTumbling(SaleOrderPostProcessTumblingTableVm item)
        {
            if (item.JumboActualList.Any(x => x.ActualQuantity < 1))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Submitted Request don't have Jumbo QTY for either one or all the Jumbo Rolls");
            }
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var ExistingRecordCheck = db.SaleOrderPostProcessTumblingTables.Any(x => x.SaleOrderId == item.SaleOrderId && x.TumblingStatus == item.TumblingStatus && x.Rank == item.Rank);
                if (ExistingRecordCheck)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Duplicate record found. Refresh the page and try again.");
                }
                long id = item.SaleOrderPostProcessTumblingId;
                if (item.TumblingStatus != PostProcessItemsStatus.Remove)
                {
                    if (item.SaleOrderPostProcessTumblingId == 0)
                    {
                        SaleOrderPostProcessTumblingTable mm = new SaleOrderPostProcessTumblingTable();
                        mm.SaleOrderId = item.SaleOrderId;
                        mm.TumblingRack = item.TumblingRack;
                        mm.ReceivedQuantity = item.ReceivedQuantity;
                        mm.TumblingMeasurementUnit = item.TumblingMeasurementUnit;
                        mm.Remark = item.Remark;
                        mm.TumblingStatus = PostProcessItemsStatus.Assigned;
                        mm.AddedBy = GlobalData.loggedInUser;
                        mm.AddedDate = System.DateTime.Now;
                        mm.Rank = item.Rank;
                        mm.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaleOrderPostProcessTumblingTables.Add(mm);
                        db.SaveChanges();
                        id = mm.SaleOrderPostProcessTumblingId;
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageTumbling = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.TumblingAssigned, GlobalData.loggedInUser);
                    }
                    if (item.TumblingStatus == PostProcessItemsStatus.Completed)
                    {
                        var res = db.SaleOrderPostProcessTumblingTables.FirstOrDefault(x => x.SaleOrderPostProcessTumblingId == id && x.Rank == item.Rank);
                        res.TumblingCompletedQuantity = item.TumblingCompletedQuantity;
                        res.TumblingWastageQuantity = item.TumblingWastageQuantity;
                        res.TumblingStatus = item.TumblingStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.StartDateTime = item.StartDateTime;
                        res.EndDateTime = item.EndDateTime;
                        res.PricePerUnit = item.PricePerUnit;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageTumbling = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.TumblingCompleted, GlobalData.loggedInUser);
                    }
                    if (item.TumblingStatus == PostProcessItemsStatus.InProcess)
                    {
                        var res = db.SaleOrderPostProcessTumblingTables.FirstOrDefault(x => x.SaleOrderPostProcessTumblingId == id && x.Rank == item.Rank);
                        res.TumblingStatus = item.TumblingStatus;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        res.LineNo = item.LineNo;
                        res.ShiftSupervisorWorkerId = item.ShiftSupervisorWorkerId;
                        db.SaveChanges();
                        foreach (var jumbo in item.JumboActualList)
                        {
                            var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumbo.WorkPlanJumboMasterId);
                            var wastage = jumborec.ActualQuantity - jumbo.ActualQuantity;
                            jumborec.WastageTumbling = wastage;
                            jumborec.ActualQuantity = jumbo.ActualQuantity;
                            db.SaveChanges();
                        }
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.TumblingInProcess, GlobalData.loggedInUser);
                    }
                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                }
                else
                {
                    var res = (from s in db.SaleOrderProductionTumblingMasters
                               join a in db.SaleOrderProductionTables on s.SaleOrderProductionId equals a.SaleOrderProductionId
                               where a.SaleOrderId == item.SaleOrderId && s.Rank == item.Rank && s.Removed != true
                               select s).ToList();
                    //db.SaleOrderProductionTumblingMasters.RemoveRange(res);
                    foreach (var rec in res)
                    {
                        rec.Removed = true;
                        rec.RemovedBy = GlobalData.loggedInUser;
                        rec.RemovedDate = DateTime.Now;
                    }
                    db.SaveChanges();
                    var orderRec = db.SaleOrderPostProcessOrderTables.FirstOrDefault(x => x.PostProcessName == "Tumbling" && x.SaleOrderId == item.SaleOrderId && x.Rank == item.Rank && x.Removed != true);
                    orderRec.Removed = true;
                    db.SaveChanges();
                    CheckMoveToJumboInspection(db, transaction, item.SaleOrderId, GlobalData.loggedInUser);
                    UpdateSaleorderCode(db, transaction, item.SaleOrderId);
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Post Process Request Submitted Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                throw;
            }
        }


        // public ApiFunctionResponseVm AddJumboInspection(WorkPlanJumboMasterVm item)
        // {
        //     using (var db = new Models.pmsdbContext())
        //     {
        //         var rec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId);
        //         rec.Yield = item.Yield;
        //         if (item.IsSampleJumbo != true)
        //         {
        //             rec.IsInspectionCompleted = true;
        //         }
        //         db.SaveChanges();

        //         foreach (var j in item.JumboInspection)
        //         {
        //             JumboInspectionTable data = new JumboInspectionTable
        //             {
        //                 WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
        //                 Grade = j.Grade,
        //                 Unit = j.Unit,
        //                 InspectedBy = j.InspectedBy,
        //                 Quantity = j.Quantity,
        //                 AddedBy = GlobalData.loggedInUser,
        //                 AddedDate = System.DateTime.Now
        //             };
        //             db.JumboInspectionTables.Add(data);
        //             db.SaveChanges();

        //             if (item.IsSampleJumbo == true)
        //             {
        //                 data.RollType = "SAMPLE";
        //             }
        //             else
        //             {
        //                 data.RollType = "FINISHED";
        //             }

        //             data.Code = item.JumboNo + "/" + data.JumboInspectionId;
        //             db.SaveChanges();

        //         }
        //         var recdata = db.WorkPlanJumboMasters.Where(x => x.SaleOrderId == item.SaleOrderId).ToList();
        //         if (recdata != null)
        //         {
        //             if (!recdata.Any(x => x.IsInspectionCompleted != true))
        //                 UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.MoveToDispatch, GlobalData.loggedInUser);
        //         }
        //     }
        //     return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        // }
        public ApiFunctionResponseVm CompleteJumboInspection(WorkPlanJumboMasterVm item)
        {
            using var db = new Models.pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var rec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId);
                rec.Yield = item.Yield;
                if (item.IsSampleJumbo != true)
                {
                    rec.IsInspectionCompleted = true;
                }
                db.SaveChanges();
                var recdata = db.WorkPlanJumboMasters.Where(x => x.SaleOrderId == item.SaleOrderId).ToList();
                if (recdata != null)
                {
                    if (!recdata.Any(x => x.IsInspectionCompleted != true))
                    {
                        UpdateSaleOrderStatus(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.MoveToDispatch, GlobalData.loggedInUser);

                        // Use enhanced CostingStatus validation logic
                        var consumeDataFn = new ConsumeStockProductDataFn(GlobalData);
                        consumeDataFn.UpdateCostingStatusFromSaleOrder(db, item.SaleOrderId.Value, GlobalData.loggedInUser);

                        var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnFinalInspection" && x.ConfigValue == "true")).ToList();
                        if (enableSOStatusEmail.Count == 2)
                        {
                            var emailSaleOrderStatus = SaleOrderEmailStatus.FinalInspectionCompleted;
                            _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(item.SaleOrderId.Value, emailSaleOrderStatus);
                        }
                    }
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Inspection Completed Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();

                throw;
            }
        }

        public ApiFunctionResponseVm ReOpenJumboInspection(WorkPlanJumboMasterVm item)
        {
            using var db = new Models.pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var rec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId);
                rec.IsInspectionCompleted = false;
                db.SaveChanges();

                var recdata = db.WorkPlanJumboMasters.Where(x => x.SaleOrderId == item.SaleOrderId).ToList();
                if (recdata != null)
                {
                    if (recdata.Any(x => x.IsInspectionCompleted == false))
                    {
                        if (item.SaleOrderStatus.ToString() != ESalesOrderStatus.PartialDispatchReady.ToString() && item.SaleOrderStatus.ToString() != ESalesOrderStatus.JumboInspection.ToString())
                        {
                            UpdateSaleOrderStatusModified(db, transaction, item.SaleOrderId.Value, ESalesOrderStatus.MoveToDispatch, ESalesOrderStatus.JumboInspection, GlobalData.loggedInUser);
                        }
                    }
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Final Inspection for selected Sale Order is open again.");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw;
            }
        }

        public ApiFunctionResponseVm AddJumboInspectionSingleObj(JumboInspectionTableVm item)
        {
            using var db = new Models.pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                if (item.JumboInspectionId > 0)
                {
                    var existingJumboInspection = db.JumboInspectionTables.Find(item.JumboInspectionId);

                    if (existingJumboInspection == null)
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Jumbo Inspection record not found for the provided JumboInspectionId.");
                    }

                    existingJumboInspection.Weight = item.Weight ?? existingJumboInspection.Weight;

                    db.SaveChanges();
                    transaction.Commit();

                    var resdata = new JumboInspectionTableVm
                    {
                        JumboInspectionId = existingJumboInspection.JumboInspectionId,
                        WorkPlanJumboMasterId = existingJumboInspection.WorkPlanJumboMasterId,
                        Grade = existingJumboInspection.Grade,
                        Unit = existingJumboInspection.Unit,
                        InspectedBy = existingJumboInspection.InspectedBy,
                        Quantity = existingJumboInspection.Quantity,
                        Weight = existingJumboInspection.Weight,
                        Code = existingJumboInspection.Code,
                        AddedBy = existingJumboInspection.AddedBy,
                        AddedDate = existingJumboInspection.AddedDate,
                        StockId = existingJumboInspection.StockId,
                        StoreId = item.StoreId,
                        RackId = item.RackId
                    };

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, resdata);
                }
                else
                {
                    JumboInspectionTable data = new JumboInspectionTable
                    {
                        WorkPlanJumboMasterId = item.WorkPlanJumboMasterId,
                        Grade = item.Grade,
                        Unit = item.Unit,
                        InspectedBy = item.InspectedBy,
                        Quantity = item.Quantity,
                        Weight = item.Weight,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = System.DateTime.Now
                    };
                    var RollSequence = 0;
                    var jumborec = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId);
                    var res = db.JumboInspectionTables.Where(x => x.WorkPlanJumboMasterId == item.WorkPlanJumboMasterId).OrderByDescending(x => x.JumboInspectionId).ToList();
                    if (res.Count() > 0)
                    {
                        var tempNumber = res[0].Code.Split('/');
                        if (tempNumber != null && tempNumber.Count() > 1)
                        {
                            int.TryParse(tempNumber[1], out RollSequence);
                        }
                    }
                    data.Code = jumborec.JumboNo + "/" + (RollSequence + 1);
                    if (item.IsSampleJumbo == true)
                    {
                        data.RollType = "SAMPLE";
                    }
                    else
                    {
                        data.RollType = "FINISHED";
                    }
                    db.JumboInspectionTables.Add(data);
                    db.SaveChanges();
                    
                    var companyName = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "Company Name").ConfigValue;
                    var supplier = db.SupplierMasters.FirstOrDefault(x => x.SupplierName == companyName && x.Disabled != true);
                    if (supplier == null)
                    {
                        supplier = new SupplierMaster
                        {
                            SupplierName = companyName,
                            Email = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "Company Email").ConfigValue,
                            SupplierContactNumber = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "Company Phone Number").ConfigValue,
                            Address = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "Company Address").ConfigValue
                        };
                        db.SupplierMasters.Add(supplier);
                        db.SaveChanges();
                    }
                    var addstock = new StockDataFn(GlobalData);
                    var stock = new StockProductTableMasterVm
                    {
                        // ProductId = orderFCData.FinishedProductId.Value,
                        Sku = null,
                        //Barcode = item.Barcode,
                        ManufacturedDate = DateTime.Now,
                        // ExpiryDate = addstock.GetFinishedManufacturedProductExpiryDate(orderFCData),
                        Unit = item.Unit,
                        Quantity = item.Quantity,
                        //PricePerUnit = 0,
                        Grade = item.Grade,
                        isProductionStock = true,
                        SaleOrderId = item.SaleOrderId,
                        RackId = item.RackId,
                        SupplierId = supplier.SupplierId
                    };
                    var stockid = addstock.AddStockMasterPreFn(stock, db);
                    data.StockId = stockid;

                    JumboInspectionTableVm resdata = new JumboInspectionTableVm
                    {
                        JumboInspectionId = data.JumboInspectionId,
                        WorkPlanJumboMasterId = data.WorkPlanJumboMasterId,
                        Grade = data.Grade,
                        Unit = data.Unit,
                        InspectedBy = data.InspectedBy,
                        Quantity = data.Quantity,
                        Weight = data.Weight,
                        Code = data.Code,
                        RollType = data.RollType,
                        AddedBy = data.AddedBy,
                        AddedDate = data.AddedDate,
                        StockId = data.StockId,
                        StoreId = item.StoreId,
                        RackId = item.RackId
                    };
                    db.SaveChanges();

                    transaction.Commit();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, resdata);
                }
            }
            catch (System.Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured, please contact administrator. " + ex);
                throw;
            }
        }



        public ApiFunctionResponseVm RemoveJumboInspection(JumboInspectionTableVm item)
        {
            // using (var db = new Models.pmsdbContext())
            // {
            //     var res = db.JumboInspectionTables.FirstOrDefault(x => x.JumboInspectionId == item.JumboInspectionId);
            //     db.JumboInspectionTables.Remove(res);
            //     db.SaveChanges();
            // }
            //return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");

            using (var db = new Models.pmsdbContext())
            {
                if (item.StockId != null)
                {
                    var stockData = new StockDataFn(GlobalData);
                    stockData.RemoveStock(item.StockId.Value);
                }

                // Retrieve the entity to be removed using the primary key value
                var res = db.JumboInspectionTables.Find(item.JumboInspectionId);

                // If no entity is found, return an appropriate error response
                if (res == null)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Record not found. Please refresh to see latest rolls.");
                }

                // If an entity is found, remove it
                db.JumboInspectionTables.Remove(res);
                int affectedRows = db.SaveChanges();

                // Check if the expected number of rows are affected
                if (affectedRows == 0)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.Conflict, "Requested roll was already removed by someone else. Please refresh to see latest rolls.");
                }
            }
            // Return a success response if the operation completed successfully
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Record successfully deleted.");
        }


        public ApiFunctionResponseVm AddJumboInspectionDispatchedQuantity(List<JumboInspectionTableVm> jumboinspection)
        {
            using (var db = new Models.pmsdbContext())
            {
                foreach (var item in jumboinspection)
                {
                    JumboInspectionTable data = db.JumboInspectionTables.FirstOrDefault(x => x.JumboInspectionId == item.JumboInspectionId);
                    data.DispatchedQuantity = item.DispatchedQuantity;
                    db.SaveChanges();
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public SaleOrderTableVm GetSaleOrderCostingDataById(long pid)
        {
            SaleOrderTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from s in db.SaleOrderTables
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join soc in db.SaleOrderCostingTables on s.SaleOrderId equals soc.SaleOrderId into psf
                       from soc in psf.DefaultIfEmpty()
                       where a.SaleOrderId == pid
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           SaleOrderCode = s.SaleOrderCode,
                           CostingAdded = s.CostingAdded,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,
                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               InspectionFormulationMixing = (from la in db.InspectionFormulationCodeMixingTables
                                                              join a in db.MixingMasters on la.MixingId equals a.MixingId
                                                              where la.SaleOrderId == s.SaleOrderId
                                                              select new InspectionFormulationCodeMixingTableVm
                                                              {
                                                                  FormulationCodeMixingId = la.FormulationCodeMixingId,
                                                                  SaleFormulationCodeId = la.SaleFormulationCodeId,
                                                                  AddedDate = la.AddedDate,
                                                                  AddedBy = la.AddedBy,
                                                                  MixingId = la.MixingId,
                                                                  MixingName = a.MixingName,
                                                                  CostGsm = la.CostGsm,
                                                                  CostPerKg = la.CostPerKg,
                                                                  CostPerLm = la.CostPerLm,
                                                                  MixingRawMaterial = (from op in db.InspectionFormulationCodeMixingRawMaterialTables
                                                                                       join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                       where op.FormulationCodeMixingId == la.FormulationCodeMixingId
                                                                                       select new InspectionFormulationCodeMixingRawMaterialTableVm
                                                                                       {
                                                                                           FormulationCodeMixingRawMaterialId = op.FormulationCodeMixingRawMaterialId,
                                                                                           FormulationCodeMixingId = op.FormulationCodeMixingId,
                                                                                           ProductId = op.ProductId,
                                                                                           ProductName = p.ProductName,
                                                                                           ProductCode = p.ProductCode,
                                                                                           Quantity = op.Quantity,
                                                                                           Scquantity = op.Scquantity,
                                                                                           AvgGsm = p.AvgGsm,
                                                                                           Unit = op.Unit,
                                                                                           Price = op.Price
                                                                                       }).ToList()
                                                              }).ToList(),

                           },
                           SaleOrderCosting = new SaleOrderCostingTableVm
                           {
                               SaleOrderId = s.SaleOrderId,
                               FabricCost = soc.FabricCost,
                               CoatingCost = soc.CoatingCost,
                               FabricCostLm = soc.FabricCostLm,
                               PasteCostLm = soc.PasteCostLm,
                               GrainCostLm = soc.GrainCostLm,
                               FinishingCostLm = soc.FinishingCostLm,
                               RmcostLm = soc.RmcostLm,
                               Rejection = soc.Rejection,
                               ProductionCostLm = soc.Rejection,
                               PerLmconstant = soc.PerLmconstant,
                               OverheadCost = soc.OverheadCost,
                               InlineScraping = soc.InlineScraping,
                               PrintCostPerUnit = soc.PrintCostPerUnit,
                               EmbossingCostPerUnit = soc.EmbossingCostPerUnit,
                               TumblingCostPerUnit = soc.TumblingCostPerUnit,
                               VacuumCostPerUnit = soc.VacuumCostPerUnit,
                               LacquerCostPerUnit = soc.LacquerCostPerUnit,
                               AddedBy = soc.AddedBy,
                               AddedDate = soc.AddedDate,
                               IsLiningOrder = db.LinkedSaleOrderTables.Where(x => x.LinkedSaleOrder == s.SaleOrderId).FirstOrDefault() != null,
                               IsUpperOrder = db.LinkedSaleOrderTables.Where(x => x.ParentSaleOrder == s.SaleOrderId).FirstOrDefault() != null,
                           }
                       }).FirstOrDefault();
                res.LinkedSaleOrder = res.SaleOrderCosting.IsLiningOrder ? db.LinkedSaleOrderTables.Where(x => x.LinkedSaleOrder == res.SaleOrderId).Select(x => new LinkedSaleOrderTableVm
                {
                    LinkedSaleOrder = x.LinkedSaleOrder,
                    ParentSaleOrder = x.ParentSaleOrder,
                }).ToList() : null;
                res.ParentSaleOrder = res.SaleOrderCosting.IsUpperOrder ? db.LinkedSaleOrderTables.Where(x => x.ParentSaleOrder == res.SaleOrderId).Select(x => new LinkedSaleOrderTableVm
                {
                    LinkedSaleOrder = x.LinkedSaleOrder,
                    ParentSaleOrder = x.ParentSaleOrder,
                }).ToList() : null;

            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateSaleOrderCosting(SaleOrderTableVm saleOrder)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = db.SaleOrderCostingTables.FirstOrDefault(x => x.SaleOrderId == saleOrder.SaleOrderId);
                if (res == null)
                {
                    if (saleOrder.SaleOrderCosting != null)
                    {
                        res = new SaleOrderCostingTable
                        {
                            SaleOrderId = saleOrder.SaleOrderId,
                            FabricCost = saleOrder.SaleOrderCosting.FabricCost,
                            CoatingCost = saleOrder.SaleOrderCosting.CoatingCost,
                            FabricCostLm = saleOrder.SaleOrderCosting.FabricCostLm,
                            PasteCostLm = saleOrder.SaleOrderCosting.PasteCostLm,
                            GrainCostLm = saleOrder.SaleOrderCosting.GrainCostLm,
                            FinishingCostLm = saleOrder.SaleOrderCosting.FinishingCostLm,
                            RmcostLm = saleOrder.SaleOrderCosting.RmcostLm,
                            Rejection = saleOrder.SaleOrderCosting.Rejection,
                            ProductionCostLm = saleOrder.SaleOrderCosting.Rejection,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now
                        };
                        db.SaleOrderCostingTables.Add(res);
                        db.SaveChanges();
                    }
                }
                else
                {
                    if (saleOrder.SaleOrderCosting != null)
                    {
                        res.FabricCost = saleOrder.SaleOrderCosting.FabricCost;
                        res.CoatingCost = saleOrder.SaleOrderCosting.CoatingCost;
                        res.FabricCostLm = saleOrder.SaleOrderCosting.FabricCostLm;
                        res.PasteCostLm = saleOrder.SaleOrderCosting.PasteCostLm;
                        res.GrainCostLm = saleOrder.SaleOrderCosting.GrainCostLm;
                        res.FinishingCostLm = saleOrder.SaleOrderCosting.FinishingCostLm;
                        res.RmcostLm = saleOrder.SaleOrderCosting.RmcostLm;
                        res.Rejection = saleOrder.SaleOrderCosting.Rejection;
                        res.ProductionCostLm = saleOrder.SaleOrderCosting.Rejection;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        db.SaveChanges();
                    }
                }

                foreach (var item in saleOrder.SaleOrderProduction.InspectionFormulationMixing)
                {
                    var ifm = db.InspectionFormulationCodeMixingTables.FirstOrDefault(x => x.FormulationCodeMixingId == item.FormulationCodeMixingId);
                    if (ifm != null)
                    {
                        ifm.CostPerKg = item.CostPerKg;
                        ifm.CostPerLm = item.CostPerLm;
                        ifm.CostGsm = item.CostGsm;
                        db.SaveChanges();
                        foreach (var itemraw in item.MixingRawMaterial)
                        {
                            var ifmraw = db.InspectionFormulationCodeMixingRawMaterialTables.FirstOrDefault(x => x.FormulationCodeMixingRawMaterialId == itemraw.FormulationCodeMixingRawMaterialId);
                            if (ifmraw != null)
                            {
                                ifmraw.Price = itemraw.Price;
                            }
                        }
                    }
                    db.SaveChanges();
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        private void CheckMoveToJumboInspection(pmsdbContext dbContext, IDbContextTransaction dbtransaction, long? saleOrderId, string addedBy)
        {
            var db = dbContext;
            var transaction = dbtransaction;

            var postProcessOrders = db.SaleOrderPostProcessOrderTables
                .Where(x => x.SaleOrderId == saleOrderId)
                .ToList();

            bool allProcessesCompleted = true;

            foreach (var process in postProcessOrders)
            {
                bool isProcessCompleted = IsProcessCompleted(db, saleOrderId.Value, process.Rank, process.PostProcessName);

                if (!isProcessCompleted)
                {
                    allProcessesCompleted = false;
                    break;
                }
            }

            var rec = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleOrderId);

            if (allProcessesCompleted)
            {
                rec.Status = (int)ESalesOrderStatus.JumboInspection;
                UpdateSaleOrderTimeline(db, rec.SaleOrderId, ESalesOrderStatus.JumboInspection, addedBy);
                SendProductionCompletedEmail(db, saleOrderId.Value);
            }

            db.SaveChanges();
        }

        private bool IsProcessCompleted(pmsdbContext db, long saleOrderId, int rank, string postProcessName)
        {
            switch (postProcessName.ToLower())
            {
                case "print":
                    return db.SaleOrderPostProcessPrintTables.Any(x => x.SaleOrderId == saleOrderId && x.Rank == rank && x.PrintStatus == PostProcessItemsStatus.Completed);
                case "embossing":
                    return db.SaleOrderPostProcessEmbossingTables.Any(x => x.SaleOrderId == saleOrderId && x.Rank == rank && x.EmbossingStatus == PostProcessItemsStatus.Completed);
                case "lacquar":
                    return db.SaleOrderPostProcessLacquerTables.Any(x => x.SaleOrderId == saleOrderId && x.Rank == rank && x.LacquerStatus == PostProcessItemsStatus.Completed);
                case "vaccum":
                    return db.SaleOrderPostProcessVacuumTables.Any(x => x.SaleOrderId == saleOrderId && x.Rank == rank && x.VacuumStatus == PostProcessItemsStatus.Completed);
                case "tumbling":
                    return db.SaleOrderPostProcessTumblingTables.Any(x => x.SaleOrderId == saleOrderId && x.Rank == rank && x.TumblingStatus == PostProcessItemsStatus.Completed);
                default:
                    return false;
            }
        }

        private void UpdateSaleOrderTimeline(pmsdbContext db, long saleOrderId, ESalesOrderStatus status, string addedBy)
        {
            if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == saleOrderId && x.Status == (int)status))
            {
                db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                {
                    SaleOrderId = saleOrderId,
                    Status = (int)status,
                    AddedBy = String.IsNullOrEmpty(addedBy) ? "System" : addedBy,
                    AddedDate = System.DateTime.Now,
                    WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == saleOrderId).WorkplanId
                });
                db.SaveChanges();
            }
        }

        private void SendProductionCompletedEmail(pmsdbContext db, long saleOrderId)
        {
            var enableSOStatusEmail = db.ConfigTables.Where(x =>
                (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") ||
                (x.ConfigItem == "EnableSaleOrderStatusEmailOnProductionCompleted" && x.ConfigValue == "true")
            ).ToList();

            if (enableSOStatusEmail.Count == 2)
            {
                var emailSaleOrderStatus = SaleOrderEmailStatus.ProductionCompleted;
                _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(saleOrderId, emailSaleOrderStatus);
            }
        }



        public List<JumboInspectionTableVm> GetJumboInspectionList()
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = (from j in db.JumboInspectionTables
                           join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                           select new JumboInspectionTableVm
                           {
                               JumboInspectionId = j.JumboInspectionId,
                               WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                               SaleOrderId = wj.SaleOrderId,
                               Grade = j.Grade,
                               Quantity = j.Quantity,
                               Weight = j.Weight,
                               Code = j.Code,
                               Unit = j.Unit,
                               AddedBy = j.AddedBy,
                               AddedDate = j.AddedDate,
                               DispatchedQuantity = j.DispatchedQuantity,
                               DispatchStatus = j.DispatchStatus,
                               InspectedBy = j.InspectedBy,
                               JumboDispatchId = j.JumboDispatchId,
                               RollType = j.RollType
                           }).ToList();
                return res;
            }
        }

        public List<JumboInspectionTableVm> GetJumboInspectionListWithFilter(JumboInspectionFilterVm filter)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = (from j in db.JumboInspectionTables
                           join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                           join sale in db.SaleOrderTables on wj.SaleOrderId equals sale.SaleOrderId
                           join salepro in db.SaleOrderProductionTables on sale.SaleOrderId equals salepro.SaleOrderId
                           join ifcm in db.InspectionFormulationCodeMixingTables on sale.SaleOrderId equals ifcm.SaleOrderId
                           join isfcm in db.InspectionSaleFormulationCodeMasters on ifcm.InspectionSaleFormulationCodeId equals isfcm.InspectionSaleFormulationCodeId
                           where j.DispatchStatus == null && (filter.SaleOrderId == null || sale.SaleOrderId == filter.SaleOrderId)
                           && (filter.CustomerId == null || sale.CustomerId == filter.CustomerId)
                           && (filter.SaleFormulationCodeId == null || sale.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                           && (filter.GrainId == null || salepro.GrainId == filter.GrainId)
                           && (filter.ColorId == null || salepro.ColorId == filter.ColorId)
                           && (filter.FabricColorId == null || salepro.FabricColorId == filter.FabricColorId)
                           && (filter.Thick == null || salepro.Thick == filter.Thick)
                           && (filter.Width == null || salepro.Width == filter.Width)
                           && (filter.OrderDateFrom == null || sale.SaleOrderDate >= filter.OrderDateFrom)
                           && (filter.OrderDateTo == null || sale.SaleOrderDate <= filter.OrderDateTo)
                           && (filter.Width == null || salepro.Width == filter.Width)
                           && (filter.FabricProductId == null || isfcm.FabricProductId == filter.FabricProductId)
                           && (filter.Grade == null || j.Grade == filter.Grade)
                           && (filter.RollType == null || j.RollType == filter.RollType)
                           && (filter.PrintMasterId == null || db.SaleOrderProductionPrintMasters.Any(x => x.SaleOrderProductionId == salepro.SaleOrderProductionId && x.PrintMasterId == filter.PrintMasterId && x.Removed != true))
                           && (filter.EmbossingMasterId == null || db.SaleOrderProductionEmbossingMasters.Any(x => x.SaleOrderProductionId == salepro.SaleOrderProductionId && x.EmbossingMasterId == filter.EmbossingMasterId && x.Removed != true))
                           && (filter.VacuumMasterId == null || db.SaleOrderProductionVacuumMasters.Any(x => x.SaleOrderProductionId == salepro.SaleOrderProductionId && x.VacuumMasterId == filter.VacuumMasterId && x.Removed != true))
                           && (filter.TumblingMasterId == null || db.SaleOrderProductionTumblingMasters.Any(x => x.SaleOrderProductionId == salepro.SaleOrderProductionId && x.TumblingMasterId == filter.TumblingMasterId && x.Removed != true))
                           && (filter.LacquerMasterId == null || db.SaleOrderProductionLacquerRawMaterialTables.Any(x => x.SaleOrderProductionId == salepro.SaleOrderProductionId && x.LacquerMasterId == filter.LacquerMasterId && x.Removed != true))
                           select new JumboInspectionTableVm
                           {
                               JumboInspectionId = j.JumboInspectionId,
                               WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                               SaleOrderId = wj.SaleOrderId,
                               SaleOrderCode = sale.SaleOrderCode,
                               Grade = j.Grade,
                               Quantity = j.Quantity,
                               Weight = j.Weight,
                               Code = j.Code,
                               Unit = j.Unit,
                               AddedBy = j.AddedBy,
                               AddedDate = j.AddedDate,
                               DispatchedQuantity = j.DispatchedQuantity,
                               DispatchStatus = j.DispatchStatus,
                               InspectedBy = j.InspectedBy,
                               JumboDispatchId = j.JumboDispatchId,
                               RollType = j.RollType
                           });
                return res.Distinct().OrderBy(x => x.JumboInspectionId).ToList();
            }
        }

        public ApiFunctionResponseVm AddJumboDispatch(JumboDispatchTableVm jumboDispatch)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var saleorderid = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == jumboDispatch.JumboInspection[0].WorkPlanJumboMasterId).SaleOrderId;
                        var salerec = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
                        {
                            if (saleorderid != null && saleorderid > 0)
                            {
                                if (salerec.Status == (int)ESalesOrderStatus.DispatchCompleted)
                                {
                                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Cannot add items as sale order is already dispatched");
                                }
                            }
                        }
                        JumboDispatchTable mm = new JumboDispatchTable();
                        if (jumboDispatch.JumboDispatchId > 0)
                        {
                            mm = db.JumboDispatchTables.FirstOrDefault(x => x.JumboDispatchId == jumboDispatch.JumboDispatchId);
                        }
                        mm.TransportId = jumboDispatch.TransportId;
                        mm.VehicleId = jumboDispatch.VehicleId;
                        mm.DispatchNumber = jumboDispatch.DispatchNumber;
                        mm.DispatchDate = jumboDispatch.DispatchDate;
                        mm.Barcode = jumboDispatch.Barcode;
                        mm.Weight = jumboDispatch.Weight;
                        mm.AddedBy = GlobalData.loggedInUser;
                        mm.AddedDate = System.DateTime.Now;
                        mm.IsGateIn = jumboDispatch.VehicleId > 0;
                        mm.CustomerId = jumboDispatch.CustomerId;
                        mm.DispatchQuantity = jumboDispatch.DispatchQuantity;
                        mm.StoreId = jumboDispatch.StoreId;
                        mm.RackId = jumboDispatch.RackId;
                        mm.Remark = jumboDispatch.Remark;
                        mm.PackageId = jumboDispatch.PackageId;
                        if (jumboDispatch.JumboDispatchId < 1)
                        {
                            db.JumboDispatchTables.Add(mm);
                        }
                        db.SaveChanges();
                        var yrSuf = (DateTime.Now.Month >= 4 ? DateTime.Now.Year + 1 : DateTime.Now.Year);
                        var yrPre = (DateTime.Now.Month < 4 ? DateTime.Now.Year - 1 : DateTime.Now.Year);
                        mm.PackingNumber = "ZB/PCK/" + yrPre + "-" + yrSuf + "/DP-" + mm.JumboDispatchId;
                        db.SaveChanges();
                        if (jumboDispatch.VehicleId > 0)
                        {
                            GateInTable sm = db.GateInTables.FirstOrDefault(x => x.VehicleId == jumboDispatch.VehicleId && x.GateIn == true && x.GateOut != true);
                            if (sm == null)
                            {
                                sm = new GateInTable();
                                sm.VehicleId = jumboDispatch.VehicleId.Value;
                                sm.GateInDate = System.DateTime.Now;
                                sm.GateInPerson = "System";
                                sm.GateInPersonContact = "System";
                                sm.GateIn = true;
                                sm.GateOut = false;
                                sm.AddedBy = GlobalData.loggedInUser;
                                sm.AddedDate = System.DateTime.Now;
                                sm.Type = "Order Dispatch";
                                db.GateInTables.Add(sm);
                                db.SaveChanges();
                            }
                            GateInInvoiceMappingTable gi = new GateInInvoiceMappingTable();
                            gi.JumboDispatchId = mm.JumboDispatchId;
                            gi.GateInId = sm.GateInId;
                            db.GateInInvoiceMappingTables.Add(gi);
                            db.SaveChanges();
                        }
                        if (jumboDispatch.JumboDispatchId < 1)
                        {
                            var currentDispatchRollsCount = jumboDispatch.JumboInspection.Count();
                            int currentItemIndex = 0;
                            foreach (var j in jumboDispatch.JumboInspection)
                            {
                                bool isLastItem = currentItemIndex == currentDispatchRollsCount - 1;
                                var rec = db.JumboInspectionTables.FirstOrDefault(x => x.JumboInspectionId == j.JumboInspectionId);
                                rec.JumboDispatchId = mm.JumboDispatchId;
                                rec.DispatchStatus = "Dispatched";
                                db.SaveChanges();

                                saleorderid = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == rec.WorkPlanJumboMasterId).SaleOrderId;
                                var gateInMapping = db.GateInInvoiceMappingTables.FirstOrDefault(x => x.JumboDispatchId == mm.JumboDispatchId);
                                if (gateInMapping != null && gateInMapping?.SaleOrderId == null)
                                {
                                    gateInMapping.SaleOrderId = saleorderid;
                                    db.SaveChanges();
                                }
                                else if (gateInMapping != null && gateInMapping?.SaleOrderId != saleorderid)
                                {
                                    GateInInvoiceMappingTable gi = new GateInInvoiceMappingTable();
                                    gi.SaleOrderId = saleorderid;
                                    gi.JumboDispatchId = mm.JumboDispatchId;
                                    gi.GateInId = gateInMapping.GateInId;
                                    db.GateInInvoiceMappingTables.Add(gi);
                                    db.SaveChanges();
                                }

                                var allJumInspectedRolls = (from jum in db.JumboInspectionTables
                                                            join wj in db.WorkPlanJumboMasters on jum.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                                                            where wj.SaleOrderId == saleorderid && wj.IsInspectionCompleted == true && jum.RollType != "SAMPLE"
                                                            select new JumboInspectionTableVm
                                                            {
                                                                DispatchStatus = jum.DispatchStatus
                                                            }).ToList();
                                var PendingRolls = allJumInspectedRolls.Where(x => x.DispatchStatus == null).ToList();
                                if (!db.WorkPlanJumboMasters.Any(x => x.SaleOrderId == saleorderid && x.IsInspectionCompleted != true))
                                {
                                    var allRollsIncDispatchCount = allJumInspectedRolls.Where(x => x.DispatchStatus == "Dispatched").Count();
                                    if (PendingRolls == null || (PendingRolls.Count() < 1 && allJumInspectedRolls.Count == allRollsIncDispatchCount))
                                    {
                                        salerec.Status = (int)ESalesOrderStatus.DispatchReady;
                                        db.SaveChanges();
                                        var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnDispatchReady" && x.ConfigValue == "true")).ToList();
                                        if (enableSOStatusEmail.Count == 2)
                                        {
                                            var emailSaleOrderStatus = SaleOrderEmailStatus.ReadyToDispatch;
                                            _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(saleorderid.Value, emailSaleOrderStatus);
                                        }
                                        if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == salerec.SaleOrderId && x.Status == (int)ESalesOrderStatus.DispatchReady))
                                        {
                                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                            {
                                                SaleOrderId = salerec.SaleOrderId,
                                                Status = (int)ESalesOrderStatus.DispatchReady,
                                                AddedBy = GlobalData.loggedInUser,
                                                AddedDate = System.DateTime.Now
                                            });
                                            db.SaveChanges();
                                        }
                                    }
                                }
                                if (salerec.Status != (int)ESalesOrderStatus.DispatchReady && salerec.Status != (int)ESalesOrderStatus.PartialDispatchReady && currentDispatchRollsCount < allJumInspectedRolls.Count && isLastItem)
                                {
                                    salerec.Status = (int)ESalesOrderStatus.PartialDispatchReady;
                                    db.SaveChanges();
                                    var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnPartialDispatchReady" && x.ConfigValue == "true")).ToList();
                                    if (enableSOStatusEmail.Count == 2)
                                    {
                                        var emailSaleOrderStatus = SaleOrderEmailStatus.PartialDispatchReady;
                                        _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(saleorderid.Value, emailSaleOrderStatus);
                                    }
                                    if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == salerec.SaleOrderId && x.Status == (int)ESalesOrderStatus.PartialDispatchReady))
                                    {
                                        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                        {
                                            SaleOrderId = salerec.SaleOrderId,
                                            Status = (int)ESalesOrderStatus.PartialDispatchReady,
                                            AddedBy = GlobalData.loggedInUser,
                                            AddedDate = System.DateTime.Now
                                        });
                                        db.SaveChanges();
                                    }
                                }
                                currentItemIndex++;
                            }
                        }
                        transaction.Commit();

                        var addstock = new StockDataFn(GlobalData);
                        var stock = new StockProductTableMasterVm
                        {
                            ProductId = 0,
                            Sku = null,
                            Barcode = jumboDispatch.Barcode,
                            ManufacturedDate = DateTime.Now,
                            ExpiryDate = null,
                            //Unit = 0,
                            Quantity = jumboDispatch.DispatchQuantity,
                            //PricePerUnit = 0,
                            //Grade = 0,
                            isProductionStock = true,
                            SaleOrderId = saleorderid
                        };
                        addstock.AddStockMaster(stock);

                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Packaging List Created Successfully.");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();

                        throw;
                    }
                }
            }
        }
        public ApiFunctionResponseVm JumboDispatchEdit(JumboDispatchTableVm jumboDispatch)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        if (jumboDispatch.JumboDispatchId > 0)
                        {
                            JumboDispatchTable JDispatch = db.JumboDispatchTables.FirstOrDefault(x => x.JumboDispatchId == jumboDispatch.JumboDispatchId);

                            var DispatchQty = JDispatch.DispatchQuantity;
                            DispatchQty = DispatchQty + jumboDispatch.DispatchQuantity;
                            JDispatch.DispatchQuantity = DispatchQty;

                            decimal DispatchWeight = decimal.Parse(JDispatch.Weight.ToString());
                            DispatchWeight = DispatchWeight + decimal.Parse(jumboDispatch.Weight.ToString());
                            JDispatch.Weight = DispatchWeight.ToString();

                            db.SaveChanges();
                            var currentDispatchRollsCount = jumboDispatch.JumboInspection.Count();
                            int currentItemIndex = 0;
                            foreach (var j in jumboDispatch.JumboInspection)
                            {
                                bool isLastItem = currentItemIndex == currentDispatchRollsCount - 1;
                                var InspectionRecords = db.JumboInspectionTables.FirstOrDefault(x => x.JumboInspectionId == j.JumboInspectionId);
                                InspectionRecords.JumboDispatchId = JDispatch.JumboDispatchId;
                                InspectionRecords.DispatchStatus = "Dispatched";
                                db.SaveChanges();
                                var saleorderid = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == InspectionRecords.WorkPlanJumboMasterId).SaleOrderId;
                                var salerec = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
                                var allJumInspectedRolls = (from jum in db.JumboInspectionTables
                                                            join wj in db.WorkPlanJumboMasters on jum.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                                                            where wj.SaleOrderId == saleorderid && wj.IsInspectionCompleted == true && jum.RollType != "SAMPLE"
                                                            select new JumboInspectionTableVm
                                                            {
                                                                DispatchStatus = jum.DispatchStatus
                                                            }).ToList();
                                var PendingRolls = allJumInspectedRolls.Where(x => x.DispatchStatus == null).ToList();
                                if (!db.WorkPlanJumboMasters.Any(x => x.SaleOrderId == saleorderid && x.IsInspectionCompleted != true))
                                {
                                    var allRollsIncDispatchCount = allJumInspectedRolls.Where(x => x.DispatchStatus == "Dispatched").Count();
                                    if (PendingRolls == null || (PendingRolls.Count() < 1 && allJumInspectedRolls.Count == allRollsIncDispatchCount))
                                    {
                                        salerec.Status = (int)ESalesOrderStatus.DispatchReady;
                                        db.SaveChanges();
                                        var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnDispatchReady" && x.ConfigValue == "true")).ToList();
                                        if (enableSOStatusEmail.Count == 2)
                                        {
                                            var emailSaleOrderStatus = SaleOrderEmailStatus.ReadyToDispatch;
                                            _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(saleorderid.Value, emailSaleOrderStatus);
                                        }
                                        if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == salerec.SaleOrderId && x.Status == (int)ESalesOrderStatus.DispatchReady))
                                        {
                                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                            {
                                                SaleOrderId = salerec.SaleOrderId,
                                                Status = (int)ESalesOrderStatus.DispatchReady,
                                                AddedBy = GlobalData.loggedInUser,
                                                AddedDate = System.DateTime.Now
                                            });
                                            db.SaveChanges();
                                        }
                                    }
                                }
                                if (salerec.Status != (int)ESalesOrderStatus.DispatchReady && salerec.Status != (int)ESalesOrderStatus.PartialDispatchReady && currentDispatchRollsCount < allJumInspectedRolls.Count && isLastItem)
                                {
                                    salerec.Status = (int)ESalesOrderStatus.PartialDispatchReady;
                                    db.SaveChanges();
                                    var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnDispatchReady" && x.ConfigValue == "true")).ToList();
                                    if (enableSOStatusEmail.Count == 2)
                                    {
                                        var emailSaleOrderStatus = SaleOrderEmailStatus.PartialDispatchReady;
                                        _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(saleorderid.Value, emailSaleOrderStatus);
                                    }
                                    if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == salerec.SaleOrderId && x.Status == (int)ESalesOrderStatus.PartialDispatchReady))
                                    {
                                        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                        {
                                            SaleOrderId = salerec.SaleOrderId,
                                            Status = (int)ESalesOrderStatus.PartialDispatchReady,
                                            AddedBy = GlobalData.loggedInUser,
                                            AddedDate = System.DateTime.Now
                                        });
                                        db.SaveChanges();
                                    }
                                }
                                currentItemIndex++;
                            }
                            transaction.Commit();
                            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Item Added Successfully");
                        }
                        else
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Dispatch Id Not Found");
                        }
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public ApiFunctionResponseVm JumboDispatchRemoveRoll(JumboDispatchTableVm jumboDispatch)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        if (jumboDispatch.JumboDispatchId > 0)
                        {
                            decimal removeQty = 0;
                            decimal removeWeight = 0;
                            foreach (var item in jumboDispatch.JumboInspection)
                            {
                                removeQty += item.Quantity.Value;
                                if (item.Weight != null)
                                {
                                    removeWeight += item.Weight.Value;
                                }
                            }
                            JumboDispatchTable JDispatch = db.JumboDispatchTables.FirstOrDefault(x => x.JumboDispatchId == jumboDispatch.JumboDispatchId);

                            var DispatchQty = JDispatch.DispatchQuantity;
                            DispatchQty = DispatchQty - removeQty;
                            JDispatch.DispatchQuantity = DispatchQty;

                            decimal DispatchWeight = decimal.Parse(JDispatch.Weight);
                            DispatchWeight = DispatchWeight - removeWeight;
                            JDispatch.Weight = DispatchWeight.ToString();

                            db.SaveChanges();

                            foreach (var j in jumboDispatch.JumboInspection)
                            {
                                var InspectionRecords = db.JumboInspectionTables.FirstOrDefault(x => x.JumboInspectionId == j.JumboInspectionId);
                                InspectionRecords.JumboDispatchId = null;
                                InspectionRecords.DispatchStatus = null;
                                db.SaveChanges();

                                var saleorderid = db.WorkPlanJumboMasters.FirstOrDefault(x => x.WorkPlanJumboMasterId == InspectionRecords.WorkPlanJumboMasterId).SaleOrderId;

                                var resjum = (from jum in db.JumboInspectionTables
                                              join wj in db.WorkPlanJumboMasters on jum.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                                              where jum.DispatchStatus != null && wj.SaleOrderId == saleorderid && wj.IsInspectionCompleted == true
                                              select jum).ToList();

                                if (resjum == null)
                                {
                                    var salerec = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
                                    salerec.Status = (int)ESalesOrderStatus.MoveToDispatch;
                                    db.SaveChanges();

                                    var Result = db.SaleOrderTimelineTables.FirstOrDefault(x => x.SaleOrderId == salerec.SaleOrderId && x.Status == (int)ESalesOrderStatus.DispatchCompleted);
                                    if (Result != null)
                                        db.SaleOrderTimelineTables.Remove(Result);
                                    db.SaveChanges();
                                    if (JDispatch.IsGateIn.Value)
                                    {
                                        var GateInMapping = db.GateInInvoiceMappingTables.FirstOrDefault(x => x.JumboDispatchId == jumboDispatch.JumboDispatchId);
                                        db.GateInInvoiceMappingTables.Remove(GateInMapping);
                                        db.SaveChanges();

                                        JDispatch.VehicleId = null;
                                        JDispatch.TransportId = 0;
                                        JDispatch.IsGateIn = false;
                                        db.SaveChanges();

                                    }
                                    JDispatch.DispatchNumber = null;
                                    JDispatch.Barcode = null;
                                    JDispatch.Weight = null;
                                    JDispatch.Remark = null;
                                    JDispatch.CustomerId = null;
                                    JDispatch.StoreId = null;
                                    JDispatch.RackId = null;
                                    db.SaveChanges();
                                }
                            }
                            transaction.Commit();
                            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Selected roll removed successfully");
                        }
                        else
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Dispatch Id Not Found");
                        }
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
        public List<JumboDispatchListVm> GetJumboDispatchList()
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = (from jumboDispatch in db.JumboDispatchTables
                           select new JumboDispatchListVm
                           {
                               JumboDispatchId = jumboDispatch.JumboDispatchId,
                               PackingNumber = jumboDispatch.PackingNumber,
                               TransportId = jumboDispatch.TransportId,
                               TransportName = db.TransportCompanyMasters.Where(x => x.TransportId == jumboDispatch.TransportId).FirstOrDefault().TransportCompanyName,
                               VehicleId = jumboDispatch.VehicleId,
                               VehicleNumber = db.TransportVehicleTables.Where(x => x.VehicleId == jumboDispatch.VehicleId).FirstOrDefault().VehicleNumber,
                               DispatchNumber = jumboDispatch.DispatchNumber,
                               DispatchDate = jumboDispatch.DispatchDate,
                               Barcode = jumboDispatch.Barcode,
                               Weight = jumboDispatch.Weight,
                               AddedBy = jumboDispatch.AddedBy,
                               AddedDate = jumboDispatch.AddedDate,
                               IsGateIn = jumboDispatch.IsGateIn,
                               IsGateOut = jumboDispatch.IsGateOut,
                               Remark = jumboDispatch.Remark,
                               CustomerDetails = (from cus in db.CustomerMasters
                                                  where cus.CustomerId == jumboDispatch.CustomerId
                                                  select new CustomerMasterVm
                                                  {
                                                      CustomerId = cus.CustomerId,
                                                      CustomerName = cus.CustomerName,
                                                      CustomerContactNumber = cus.CustomerContactNumber,
                                                      Address = cus.Address,
                                                      State = cus.State,
                                                      Country = cus.Country,
                                                      Email = cus.Email,
                                                      Gstnumber = cus.Gstnumber
                                                  }).ToList(),
                               DispatchQuantity = jumboDispatch.DispatchQuantity,
                               StoreId = jumboDispatch.StoreId,
                               StoreName = db.StoreMasters.Where(x => x.StoreId == jumboDispatch.StoreId).FirstOrDefault().StoreName,
                               RackId = jumboDispatch.RackId,
                               RackName = db.RackMasters.Where(x => x.RackId == jumboDispatch.RackId).FirstOrDefault().RackName,
                               PackageId = jumboDispatch.PackageId,
                               JumboInspection = (from j in db.JumboInspectionTables
                                                  join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                                                  join so in db.SaleOrderTables on wj.SaleOrderId equals so.SaleOrderId
                                                  join sfcm in db.SaleFormulationCodeMasters on so.SaleFormulationCodeId equals sfcm.SaleFormulationCodeId
                                                  join th in db.ThicknessMasters on sfcm.ThicknessId equals th.ThicknessId
                                                  where j.JumboDispatchId == jumboDispatch.JumboDispatchId
                                                  select new JumboInspectionTableVm
                                                  {
                                                      JumboInspectionId = j.JumboInspectionId,
                                                      WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                                                      SaleOrderId = wj.SaleOrderId,
                                                      Grade = j.Grade,
                                                      Quantity = j.Quantity,
                                                      Weight = j.Weight,
                                                      Code = j.Code,
                                                      Unit = j.Unit,
                                                      ThicknessNumber = th.ThicknessNumber,
                                                      AddedBy = j.AddedBy,
                                                      AddedDate = j.AddedDate,
                                                      DispatchedQuantity = j.DispatchedQuantity,
                                                      DispatchStatus = j.DispatchStatus,
                                                      InspectedBy = j.InspectedBy,
                                                      JumboDispatchId = j.JumboDispatchId,
                                                      RollType = j.RollType
                                                  }).ToList(),
                           }).OrderByDescending(x => x.JumboDispatchId).ToList();
                return res;
            }

        }
        public List<JumboDispatchListVm> GetJumboDispatchListWithFilter(JumboDispatchListFilterVm filter)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Base query with filters
                var baseQuery = db.JumboDispatchTables
                    .Where(jumboDispatch =>
                        (filter.TransportId == 0 || jumboDispatch.TransportId == filter.TransportId) &&
                        (filter.VehicleId == 0 || jumboDispatch.VehicleId == filter.VehicleId) &&
                        (filter.CustomerId == 0 || jumboDispatch.CustomerId == filter.CustomerId) &&
                        (filter.DispatchDateFrom == null || jumboDispatch.DispatchDate >= filter.DispatchDateFrom) &&
                        (filter.DispatchDateTo == null || jumboDispatch.DispatchDate <= filter.DispatchDateTo) &&
                        (String.IsNullOrEmpty(filter.PackingNumber) || jumboDispatch.PackingNumber.ToLower().Contains(filter.PackingNumber.ToLower()))
                    );

                // Apply sale order related filters
                if (!string.IsNullOrEmpty(filter.JumboNumber) ||
                    !string.IsNullOrEmpty(filter.RollCode) ||
                    !string.IsNullOrEmpty(filter.SaleOrderNumber) ||
                    filter.SaleFormulationCodeId != 0)
                {
                    baseQuery = baseQuery.Where(jd =>
                        db.JumboInspectionTables.Any(jit =>
                            jit.JumboDispatchId == jd.JumboDispatchId &&
                            db.WorkPlanJumboMasters.Any(wpjm =>
                                wpjm.WorkPlanJumboMasterId == jit.WorkPlanJumboMasterId &&
                                (string.IsNullOrEmpty(filter.JumboNumber) || wpjm.JumboNo == filter.JumboNumber) &&
                                db.SaleOrderTables.Any(so =>
                                    so.SaleOrderId == wpjm.SaleOrderId &&
                                    (string.IsNullOrEmpty(filter.SaleOrderNumber) || so.SaleOrderNumber == filter.SaleOrderNumber) &&
                                    (filter.SaleFormulationCodeId == 0 || so.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                                )
                            ) &&
                            (string.IsNullOrEmpty(filter.RollCode) || jit.Code == filter.RollCode)
                        )
                    );
                }

                // Main query with grouping to prevent duplicates
                var res = (from jumboDispatch in baseQuery
                           join tc in db.TransportCompanyMasters on jumboDispatch.TransportId equals tc.TransportId into tcd
                           from tc in tcd.DefaultIfEmpty()
                           join tv in db.TransportVehicleTables on jumboDispatch.VehicleId equals tv.VehicleId into tvd
                           from tv in tvd.DefaultIfEmpty()
                           join st in db.StoreMasters on jumboDispatch.StoreId equals st.StoreId
                           join rk in db.RackMasters on jumboDispatch.RackId equals rk.RackId
                           join cm in db.CustomerMasters on jumboDispatch.CustomerId equals cm.CustomerId
                           group new { jumboDispatch, tc, tv, st, rk, cm } by new
                           {
                               jumboDispatch.JumboDispatchId,
                               jumboDispatch.PackingNumber,
                               jumboDispatch.TransportId,
                               jumboDispatch.VehicleId,
                               jumboDispatch.DispatchNumber,
                               jumboDispatch.DispatchDate,
                               jumboDispatch.Barcode,
                               jumboDispatch.Weight,
                               jumboDispatch.AddedBy,
                               jumboDispatch.Remark,
                               jumboDispatch.CustomerId,
                               jumboDispatch.DispatchQuantity,
                               jumboDispatch.StoreId,
                               jumboDispatch.RackId,
                               jumboDispatch.IsGateIn,
                               jumboDispatch.IsGateOut
                           } into grp
                           select new JumboDispatchListVm
                           {
                               JumboDispatchId = grp.Key.JumboDispatchId,
                               PackingNumber = grp.Key.PackingNumber,
                               TransportId = grp.Key.TransportId,
                               TransportName = grp.FirstOrDefault().tc.TransportCompanyName,
                               VehicleId = grp.Key.VehicleId,
                               VehicleNumber = grp.FirstOrDefault().tv.VehicleNumber,
                               DispatchNumber = grp.Key.DispatchNumber,
                               DispatchDate = grp.Key.DispatchDate,
                               Barcode = grp.Key.Barcode,
                               Weight = grp.Key.Weight,
                               AddedBy = grp.Key.AddedBy,
                               Remark = grp.Key.Remark,
                               CustomerId = grp.Key.CustomerId,
                               DispatchQuantity = grp.Key.DispatchQuantity,
                               StoreId = grp.Key.StoreId,
                               StoreName = grp.FirstOrDefault().st.StoreName,
                               RackId = grp.Key.RackId,
                               RackName = grp.FirstOrDefault().rk.RackName,
                               IsGateIn = grp.Key.IsGateIn,
                               IsGateOut = grp.Key.IsGateOut,
                               CustomerDetails = grp.Select(x => new CustomerMasterVm
                               {
                                   CustomerId = x.cm.CustomerId,
                                   CustomerName = x.cm.CustomerName,
                                   Email = x.cm.Email
                               }).ToList()
                           }).ToList();

                // Get inspection details if main results exist
                if (res.Any())
                {
                    var dispatchidlist = res.Select(x => x.JumboDispatchId).Distinct().ToList();
                    var data = (from j in db.JumboInspectionTables
                                join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                                join so in db.SaleOrderTables on wj.SaleOrderId equals so.SaleOrderId
                                where dispatchidlist.Contains(j.JumboDispatchId.Value)
                                select new JumboInspectionTableVm
                                {
                                    JumboInspectionId = j.JumboInspectionId,
                                    WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                                    SaleOrderId = so.SaleOrderId,
                                    SaleOrderNumber = so.SaleOrderNumber,
                                    Grade = j.Grade,
                                    Quantity = j.Quantity,
                                    Weight = j.Weight,
                                    Code = j.Code,
                                    JumboDispatchId = j.JumboDispatchId,
                                    RollType = j.RollType,
                                    DispatchStatus = j.DispatchStatus,
                                    SaleOrderStatus = (ESalesOrderStatus)so.Status
                                }).OrderBy(x => x.SaleOrderId).ThenBy(x => x.JumboInspectionId).ToList();
                    foreach (var item in res)
                    {
                        item.JumboInspection = data.Where(x => x.JumboDispatchId == item.JumboDispatchId).ToList();
                    }
                }
                return res.OrderByDescending(x => x.JumboDispatchId).ToList();
            }
        }
        public List<JumboDispatchListVm> GetPackingNumbers()
        {
            List<JumboDispatchListVm> PackingNumbers = new List<JumboDispatchListVm>();
            using (var db = new Models.pmsdbContext())
            {
                PackingNumbers = (from jumboDispatch in db.JumboDispatchTables
                                  select new JumboDispatchListVm
                                  {
                                      PackingNumber = jumboDispatch.PackingNumber,
                                      JumboDispatchId = jumboDispatch.JumboDispatchId
                                  }).ToList();
            }
            return PackingNumbers;
        }
        public JumboDispatchListVm GetJumboDispatchListByDispatchId(long dispatchId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = (from jumboDispatch in db.JumboDispatchTables
                           join tcm in db.TransportCompanyMasters on jumboDispatch.TransportId equals tcm.TransportId into tcmd
                           from tcm in tcmd.DefaultIfEmpty()
                           join tvm in db.TransportVehicleTables on jumboDispatch.VehicleId equals tvm.VehicleId into tvmd
                           from tvm in tvmd.DefaultIfEmpty()
                           where jumboDispatch.JumboDispatchId == dispatchId
                           select new JumboDispatchListVm
                           {
                               JumboDispatchId = jumboDispatch.JumboDispatchId,
                               PackingNumber = jumboDispatch.PackingNumber,
                               TransportId = jumboDispatch.TransportId,
                               TransportName = tcm.TransportCompanyName,
                               VehicleId = jumboDispatch.VehicleId,
                               VehicleNumber = tvm.VehicleNumber,
                               DispatchNumber = jumboDispatch.DispatchNumber,
                               DispatchDate = jumboDispatch.DispatchDate,
                               Barcode = jumboDispatch.Barcode,
                               Weight = jumboDispatch.Weight,
                               Remark = jumboDispatch.Remark,
                               AddedBy = jumboDispatch.AddedBy,
                               AddedDate = jumboDispatch.AddedDate,
                               IsGateIn = jumboDispatch.IsGateIn,
                               IsGateOut = jumboDispatch.IsGateOut,
                               CustomerDetails = (from cus in db.CustomerMasters
                                                  where cus.CustomerId == jumboDispatch.CustomerId
                                                  select new CustomerMasterVm
                                                  {
                                                      CustomerId = cus.CustomerId,
                                                      CustomerName = cus.CustomerName,
                                                      CustomerContactNumber = cus.CustomerContactNumber,
                                                      Address = cus.Address,
                                                      State = cus.State,
                                                      Country = cus.Country,
                                                      Email = cus.Email,
                                                      Gstnumber = cus.Gstnumber
                                                  }).ToList(),
                               DispatchQuantity = jumboDispatch.DispatchQuantity,
                               PackageId = jumboDispatch.PackageId,
                               //JumboInspection = (from j in db.JumboInspectionTables
                               //                   join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                               //                   join so in db.SaleOrderTables on wj.SaleOrderId equals so.SaleOrderId
                               //                   join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                               //                   join th in db.ThicknessMasters on sop.Thick equals th.ThicknessId
                               //                   where j.JumboDispatchId == jumboDispatch.JumboDispatchId

                               //                   select new JumboInspectionTableVm
                               //                   {
                               //                       JumboInspectionId = j.JumboInspectionId,
                               //                       WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                               //                       SaleOrderId = wj.SaleOrderId,
                               //                       Grade = j.Grade,
                               //                       Quantity = j.Quantity,
                               //                       Weight = j.Weight,
                               //                       Code = j.Code,
                               //                       Unit = j.Unit,
                               //                       ThicknessNumber = th.ThicknessNumber,
                               //                       AddedBy = j.AddedBy,
                               //                       AddedDate = j.AddedDate,
                               //                       DispatchedQuantity = j.DispatchedQuantity,
                               //                       DispatchStatus = j.DispatchStatus,
                               //                       InspectedBy = j.InspectedBy,
                               //                       JumboDispatchId = j.JumboDispatchId,
                               //                       RollType = j.RollType,
                               //                       SaleOrderCode = so.SaleOrderCode
                               //                   }).ToList(),
                               //JumboList = (from model in db.WorkPlanJumboMasters
                               //             from jit in db.JumboInspectionTables.Where(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.JumboDispatchId == dispatchId).ToList()
                               //             where model.IsInspectionCompleted == true || model.IsInspectionCompleted != true
                               //             && jit.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId
                               //             //where model.IsInspectionCompleted == true && db.JumboInspectionTables.Any(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.JumboDispatchId == dispatchId)

                               //             select new WorkPlanJumboMasterVm
                               //             {
                               //                 WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                               //                 JumboRollDate = model.JumboRollDate,
                               //                 JumboRollStartTime = model.JumboRollStartTime,
                               //                 JumboRollEndTime = model.JumboRollEndTime,
                               //                 JumboNo = model.JumboNo,
                               //                 SaleOrderCode = db.SaleOrderTables.Where(x => x.SaleOrderId == model.SaleOrderId).FirstOrDefault().SaleOrderCode,
                               //                 SaleOrderManufacturingProductName = db.SaleOrderProductionTables.Where(x => x.SaleOrderId == model.SaleOrderId).FirstOrDefault().ManufacturingProductName,
                               //                 Rate = model.Rate,
                               //                 Amount = model.Amount,
                               //                 JumboRolQty = model.JumboRolQty,
                               //                 ActualQuantity = model.ActualQuantity,
                               //                 WastageEmbossing = model.WastageEmbossing,
                               //                 WastageLacquer = model.WastageLacquer,
                               //                 WastagePrint = model.WastagePrint,
                               //                 WastageTumbling = model.WastageTumbling,
                               //                 WastageVacuum = model.WastageVacuum,
                               //                 Weight = model.Weight,
                               //                 RackId = model.RackId,
                               //                 StoreId = model.StoreId,
                               //                 RackCode = model.RackCode,
                               //                 RackName = model.RackName,
                               //                 StoreCode = model.StoreCode,
                               //                 StoreName = model.StoreName,
                               //                 Remark = model.Remark,
                               //                 AddedBy = model.AddedBy,
                               //                 AddedDate = model.AddedDate,
                               //                 IsInspectionCompleted = model.IsInspectionCompleted,
                               //                 Yield = model.Yield,
                               //                 Grade = jit.Grade,
                               //                 JumboInspection = (from j in db.JumboInspectionTables
                               //                                    join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                               //                                    join so in db.SaleOrderTables on wj.SaleOrderId equals so.SaleOrderId
                               //                                    join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                               //                                    join th in db.ThicknessMasters on sop.Thick equals th.ThicknessId
                               //                                    where j.JumboDispatchId == jumboDispatch.JumboDispatchId
                               //                                   && j.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId
                               //                                   && j.Grade == jit.Grade
                               //                                   && j.WorkPlanJumboMasterId == jit.WorkPlanJumboMasterId
                               //                                   // && j.JumboInspectionId == jit.JumboInspectionId
                               //                                   && j.JumboDispatchId == jit.JumboDispatchId
                               //                                   && j.JumboDispatchId == dispatchId
                               //                                    select new JumboInspectionTableVm
                               //                                    {
                               //                                        JumboInspectionId = j.JumboInspectionId,
                               //                                        WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                               //                                        SaleOrderId = wj.SaleOrderId,
                               //                                        Grade = j.Grade,
                               //                                        Quantity = j.Quantity,
                               //                                        Weight = j.Weight,
                               //                                        Code = j.Code,
                               //                                        Unit = j.Unit,
                               //                                        ThicknessNumber = th.ThicknessNumber,
                               //                                        AddedBy = j.AddedBy,
                               //                                        AddedDate = j.AddedDate,
                               //                                        DispatchedQuantity = j.DispatchedQuantity,
                               //                                        DispatchStatus = j.DispatchStatus,
                               //                                        InspectedBy = j.InspectedBy,
                               //                                        JumboDispatchId = j.JumboDispatchId,
                               //                                        RollType = j.RollType
                               //                                    }).ToList(),
                               //             }).OrderByDescending(x => x.WorkPlanJumboMasterId).ToList(),
                           }).FirstOrDefault();

                res.JumboInspection = (from j in db.JumboInspectionTables
                                       join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                                       join so in db.SaleOrderTables on wj.SaleOrderId equals so.SaleOrderId
                                       join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                                       join th in db.ThicknessMasters on sop.Thick equals th.ThicknessId
                                       where j.JumboDispatchId == res.JumboDispatchId

                                       select new JumboInspectionTableVm
                                       {
                                           JumboInspectionId = j.JumboInspectionId,
                                           WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                                           SaleOrderId = wj.SaleOrderId,
                                           Grade = j.Grade,
                                           Quantity = j.Quantity,
                                           Weight = j.Weight,
                                           Code = j.Code,
                                           Unit = j.Unit,
                                           ThicknessNumber = th.ThicknessNumber,
                                           AddedBy = j.AddedBy,
                                           AddedDate = j.AddedDate,
                                           DispatchedQuantity = j.DispatchedQuantity,
                                           DispatchStatus = j.DispatchStatus,
                                           InspectedBy = j.InspectedBy,
                                           JumboDispatchId = j.JumboDispatchId,
                                           RollType = j.RollType,
                                           SaleOrderCode = so.SaleOrderCode
                                       }).OrderBy(x => x.JumboInspectionId).ToList();

                var uniqueSaleOrders = res.JumboInspection.Select(x => x.SaleOrderId).Distinct().ToList();
                var linkedSaleOrderDataList = new List<SaleOrderTableVm>();
                foreach (var saleOrderId in uniqueSaleOrders)
                {
                    var checklink = db.LinkedSaleOrderTables.Where(x => x.ParentSaleOrder == saleOrderId).FirstOrDefault();
                    if (checklink != null)
                    {
                        linkedSaleOrderDataList.Add(GetSaleOrderDataByIdForGradePrint(checklink.ParentSaleOrder.Value));
                    }
                }
                if (linkedSaleOrderDataList.Count > 0)
                {
                    foreach (var item in res.JumboInspection)
                    {
                        if (linkedSaleOrderDataList.Any(x => x.SaleOrderId == item.SaleOrderId))
                        {
                            item.ThicknessNumber = linkedSaleOrderDataList.Where(x => x.SaleOrderId == item.SaleOrderId).Select(y => y.SaleOrderProduction.ThicknessValue).FirstOrDefault();
                        }
                    }
                }
                res.JumboList = (from model in db.WorkPlanJumboMasters
                                 from jit in db.JumboInspectionTables.Where(x => x.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId && x.JumboDispatchId == dispatchId).ToList()
                                 where model.IsInspectionCompleted == true || model.IsInspectionCompleted != true
                                 && jit.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId

                                 select new WorkPlanJumboMasterVm
                                 {
                                     WorkPlanJumboMasterId = model.WorkPlanJumboMasterId,
                                     JumboRollDate = model.JumboRollDate,
                                     JumboRollStartTime = model.JumboRollStartTime,
                                     JumboRollEndTime = model.JumboRollEndTime,
                                     JumboNo = model.JumboNo,
                                     SaleOrderCode = db.SaleOrderTables.Where(x => x.SaleOrderId == model.SaleOrderId).FirstOrDefault().SaleOrderCode,
                                     SaleOrderManufacturingProductName = db.SaleOrderProductionTables.Where(x => x.SaleOrderId == model.SaleOrderId).FirstOrDefault().ManufacturingProductName,
                                     Rate = model.Rate,
                                     Amount = model.Amount,
                                     JumboRolQty = model.JumboRolQty,
                                     ActualQuantity = model.ActualQuantity,
                                     WastageEmbossing = model.WastageEmbossing,
                                     WastageLacquer = model.WastageLacquer,
                                     WastagePrint = model.WastagePrint,
                                     WastageTumbling = model.WastageTumbling,
                                     WastageVacuum = model.WastageVacuum,
                                     Weight = model.Weight,
                                     RackId = model.RackId,
                                     StoreId = model.StoreId,
                                     RackCode = model.RackCode,
                                     RackName = model.RackName,
                                     StoreCode = model.StoreCode,
                                     StoreName = model.StoreName,
                                     Remark = model.Remark,
                                     AddedBy = model.AddedBy,
                                     AddedDate = model.AddedDate,
                                     IsInspectionCompleted = model.IsInspectionCompleted,
                                     Yield = model.Yield,
                                     Grade = jit.Grade,
                                     JumboInspection = (from j in db.JumboInspectionTables
                                                        join wj in db.WorkPlanJumboMasters on j.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                                                        join so in db.SaleOrderTables on wj.SaleOrderId equals so.SaleOrderId
                                                        join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                                                        join th in db.ThicknessMasters on sop.Thick equals th.ThicknessId
                                                        where j.JumboDispatchId == res.JumboDispatchId
                                                       && j.WorkPlanJumboMasterId == model.WorkPlanJumboMasterId
                                                       && j.Grade == jit.Grade
                                                       && j.WorkPlanJumboMasterId == jit.WorkPlanJumboMasterId
                                                       // && j.JumboInspectionId == jit.JumboInspectionId
                                                       && j.JumboDispatchId == jit.JumboDispatchId
                                                       && j.JumboDispatchId == dispatchId
                                                        select new JumboInspectionTableVm
                                                        {
                                                            JumboInspectionId = j.JumboInspectionId,
                                                            WorkPlanJumboMasterId = j.WorkPlanJumboMasterId,
                                                            SaleOrderId = wj.SaleOrderId,
                                                            Grade = j.Grade,
                                                            Quantity = j.Quantity,
                                                            Weight = j.Weight,
                                                            Code = j.Code,
                                                            Unit = j.Unit,
                                                            ThicknessNumber = th.ThicknessNumber,
                                                            AddedBy = j.AddedBy,
                                                            AddedDate = j.AddedDate,
                                                            DispatchedQuantity = j.DispatchedQuantity,
                                                            DispatchStatus = j.DispatchStatus,
                                                            InspectedBy = j.InspectedBy,
                                                            JumboDispatchId = j.JumboDispatchId,
                                                            RollType = j.RollType
                                                        }).OrderBy(x => x.JumboInspectionId).ToList(),
                                 }).OrderByDescending(x => x.WorkPlanJumboMasterId).ToList();

                var newJl = new List<WorkPlanJumboMasterVm>();
                foreach (var jl in res.JumboList)
                {
                    if (
                        newJl?.Any(x =>
                            x.WorkPlanJumboMasterId == jl.WorkPlanJumboMasterId
                            && x.Grade == jl.Grade
                            ) == false
                    )
                    {
                        newJl.Add(jl);
                    }

                }
                res.JumboList = newJl;

                return res;
            }
        }

        public void UpdateSaleorderCode(pmsdbContext dbContext, IDbContextTransaction dbtransaction, long? saleorderid)
        {
            var db = dbContext;
            var transaction = dbtransaction;
            {
                var saleOrderProduct = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
                var saleorderproductionid = saleOrderProduct.SaleOrderProductionId;
                var EmbossingQry = (from e in db.EmbossingMasters
                                    join i in db.SaleOrderProductionEmbossingMasters on e.EmbossingMasterId equals i.EmbossingMasterId
                                    where e.Code != null && i.SaleOrderProductionId == saleorderproductionid
                                    select new KeyValue
                                    {
                                        Key = i.Rank,
                                        Value = e.Code
                                    }).Distinct().ToList();

                var PrintQry = (from e in db.PrintMasters
                                join i in db.SaleOrderProductionPrintMasters on e.PrintMasterId equals i.PrintMasterId
                                where e.Code != null && i.SaleOrderProductionId == saleorderproductionid
                                select new KeyValue
                                {
                                    Key = i.Rank,
                                    Value = e.Code
                                }).Distinct().ToList();

                var TumblingQry = (from e in db.TumblingMasters
                                   join i in db.SaleOrderProductionTumblingMasters on e.TumblingMasterId equals i.TumblingMasterId
                                   where e.Code != null && i.SaleOrderProductionId == saleorderproductionid
                                   select new KeyValue
                                   {
                                       Key = i.Rank,
                                       Value = e.Code
                                   }).Distinct().ToList();

                var VacuumQry = (from e in db.VacuumMasters
                                 join i in db.SaleOrderProductionVacuumMasters on e.VacuumMasterId equals i.VacuumMasterId
                                 where e.Code != null && i.SaleOrderProductionId == saleorderproductionid
                                 select new KeyValue
                                 {
                                     Key = i.Rank,
                                     Value = e.Code
                                 }).Distinct().ToList();

                var LacquerQry = (from e in db.LacquerMasters
                                  join i in db.SaleOrderProductionLacquerRawMaterialTables on e.LacquerMasterId equals i.LacquerMasterId
                                  where e.Code != null && i.SaleOrderProductionId == saleorderproductionid
                                  select new KeyValue
                                  {
                                      Key = i.Rank,
                                      Value = e.Code
                                  }).Distinct().ToList();

                var graincode = SanitizeCodeComponent(db.GrainMasters.FirstOrDefault(x => x.GrainId == saleOrderProduct.GrainId)?.GrainCode);
                var colorcode = SanitizeCodeComponent(db.ColorMasters.FirstOrDefault(x => x.ColorId == saleOrderProduct.ColorId)?.ColorCode);
                var thicknesscode = SanitizeCodeComponent(db.ThicknessMasters.FirstOrDefault(x => x.ThicknessId == saleOrderProduct.Thick)?.Code);
                var saleFormulationCode = SanitizeCodeComponent(((from s in db.SaleFormulationCodeMasters
                                                                  join a in db.SaleOrderTables on s.SaleFormulationCodeId equals a.SaleFormulationCodeId
                                                                  where a.SaleOrderId == saleorderid
                                                                  select s).FirstOrDefault())?.SaleFormulationCode);

                var sot = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);

                // Handle linked sale orders for special cases
                if (sot.Status == (int)ESalesOrderStatus.JumboInspection || sot.Status == (int)ESalesOrderStatus.MoveToPostProcess)
                {
                    var checklink = db.LinkedSaleOrderTables.Where(x => x.ParentSaleOrder == saleorderid).FirstOrDefault();
                    if (checklink != null)
                    {
                        var linkedSaleOrderData = GetSaleOrderDataByIdForGradePrint(checklink.ParentSaleOrder.Value);
                        graincode = SanitizeCodeComponent(linkedSaleOrderData.SaleOrderProduction.GrainCode);
                        colorcode = SanitizeCodeComponent(linkedSaleOrderData.SaleOrderProduction.ColorCode);
                        thicknesscode = SanitizeCodeComponent(linkedSaleOrderData.SaleOrderProduction.ThicknessValue);
                    }
                }

                // Build components list with validation
                var saleCodeComponents = new List<string>();

                // Add formulation code
                if (!string.IsNullOrWhiteSpace(saleFormulationCode))
                    saleCodeComponents.Add(saleFormulationCode);

                // Add grain code
                if (!string.IsNullOrWhiteSpace(graincode))
                    saleCodeComponents.Add(graincode);

                // Add color code
                if (!string.IsNullOrWhiteSpace(colorcode))
                    saleCodeComponents.Add(colorcode);

                // Get process codes
                var RankCodes = new List<KeyValue>();
                if (PrintQry != null)
                    RankCodes.AddRange(PrintQry);
                if (EmbossingQry != null)
                    RankCodes.AddRange(EmbossingQry);
                if (TumblingQry != null)
                    RankCodes.AddRange(TumblingQry);
                if (VacuumQry != null)
                    RankCodes.AddRange(VacuumQry);
                if (LacquerQry != null)
                    RankCodes.AddRange(LacquerQry);

                var newsaleCode = GetSaleOrderProcessCode(RankCodes);
                var finishcode = newsaleCode;

                // Add process codes only if they exist
                if (!string.IsNullOrWhiteSpace(newsaleCode))
                    saleCodeComponents.Add(newsaleCode);

                // Add thickness code
                if (!string.IsNullOrWhiteSpace(thicknesscode))
                    saleCodeComponents.Add(thicknesscode);

                // Join all components with single slash
                var salecode = string.Join("/", saleCodeComponents);
                sot.SaleOrderCode = salecode;
                sot.FinishCode = string.IsNullOrEmpty(finishcode) == true ? "DIRECT" : finishcode;
                db.SaveChanges();
            }
        }

        public ApiFunctionResponseVm UpdateManufacturingQuantity(long saleorderid, decimal qty)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
                res.ManufacturingQuantity = qty;
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public SaleOrderTableVm GetSaleOrderDataForViewById(long pid)
        {
            SaleOrderTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                var saleprodid = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == pid).SaleOrderProductionId;
                // var lacqids = db.SaleOrderProductionLacquerRawMaterialTables.Where(x => x.SaleOrderProductionId == saleprodid).Select(a => a.LacquerMasterId).Distinct().ToList();
                var insform = (from a in db.InspectionSaleFormulationCodeMasters
                               join la in db.InspectionFormulationCodeMixingTables on a.InspectionSaleFormulationCodeId equals la.InspectionSaleFormulationCodeId
                               join p in db.ProductMasters on a.FabricProductId equals p.ProductId
                               where la.SaleOrderId == pid
                               select new InspectionSaleFormulationCodeMasterVm
                               {
                                   FabricProductId = a.FabricProductId,
                                   FabricProductName = p.ProductName,
                                   FabricProductQty = a.FabricProductQty,
                                   AddedBy = a.AddedBy,
                                   AddedDate = a.AddedDate
                               }).FirstOrDefault();

                res = (from s in db.SaleOrderTables
                       join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                       from pcm in spc.DefaultIfEmpty()
                       join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                       from cust in scu.DefaultIfEmpty()
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join prf in db.ProductMasters on a.FabricProductId equals prf.ProductId into prsf
                       from prf in prsf.DefaultIfEmpty()
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join cf in db.ColorMasters on a.FabricColorId equals cf.ColorId into cagrf
                       from cf in cagrf.DefaultIfEmpty()
                       join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                       from g in agr.DefaultIfEmpty()
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId into pra
                       from pr in pra.DefaultIfEmpty()
                       join p in db.ProductCategoryMasters on pr.ProductCategoryId equals p.ProductCategoryId into ps
                       from p in ps.DefaultIfEmpty()
                       join pf in db.ProductFirstSubCategoryMasters on pr.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                       from pf in psf.DefaultIfEmpty()
                       join ext in db.ProductMasterExtensions on a.ProductId equals ext.ProductId into aext
                       from ext in aext.DefaultIfEmpty()
                       join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                       from thm in thmsa.DefaultIfEmpty()
                       join wdm in db.WidthMasters on a.Width equals wdm.WidthId into wdmsa
                       from wdm in wdmsa.DefaultIfEmpty()
                       where a.SaleOrderId == pid
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           CostingAdded = s.CostingAdded,
                           CustomerId = s.CustomerId,
                           CustomerName = cust.CustomerName,
                           Category = pcm.ProductCategory,
                           AddedBy = s.AddedBy,
                           AddedDate = s.AddedDate,
                           DeliveryDate = s.DeliveryDate,
                           Remarks = s.Remarks,
                           BORNumber = s.Bornumber,
                           SaleOrderStatus = s.SaleOrderStatus,
                           WorkPlanStatus = s.WorkPlanStatus,
                           WorkPlanNumber = s.WorkPlanStatus == true ? db.WorkPlanMasters.Where(y => y.WorkPlanId == (db.WorkPlanOrders.Where(x => x.OrderId == s.SaleOrderId).FirstOrDefault().WorkplanId)).FirstOrDefault().WorkPlanNo : "Not Created",
                           IsRawMaterialIssued = s.IsRawMaterialIssued,
                           ProformaInvoiceId = s.ProformaInvoiceId,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,
                           ProductionCompletionRemarks = s.ProductionCompletionRemarks,
                           SaleFormulationCode = fcm.SaleFormulationCode,
                           SaleFormulationCodeId = fcm.SaleFormulationCodeId,
                           FormulationFabricProductName = insform == null ? prf.ProductName : insform.FabricProductName,
                           FormulationFabricProductId = insform == null ? prf.ProductId : insform.FabricProductId.Value,
                           FormulationFabricProductQty = insform == null ? 0 : insform.FabricProductQty.Value,

                           SaleOrderPostProcessOrder = (from sopp in db.SaleOrderPostProcessOrderTables
                                                        where sopp.SaleOrderId == s.SaleOrderId
                                                        select new SaleOrderPostProcessOrderTableVm
                                                        {
                                                            SaleOrderId = sopp.SaleOrderId,
                                                            Rank = sopp.Rank,
                                                            PostProcessName = sopp.PostProcessName,
                                                            ProcessMasterId = sopp.ProcessMasterId
                                                        }).OrderBy(x => x.Rank).ToList(),

                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               ManufacturingProductName = a.ManufacturingProductName,
                               ManufacturingProductCode = a.ManufacturingProductCode,
                               OrderQuantity = a.OrderQuantity,
                               ManufacturingQuantity = a.ManufacturingQuantity,
                               Unit = a.Unit,
                               ColorId = a.ColorId,
                               ColorName = c.ColorName,
                               ColorCode = c.ColorCode,
                               FabricColorId = a.FabricColorId,
                               FabricColorName = cf.ColorName,
                               FabricProductId = a.FabricProductId,
                               GrainId = a.GrainId,
                               GrainName = g.GrainName,
                               GrainCode = g.GrainCode,
                               Thick = a.Thick,
                               ThicknessValue = thm.ThicknessNumber,
                               Width = a.Width,
                               WidthNumber = wdm.WidthNumber,
                               Barcode = a.Barcode,
                               ProductionStatus = a.ProductionStatus,
                               CostingStatus = a.CostingStatus,
                               AddedBy = a.AddedBy,
                               AddedDate = a.AddedDate,
                               SalePrice = a.SalePrice,
                               PreSkinGsm = a.PreSkinGsm,
                               SkinGsm = a.SkinGsm,
                               FoamGsm = a.FoamGsm,
                               AdhesiveGsm = a.AdhesiveGsm,
                               FabricGsm = a.FabricGsm,
                               TotalGsm = a.TotalGsm,
                               SaleOrderProductionEmbossing = (from op in db.SaleOrderProductionEmbossingMasters
                                                               join p in db.EmbossingMasters on op.EmbossingMasterId equals p.EmbossingMasterId
                                                               where op.SaleOrderProductionId == a.SaleOrderProductionId
                                                               select new SaleOrderProductionEmbossingMasterVm
                                                               {
                                                                   SaleOrderProductionEmbossingMasterId = op.SaleOrderProductionEmbossingMasterId,
                                                                   SaleOrderProductionId = op.SaleOrderProductionId,
                                                                   EmbossingMasterId = op.EmbossingMasterId,
                                                                   Name = p.Name,
                                                                   ImageName = p.ImageName,
                                                                   Code = p.Code,
                                                                   Description = p.Description,
                                                                   Price = op.Price,
                                                                   Quantity = op.Quantity,
                                                                   Total = op.Total,
                                                                   Removed = op.Removed,
                                                                   RemovedBy = op.RemovedBy,
                                                                   RemovedDate = op.RemovedDate,
                                                                   Rank = op.Rank
                                                               }).ToList(),
                               SaleOrderProductionPrint = (from op in db.SaleOrderProductionPrintMasters
                                                           join p in db.PrintMasters on op.PrintMasterId equals p.PrintMasterId
                                                           where op.SaleOrderProductionId == a.SaleOrderProductionId
                                                           select new SaleOrderProductionPrintMasterVm
                                                           {
                                                               SaleOrderProductionPrintMasterId = op.SaleOrderProductionPrintMasterId,
                                                               SaleOrderProductionId = op.SaleOrderProductionId,
                                                               PrintMasterId = op.PrintMasterId,
                                                               Name = p.Name,
                                                               ImageName = p.ImageName,
                                                               Code = p.Code,
                                                               Description = p.Description,
                                                               Price = op.Price,
                                                               Quantity = op.Quantity,
                                                               Total = op.Total,
                                                               Removed = op.Removed,
                                                               RemovedBy = op.RemovedBy,
                                                               RemovedDate = op.RemovedDate,
                                                               Rank = op.Rank
                                                           }).ToList(),
                               SaleOrderProductionTumbling = (from op in db.SaleOrderProductionTumblingMasters
                                                              join p in db.TumblingMasters on op.TumblingMasterId equals p.TumblingMasterId
                                                              where op.SaleOrderProductionId == a.SaleOrderProductionId
                                                              select new SaleOrderProductionTumblingMasterVm
                                                              {
                                                                  SaleOrderProductionTumblingMasterId = op.SaleOrderProductionTumblingMasterId,
                                                                  SaleOrderProductionId = op.SaleOrderProductionId,
                                                                  TumblingMasterId = op.TumblingMasterId,
                                                                  Name = p.Name,
                                                                  Code = p.Code,
                                                                  Description = p.Description,
                                                                  Price = op.Price,
                                                                  Quantity = op.Quantity,
                                                                  Total = op.Total,
                                                                  Removed = op.Removed,
                                                                  RemovedBy = op.RemovedBy,
                                                                  RemovedDate = op.RemovedDate,
                                                                  Rank = op.Rank
                                                              }).ToList(),
                               SaleOrderProductionVacuum = (from op in db.SaleOrderProductionVacuumMasters
                                                            join p in db.VacuumMasters on op.VacuumMasterId equals p.VacuumMasterId
                                                            where op.SaleOrderProductionId == a.SaleOrderProductionId
                                                            select new SaleOrderProductionVacuumMasterVm
                                                            {
                                                                SaleOrderProductionVacuumMasterId = op.SaleOrderProductionVacuumMasterId,
                                                                SaleOrderProductionId = op.SaleOrderProductionId,
                                                                VacuumMasterId = op.VacuumMasterId,
                                                                Name = p.Name,
                                                                Code = p.Code,
                                                                Description = p.Description,
                                                                Price = op.Price,
                                                                Quantity = op.Quantity,
                                                                Total = op.Total,
                                                                Removed = op.Removed,
                                                                RemovedBy = op.RemovedBy,
                                                                RemovedDate = op.RemovedDate,
                                                                Rank = op.Rank
                                                            }).ToList(),

                               Lacquer = (from am in db.LacquerMasters
                                          join lrm in db.SaleOrderProductionLacquerRawMaterialTables on am.LacquerMasterId equals lrm.LacquerMasterId
                                          where lrm.SaleOrderProductionId == saleprodid
                                          select new LacquerMasterVm
                                          {
                                              LacquerMasterId = am.LacquerMasterId,
                                              Name = am.Name,
                                              Code = am.Code,
                                              Description = am.Description,
                                              AddedBy = am.AddedBy,
                                              AddedDate = am.AddedDate,
                                              Removed = db.SaleOrderProductionLacquerRawMaterialTables.Any(x => x.LacquerMasterId == am.LacquerMasterId && x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed == true),
                                              RemovedBy = db.SaleOrderProductionLacquerRawMaterialTables.FirstOrDefault(x => x.LacquerMasterId == am.LacquerMasterId && x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed == true).RemovedBy,
                                              RemovedDate = db.SaleOrderProductionLacquerRawMaterialTables.FirstOrDefault(x => x.LacquerMasterId == am.LacquerMasterId && x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed == true).RemovedDate,
                                              Rank = lrm.Rank,
                                          }).OrderByDescending(x => x.LacquerMasterId).ToList(),
                           }
                       }).FirstOrDefault();
                res.PreInspectionCompletedBy = res.Status >= ESalesOrderStatus.Inspection ? (insform == null ? null : insform.AddedBy) : null;
                res.PreInspectionCompletedOn = res.Status >= ESalesOrderStatus.Inspection ? (insform == null ? null : insform.AddedDate) : null;
            }
            return res;
        }
        public SaleOrderTableVm GetSaleOrderDataForEmailById(long pid)
        {
            SaleOrderTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from s in db.SaleOrderTables
                       join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                       from cust in scu.DefaultIfEmpty()
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join prf in db.ProductMasters on a.FabricProductId equals prf.ProductId into prsf
                       from prf in prsf.DefaultIfEmpty()
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join cf in db.ColorMasters on a.FabricColorId equals cf.ColorId into cagrf
                       from cf in cagrf.DefaultIfEmpty()
                       join g in db.GrainMasters on a.GrainId equals g.GrainId into agr
                       from g in agr.DefaultIfEmpty()
                       join thm in db.ThicknessMasters on a.Thick equals thm.ThicknessId into thmsa
                       from thm in thmsa.DefaultIfEmpty()
                       join wdm in db.WidthMasters on a.Width equals wdm.WidthId into wdmsa
                       from wdm in wdmsa.DefaultIfEmpty()
                       where a.SaleOrderId == pid
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           SaleOrderCode = s.SaleOrderCode,
                           FinishCode = s.FinishCode,
                           CostingAdded = s.CostingAdded,
                           CustomerId = s.CustomerId,
                           CustomerName = cust.CustomerName,
                           AddedBy = s.AddedBy,
                           AddedDate = s.AddedDate,
                           DeliveryDate = s.DeliveryDate,
                           Remarks = s.Remarks,
                           BORNumber = s.Bornumber,
                           SaleOrderStatus = s.SaleOrderStatus,
                           ProformaInvoiceId = s.ProformaInvoiceId,
                           Status = (ESalesOrderStatus)s.Status,
                           IsJumboRequired = s.IsJumboRequired,
                           ProductionCompletionRemarks = s.ProductionCompletionRemarks,
                           SaleFormulationCode = fcm.SaleFormulationCode,

                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               Lot = a.Lot,
                               Batch = a.Batch,
                               OrderQuantity = a.OrderQuantity,
                               ManufacturingQuantity = a.ManufacturingQuantity,
                               Unit = a.Unit,
                               ColorId = a.ColorId,
                               ColorName = c.ColorName,
                               ColorCode = c.ColorCode,
                               FabricColorId = a.FabricColorId,
                               FabricColorName = cf.ColorName,
                               FabricProductId = a.FabricProductId,
                               ColorPrice = a.ColorPrice,
                               GrainId = a.GrainId,
                               GrainName = g.GrainName,
                               GrainCode = g.GrainCode,
                               GrainPrice = a.GrainPrice,
                               Thick = a.Thick,
                               ThickPrice = a.ThickPrice,
                               ThicknessValue = thm.ThicknessNumber,
                               Width = a.Width,
                               WidthNumber = wdm.WidthNumber,
                               WidthPrice = a.WidthPrice,
                               Barcode = a.Barcode,
                               SlippagePercent = a.SlippagePercent,
                               TotalCost = a.TotalCost,
                               AddedBy = a.AddedBy,
                               AddedDate = a.AddedDate,
                               SalePrice = a.SalePrice,
                           }
                       }).FirstOrDefault();
            }
            return res;
        }

        public List<PostProcessCostingMasterVm> GetPostProcessCosting()
        {
            List<PostProcessCostingMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from s in db.PostProcessCostingMasters
                       select new PostProcessCostingMasterVm
                       {
                           PostProcessCostingMasterId = s.PostProcessCostingMasterId,
                           PostProcessName = s.PostProcessName,
                           SaleOrderType = s.SaleOrderType,
                           Cost = s.Cost,
                           AddedBy = s.AddedBy,
                           AddedDate = s.AddedDate,
                       }).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm LinkSaleOrder(List<LinkedSaleOrderTableVm> listItem)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                foreach (var item in listItem)
                {
                    if (item.ParentSaleOrder != item.LinkedSaleOrder)
                    {
                        LinkedSaleOrderTable res = new LinkedSaleOrderTable()
                        {
                            ParentSaleOrder = item.ParentSaleOrder,
                            LinkedSaleOrder = item.LinkedSaleOrder,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now
                        };
                        db.LinkedSaleOrderTables.Add(res);
                        var linkll = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == item.LinkedSaleOrder);
                        linkll.Status = (int)ESalesOrderStatus.LiningOrderMerged;

                        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                        {
                            SaleOrderId = linkll.SaleOrderId,
                            Status = (int)ESalesOrderStatus.LiningOrderMerged,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                            WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == linkll.SaleOrderId).WorkplanId
                        });
                        db.SaveChanges();

                    }
                }
                db.SaveChanges();
                transaction.Commit();

                using var innerTransaction = db.Database.BeginTransaction();
                try
                {
                    foreach (var item in listItem)
                    {
                        if (item.ParentSaleOrder != item.LinkedSaleOrder)
                        {
                            UpdateSaleorderCode(db, transaction, item.ParentSaleOrder.Value);
                        }
                    }
                    innerTransaction.Commit();
                }
                catch (Exception ex)
                {
                    innerTransaction.Rollback();
                    throw;
                }

                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Sandwich Order Linking Completed");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                throw;
            }
        }
        public ApiFunctionResponseVm RemoveLinkSaleOrder(LinkedSaleOrderTableVm item)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                if (item.ParentSaleOrder != item.LinkedSaleOrder)
                {
                    var lsot = db.LinkedSaleOrderTables.FirstOrDefault(x => x.LinkedId == item.LinkedId);
                    db.LinkedSaleOrderTables.Remove(lsot);
                    db.SaveChanges();

                    var checkPostProcessExist = db.SaleOrderPostProcessOrderTables.FirstOrDefault(x => x.SaleOrderId == lsot.LinkedSaleOrder);
                    var linkll = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == lsot.LinkedSaleOrder);
                    if (checkPostProcessExist != null)
                    {
                        linkll.Status = (int)ESalesOrderStatus.MoveToPostProcess;
                    }
                    else
                    {
                        linkll.Status = (int)ESalesOrderStatus.JumboInspection;
                    }
                    var sott = db.SaleOrderTimelineTables.FirstOrDefault(x => x.SaleOrderId == lsot.LinkedSaleOrder && x.Status == (int)ESalesOrderStatus.LiningOrderMerged);
                    if (sott != null)
                    {
                        db.SaleOrderTimelineTables.Remove(sott);
                    }
                    db.SaveChanges();
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Sandwich Order Linking Removed Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex.Message);
                throw;
            }
        }

        public List<SaleOrderTableVm> GetAllSaleOrderDataForLinking()
        {
            ESalesOrderStatus sts = ESalesOrderStatus.JumboInspection;
            ESalesOrderStatus sts2 = ESalesOrderStatus.MoveToPostProcess;
            List<SaleOrderTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from s in db.SaleOrderTables
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcms
                       from fcm in fcms.DefaultIfEmpty()
                       join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                       from pcm in spc.DefaultIfEmpty()
                       join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                       from cust in scu.DefaultIfEmpty()
                       join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                       from c in cagr.DefaultIfEmpty()
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId into apr
                       from pr in apr.DefaultIfEmpty()
                       where (s.Status == (int)sts || s.Status == (int)sts2)
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderDate = s.SaleOrderDate,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderType = s.SaleOrderType,
                           SaleOrderCode = s.SaleOrderCode,
                           CostingAdded = s.CostingAdded,
                           CustomerId = s.CustomerId,
                           CustomerName = cust.CustomerName,
                           CategoryId = s.CategoryId,
                           Category = pcm.ProductCategory,
                           AddedBy = s.AddedBy,
                           AddedDate = s.AddedDate,
                           DeliveryDate = s.DeliveryDate,
                           Remarks = s.Remarks,
                           SaleOrderStatus = s.SaleOrderStatus,
                           WorkPlanStatus = s.WorkPlanStatus,
                           IsRawMaterialIssued = s.IsRawMaterialIssued,
                           SaleFormulationCode = fcm.SaleFormulationCode,
                           Status = (PmsCommon.ESalesOrderStatus)s.Status,
                           IsJumboRequired = s.IsJumboRequired,
                           ProformaInvoiceId = s.ProformaInvoiceId,
                           ProductionCompletionRemarks = s.ProductionCompletionRemarks,
                           IsPrintRequired = db.SaleOrderProductionPrintMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsEmbossingRequired = db.SaleOrderProductionEmbossingMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsLacquerRequired = db.SaleOrderProductionLacquerRawMaterialTables.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsTumblingRequired = db.SaleOrderProductionTumblingMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           IsVacuumRequired = db.SaleOrderProductionVacuumMasters.Any(x => x.SaleOrderProductionId == a.SaleOrderProductionId && x.Removed != true),
                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               SaleOrderProductionId = a.SaleOrderProductionId,
                               ProductId = a.ProductId,
                               ProductName = pr.ProductName,
                               ProductCode = pr.ProductCode,
                               ManufacturingProductName = a.ManufacturingProductName,
                               ManufacturingProductCode = a.ManufacturingProductCode,
                               Lot = a.Lot,
                               Batch = a.Batch,
                               OrderQuantity = a.OrderQuantity,
                               ManufacturingQuantity = a.ManufacturingQuantity,
                               Unit = a.Unit,
                               ColorId = a.ColorId,
                               ColorName = c.ColorName,
                               Barcode = a.Barcode,
                               ProductionStatus = a.ProductionStatus,
                               CostingStatus = a.CostingStatus,
                               SlippagePercent = a.SlippagePercent,
                               TotalCost = a.TotalCost,
                               ProcessFormulationCode = a.ProcessFormulationCode,
                               MixingFormulationCode = a.MixingFormulationCode,
                               SalePrice = a.SalePrice,
                               PreSkinGsm = a.PreSkinGsm,
                               SkinGsm = a.SkinGsm,
                               FoamGsm = a.FoamGsm,
                               AdhesiveGsm = a.AdhesiveGsm,
                               FabricGsm = a.FabricGsm,
                               TotalGsm = a.TotalGsm,
                               AddedBy = a.AddedBy,
                               AddedDate = a.AddedDate
                           }
                       }).OrderByDescending(x => x.SaleOrderId).ToList();

                var plist = db.LinkedSaleOrderTables.Select(a => a.ParentSaleOrder).Distinct().ToList();
                //var llist = db.SaleOrderTables.Where(x => x.Status == (int)ESalesOrderStatus.Linked).Select(a => a.SaleOrderId).ToList();
                var rejectList = res.Where(i => plist.Contains(i.SaleOrderId)).ToList();
                //rejectList.AddRange(res.Where(i => llist.Contains(i.SaleOrderId)).ToList());
                res = res.Except(rejectList).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm HoldSaleOrder(long saleorderId)
        {
            if (saleorderId > 0)
            {
                var db = new pmsdbContext();

                var orderData = db.SaleOrderTables.FirstOrDefault(O => O.SaleOrderId == saleorderId);

                if (orderData != null)
                {
                    orderData.HoldBy = GlobalData.loggedInUser;
                    orderData.HoldDate = DateTime.Now;
                    orderData.SaleOrderStatus = PmsCommon.PMSSaleOrderStatus.Hold;

                    db.SaveChanges();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Sale Order Status On Hold.");
                }
            }

            return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Sale Order Data Not Found");
        }

        public ApiFunctionResponseVm ApproveSaleOrder(long saleorderId)
        {
            if (saleorderId > 0)
            {
                var db = new pmsdbContext();

                var orderData = db.SaleOrderTables.FirstOrDefault(O => O.SaleOrderId == saleorderId);

                if (orderData != null)
                {
                    orderData.ApprovedBy = GlobalData.loggedInUser;
                    orderData.ApprovedDate = DateTime.Now;
                    orderData.SaleOrderStatus = PmsCommon.PMSSaleOrderStatus.Active;
                    db.SaveChanges();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Sale Order Approved.");
                }
            }

            return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Sale Order Data Not Found");
        }
        public SaleOrderTableVm GetSaleOrderFormulationMixing(long saleOrderId)
        {
            SaleOrderTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from s in db.SaleOrderTables
                       join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                       where s.SaleOrderId == saleOrderId
                       select new SaleOrderTableVm
                       {
                           SaleOrderId = s.SaleOrderId,
                           SaleOrderNumber = s.SaleOrderNumber,
                           SaleOrderProduction = new SaleOrderProductionTableVm
                           {
                               SaleOrderId = a.SaleOrderId,
                               PreSkinGsm = a.PreSkinGsm,
                               SkinGsm = a.SkinGsm,
                               FoamGsm = a.FoamGsm,
                               AdhesiveGsm = a.AdhesiveGsm,
                               FabricGsm = a.FabricGsm,
                               TotalGsm = a.TotalGsm,
                               FormulationMixing = (from la in db.FormulationCodeMixingTables
                                                    join a in db.MixingMasters on la.MixingId equals a.MixingId
                                                    where la.SaleFormulationCodeId == s.SaleFormulationCodeId
                                                    select new FormulationCodeMixingTableVm
                                                    {
                                                        FormulationCodeMixingId = la.FormulationCodeMixingId,
                                                        MixingId = la.MixingId,
                                                        MixingName = a.MixingName,
                                                    }).ToList()
                           }
                       }).FirstOrDefault();
            }
            return res;
        }
        public List<SaleOrderTableVm> GetPostProcessListWithFilters(SaleOrderRequestFilter filter)
        {
            List<ESalesOrderStatus> statusList = new()
            {
                ESalesOrderStatus.MoveToPostProcess,
                ESalesOrderStatus.PrintAssigned,
                ESalesOrderStatus.PrintInProcess,
                ESalesOrderStatus.PrintCompleted,
                ESalesOrderStatus.EmbossingAssigned,
                ESalesOrderStatus.EmbossingInProcess,
                ESalesOrderStatus.EmbossingCompleted,
                ESalesOrderStatus.VacuumAssigned,
                ESalesOrderStatus.VacuumInProcess,
                ESalesOrderStatus.VacuumCompleted,
                ESalesOrderStatus.LacquerAssigned,
                ESalesOrderStatus.LacquerInProcess,
                ESalesOrderStatus.LacquerCompleted,
                ESalesOrderStatus.TumblingAssigned,
                ESalesOrderStatus.TumblingInProcess,
                ESalesOrderStatus.TumblingCompleted
            };

            List<SaleOrderTableVm> vmModel = new List<SaleOrderTableVm>();
            using (var db = new Models.pmsdbContext())
            {
                // First, get distinct SaleOrderIds based on the filter criteria
                var distinctSaleOrderIds = (from s in db.SaleOrderTables
                                            join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                                            join fcm in db.SaleFormulationCodeMasters on s.SaleFormulationCodeId equals fcm.SaleFormulationCodeId
                                            join sopc in db.SaleOrderProductionCompleteTables on s.SaleOrderId equals sopc.SaleOrderId
                                            where ((((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "saleorder") && (filter.FromAddedDate == null || s.AddedDate >= filter.FromAddedDate))
                                                && ((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "saleorder") && (filter.ToAddedDate == null || s.AddedDate <= filter.ToAddedDate)))
                                                || (((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "productioncomplete") && (filter.FromAddedDate == null || sopc.AddedDate >= filter.FromAddedDate))
                                                && ((String.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "productioncomplete") && (filter.ToAddedDate == null || sopc.AddedDate <= filter.ToAddedDate))))
                                                && (filter.SaleFormulationCodeId == 0 || filter.SaleFormulationCodeId == null || s.SaleFormulationCodeId == filter.SaleFormulationCodeId)
                                                && (String.IsNullOrEmpty(filter.SaleOrderNumber) || s.SaleOrderNumber.Contains(filter.SaleOrderNumber))
                                                && (filter.Status == null && statusList.Contains((ESalesOrderStatus)s.Status) || s.Status == (int)filter.Status)
                                                && (String.IsNullOrEmpty(filter.ArticleName) || a.ManufacturingProductName.ToLower().Contains(filter.ArticleName.ToLower()))
                                                && (filter.ColorId == 0 || filter.ColorId == null || a.ColorId == filter.ColorId)
                                                && (filter.GrainId == 0 || filter.GrainId == null || a.GrainId == filter.GrainId)
                                                && (filter.CustomerId == 0 || filter.CustomerId == null || s.CustomerId == filter.CustomerId)
                                                && (String.IsNullOrEmpty(filter.ProductType) || fcm.SaleFormulationCode.ToLower().StartsWith(filter.ProductType.ToLower()))
                                                && (String.IsNullOrEmpty(filter.OrderType) || s.SaleOrderType.ToLower().Contains(filter.OrderType.ToLower()))
                                                && (string.IsNullOrEmpty(filter.SaleOrderStatus) || s.SaleOrderStatus == filter.SaleOrderStatus)
                                            select s.SaleOrderId).Distinct().ToList();

                // Now, use these distinct SaleOrderIds to fetch the complete data
                foreach (var saleOrderId in distinctSaleOrderIds)
                {
                    var saleOrder = (from s in db.SaleOrderTables
                                     join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                                     join sopc in db.SaleOrderProductionCompleteTables on s.SaleOrderId equals sopc.SaleOrderId
                                     join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                                     from cust in scu.DefaultIfEmpty()
                                     where s.SaleOrderId == saleOrderId
                                     select new SaleOrderTableVm
                                     {
                                         SaleOrderId = s.SaleOrderId,
                                         SaleOrderDate = s.SaleOrderDate,
                                         SaleOrderNumber = s.SaleOrderNumber,
                                         CustomerName = cust.CustomerName,
                                         Status = (PmsCommon.ESalesOrderStatus)s.Status,
                                         SaleOrderProductionComplete = new SaleOrderProductionCompleteTableVm
                                         {
                                             AddedDate = sopc.AddedDate
                                         },
                                         SaleOrderProductionPostProcess = new SaleOrderProductionPostProcessVm
                                         {
                                             SaleOrderProductionId = a.SaleOrderProductionId,
                                             ManufacturingProductName = a.ManufacturingProductName
                                         }
                                     }).FirstOrDefault();

                    if (saleOrder != null)
                    {
                        saleOrder.SaleOrderPostProcessOrder = db.SaleOrderPostProcessOrderTables
                            .Where(sopp => sopp.SaleOrderId == saleOrderId && sopp.Removed != true)
                            .OrderBy(x => x.Rank)
                            .Select(sopp => new SaleOrderPostProcessOrderTableVm
                            {
                                SaleOrderId = sopp.SaleOrderId,
                                Rank = sopp.Rank,
                                PostProcessName = sopp.PostProcessName
                            }).ToList();

                        saleOrder.SaleOrderProductionPostProcess.SaleOrderPostProcessPrint = db.SaleOrderPostProcessPrintTables
                            .Where(slpp => slpp.SaleOrderId == saleOrderId)
                            .Select(slpp => new SaleOrderPostProcessPrintTableVm
                            {
                                SaleOrderPostProcessPrintId = slpp.SaleOrderPostProcessPrintId,
                                SaleOrderId = slpp.SaleOrderId,
                                ReceivedQuantity = slpp.ReceivedQuantity,
                                PrintCompletedQuantity = slpp.PrintCompletedQuantity,
                                PrintMeasurementUnit = slpp.PrintMeasurementUnit,
                                PrintRack = slpp.PrintRack,
                                PrintWastageQuantity = slpp.PrintWastageQuantity,
                                PrintStatus = slpp.PrintStatus,
                                AddedBy = slpp.AddedBy,
                                AddedDate = slpp.AddedDate,
                                Rank = slpp.Rank
                            }).ToList();

                        saleOrder.SaleOrderProductionPostProcess.SaleOrderPostProcessEmbossing = db.SaleOrderPostProcessEmbossingTables
                                        .Where(slpe => slpe.SaleOrderId == saleOrderId)
                                        .Select(slpe => new SaleOrderPostProcessEmbossingTableVm
                                        {
                                            SaleOrderPostProcessEmbossingId = slpe.SaleOrderPostProcessEmbossingId,
                                            SaleOrderId = slpe.SaleOrderId,
                                            ReceivedQuantity = slpe.ReceivedQuantity,
                                            EmbossingCompletedQuantity = slpe.EmbossingCompletedQuantity,
                                            EmbossingMeasurementUnit = slpe.EmbossingMeasurementUnit,
                                            EmbossingRack = slpe.EmbossingRack,
                                            EmbossingWastageQuantity = slpe.EmbossingWastageQuantity,
                                            EmbossingStatus = slpe.EmbossingStatus,
                                            AddedBy = slpe.AddedBy,
                                            AddedDate = slpe.AddedDate,
                                            Rank = slpe.Rank
                                        }).ToList();
                        saleOrder.SaleOrderProductionPostProcess.SaleOrderPostProcessVacuum = db.SaleOrderPostProcessVacuumTables
                            .Where(slpv => slpv.SaleOrderId == saleOrderId)
                            .Select(slpv => new SaleOrderPostProcessVacuumTableVm
                            {
                                SaleOrderPostProcessVacuumId = slpv.SaleOrderPostProcessVacuumId,
                                SaleOrderId = slpv.SaleOrderId,
                                ReceivedQuantity = slpv.ReceivedQuantity,
                                VacuumCompletedQuantity = slpv.VacuumCompletedQuantity,
                                VacuumMeasurementUnit = slpv.VacuumMeasurementUnit,
                                VacuumRack = slpv.VacuumRack,
                                VacuumWastageQuantity = slpv.VacuumWastageQuantity,
                                VacuumStatus = slpv.VacuumStatus,
                                AddedBy = slpv.AddedBy,
                                AddedDate = slpv.AddedDate,
                                Rank = slpv.Rank
                            }).ToList();
                        saleOrder.SaleOrderProductionPostProcess.SaleOrderPostProcessLacquer = db.SaleOrderPostProcessLacquerTables
                            .Where(slpl => slpl.SaleOrderId == saleOrderId)
                            .Select(slpl => new SaleOrderPostProcessLacquerTableVm
                            {
                                SaleOrderPostProcessLacquerId = slpl.SaleOrderPostProcessLacquerId,
                                SaleOrderId = slpl.SaleOrderId,
                                ReceivedQuantity = slpl.ReceivedQuantity,
                                LacquerCompletedQuantity = slpl.LacquerCompletedQuantity,
                                LacquerMeasurementUnit = slpl.LacquerMeasurementUnit,
                                LacquerRack = slpl.LacquerRack,
                                LacquerWastageQuantity = slpl.LacquerWastageQuantity,
                                LacquerStatus = slpl.LacquerStatus,
                                AddedBy = slpl.AddedBy,
                                AddedDate = slpl.AddedDate,
                                Rank = slpl.Rank
                            }).ToList();
                        saleOrder.SaleOrderProductionPostProcess.SaleOrderPostProcessTumbling = db.SaleOrderPostProcessTumblingTables
                            .Where(slpt => slpt.SaleOrderId == saleOrderId)
                            .Select(slpt => new SaleOrderPostProcessTumblingTableVm
                            {
                                SaleOrderPostProcessTumblingId = slpt.SaleOrderPostProcessTumblingId,
                                SaleOrderId = slpt.SaleOrderId,
                                ReceivedQuantity = slpt.ReceivedQuantity,
                                TumblingCompletedQuantity = slpt.TumblingCompletedQuantity,
                                TumblingMeasurementUnit = slpt.TumblingMeasurementUnit,
                                TumblingRack = slpt.TumblingRack,
                                TumblingWastageQuantity = slpt.TumblingWastageQuantity,
                                TumblingStatus = slpt.TumblingStatus,
                                AddedBy = slpt.AddedBy,
                                AddedDate = slpt.AddedDate,
                                Rank = slpt.Rank
                            }).ToList();

                        vmModel.Add(saleOrder);
                    }
                }

                vmModel = vmModel.OrderByDescending(x => x.SaleOrderProductionComplete.AddedDate).ToList();
            }
            return vmModel;
        }
        public ApiFunctionResponseVm GetSalePriceByProduct(SaleOrderRequestFilter request)
        {
            using var db = new Models.pmsdbContext();
            var res = db.SaleOrderTables
                        .Join(db.SaleOrderProductionTables, x => x.SaleOrderId, y => y.SaleOrderId, (x, y) => new { x, y })
                        .Where(x => x.x.SaleFormulationCodeId == request.SaleFormulationCodeId
                        && x.x.CustomerId == request.CustomerId
                        && x.y.Thick == request.Thick
                        ).Select(x =>
                        new
                        {
                            x.y.SalePrice,
                            x.x.SaleOrderId,
                            x.x.SaleOrderNumber
                        }).Take(request.NumberOfRecords).OrderByDescending(x => x.SaleOrderId).ToList();
            return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
        }
    }
}