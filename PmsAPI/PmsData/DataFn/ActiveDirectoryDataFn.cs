﻿using PmsEntity.ViewModel;
using System.Net;
using Microsoft.Graph;
using Azure.Identity;
using PmsCommon;
using System.Threading.Tasks;

namespace PmsData.DataFn
{
    public class ActiveDirectoryDataFn
    {
        public ApiFunctionResponseVm CreateADuser(UserMasterVm param)
        {
            var graphClient = GetGraphServiceClient();

            var user = new User
            {
                AccountEnabled = true,
                DisplayName = param.Name,
                MailNickname = param.Name,
                UserPrincipalName = param.Email,
                PasswordProfile = new PasswordProfile
                {
                    ForceChangePasswordNextSignIn = false,
                    Password = "xWwvJ]6NMw+bWH-d"
                }
            };

            var res = graphClient.Users.Request().AddAsync(user).Result;
            return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
        }

        public ApiFunctionResponseVm ChangeADuserpassword(UserMasterVm param)
        {
            var graphClient = GetGraphServiceClient();
            var res = graphClient.Me.ChangePassword(param.Password, param.NewPassword).Request().PostAsync();
            return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
        }

        public ApiFunctionResponseVm GetADUser()
        {
            var graphClient = GetGraphServiceClient();
            var res = graphClient.Users["********-0000-0000-c000-************"].Request().GetAsync().Result;
            return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
        }
        public async Task<ApiFunctionResponseVm> DisableADUserAsync(string ADObjectId)
        {
            try
            {
                var user = new User
                {
                    AccountEnabled = false // Disable the user
                };

                var graphClient = GetGraphServiceClient();
                await graphClient.Users[ADObjectId].Request().UpdateAsync(user);

                return new ApiFunctionResponseVm(HttpStatusCode.OK, "User Disabled Successfully.");
            }
            catch (ServiceException ex)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, $"Error disabling user: {ex.Message}");
            }
        }

        public async Task<ApiFunctionResponseVm> ListADuserpasswordAsync()
        {
            try
            {
                var graphClient = GetGraphServiceClient();
                var res = await graphClient.Users.Request().GetAsync();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
            catch (ServiceException ex)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, $"Error retrieving users: {ex.Message}");
            }
        }

        private GraphServiceClient GetGraphServiceClient()
        {
            // The client credentials flow requires that you request the
            // /.default scope, and preconfigure your permissions on the
            // app registration in Azure. An administrator must grant consent
            // to those permissions beforehand.
            var scopes = new[] { "https://graph.microsoft.com/.default" };

            // Multi-tenant apps can use "common",
            // single-tenant apps must use the tenant ID from the Azure portal
            var tenantId = KeyVault.GetKeyValue("aad-tenant-id");

            // Values from app registration
            var clientId = KeyVault.GetKeyValue("appreg-usermanagement-clientid");
            var clientSecret = KeyVault.GetKeyValue("appreg-usermanagement-secret");

            // using Azure.Identity;
            var options = new TokenCredentialOptions
            {
                AuthorityHost = AzureAuthorityHosts.AzurePublicCloud
            };

            var clientSecretCredential = new ClientSecretCredential(
                tenantId, clientId, clientSecret, options);

            return new GraphServiceClient(clientSecretCredential, scopes);
        }
    }
}
