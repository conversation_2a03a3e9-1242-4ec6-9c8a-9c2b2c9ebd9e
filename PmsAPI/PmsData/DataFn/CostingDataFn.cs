﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsCommon;
using Azure.Storage.Sas;
using PmsData.Models;
using System.Net;

namespace PmsData.DataFn
{
    public class CostingDataFn
    {
        public GlobalDataEntity GlobalData;
        public CostingDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ApiFunctionResponseVm GetFilteredCostingList(SaleOrderCostingRequestFilter filter)
        {
            List<ESalesOrderStatus> stsList = new()
            {
                ESalesOrderStatus.NotYet,
                ESalesOrderStatus.WorkPlan,
                ESalesOrderStatus.Inspection,
                ESalesOrderStatus.RawMaterialRequested,
                ESalesOrderStatus.RawMaterialIssued,
                ESalesOrderStatus.ProductionStarted,
                ESalesOrderStatus.InJumbo,
            };

            using var db = new pmsdbContext();

            // Start with essential tables and include potential joins
            var baseQuery = from so in db.SaleOrderTables
                            join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                            join soct in db.SaleOrderCostingTables on so.SaleOrderId equals soct.SaleOrderId into soctGroup
                            from soct in soctGroup.DefaultIfEmpty()
                            join sopc in db.SaleOrderProductionCompleteTables on so.SaleOrderId equals sopc.SaleOrderId into sopcGroup
                            from sopc in sopcGroup.DefaultIfEmpty()
                            join sot in db.SaleOrderTimelineTables on so.SaleOrderId equals sot.SaleOrderId into sotGroup
                            from sot in sotGroup.DefaultIfEmpty()
                            join cust in db.CustomerMasters on so.CustomerId equals cust.CustomerId into custGroup
                            from cust in custGroup.DefaultIfEmpty()
                            join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId into wpoGroup
                            from wpo in wpoGroup.DefaultIfEmpty()
                            join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId into wpGroup
                            from wp in wpGroup.DefaultIfEmpty()
                            join fcm in db.SaleFormulationCodeMasters on so.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcmGroup
                            from fcm in fcmGroup.DefaultIfEmpty()
                            join us in db.UserMasters on soct.AddedBy equals us.Email into usGroup
                            from us in usGroup.DefaultIfEmpty()
                            join ls in db.LinkedSaleOrderTables on so.SaleOrderId equals ls.ParentSaleOrder into lsGroup
                            from ls in lsGroup.DefaultIfEmpty()
                            join lsc in db.LinkedSaleOrderTables on so.SaleOrderId equals lsc.LinkedSaleOrder into lscGroup
                            from lsc in lscGroup.DefaultIfEmpty()
                            where !stsList.Contains((ESalesOrderStatus)so.Status)
                            select new { so, sop, soct, sopc, sot, cust, wp, fcm, us, ls, lsc };

            // Apply date filters based on DateType
            if (!string.IsNullOrEmpty(filter.DateType))
            {
                switch (filter.DateType.ToLowerInvariant())
                {
                    case "submitteddate":
                        baseQuery = baseQuery.Where(x =>
                            (filter.FromAddedDate == null || x.soct.AddedDate >= filter.FromAddedDate)
                            && (filter.ToAddedDate == null || x.soct.AddedDate <= filter.ToAddedDate));
                        break;

                    case "productiondate":
                        baseQuery = baseQuery.Where(x =>
                            (filter.FromProductionDate == null || x.sopc.AddedDate >= filter.FromProductionDate)
                            && (filter.ToProductionDate == null || x.sopc.AddedDate <= filter.ToProductionDate));
                        break;

                    case "finalinspectiondate":
                        baseQuery = baseQuery.Where(x =>
                            x.sot.Status == (int)ESalesOrderStatus.MoveToDispatch
                            && (filter.FromFinalInspectionDate == null || x.sot.AddedDate >= filter.FromFinalInspectionDate)
                            && (filter.ToFinalInspectionDate == null || x.sot.AddedDate <= filter.ToFinalInspectionDate));
                        break;

                    case "saleorderdate":
                        baseQuery = baseQuery.Where(x =>
                            (filter.FromSaleOrderDate == null || x.so.SaleOrderDate >= filter.FromSaleOrderDate)
                            && (filter.ToSaleOrderDate == null || x.so.SaleOrderDate <= filter.ToSaleOrderDate));
                        break;

                    case "deliverydate":
                        baseQuery = baseQuery.Where(x =>
                            (filter.FromDeliveryDate == null || x.so.DeliveryDate >= filter.FromDeliveryDate)
                            && (filter.ToDeliveryDate == null || x.so.DeliveryDate <= filter.ToDeliveryDate));
                        break;
                }
            }

            // Apply other conditional filters
            if (!string.IsNullOrEmpty(filter.CostingStatus))
            {
                baseQuery = baseQuery.Where(x => x.sop.CostingStatus == filter.CostingStatus);
            }

            if (filter.CategoryId > 0)
            {
                baseQuery = baseQuery.Where(x => x.so.CategoryId == filter.CategoryId);
            }

            if (filter.CustomerId > 0)
            {
                baseQuery = baseQuery.Where(x => x.so.CustomerId == filter.CustomerId);
            }

            if (!string.IsNullOrEmpty(filter.WorkShift))
            {
                baseQuery = baseQuery.Where(x => x.wp.WorkShift == filter.WorkShift);
            }

            if (filter.ProductionLineNo > 0)
            {
                baseQuery = baseQuery.Where(x => x.wp.ProductionLineNo == filter.ProductionLineNo);
            }

            if (filter.SaleFormulationCodeId > 0)
            {
                baseQuery = baseQuery.Where(x => x.so.SaleFormulationCodeId == filter.SaleFormulationCodeId);
            }

            if (!string.IsNullOrEmpty(filter.SaleOrderNumber))
            {
                baseQuery = baseQuery.Where(x => x.so.SaleOrderNumber.Contains(filter.SaleOrderNumber));
            }

            if (!string.IsNullOrEmpty(filter.AddedBy))
            {
                baseQuery = baseQuery.Where(x => x.soct.AddedBy.ToLower().Contains(filter.AddedBy.ToLower()));
            }

            if (!string.IsNullOrEmpty(filter.ArticleName))
            {
                baseQuery = baseQuery.Where(x => x.sop.ManufacturingProductName.ToLower().Contains(filter.ArticleName.ToLower()));
            }

            if (filter.ColorId > 0)
            {
                baseQuery = baseQuery.Where(x => x.sop.ColorId == filter.ColorId);
            }

            if (filter.GrainId > 0)
            {
                baseQuery = baseQuery.Where(x => x.sop.GrainId == filter.GrainId);
            }

            if (!string.IsNullOrEmpty(filter.ProductType))
            {
                baseQuery = baseQuery.Where(x => x.fcm.SaleFormulationCode.ToLower().StartsWith(filter.ProductType.ToLower()));
            }

            if (!string.IsNullOrEmpty(filter.OrderType))
            {
                baseQuery = baseQuery.Where(x => x.so.SaleOrderType.ToLower().Contains(filter.OrderType.ToLower()));
            }

            if (filter.ThicknessId > 0)
            {
                baseQuery = baseQuery.Where(x => x.fcm.ThicknessId == filter.ThicknessId);
            }

            // Project to final shape
            var query = baseQuery.Select(x => new SaleOrderCostingTableVm
            {
                SaleOrderId = x.so.SaleOrderId,
                FabricCost = x.soct.FabricCost,
                FabricCostLm = x.soct.FabricCostLm,
                CoatingCost = x.soct.CoatingCost,
                PasteCostLm = x.soct.PasteCostLm,
                GrainCostLm = x.soct.GrainCostLm,
                FinishingCostLm = x.soct.FinishingCostLm,
                Rejection = x.soct.Rejection,
                RmcostLm = x.soct.RmcostLm,
                ProductionCostLm = x.soct.ProductionCostLm,
                PerLmconstant = x.soct.PerLmconstant,
                OverheadCost = x.soct.OverheadCost,
                SaleOrderMaterialType = x.soct.SaleOrderMaterialType,
                PrintCostPerUnit = x.soct.PrintCostPerUnit,
                EmbossingCostPerUnit = x.soct.EmbossingCostPerUnit,
                VacuumCostPerUnit = x.soct.VacuumCostPerUnit,
                TumblingCostPerUnit = x.soct.TumblingCostPerUnit,
                LacquerCostPerUnit = x.soct.LacquerCostPerUnit,
                InlineScraping = x.soct.InlineScraping,
                PackagingCostPerUnit = x.soct.PackagingCostPerUnit,
                MiscellaneousCostPerUnit = x.soct.MiscellaneousCostPerUnit,
                AddedBy = x.us.Name,
                AddedDate = x.soct.AddedDate,
                ProductionCompletedDate = x.sopc.AddedDate,
                SaleOrderNumber = x.so.SaleOrderNumber,
                SaleOrderCode = x.so.SaleOrderCode,
                CustomerName = x.cust.CustomerName,
                CostingStatus = x.sop.CostingStatus,
                IsLiningOrder = x.lsc.LinkedSaleOrder != null,
                IsUpperOrder = x.ls.ParentSaleOrder != null,
                SaleOrderProduction = new SaleOrderProductionTableVm
                {
                    ManufacturingProductName = x.sop.ManufacturingProductName,
                    OrderQuantity = x.sop.OrderQuantity,
                    ManufacturingQuantity = x.sop.ManufacturingQuantity,
                    SalePrice = x.sop.SalePrice,
                    GrainPrice = x.sop.GrainPrice
                }
            }).Distinct();

            // Get total count and grand totals
            var totalQuery = (from b in query
                              join ls in db.LinkedSaleOrderTables on b.SaleOrderId equals ls.ParentSaleOrder into lsd
                              from ls in lsd.DefaultIfEmpty()
                              join lc in db.SaleOrderCostingTables on ls.LinkedSaleOrder equals lc.SaleOrderId into lcd
                              from lc in lcd.DefaultIfEmpty()
                              group new { b, lc } by 1 into g
                              select new
                              {
                                  Count = g.Count(),
                                  TotalOrderQty = g.Sum(x => x.b.SaleOrderProduction.OrderQuantity ?? 0),
                                  TotalManufacturingQty = g.Sum(x => x.b.SaleOrderProduction.ManufacturingQuantity ?? 0),
                                  GrandFinalTotalCost = g.Sum(x =>
                                      x.b.CostingStatus != CostingStatus.Pending &&
                                      x.b.CostingStatus != CostingStatus.PartialReady &&
                                      x.b.CostingStatus != CostingStatus.Ready ?
                                      (
                                          // Base costs
                                          (x.b.ProductionCostLm ?? 0) +
                                          (x.b.OverheadCost ?? 0) +
                                          (x.b.PackagingCostPerUnit ?? 0) +
                                          (x.b.MiscellaneousCostPerUnit ?? 0) +
                                          // Lining order costs
                                          (x.lc != null ?
                                              (x.lc.ProductionCostLm ?? 0) +
                                              (x.lc.OverheadCost ?? 0) +
                                              (x.lc.PackagingCostPerUnit ?? 0) +
                                              (x.lc.MiscellaneousCostPerUnit ?? 0)
                                              : 0)
                                      ) * (x.b.SaleOrderProduction.ManufacturingQuantity ?? 0)
                                      : 0),
                                  GrandTotalProfitLoss = g.Sum(x =>
                                      x.b.CostingStatus != CostingStatus.Pending &&
                                      x.b.CostingStatus != CostingStatus.PartialReady &&
                                      x.b.CostingStatus != CostingStatus.Ready ?
                                      (
                                          // Revenue
                                          (x.b.SaleOrderProduction.ManufacturingQuantity ?? 0) *
                                          (x.b.SaleOrderProduction.SalePrice ?? 0)
                                      ) - (
                                          // Total costs including lining
                                          (
                                              (x.b.ProductionCostLm ?? 0) +
                                              (x.b.OverheadCost ?? 0) +
                                              (x.b.PackagingCostPerUnit ?? 0) +
                                              (x.b.MiscellaneousCostPerUnit ?? 0) +
                                              // Lining order costs
                                              (x.lc != null ?
                                                  (x.lc.ProductionCostLm ?? 0) +
                                                  (x.lc.OverheadCost ?? 0) +
                                                  (x.lc.PackagingCostPerUnit ?? 0) +
                                                  (x.lc.MiscellaneousCostPerUnit ?? 0)
                                                  : 0)
                                          ) * (x.b.SaleOrderProduction.ManufacturingQuantity ?? 0)
                                      )
                                      : 0)
                              }).FirstOrDefault();

            var totalRecords = totalQuery?.Count ?? 0;
            var grandFinalTotalCost = decimal.Round(totalQuery?.GrandFinalTotalCost ?? 0, 3);
            var grandTotalProfitLoss = decimal.Round(totalQuery?.GrandTotalProfitLoss ?? 0, 3);
            var totalOrderQty = totalQuery?.TotalOrderQty ?? 0;
            var totalManufacturingQty = totalQuery?.TotalManufacturingQty ?? 0;


            // Get paginated data
            var orderedQuery = query.OrderByDescending(x => x.AddedDate)
                    .ThenByDescending(x => x.SaleOrderId);

            var finalQuery = filter.IsShowAll ? orderedQuery : orderedQuery.Skip((filter.PageNo - 1) * filter.PageSize)
                                        .Take(filter.PageSize);

            var result = finalQuery.Where(x => x.AddedDate.HasValue)
                                  .Concat(finalQuery.Where(x => !x.AddedDate.HasValue))
                                  .ToList();

            // Calculate individual record totals
            foreach (var item in result)
            {
                if (item.CostingStatus != CostingStatus.Pending &&
                    item.CostingStatus != CostingStatus.PartialReady &&
                    item.CostingStatus != CostingStatus.Ready)
                {
                    decimal LiningOrderCostPerLm = 0;
                    if (item.IsLiningOrder)
                    {
                        item.RejectionCostLm = decimal.Round(item.RmcostLm.Value * (item.Rejection.Value / 100));
                    }
                    else
                    {
                        item.RejectionCostLm = decimal.Round(item.RmcostLm.Value * (item.Rejection.Value / 100), 3);
                        var linkedSaleOrderId = db.LinkedSaleOrderTables
                            .Where(x => x.ParentSaleOrder == item.SaleOrderId)
                            .FirstOrDefault()?.LinkedSaleOrder;
                        var linkedSaleOrderCosting = db.SaleOrderCostingTables
                            .Where(x => x.SaleOrderId == linkedSaleOrderId)
                            .FirstOrDefault();
                        if (linkedSaleOrderCosting != null)
                        {
                            LiningOrderCostPerLm = linkedSaleOrderCosting.ProductionCostLm.Value +
                                                 linkedSaleOrderCosting.OverheadCost.Value +
                                                 linkedSaleOrderCosting.PackagingCostPerUnit.Value +
                                                 linkedSaleOrderCosting.MiscellaneousCostPerUnit.Value;
                        }
                    }
                    decimal PackagingMiscCostPerLm = (item.PackagingCostPerUnit ?? 0) + (item.MiscellaneousCostPerUnit ?? 0);

                    item.TotalCostLm = decimal.Round(item.ProductionCostLm.Value + item.OverheadCost.Value +
                                                   PackagingMiscCostPerLm + LiningOrderCostPerLm, 3);
                    item.FinalTotalCost = decimal.Round(item.TotalCostLm.Value *
                                                      item.SaleOrderProduction.ManufacturingQuantity.Value, 3);
                    item.ProfitLossLm = decimal.Round(item.SaleOrderProduction.SalePrice.Value - item.TotalCostLm.Value, 3);
                    item.TotalProfitLoss = decimal.Round((item.SaleOrderProduction.ManufacturingQuantity.Value *
                                                        item.SaleOrderProduction.SalePrice.Value) -
                                                       (item.TotalCostLm.Value *
                                                        item.SaleOrderProduction.ManufacturingQuantity.Value), 3);
                }
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, new
            {
                CostingListData = result,
                TotalRecords = totalRecords,
                GrandFinalTotalCost = grandFinalTotalCost,
                GrandTotalProfitLoss = grandTotalProfitLoss,
                GrandTotalOrderQty = totalOrderQty,
                GrandTotalManufacturingQty = totalManufacturingQty
            });
        }

        public ApiFunctionResponseVm AddUpdateCosting(SaleOrderCostingTableVm soc)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var existing = db.SaleOrderCostingTables.FirstOrDefault(x => x.SaleOrderId == soc.SaleOrderId);
                if (existing == null)
                {
                    SaleOrderCostingTable res = new SaleOrderCostingTable()
                    {
                        SaleOrderId = soc.SaleOrderId,
                        FabricCost = soc.FabricCost,
                        CoatingCost = soc.CoatingCost,
                        FabricCostLm = soc.FabricCostLm,
                        PasteCostLm = soc.PasteCostLm,
                        GrainCostLm = soc.GrainCostLm,
                        FinishingCostLm = soc.FinishingCostLm,
                        RmcostLm = soc.RmcostLm,
                        Rejection = soc.Rejection,
                        ProductionCostLm = soc.ProductionCostLm,
                        PerLmconstant = soc.PerLmconstant,
                        OverheadCost = soc.OverheadCost,
                        SaleOrderMaterialType = soc.SaleOrderMaterialType,
                        InlineScraping = soc.InlineScraping,
                        PrintCostPerUnit = soc.PrintCostPerUnit,
                        EmbossingCostPerUnit = soc.EmbossingCostPerUnit,
                        TumblingCostPerUnit = soc.TumblingCostPerUnit,
                        VacuumCostPerUnit = soc.VacuumCostPerUnit,
                        LacquerCostPerUnit = soc.LacquerCostPerUnit,
                        PackagingCostPerUnit = soc.PackagingCostPerUnit,
                        MiscellaneousCostPerUnit = soc.MiscellaneousCostPerUnit,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now
                    };
                    db.SaleOrderCostingTables.Add(res);
                    db.SaveChanges();
                    SaleOrderProductionTable sop = db.SaleOrderProductionTables.Where(x => x.SaleOrderId == soc.SaleOrderId).FirstOrDefault();
                    sop.CostingStatus = "Submitted";
                    db.SaveChanges();
                }
                else
                {
                    existing.FabricCost = soc.FabricCost;
                    existing.CoatingCost = soc.CoatingCost;
                    existing.FabricCostLm = soc.FabricCostLm;
                    existing.PasteCostLm = soc.PasteCostLm;
                    existing.GrainCostLm = soc.GrainCostLm;
                    existing.FinishingCostLm = soc.FinishingCostLm;
                    existing.RmcostLm = soc.RmcostLm;
                    existing.Rejection = soc.Rejection;
                    existing.ProductionCostLm = soc.ProductionCostLm;
                    existing.PerLmconstant = soc.PerLmconstant;
                    existing.OverheadCost = soc.OverheadCost;
                    existing.SaleOrderMaterialType = soc.SaleOrderMaterialType;
                    existing.InlineScraping = soc.InlineScraping;
                    existing.PrintCostPerUnit = soc.PrintCostPerUnit;
                    existing.EmbossingCostPerUnit = soc.EmbossingCostPerUnit;
                    existing.TumblingCostPerUnit = soc.TumblingCostPerUnit;
                    existing.VacuumCostPerUnit = soc.VacuumCostPerUnit;
                    existing.LacquerCostPerUnit = soc.LacquerCostPerUnit;
                    existing.PackagingCostPerUnit = soc.PackagingCostPerUnit;
                    existing.MiscellaneousCostPerUnit = soc.MiscellaneousCostPerUnit;
                    existing.UpdatedBy = GlobalData.loggedInUser;
                    existing.UpdatedDate = DateTime.Now;
                    db.SaveChanges();
                    SaleOrderProductionTable sop = db.SaleOrderProductionTables.Where(x => x.SaleOrderId == soc.SaleOrderId).FirstOrDefault();
                    sop.CostingStatus = "Submitted";
                    db.SaveChanges();
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Costing Saved Successfully.");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
            }
        }

        public decimal CalculateFabricProductCostPerLm(long saleOrderId, decimal perLmConstant)
        {
            decimal res = 0;
            SaleOrderTableVm saleOrderData = null;
            using (var db = new Models.pmsdbContext())
            {
                var insform = (from a in db.InspectionSaleFormulationCodeMasters
                               join la in db.InspectionFormulationCodeMixingTables on a.InspectionSaleFormulationCodeId equals la.InspectionSaleFormulationCodeId
                               join p in db.ProductMasters on a.FabricProductId equals p.ProductId
                               where la.SaleOrderId == saleOrderId
                               select new InspectionSaleFormulationCodeMasterVm
                               {
                                   FabricProductId = a.FabricProductId
                               }).FirstOrDefault();
                var costdata = (from cs in db.ConsumeStockProductMasters
                                join sp in db.StockProductTables on cs.StockProductId equals sp.StockProductId
                                where cs.SaleOrderId == saleOrderId
                                select new
                                {
                                    cs.ProductId,
                                    sp.PricePerUnit,
                                    cs.Unit
                                }).ToList();
                var FabricGsm = db.SaleOrderProductionTables.Where(x => x.SaleOrderId == saleOrderId).FirstOrDefault().FabricGsm;
                saleOrderData = new SaleOrderTableVm
                {
                    FormulationFabricProductPrice = costdata.Any(x => x.ProductId == insform.FabricProductId) ? costdata.FirstOrDefault(x => x.ProductId == insform.FabricProductId).PricePerUnit : 0,
                    FormulationFabricProductUnit = costdata.Any(x => x.ProductId == insform.FabricProductId) ? costdata.FirstOrDefault(x => x.ProductId == insform.FabricProductId).Unit : null
                };

                if (saleOrderData.FormulationFabricProductUnit == "Mtrs")
                {
                    res = saleOrderData.FormulationFabricProductPrice.Value;
                }
                else
                {
                    res = decimal.Round(((FabricGsm.Value / 1000) * perLmConstant) * saleOrderData.FormulationFabricProductPrice.Value, 3);

                }
            }
            return res;
        }
    }
}
