﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class BranchDataFn
    {
        public List<BranchMasterVm> GetAllBranches()
        {
            List<BranchMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.BranchMasters
                       select new BranchMasterVm
                       {
                           BranchId = a.BranchId,
                           BranchName = a.BranchName,
                           BranchCode = a.BranchCode,
                           BranchDesc = a.BranchDesc,
                           BranchAddedBy = a.BranchAddedBy,
                           BranchAddedDate = a.BranchAddedDate
                       }).OrderBy(x => x.BranchCode).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateBranch(BranchMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.BranchId == 0)
                {
                    var rec = db.BranchMasters.Where(x => x.BranchCode == br.BranchCode).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                BranchMaster res = new BranchMaster();
                if (br.BranchId == 0)
                {
                    res.BranchName = br.BranchName;
                    res.BranchCode = br.BranchCode;
                    res.BranchDesc = br.BranchDesc;
                    res.BranchAddedBy = br.BranchAddedBy;
                    res.BranchAddedDate = System.DateTime.Now;
                    db.BranchMasters.Add(res);
                }
                else
                {
                    res = db.BranchMasters.Where(x => x.BranchId == br.BranchId).FirstOrDefault();
                    if (res != null)
                    {
                        res.BranchName = br.BranchName;
                        res.BranchCode = br.BranchCode;
                        res.BranchDesc = br.BranchDesc;
                        res.BranchAddedBy = br.BranchAddedBy;
                        res.BranchAddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
    }
}
