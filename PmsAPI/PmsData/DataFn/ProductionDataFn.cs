﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace PmsData.DataFn
{
    public class ProductionDataFn
    {
        public GlobalDataEntity GlobalData;
        public ProductionDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ApiFunctionResponseVm AddUpdateProductionDowntimeReason(ProductionDowntimeReasonMasterVm model)
        {
            var db = new pmsdbContext();
            if (model.ProductionDowntimeReasonId == 0)
            {
                var existingCheck = db.ProductionDowntimeReasonMasters.FirstOrDefault(x => x.ReasonCode == model.ReasonCode || x.ReasonName == model.ReasonName);
                if (existingCheck != null && existingCheck.IsDeleted == false)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Production downtime reason already exists");
                }
                else if (existingCheck != null && existingCheck.IsDeleted == true)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.EarlyHints, "Production downtime existing found in deleted status, please update the existingreason to enable it again.");
                }
                else if (existingCheck != null && existingCheck.IsActive == false)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.EarlyHints, "Production downtime reason is inactive, please enable it to use.");
                }
            }

            if (model.ProductionDowntimeReasonId == 0)
            {
                var productionDowntimeReason = new ProductionDowntimeReasonMaster
                {
                    ReasonCode = model.ReasonCode ?? GenerateReasonCode(model.ReasonName),
                    ReasonName = model.ReasonName,
                    Description = model.Description,
                    DowntimeType = model.DowntimeType,
                    ProductionLineType = model.ProductionLineType,
                    StandardDurationMinutes = model.StandardDurationMinutes,
                    IsActive = model.IsActive,
                    CreatedBy = GlobalData.loggedInUser,
                    CreatedOn = DateTime.Now,
                    IsDeleted = false
                };
                db.ProductionDowntimeReasonMasters.Add(productionDowntimeReason);
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Production downtime reason added successfully");
            }
            else
            {
                var productionDowntimeReason = db.ProductionDowntimeReasonMasters.FirstOrDefault(x => x.ProductionDowntimeReasonId == model.ProductionDowntimeReasonId);
                if (productionDowntimeReason != null)
                {
                    productionDowntimeReason.ReasonCode = model.ReasonCode ?? GenerateReasonCode(model.ReasonName);
                    productionDowntimeReason.ReasonName = model.ReasonName;
                    productionDowntimeReason.Description = model.Description;
                    productionDowntimeReason.DowntimeType = model.DowntimeType;
                    productionDowntimeReason.ProductionLineType = model.ProductionLineType;
                    productionDowntimeReason.StandardDurationMinutes = model.StandardDurationMinutes;
                    productionDowntimeReason.IsActive = model.IsActive;
                    productionDowntimeReason.ModifiedBy = GlobalData.loggedInUser;
                    productionDowntimeReason.ModifiedOn = DateTime.Now;
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Production downtime reason updated successfully");
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Production downtime reason not found");
        }
        private string GenerateReasonCode(string reasonName)
        {
            if (string.IsNullOrEmpty(reasonName))
                return null;

            using var db = new pmsdbContext();
            var words = reasonName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            string generatedCode;

            // Strategy 1: 3 letters of first word + 2 letters of second word
            if (words.Length >= 2)
            {
                generatedCode = (words[0].Length >= 3 ? words[0].Substring(0, 3) : words[0].PadRight(3, 'X')) +
                               (words[1].Length >= 2 ? words[1].Substring(0, 2) : words[1].PadRight(2, 'X'));
            }
            else
            {
                // For single word, take first 5 letters
                generatedCode = words[0].Length >= 5
                    ? words[0].Substring(0, 5)
                    : words[0].PadRight(5, 'X');
            }

            generatedCode = generatedCode.ToUpper();

            // Check if code exists
            if (!db.ProductionDowntimeReasonMasters.Any(x => x.ReasonCode == generatedCode))
            {
                return generatedCode;
            }

            // Strategy 2: First letter of each word + padding
            generatedCode = string.Join("", words.Select(w => w[0])).ToUpper().PadRight(5, 'X');
            if (!db.ProductionDowntimeReasonMasters.Any(x => x.ReasonCode == generatedCode))
            {
                return generatedCode;
            }

            // Strategy 3: First two letters of each word + padding
            if (words.Length >= 2)
            {
                generatedCode = string.Join("", words.Select(w => w.Length >= 2 ? w.Substring(0, 2) : w.PadRight(2, 'X')))
                    .Substring(0, Math.Min(5, words.Length * 2))
                    .PadRight(5, 'X')
                    .ToUpper();

                if (!db.ProductionDowntimeReasonMasters.Any(x => x.ReasonCode == generatedCode))
                {
                    return generatedCode;
                }
            }

            // Strategy 4: Add numbers at the end (keeping total length at 5)
            var baseCode = words[0].Length >= 3
                ? words[0].Substring(0, 3).ToUpper()
                : words[0].PadRight(3, 'X').ToUpper();

            int counter = 1;
            while (counter <= 99)
            {
                var numberSuffix = counter.ToString("00");
                var candidateCode = baseCode + numberSuffix;
                if (!db.ProductionDowntimeReasonMasters.Any(x => x.ReasonCode == candidateCode))
                {
                    return candidateCode;
                }
                counter++;
            }

            // Final fallback: Use 'R' + 4 characters from GUID
            return "R" + Guid.NewGuid().ToString().Substring(0, 4).ToUpper();
        }
        public ApiFunctionResponseVm ProductionDowntimeReasonStatusChange(ProductionDowntimeReasonMasterVm model)
        {
            var db = new pmsdbContext();
            var productionDowntimeReason = db.ProductionDowntimeReasonMasters.FirstOrDefault(x => x.ProductionDowntimeReasonId == model.ProductionDowntimeReasonId);
            if (productionDowntimeReason != null)
            {
                if (model.Action == "Active")
                {
                    productionDowntimeReason.IsActive = true;
                }
                else if (model.Action == "Inactive")
                {
                    productionDowntimeReason.IsActive = false;
                }
                else if (model.Action == "Delete")
                {
                    productionDowntimeReason.IsActive = false;
                    productionDowntimeReason.IsDeleted = true;
                }
                productionDowntimeReason.ModifiedBy = GlobalData.loggedInUser;
                productionDowntimeReason.ModifiedOn = DateTime.Now;
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Production downtime reason status changed successfully");
            }
            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Production downtime reason not found");
        }
        public ApiFunctionResponseVm GetProductionDowntimeReasonList()
        {
            var db = new pmsdbContext();
            var productionDowntimeReasonList = db.ProductionDowntimeReasonMasters.Where(x => x.IsDeleted == false).ToList();
            return new ApiFunctionResponseVm(HttpStatusCode.OK, productionDowntimeReasonList);
        }
        public ApiFunctionResponseVm GetProductionDowntimeActiveReasonList()
        {
            var db = new pmsdbContext();
            var productionDowntimeReasonList = db.ProductionDowntimeReasonMasters
            .Where(x => x.IsDeleted == false && x.IsActive == true)
            .Select(x => new ProductionDowntimeReasonMasterVm
            {
                ProductionDowntimeReasonId = x.ProductionDowntimeReasonId,
                ReasonName = x.ReasonName,
                ReasonCode = x.ReasonCode
            })
            .ToList();
            return new ApiFunctionResponseVm(HttpStatusCode.OK, productionDowntimeReasonList);
        }
        public ApiFunctionResponseVm GetProductionDowntimeReasonById(long productionDowntimeReasonId)
        {
            var db = new pmsdbContext();
            var productionDowntimeReason = db.ProductionDowntimeReasonMasters.FirstOrDefault(x => x.ProductionDowntimeReasonId == productionDowntimeReasonId);
            return new ApiFunctionResponseVm(HttpStatusCode.OK, productionDowntimeReason);
        }
        public ApiFunctionResponseVm GetProductionDowntimeList(ProductionDowntimeFilterVm filter)
        {
            var db = new pmsdbContext();

            // Build base query
            var baseQuery = db.ProductionDowntimeTables
                .Include(x => x.ProductionDowntimeReason)
                .Where(x => x.IsDeleted == false
                    && (filter.StartDate == null || x.StartTime >= filter.StartDate)
                    && (filter.EndDate == null || x.EndTime <= filter.EndDate)
                    && (string.IsNullOrEmpty(filter.ProductionLineType) || x.ProductionLineType == filter.ProductionLineType)
                    && (filter.ProductionLineNo <= 0 || x.ProductionLineNo == filter.ProductionLineNo)
                    && (filter.ProductionDowntimeReasonId == 0 || x.ProductionDowntimeReasonId == filter.ProductionDowntimeReasonId)
                    && (filter.IsExceededDuration == null ||
                        (x.ExcessDurationMinutes > 0 && filter.IsExceededDuration == true) ||
                        (x.ExcessDurationMinutes == 0 && filter.IsExceededDuration == false))
                );

            // Get total count
            var totalRecords = baseQuery.Count();

            // Get paginated data
            var productionDowntimeList = baseQuery
                .OrderByDescending(x => x.ProductionDowntimeId)
                .Skip((filter.PageNo - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(x => new ProductionDowntimeTableVm
                {
                    ProductionDowntimeId = x.ProductionDowntimeId,
                    ProductionLineNo = x.ProductionLineNo,
                    ProductionDowntimeReasonId = x.ProductionDowntimeReasonId,
                    ReasonName = x.ProductionDowntimeReason.ReasonName,
                    ReasonCode = x.ProductionDowntimeReason.ReasonCode,
                    ProductionLineType = x.ProductionLineType,
                    StartTime = x.StartTime,
                    EndTime = x.EndTime,
                    StandardDurationMinutes = x.ProductionDowntimeReason.StandardDurationMinutes,
                    ActualDurationMinutes = x.ActualDurationMinutes,
                    ExcessDurationMinutes = x.ExcessDurationMinutes,
                    Comments = x.Comments,
                    CreatedBy = x.CreatedBy,
                    CreatedOn = x.CreatedOn,
                    ModifiedBy = x.ModifiedBy,
                    ModifiedOn = x.ModifiedOn
                })
                .ToList();

            // Format durations
            productionDowntimeList.ForEach(x =>
            {
                x.ActualDurationFormatted = FormatDurationToTime(x.ActualDurationMinutes);
                x.ExcessDurationFormatted = FormatDurationToTime(x.ExcessDurationMinutes);
                x.StandardDurationFormatted = FormatDurationToTime(x.StandardDurationMinutes);
            });

            return new ApiFunctionResponseVm(HttpStatusCode.OK, new { ProductionDowntimeList = productionDowntimeList, TotalRecords = totalRecords });
        }
        public ApiFunctionResponseVm AddUpdateProductionDowntime(ProductionDowntimeTableVm model)
        {
            try
            {
                var db = new pmsdbContext();
                // Validate start and end times are not the same
                if (model.StartTime == model.EndTime)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Start time and end time cannot be the same");
                }

                // Validate start time is before end time
                if (model.StartTime > model.EndTime)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Start time must be before end time");
                }

                var productionDowntime = new ProductionDowntimeTable();
                if (model.ProductionDowntimeId == 0)
                {
                    productionDowntime.ProductionDowntimeReasonId = model.ProductionDowntimeReasonId;
                    productionDowntime.ProductionLineType = model.ProductionLineType;
                    productionDowntime.ProductionLineNo = model.ProductionLineNo;
                    productionDowntime.StartTime = model.StartTime;
                    productionDowntime.EndTime = model.EndTime;
                    productionDowntime.Comments = model.Comments;
                    productionDowntime.CreatedBy = GlobalData.loggedInUser;
                    productionDowntime.CreatedOn = DateTime.Now;
                    productionDowntime.IsDeleted = false;
                    db.ProductionDowntimeTables.Add(productionDowntime);
                    db.SaveChanges();

                    var productionDowntimeReason = db.ProductionDowntimeReasonMasters.FirstOrDefault(x => x.ProductionDowntimeReasonId == model.ProductionDowntimeReasonId && x.IsDeleted == false && x.IsActive == true);
                    if (productionDowntimeReason != null)
                    {
                        decimal? standardDurationMinutes = productionDowntimeReason.StandardDurationMinutes;
                        decimal? actualDurationMinutes = (decimal)((model.EndTime - model.StartTime).Value.TotalSeconds / 60.0);
                        decimal? excessDurationMinutes = (actualDurationMinutes - standardDurationMinutes) < 0 ? 0 : (actualDurationMinutes - standardDurationMinutes);

                        // Round to 2 decimal places for minutes.seconds format
                        productionDowntime.ExcessDurationMinutes = Math.Round(excessDurationMinutes.Value, 2);
                    }

                    if (productionDowntime.ExcessDurationMinutes < 0 || productionDowntime.ActualDurationMinutes < 0)
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Invalid downtime duration");
                    }
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Production downtime added successfully");
                }
                else
                {
                    productionDowntime = db.ProductionDowntimeTables
                    .Include(x => x.ProductionDowntimeReason)
                    .FirstOrDefault(x => x.ProductionDowntimeId == model.ProductionDowntimeId);
                    if (productionDowntime != null)
                    {
                        productionDowntime.ProductionDowntimeReasonId = model.ProductionDowntimeReasonId;
                        productionDowntime.ProductionLineType = model.ProductionLineType;
                        productionDowntime.ProductionLineNo = model.ProductionLineNo;
                        productionDowntime.StartTime = model.StartTime;
                        productionDowntime.EndTime = model.EndTime;
                        productionDowntime.Comments = model.Comments;
                        productionDowntime.ModifiedBy = GlobalData.loggedInUser;
                        productionDowntime.ModifiedOn = DateTime.Now;
                        db.SaveChanges();

                        var productionDowntimeReason = db.ProductionDowntimeReasonMasters.FirstOrDefault(x => x.ProductionDowntimeReasonId == model.ProductionDowntimeReasonId && x.IsDeleted == false && x.IsActive == true);
                        if (productionDowntimeReason != null)
                        {
                            decimal? standardDurationMinutes = productionDowntimeReason.StandardDurationMinutes;
                            decimal? actualDurationMinutes = (decimal)((model.EndTime - model.StartTime).Value.TotalSeconds / 60.0);
                            decimal? excessDurationMinutes = (actualDurationMinutes - standardDurationMinutes) < 0 ? 0 : (actualDurationMinutes - standardDurationMinutes);

                            // Round to 2 decimal places for minutes.seconds format
                            productionDowntime.ExcessDurationMinutes = Math.Round(excessDurationMinutes.Value, 2);
                        }

                        if (productionDowntime.ExcessDurationMinutes < 0 || productionDowntime.ActualDurationMinutes < 0)
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Invalid downtime duration");
                        }
                        db.SaveChanges();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Production downtime updated successfully");
                    }
                }
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Production downtime not found");
            }
            catch (Exception ex)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, ex.Message);
            }
        }
        public ApiFunctionResponseVm GetProductionDowntimeById(long productionDowntimeId)
        {
            var db = new pmsdbContext();
            var productionDowntime = db.ProductionDowntimeTables
                .Include(x => x.ProductionDowntimeReason)
                .Select(x => new ProductionDowntimeTableVm
                {
                    ProductionDowntimeId = x.ProductionDowntimeId,
                    ProductionLineNo = x.ProductionLineNo,
                    ProductionDowntimeReasonId = x.ProductionDowntimeReasonId,
                    ReasonName = x.ProductionDowntimeReason.ReasonName,
                    ReasonCode = x.ProductionDowntimeReason.ReasonCode,
                    ProductionLineType = x.ProductionLineType,
                    StartTime = x.StartTime,
                    EndTime = x.EndTime,
                    ActualDurationMinutes = x.ActualDurationMinutes,
                    ExcessDurationMinutes = x.ExcessDurationMinutes,
                    Comments = x.Comments,
                    CreatedBy = x.CreatedBy,
                    CreatedOn = x.CreatedOn,
                    ModifiedBy = x.ModifiedBy,
                    ModifiedOn = x.ModifiedOn
                })
                .FirstOrDefault(x => x.ProductionDowntimeId == productionDowntimeId);
            return new ApiFunctionResponseVm(HttpStatusCode.OK, productionDowntime);
        }
        public ApiFunctionResponseVm DeleteProductionDowntime(long productionDowntimeId)
        {
            var db = new pmsdbContext();
            var productionDowntime = db.ProductionDowntimeTables.FirstOrDefault(x => x.ProductionDowntimeId == productionDowntimeId);
            if (productionDowntime != null)
            {
                productionDowntime.IsDeleted = true;
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Production downtime deleted successfully");
            }
            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Production downtime not found");
        }
        public ApiFunctionResponseVm GetProductionDowntimeScheduleById(long productionDowntimeScheduleId)
        {
            var db = new pmsdbContext();
            var productionDowntimeSchedule = db.ProductionDowntimeScheduleds
            .Include(x => x.ProductionDowntimeReason)
            .Select(x => new ProductionDowntimeScheduledVm
            {
                ScheduledDowntimeId = x.ScheduledDowntimeId,
                ProductionDowntimeReasonId = x.ProductionDowntimeReasonId,
                ReasonName = x.ProductionDowntimeReason.ReasonName,
                ReasonCode = x.ProductionDowntimeReason.ReasonCode,
                StartTime = DateTime.Today.Add(x.StartTime),
                EndTime = DateTime.Today.Add(x.EndTime),
                RecurrencePattern = x.RecurrencePattern,
                ApplicableDays = x.ApplicableDays,
                DayOfMonth = x.DayOfMonth,
                IsRecurring = x.IsRecurring,
                EffectiveFrom = x.EffectiveFrom,
                EffectiveTo = x.EffectiveTo,
                ProductionLineNo = x.ProductionLineNo,
                IsActive = x.IsActive,
                CreatedBy = x.CreatedBy,
                CreatedOn = x.CreatedOn,
                ModifiedBy = x.ModifiedBy,
                ModifiedOn = x.ModifiedOn,
                IsDeleted = x.IsDeleted
            })
            .FirstOrDefault(x => x.ScheduledDowntimeId == productionDowntimeScheduleId);
            return new ApiFunctionResponseVm(HttpStatusCode.OK, productionDowntimeSchedule);
        }
        public ApiFunctionResponseVm AddUpdateProductionDowntimeSchedule(ProductionDowntimeScheduledVm model)
        {
            var db = new pmsdbContext();
            var productionDowntimeSchedule = new ProductionDowntimeScheduled();
            try
            {
                if (model.ScheduledDowntimeId == 0)
                {
                    if (ValidateScheduleOverlap(model, db))
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                            "Schedule overlaps with existing downtime schedule");
                    }
                    productionDowntimeSchedule.ProductionDowntimeReasonId = model.ProductionDowntimeReasonId;
                    productionDowntimeSchedule.ProductionLineNo = model.ProductionLineNo;
                    productionDowntimeSchedule.StartTime = new TimeSpan(model.StartTime.Hour, model.StartTime.Minute, 0);
                    productionDowntimeSchedule.EndTime = new TimeSpan(model.EndTime.Hour, model.EndTime.Minute, 0);
                    productionDowntimeSchedule.RecurrencePattern = model.RecurrencePattern;
                    productionDowntimeSchedule.ApplicableDays = JsonConvert.SerializeObject(model.ApplicableDays?.Split(",").ToList());
                    productionDowntimeSchedule.DayOfMonth = model.DayOfMonth;
                    productionDowntimeSchedule.IsRecurring = model.IsRecurring;
                    productionDowntimeSchedule.EffectiveFrom = model.EffectiveFrom;
                    productionDowntimeSchedule.EffectiveTo = model.EffectiveTo;
                    productionDowntimeSchedule.IsActive = model.IsActive;
                    productionDowntimeSchedule.CreatedBy = GlobalData.loggedInUser;
                    productionDowntimeSchedule.CreatedOn = DateTime.Now;
                    productionDowntimeSchedule.IsDeleted = false;
                    db.ProductionDowntimeScheduleds.Add(productionDowntimeSchedule);
                    db.SaveChanges();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Production downtime schedule added successfully");
                }
                else
                {
                    if (ValidateScheduleOverlap(model, db))
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                            "Schedule overlaps with existing downtime schedule");
                    }
                    productionDowntimeSchedule = db.ProductionDowntimeScheduleds.FirstOrDefault(x => x.ScheduledDowntimeId == model.ScheduledDowntimeId);
                    if (productionDowntimeSchedule != null)
                    {
                        productionDowntimeSchedule.ProductionDowntimeReasonId = model.ProductionDowntimeReasonId;
                        productionDowntimeSchedule.ProductionLineNo = model.ProductionLineNo;
                        productionDowntimeSchedule.StartTime = new TimeSpan(model.StartTime.Hour, model.StartTime.Minute, 0);
                        productionDowntimeSchedule.EndTime = new TimeSpan(model.EndTime.Hour, model.EndTime.Minute, 0);
                        productionDowntimeSchedule.RecurrencePattern = model.RecurrencePattern;
                        productionDowntimeSchedule.ApplicableDays = JsonConvert.SerializeObject(model.ApplicableDays?.Split(",").ToList());
                        productionDowntimeSchedule.DayOfMonth = model.DayOfMonth;
                        productionDowntimeSchedule.IsRecurring = model.IsRecurring;
                        productionDowntimeSchedule.EffectiveFrom = model.EffectiveFrom;
                        productionDowntimeSchedule.EffectiveTo = model.EffectiveTo;
                        productionDowntimeSchedule.IsActive = model.IsActive;
                        productionDowntimeSchedule.ModifiedBy = GlobalData.loggedInUser;
                        productionDowntimeSchedule.ModifiedOn = DateTime.Now;
                        db.SaveChanges();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Production downtime schedule updated successfully");
                    }
                }
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Production downtime schedule not found");
            }
            catch (Exception ex)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, ex.Message);
            }
        }
        public ApiFunctionResponseVm DeleteProductionDowntimeSchedule(long productionDowntimeScheduleId)
        {
            var db = new pmsdbContext();
            var productionDowntimeSchedule = db.ProductionDowntimeScheduleds.FirstOrDefault(x => x.ScheduledDowntimeId == productionDowntimeScheduleId);
            if (productionDowntimeSchedule != null)
            {
                productionDowntimeSchedule.IsDeleted = true;
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Production downtime schedule deleted successfully");
            }
            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Production downtime schedule not found");
        }
        public ApiFunctionResponseVm GetProductionDowntimeScheduleListByReasonId(long productionDowntimeReasonId)
        {
            var db = new pmsdbContext();
            var productionDowntimeScheduleList = db.ProductionDowntimeScheduleds
            .Include(x => x.ProductionDowntimeReason)
            .Where(x => x.ProductionDowntimeReasonId == productionDowntimeReasonId && x.IsDeleted == false)
            .Select(x => new ProductionDowntimeScheduledVm
            {
                ScheduledDowntimeId = x.ScheduledDowntimeId,
                ProductionDowntimeReasonId = x.ProductionDowntimeReasonId,
                ReasonName = x.ProductionDowntimeReason.ReasonName,
                ReasonCode = x.ProductionDowntimeReason.ReasonCode,
                StartTime = DateTime.Today.Add(x.StartTime),
                EndTime = DateTime.Today.Add(x.EndTime),
                RecurrencePattern = x.RecurrencePattern,
                ApplicableDays = x.ApplicableDays,
                DayOfMonth = x.DayOfMonth,
                IsRecurring = x.IsRecurring,
                EffectiveFrom = x.EffectiveFrom,
                EffectiveTo = x.EffectiveTo,
                ProductionLineNo = x.ProductionLineNo,
                IsActive = x.IsActive,
            })
            .ToList();
            productionDowntimeScheduleList.ForEach(x =>
            {
                if (x.ApplicableDays != null && x.ApplicableDays != "null")
                    x.ApplicableDays = string.Join(", ", JsonConvert.DeserializeObject<List<string>>(x.ApplicableDays));
                else
                    x.ApplicableDays = "N/A";
            });
            return new ApiFunctionResponseVm(HttpStatusCode.OK, productionDowntimeScheduleList);
        }
        public ApiFunctionResponseVm BulkCreateDowntimeSchedule(List<ProductionDowntimeScheduledVm> models)
        {
            try
            {
                var db = new pmsdbContext();
                foreach (var model in models)
                {
                    if (ValidateScheduleOverlap(model, db))
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest,
                            $"Schedule overlap detected for Production Line {model.ProductionLineNo}");
                    }
                }

                foreach (var model in models)
                {
                    var result = AddUpdateProductionDowntimeSchedule(model);
                    if (result.StatusCode != HttpStatusCode.OK)
                        return result;
                }

                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Bulk schedules created successfully");
            }
            catch (Exception ex)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        private bool ValidateScheduleOverlap(ProductionDowntimeScheduledVm model, pmsdbContext db)
        {
            // First check if any existing schedule's date range overlaps
            var existingSchedules = db.ProductionDowntimeScheduleds
                .Include(x => x.ProductionDowntimeReason)
                .Where(x => x.ProductionLineNo == model.ProductionLineNo
                    && x.IsDeleted == false
                    && x.IsActive == true
                    && x.ScheduledDowntimeId != model.ScheduledDowntimeId
                    && x.ProductionDowntimeReasonId == model.ProductionDowntimeReasonId
                    && (
                        // Existing schedule starts before or on new schedule's end
                        x.EffectiveFrom <= (model.EffectiveTo ?? DateTime.MaxValue)
                        && (
                            // Existing schedule has no end (runs indefinitely)
                            x.EffectiveTo == null
                            ||
                            // Existing schedule ends after or on new schedule's start
                            x.EffectiveTo >= model.EffectiveFrom
                        )
                    )
                    && x.RecurrencePattern == model.RecurrencePattern)
                .ToList();

            // If no date range overlap, no need to check further
            if (!existingSchedules.Any())
                return false;

            // For overlapping date ranges, check time and pattern overlaps
            foreach (var schedule in existingSchedules)
            {
                bool timeOverlap = DoTimesOverlap(
                    model.StartTime.TimeOfDay,
                    model.EndTime.TimeOfDay,
                    schedule.StartTime,
                    schedule.EndTime);

                if (!timeOverlap) continue;

                switch (model.RecurrencePattern?.ToLower())
                {
                    case "daily":
                        return true;

                    case "weekly":
                        var modelDays = JsonConvert.DeserializeObject<List<string>>(model.ApplicableDays);
                        var scheduleDays = JsonConvert.DeserializeObject<List<string>>(schedule.ApplicableDays);
                        if (modelDays.Intersect(scheduleDays).Any())
                            return true;
                        break;

                    case "monthly":
                        if (model.DayOfMonth == schedule.DayOfMonth)
                            return true;
                        break;
                }
            }

            return false;
        }

        private bool DoTimesOverlap(TimeSpan start1, TimeSpan end1, TimeSpan start2, TimeSpan end2)
        {
            if (end1 < start1)
                end1 = end1.Add(TimeSpan.FromDays(1));
            if (end2 < start2)
                end2 = end2.Add(TimeSpan.FromDays(1));

            return start1 < end2 && end1 > start2;
        }

        private bool DoSchedulePatternsOverlap(ProductionDowntimeScheduledVm schedule1, ProductionDowntimeScheduled schedule2)
        {
            if (schedule1.RecurrencePattern != schedule2.RecurrencePattern)
                return false;

            switch (schedule1.RecurrencePattern)
            {
                case "Daily":
                    return true;
                case "Weekly":
                    var days1 = JsonConvert.DeserializeObject<List<string>>(schedule1.ApplicableDays);
                    var days2 = JsonConvert.DeserializeObject<List<string>>(schedule2.ApplicableDays);
                    return days1.Intersect(days2).Any();
                case "Monthly":
                    return schedule1.DayOfMonth == schedule2.DayOfMonth;
                default:
                    return false;
            }
        }
        public DateTime? GetNextScheduledDowntime(long productionLineNo)
        {
            var db = new pmsdbContext();
            var now = DateTime.Now;
            var schedules = db.ProductionDowntimeScheduleds
                .Where(x => x.ProductionLineNo == productionLineNo
                    && x.IsActive == true
                    && x.IsDeleted == false
                    && (x.EffectiveTo == null || x.EffectiveTo >= now))
                .ToList();

            var nextOccurrence = schedules
                .Select(s => CalculateNextOccurrence(s, now))
                .Where(d => d.HasValue)
                .OrderBy(d => d)
                .FirstOrDefault();

            return nextOccurrence;
        }

        private DateTime? CalculateNextOccurrence(ProductionDowntimeScheduled schedule, DateTime fromDate)
        {
            switch (schedule.RecurrencePattern)
            {
                case "Daily":
                    return fromDate.Date.Add(schedule.StartTime);

                case "Weekly":
                    var applicableDays = JsonConvert.DeserializeObject<List<string>>(schedule.ApplicableDays);
                    var nextDay = Enumerable.Range(0, 7)
                        .Select(i => fromDate.AddDays(i))
                        .FirstOrDefault(d => applicableDays.Contains(d.DayOfWeek.ToString()));
                    return nextDay.Date.Add(schedule.StartTime);

                case "Monthly":
                    var nextMonth = fromDate;
                    if (fromDate.Day > schedule.DayOfMonth.Value)
                        nextMonth = fromDate.AddMonths(1);
                    return new DateTime(nextMonth.Year, nextMonth.Month, schedule.DayOfMonth.Value)
                        .Add(schedule.StartTime);

                default:
                    return null;
            }
        }
        public List<ScheduledDowntimeVm> GetScheduledDowntimesForPeriod(pmsdbContext db, DateTime fromDate, DateTime toDate, int? productionLineNo, string productionLineType)
        {
            var schedules = db.ProductionDowntimeScheduleds
                .Include(x => x.ProductionDowntimeReason)
                .Where(x => x.IsActive == true && x.IsDeleted == false
                    && (x.EffectiveTo == null || x.EffectiveTo >= fromDate)
                    && (x.EffectiveFrom <= toDate)
                    && (!productionLineNo.HasValue || x.ProductionLineNo == productionLineNo.Value)
                    && (x.ProductionDowntimeReason.ProductionLineType == productionLineType))
                .Select(x => new ScheduledDowntimeVm
                {
                    StartTime = x.StartTime,
                    EndTime = x.EndTime,
                    RecurrencePattern = x.RecurrencePattern,
                    ApplicableDays = x.ApplicableDays,
                    DayOfMonth = x.DayOfMonth,
                    Reason = new ProductionDowntimeReasonMasterVm
                    {
                        ReasonName = x.ProductionDowntimeReason.ReasonName,
                        ReasonCode = x.ProductionDowntimeReason.ReasonCode,
                        ProductionLineType = x.ProductionDowntimeReason.ProductionLineType,
                        DowntimeType = x.ProductionDowntimeReason.DowntimeType
                    },
                    EffectiveFrom = x.EffectiveFrom,
                    EffectiveTo = x.EffectiveTo,
                    ProductionLineNo = x.ProductionLineNo,
                    StartDateTime = fromDate,
                    EndDateTime = toDate
                })
                .ToList();

            return GetExpandedDowntimes(schedules, fromDate, toDate);
        }

        public void AddTimeRangeWithScheduledDowntimes(
            List<MfgHearbeatReportChartDataVm> chartData,
            DateTime rangeStart,
            DateTime rangeEnd,
            List<ScheduledDowntimeVm> scheduledDowntimes,
            string defaultStatus, string defaultReason)
        {
            var currentTime = rangeStart;
            var applicableDowntimes = scheduledDowntimes
                .Where(x => x.StartDateTime < rangeEnd && x.EndDateTime > rangeStart)
                .OrderBy(x => x.StartDateTime)
                .ToList();

            foreach (var downtime in applicableDowntimes)
            {
                // Add gap before downtime if exists
                if (currentTime < downtime.StartDateTime)
                {
                    chartData.Add(new MfgHearbeatReportChartDataVm
                    {
                        startTime = currentTime,
                        endTime = downtime.StartDateTime,
                        status = defaultStatus,
                        reason = defaultReason
                    });
                }

                // Add scheduled downtime
                chartData.Add(new MfgHearbeatReportChartDataVm
                {
                    startTime = downtime.StartDateTime > rangeStart ? downtime.StartDateTime : rangeStart,
                    endTime = downtime.EndDateTime < rangeEnd ? downtime.EndDateTime : rangeEnd,
                    status = "Scheduled Downtime",
                    reason = downtime.Reason.ReasonName
                });

                currentTime = downtime.EndDateTime;
            }

            // Add remaining gap if exists
            if (currentTime < rangeEnd)
            {
                chartData.Add(new MfgHearbeatReportChartDataVm
                {
                    startTime = currentTime,
                    endTime = rangeEnd,
                    status = defaultStatus,
                    reason = defaultReason
                });
            }
        }

        private List<ScheduledDowntimeVm> GetExpandedDowntimes(List<ScheduledDowntimeVm> schedules, DateTime fromDate, DateTime toDate)
        {
            var expandedDowntimes = new List<ScheduledDowntimeVm>();

            foreach (var schedule in schedules)
            {
                // Ensure we don't process dates outside effective range
                var effectiveStart = schedule.EffectiveFrom.HasValue ?
                    new DateTime(Math.Max(fromDate.Ticks, schedule.EffectiveFrom.Value.Ticks)) :
                    fromDate;

                var effectiveEnd = schedule.EffectiveTo.HasValue ?
                    new DateTime(Math.Min(toDate.Ticks, schedule.EffectiveTo.Value.Ticks)) :
                    toDate;

                if (effectiveEnd < effectiveStart)
                    continue;

                // Process each day in the range
                var currentDate = effectiveStart.Date;
                while (currentDate <= effectiveEnd)
                {
                    bool shouldAddSchedule = false;

                    switch (schedule.RecurrencePattern)
                    {
                        case "Daily":
                            shouldAddSchedule = true;
                            break;

                        case "Weekly":
                            var applicableDays = JsonConvert.DeserializeObject<List<string>>(schedule.ApplicableDays);
                            shouldAddSchedule = applicableDays.Contains(currentDate.DayOfWeek.ToString());
                            break;
                    }

                    if (shouldAddSchedule)
                    {
                        var downtime = CreateDowntimeEntry(currentDate, schedule);

                        // Only add if it falls within our date range
                        if (downtime.EndDateTime > effectiveStart && downtime.StartDateTime < effectiveEnd)
                        {
                            // Ensure exact start/end times
                            if (downtime.StartDateTime < effectiveStart)
                                downtime.StartDateTime = effectiveStart;
                            if (downtime.EndDateTime > effectiveEnd)
                                downtime.EndDateTime = effectiveEnd;

                            expandedDowntimes.Add(downtime);
                        }
                    }

                    currentDate = currentDate.AddDays(1);
                }
            }

            // Sort and merge overlapping downtimes
            return MergeOverlappingDowntimes(expandedDowntimes.OrderBy(x => x.StartDateTime).ToList());
        }

        private List<ScheduledDowntimeVm> MergeOverlappingDowntimes(List<ScheduledDowntimeVm> downtimes)
        {
            if (!downtimes.Any())
                return downtimes;

            var merged = new List<ScheduledDowntimeVm>();
            var current = downtimes[0];

            for (int i = 1; i < downtimes.Count; i++)
            {
                var next = downtimes[i];

                // If current and next overlap or are adjacent
                if (current.EndDateTime >= next.StartDateTime)
                {
                    // Keep the higher priority reason
                    if (GetDowntimePriority(next) > GetDowntimePriority(current))
                    {
                        current.Reason = next.Reason;
                    }

                    // Extend end time if needed
                    if (next.EndDateTime > current.EndDateTime)
                    {
                        current.EndDateTime = next.EndDateTime;
                        current.EndTime = next.EndTime;
                    }
                }
                else
                {
                    merged.Add(current);
                    current = next;
                }
            }

            merged.Add(current);
            return merged;
        }

        private int GetDowntimePriority(ScheduledDowntimeVm downtime)
        {
            // Higher number means higher priority
            int priority = 0;

            // Priority based on downtime type (if you add this property)
            if (downtime.Reason.DowntimeType == "Planned")
            {
                priority += 1000;
            }
            else if (downtime.Reason.DowntimeType == "Unplanned")
            {
                priority += 500;
            }

            // Priority based on duration (longer durations might be more significant)
            var duration = (downtime.EndDateTime - downtime.StartDateTime).TotalMinutes;
            priority += (int)(duration / 60); // Add 1 point per hour of duration

            return priority;
        }
        public string FormatDurationToTime(decimal? durationInMinutes)
        {
            if (!durationInMinutes.HasValue) return "00:00";

            int totalHours = (int)Math.Floor(durationInMinutes.Value / 60);
            int totalMinutes = (int)Math.Floor(durationInMinutes.Value % 60);
            int seconds = (int)Math.Round((durationInMinutes.Value - totalHours * 60 - totalMinutes) * 60);

            // Handle case where rounding seconds results in 60
            if (seconds == 60)
            {
                totalMinutes++;
                seconds = 0;
            }

            return $"{totalHours} hr {totalMinutes:00} min {seconds:00} sec";
        }

        private ScheduledDowntimeVm CreateDowntimeEntry(DateTime baseDate, ScheduledDowntimeVm schedule)
        {
            // Create a new instance to avoid modifying the original
            var result = new ScheduledDowntimeVm
            {
                StartTime = schedule.StartTime,
                EndTime = schedule.EndTime,
                Reason = schedule.Reason,
                ProductionLineNo = schedule.ProductionLineNo,
                EffectiveFrom = schedule.EffectiveFrom,
                EffectiveTo = schedule.EffectiveTo,
                RecurrencePattern = schedule.RecurrencePattern,
                ApplicableDays = schedule.ApplicableDays
            };

            // Set proper start and end DateTimes
            result.StartDateTime = baseDate.Date.Add(schedule.StartTime);

            // For overnight schedules (e.g., night shift 14:30 to 02:30)
            if (schedule.EndTime <= schedule.StartTime)
            {
                result.EndDateTime = baseDate.Date.AddDays(1).Add(schedule.EndTime);
            }
            else
            {
                result.EndDateTime = baseDate.Date.Add(schedule.EndTime);
            }

            return result;
        }
    }
}
