using System.Collections.Generic;
using System.Threading.Tasks;
using PmsCore.Notifications.Interfaces;
using System.Linq;
using PmsCommon;
using Microsoft.EntityFrameworkCore;
using PmsData.Models;
using PmsCore.Notifications.Models;
using Microsoft.Extensions.Logging;
using System;
using Cronos;
using PmsEntity.ViewModel;
using PmsCore.PDFGeneration.Interfaces;
using PmsData.Adapters;
using PmsCommon.Services.Scheduling.Interfaces;


namespace PmsData.DataFn
{
    public class NotificationDataFn : INotificationDataAccess
    {
        private readonly ILogger<NotificationDataFn> _logger;
        private readonly GlobalDataEntity _globalData;
        private readonly IPdfService _pdfService;
        private readonly pmsdbContext _dbContext;
        private readonly ISchedulingService _schedulingService;

        public NotificationDataFn(
            ILogger<NotificationDataFn> logger,
            GlobalDataEntity globalData,
            IPdfService pdfService,
            pmsdbContext dbContext,
            ISchedulingService schedulingService)
        {
            _logger = logger;
            _globalData = globalData;
            _pdfService = pdfService;
            _dbContext = dbContext;
            _schedulingService = schedulingService;
        }

        public async Task<List<NotificationRecipient>> GetReportRecipients(string notificationType, string reportName = null, string TriggerType = null)
        {
            using (var db = new Models.pmsdbContext())
            {
                var recipients = db.NotificationGroupsTables
                    .Where(n => n.NotificationType == notificationType && n.Disabled != true)
                    .Where(n => reportName == null || n.ReportName == reportName)
                    .Where(n => TriggerType == null || n.TriggerType.Contains(TriggerType))
                    .Select(n => new NotificationRecipient
                    {
                        Id = n.NotificationGroupUserId,
                        Name = n.Name,
                        EmailId = n.Email,
                        MobileNumberString = n.MobileNumber,
                        EnableToEmail = n.EnableToEmail ?? false,
                        EnableCCEmail = n.EnableCcemail ?? false,
                        EnableBCCEmail = n.EnableBccemail ?? false,
                        IsWhatsAppNotificationEnabled = n.IsWhatsAppNotificationEnabled ?? false,
                        UserType = n.UserType,
                        UserMasterId = n.UserMasterId,
                        NotificationType = n.NotificationType,
                        ReportType = n.TriggerType,
                        WhatsAppTemplateMasterId = n.WhatsAppTemplateMasterId ?? 0
                    })
                    .ToList();

                foreach (var recipient in recipients)
                {
                    recipient.MobileNumbers = !string.IsNullOrEmpty(recipient.MobileNumberString) ? recipient.MobileNumberString.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList() : new List<string>();
                }

                foreach (var recipient in recipients)
                {
                    if (recipient.UserType == "Internal")
                    {
                        var user = db.UserMasters.Where(u => u.UserId == recipient.UserMasterId.Value).FirstOrDefault();
                        recipient.Name = user.Name;
                        recipient.EmailId = user.Email;
                        recipient.MobileNumbers = user.Contact != null ? user.Contact.Split(new[] { ',' }, StringSplitOptions.None).ToList() : new List<string>();
                    }
                    else if (recipient.UserType == "Customer")
                    {
                        var customer = db.CustomerMasters.Where(c => c.CustomerId == recipient.UserMasterId.Value).FirstOrDefault();
                        recipient.Name = customer.CustomerName;
                        recipient.EmailId = customer.Email;
                        recipient.MobileNumbers = customer.CustomerContactNumber != null ? customer.CustomerContactNumber.Split(new[] { ',' }, StringSplitOptions.None).ToList() : new List<string>();
                    }
                }

                return recipients;
            }
        }

        public async Task<NotificationTemplate> GetNotificationTemplate(string notificationType, string reportType)
        {
            using var db = new Models.pmsdbContext();
            try
            {
                _logger.LogInformation("Searching template with type: {NotificationType}, report: {ReportType}", notificationType, reportType);

                var template = db.NotificationGroupsTables
                    .FirstOrDefault(t => t.NotificationType.ToLower() == notificationType.ToLower() && t.TriggerType.ToLower() == reportType.ToLower());

                if (template == null)
                {
                    _logger.LogWarning($"No template found for type: {notificationType} and report: {reportType}");
                    return null;
                }

                var whatsappTemplate = db.WhatsAppTemplateMasters
                    .FirstOrDefault(w => w.WhatsAppTemplateMasterId == template.WhatsAppTemplateMasterId);



                return template == null ? null : new NotificationTemplate
                {
                    NotificationType = template.NotificationType,
                    ReportType = template.TriggerType,
                    Subject = "Template Subject",
                    WhatsAppProviderTemplateId = whatsappTemplate.WhatsAppTemplateMasterId,
                    WhatsAppTemplateMasterId = whatsappTemplate.WhatsAppTemplateMasterId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification template");
                return null;
            }
            // using (var db = new Models.pmsdbContext())
            // {
            //     // Implementation based on your report generation logic
            //     throw new NotImplementedException("Implement based on your report generation requirements");
            // }
        }
        public async Task<List<long>> GetRecipientTemplateIds(long recipientId, string notificationType)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Get templates based on recipient's notification group mappings
                var templateIds = (from ng in db.NotificationGroupsTables
                                   join egm in db.EmailGroupMappingTables
                                       on ng.NotificationGroupUserId equals egm.NotificationGroupUserId
                                   join wt in db.WhatsAppTemplateMasters
                                       on ng.WhatsAppTemplateMasterId equals wt.WhatsAppTemplateMasterId
                                   where ng.NotificationGroupUserId == recipientId
                                       && !ng.Disabled.GetValueOrDefault()
                                       && egm.Enabled.GetValueOrDefault()
                                       && !wt.Disabled.GetValueOrDefault()
                                       && ng.NotificationType == notificationType
                                   select wt.WhatsAppTemplateMasterId)
                                       .ToList();

                return templateIds;
            }
        }
        public async Task<(NotificationTemplate template, Dictionary<string, string> parameters)> GetSaleOrderStatusNotification(long saleOrderId, long stageId)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Get sale order details
                var saleOrder = await (from s in db.SaleOrderTables
                                       join c in db.CustomerMasters
                                       on s.CustomerId equals c.CustomerId
                                       where s.SaleOrderId == saleOrderId
                                       select new { s, c })
                                       .FirstOrDefaultAsync();

                if (saleOrder == null)
                    throw new ArgumentException($"Sale order not found: {saleOrderId}");

                // Get stage notification configuration
                var stageConfig = await db.NotificationSaleOrderStagesTables
                    .Include(s => s.WhatsappTemplate)
                    .FirstOrDefaultAsync(s => s.StageId == stageId && !s.Disabled.GetValueOrDefault());

                if (stageConfig == null)
                    throw new ArgumentException($"Stage configuration not found: {stageId}");

                // Get notification template
                var template = new NotificationTemplate
                {
                    NotificationType = "SaleOrder",
                    ReportType = stageConfig.SaleOrderStages,
                    Subject = $"Sale Order {saleOrder.s.SaleOrderNumber} - {stageConfig.SaleOrderStages}",
                    WhatsAppProviderTemplateId = stageConfig.WhatsappTemplateId
                };

                // Build parameters
                var parameters = new Dictionary<string, string>
        {
            { "SaleOrderNumber", saleOrder.s.SaleOrderNumber },
            { "Stage", stageConfig.SaleOrderStages },
            { "CustomerName", saleOrder.c.CustomerName },
            { "CustomerCode", saleOrder.c.CustomerCode }
        };

                return (template, parameters);
            }
        }

        public async Task<List<NotificationRecipient>> GetStageNotificationRecipients(long stageId, long customerId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var stageConfig = await db.NotificationSaleOrderStagesTables
                    .FirstOrDefaultAsync(s => s.StageId == stageId && !s.Disabled.GetValueOrDefault());

                if (stageConfig == null)
                    return new List<NotificationRecipient>();

                var recipients = new List<NotificationRecipient>();

                // Add internal recipients if configured
                if (stageConfig.OnlyInternal ?? false || (!stageConfig.OnlyCustomer.GetValueOrDefault() && !stageConfig.OnlyInternal.GetValueOrDefault()))
                {
                    var internalRecipients = await db.NotificationGroupsTables
                        .Where(n => n.UserType == "Internal" && !n.Disabled.GetValueOrDefault())
                        .Select(n => new NotificationRecipient
                        {
                            Id = n.NotificationGroupUserId,
                            EmailId = n.Email,
                            MobileNumbers = n.MobileNumber != null ? n.MobileNumber.Split(new[] { ',' }, StringSplitOptions.None).ToList() : new List<string>(),
                            EnableToEmail = n.EnableToEmail ?? false,
                            IsWhatsAppNotificationEnabled = n.IsWhatsAppNotificationEnabled ?? false,
                            UserType = "Internal",
                            UserMasterId = n.UserMasterId
                        })
                        .ToListAsync();

                    recipients.AddRange(internalRecipients);
                }

                // Add customer recipients if configured
                if (stageConfig.OnlyCustomer ?? false || (!stageConfig.OnlyCustomer.GetValueOrDefault() && !stageConfig.OnlyInternal.GetValueOrDefault()))
                {
                    var customer = await db.CustomerMasters
                        .FirstOrDefaultAsync(c => c.CustomerId == customerId);

                    var customerWhatsAppSubscription = await db.WhatsAppSubscriptionCustomerTables
                        .FirstOrDefaultAsync(w => w.CustomerId == customerId);

                    var customerEmailSubscription = await db.EmailSubscriptionCustomerTables
                        .FirstOrDefaultAsync(e => e.CustomerId == customerId);

                    if (customer != null)
                    {
                        var customerEmails = !string.IsNullOrEmpty(customer.Email)
                            ? customer.Email.Split(',').Select(e => e.Trim()).ToList()
                            : new List<string>();

                        var customerMobiles = !string.IsNullOrEmpty(customer.CustomerContactNumber)
                            ? customer.CustomerContactNumber.Split(',').Select(m => m.Trim()).ToList()
                            : new List<string>();



                        foreach (var email in customerEmails)
                        {
                            recipients.Add(new NotificationRecipient
                            {
                                Id = customer.CustomerId,
                                EmailId = email,
                                MobileNumbers = customerMobiles,
                                EnableToEmail = customerEmailSubscription.Enabled,
                                IsWhatsAppNotificationEnabled = customerWhatsAppSubscription.Enabled,
                                UserType = "External"
                            });
                        }
                    }
                }

                return recipients;
            }
        }

        public async Task<ReportData> GenerateReportData(string reportType)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Implementation based on your report generation logic
                throw new NotImplementedException("Implement based on your report generation requirements");
            }
        }

        public async Task UpdateNotificationStatus(string providerMessageId, string status, DateTime? deliveredTime, DateTime? readTime)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Find all tracking records with this provider message ID
                var trackingRecords = await db.NotificationTrackingTables
                    .Where(t => t.ProviderMessageId == providerMessageId)
                    .ToListAsync();

                if (trackingRecords != null && trackingRecords.Any())
                {
                    foreach (var tracking in trackingRecords)
                    {
                        tracking.Status = status;
                        tracking.DeliveredTime = deliveredTime;
                        tracking.ReadTime = readTime;
                    }

                    await db.SaveChangesAsync();
                    _logger.LogInformation("Updated status to {Status} for {Count} notification records with provider message ID {ProviderMessageId}",
                        status, trackingRecords.Count, providerMessageId);
                }
                else
                {
                    _logger.LogWarning("No notification records found with provider message ID {ProviderMessageId}", providerMessageId);
                }
            }
        }

        public async Task SendEmailNotification()
        {
            using var db = new Models.pmsdbContext();
            try
            {
                var schedules = await db.NotificationReportScheduleMappingTables
                    .Where(s => s.IsActive && !s.Disabled)
                    .ToListAsync();

                foreach (var schedule in schedules)
                {
                    try
                    {
                        var cronExpression = CronExpression.Parse(schedule.CronExpression);
                        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(schedule.TimeZone);
                        var utcNow = DateTime.UtcNow;

                        // Convert UTC to local timezone for cron calculation
                        var currentTimeInTimeZone = TimeZoneInfo.ConvertTime(utcNow, timeZone);

                        if (!schedule.NextRunTime.HasValue)
                        {
                            // Calculate next occurrence in the specified timezone, then convert back to UTC
                            var nextOccurrenceInTimeZone = cronExpression.GetNextOccurrence(
                                DateTime.SpecifyKind(currentTimeInTimeZone, DateTimeKind.Utc));
                            schedule.NextRunTime = TimeZoneInfo.ConvertTimeToUtc(
                                DateTime.SpecifyKind(nextOccurrenceInTimeZone.Value, DateTimeKind.Unspecified),
                                timeZone);
                            await db.SaveChangesAsync();
                            continue;
                        }

                        // Compare in UTC to avoid timezone conversion issues
                        if (utcNow >= schedule.NextRunTime)
                        {
                            // await _notificationService.SendScheduledReport(schedule.ReportType);

                            // Update the schedule - store times in UTC
                            schedule.LastRunTime = utcNow;

                            // Calculate next occurrence in the specified timezone, then convert back to UTC
                            var nextOccurrenceInTimeZone = cronExpression.GetNextOccurrence(
                                DateTime.SpecifyKind(currentTimeInTimeZone, DateTimeKind.Utc));
                            schedule.NextRunTime = TimeZoneInfo.ConvertTimeToUtc(
                                DateTime.SpecifyKind(nextOccurrenceInTimeZone.Value, DateTimeKind.Unspecified),
                                timeZone);
                            await db.SaveChangesAsync();

                            _logger.LogInformation(
                                "Sent scheduled report {ReportType}. Next run scheduled for {NextRunTime} UTC",
                                schedule.ReportType,
                                schedule.NextRunTime);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex,
                            "Failed to process schedule {ScheduleId} for report type {ReportType}",
                            schedule.ReportId,
                            schedule.ReportType);
                        continue;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process notification schedules");
                throw;
            }
        }
        public async Task<(WhatsAppTemplate template, WhatsAppConfig config, WhatsAppSettings settings)> GetWhatsAppTemplateAndConfig(long? templateMasterId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var dbTemplate = db.WhatsAppTemplateMasters
                    .FirstOrDefault(t => t.WhatsAppTemplateMasterId == templateMasterId && t.Disabled != true);

                if (dbTemplate == null)
                    return (null, null, null);

                var dbConfig = db.WhatsAppConfigTables
                    .FirstOrDefault(c => c.ProviderName == dbTemplate.ProviderName && c.Disabled != true);

                if (dbConfig == null)
                    return (null, null, null);

                // Count the number of parameters for this template
                var parameterCount = await db.NotificationTemplateParameterTables
                    .Where(p => p.TemplateMasterId == dbTemplate.WhatsAppTemplateMasterId)
                    .CountAsync();

                return (
                    new WhatsAppTemplate
                    {
                        TemplateId = dbTemplate.WhatsAppTemplateMasterId,
                        ProviderTemplateId = dbTemplate.ProviderTemplateId,
                        ProviderName = dbTemplate.ProviderName,
                        ProviderTemplateName = dbTemplate.ProviderTemplateName,
                        Language = dbTemplate.Language,
                    },
                    new WhatsAppConfig
                    {
                        ProviderName = dbConfig.ProviderName,
                        ProviderKey = dbConfig.ProviderKey,
                        RegisteredSenderNumber = dbConfig.RegisteredSenderNumber,
                        ApiEndpoint = dbConfig.ApiEndpoint,
                    },
                    new WhatsAppSettings
                    {
                        ApiEndpoint = dbConfig.ApiEndpoint,
                        DefaultLanguageCode = dbTemplate.Language,
                    }
                );
            }
        }

        public async Task<WhatsAppConfig> GetDefaultWhatsAppConfig()
        {
            using (var db = new Models.pmsdbContext())
            {
                var config = await db.WhatsAppConfigTables
                    .FirstOrDefaultAsync(c => c.ProviderName == "Brevo" && c.Disabled != true);

                return config == null ? null : new WhatsAppConfig
                {
                    ProviderName = config.ProviderName,
                    ProviderKey = config.ProviderKey,
                    RegisteredSenderNumber = config.RegisteredSenderNumber,
                    ApiEndpoint = config.ApiEndpoint,
                };
            }
        }
        public async Task<WhatsAppConfig> GetWhatsAppConfigByProviderName(string providerName)
        {
            using (var db = new Models.pmsdbContext())
            {
                var config = await db.WhatsAppConfigTables
                    .FirstOrDefaultAsync(c => c.ProviderName == providerName && c.Disabled != true);

                return config == null ? null : new WhatsAppConfig
                {
                    ProviderName = config.ProviderName,
                    ProviderKey = config.ProviderKey,
                    RegisteredSenderNumber = config.RegisteredSenderNumber,
                    ApiEndpoint = config.ApiEndpoint,
                };
            }
        }
        public async Task TrackNotification(NotificationTrackingModel tracking)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        // Generate a unique notification message ID if not provided
                        if (string.IsNullOrEmpty(tracking.NotificationMessageId))
                        {
                            tracking.NotificationMessageId = $"{tracking.NotificationType}-{Guid.NewGuid():N}";
                        }

                        var trackingEntity = new NotificationTrackingTable
                        {
                            NotificationType = tracking.NotificationType,
                            MessageType = tracking.MessageType,
                            RecipientId = tracking.RecipientId,
                            MessageContent = tracking.MessageContent,
                            MasterTemplateId = tracking.MasterTemplateId,
                            Status = tracking.Status,
                            ErrorMessage = tracking.ErrorMessage,
                            ProviderMessageId = tracking.ProviderMessageId,
                            SentTime = tracking.SentTime,
                            DeliveredTime = tracking.DeliveredTime,
                            ReadTime = tracking.ReadTime,
                            AddedBy = _globalData.loggedInUser,
                            AddedDate = DateTime.UtcNow,
                            NotificationGroupUserId = tracking.NotificationGroupUserId,
                            NotificationMessageId = tracking.NotificationMessageId,
                            RecipientMobileNumber = tracking.RecipientMobileNumber,
                            RecipientEmail = tracking.RecipientEmail
                        };

                        db.NotificationTrackingTables.Add(trackingEntity);
                        db.SaveChanges();
                        transaction.Commit();

                        _logger.LogInformation("Tracked notification with ID {NotificationMessageId} for recipient {RecipientId}",
                            tracking.NotificationMessageId, tracking.RecipientId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error tracking notification for recipient {RecipientId}", tracking.RecipientId);
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<bool> CheckRateLimit(string notificationType, long recipientId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var rateLimit = await db.NotificationRateLimitTables
                    .FirstOrDefaultAsync(r => r.NotificationType == notificationType &&
                                            r.RecipientId == recipientId);

                if (rateLimit == null) return true;

                var today = DateTime.UtcNow.Date;
                var thisMonth = new DateTime(today.Year, today.Month, 1);

                if (rateLimit.LastUpdated.Date != today)
                {
                    rateLimit.MessagesSentToday = 0;
                }

                if (rateLimit.LastUpdated < thisMonth)
                {
                    rateLimit.MessagesSentThisMonth = 0;
                }

                var config = await GetNotificationLimits(notificationType);
                return rateLimit.MessagesSentToday < config.MaxDailyMessages &&
                       rateLimit.MessagesSentThisMonth < config.MaxMonthlyMessages;
            }
        }
        public async Task<NotificationLimitConfig> GetNotificationLimits(string notificationType)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (notificationType.Equals("WhatsApp", StringComparison.OrdinalIgnoreCase))
                {
                    var whatsAppConfig = await db.WhatsAppConfigTables
                        .Where(w => !w.Disabled.GetValueOrDefault())
                        .FirstOrDefaultAsync();

                    return new NotificationLimitConfig
                    {
                        MaxDailyMessages = whatsAppConfig?.MaxDailyMessages ?? 1000,
                        MaxMonthlyMessages = whatsAppConfig?.MaxMonthlyMessages ?? 25000
                    };
                }
                else if (notificationType.Equals("Email", StringComparison.OrdinalIgnoreCase))
                {
                    var emailConfig = await db.EmailConfigTables
                        .Where(e => !e.Disabled.GetValueOrDefault())
                        .FirstOrDefaultAsync();

                    return new NotificationLimitConfig
                    {
                        MaxDailyMessages = emailConfig?.MaxDailyEmails ?? 1000,
                        MaxMonthlyMessages = emailConfig?.MaxDailyEmails * 30 ?? 30000
                    };
                }

                return new NotificationLimitConfig
                {
                    MaxDailyMessages = 1000,
                    MaxMonthlyMessages = 30000
                };
            }
        }

        public async Task UpdateRateLimit(string notificationType, long recipientId)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var rateLimit = await db.NotificationRateLimitTables
                            .FirstOrDefaultAsync(r => r.NotificationType == notificationType &&
                                                    r.RecipientId == recipientId);

                        var now = DateTime.UtcNow;

                        if (rateLimit == null)
                        {
                            rateLimit = new NotificationRateLimitTable
                            {
                                NotificationType = notificationType,
                                RecipientId = recipientId,
                                MessagesSentToday = 1,
                                MessagesSentThisMonth = 1,
                                LastMessageTime = now,
                                LastUpdated = now
                            };
                            db.NotificationRateLimitTables.Add(rateLimit);
                        }
                        else
                        {
                            var today = now.Date;
                            var thisMonth = new DateTime(today.Year, today.Month, 1);

                            if (rateLimit.LastUpdated.Date != today)
                            {
                                rateLimit.MessagesSentToday = 1;
                            }
                            else
                            {
                                rateLimit.MessagesSentToday++;
                            }

                            if (rateLimit.LastUpdated < thisMonth)
                            {
                                rateLimit.MessagesSentThisMonth = 1;
                            }
                            else
                            {
                                rateLimit.MessagesSentThisMonth++;
                            }

                            rateLimit.LastMessageTime = now;
                            rateLimit.LastUpdated = now;
                        }

                        await db.SaveChangesAsync();
                        await transaction.CommitAsync();
                    }
                    catch (Exception)
                    {
                        await transaction.RollbackAsync();
                        throw;
                    }
                }
            }
        }

        public async Task<NotificationRecipient> GetRecipientById(long recipientId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var recipient = db.NotificationGroupsTables
                    .Where(n => n.NotificationGroupUserId == recipientId)
                    .Select(n => new NotificationRecipient
                    {
                        Id = n.NotificationGroupUserId,
                        Name = n.Name,
                        EmailId = n.Email,
                        MobileNumberString = n.MobileNumber,
                        EnableToEmail = n.EnableToEmail ?? false,
                        EnableCCEmail = n.EnableCcemail ?? false,
                        EnableBCCEmail = n.EnableBccemail ?? false,
                        IsWhatsAppNotificationEnabled = n.IsWhatsAppNotificationEnabled ?? false,
                        UserType = n.UserType,
                        UserMasterId = n.UserMasterId,
                        NotificationType = n.NotificationType,
                        ReportType = n.TriggerType,
                        WhatsAppTemplateMasterId = n.WhatsAppTemplateMasterId ?? 0
                    })
                    .FirstOrDefault();

                recipient.MobileNumbers = !string.IsNullOrEmpty(recipient.MobileNumberString) ? recipient.MobileNumberString.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList() : new List<string>();

                if (recipient.UserType == "Internal")
                {
                    var user = db.UserMasters.Where(u => u.UserId == recipient.UserMasterId.Value).FirstOrDefault();
                    recipient.Name = user.Name;
                    recipient.EmailId = user.Email;
                    recipient.MobileNumbers = user.Contact != null ? user.Contact.Split(new[] { ',' }, StringSplitOptions.None).ToList() : new List<string>();
                }
                else if (recipient.UserType == "Customer")
                {
                    var customer = db.CustomerMasters.Where(c => c.CustomerId == recipient.UserMasterId.Value).FirstOrDefault();
                    recipient.Name = customer.CustomerName;
                    recipient.EmailId = customer.Email;
                    recipient.MobileNumbers = customer.CustomerContactNumber != null ? customer.CustomerContactNumber.Split(new[] { ',' }, StringSplitOptions.None).ToList() : new List<string>();
                }

                return recipient;
            }
        }
        public async Task<Dictionary<string, string>> GenerateLowStockParameters(long productId, long templateMasterId, ProductStockAvailabilityVm productStockAvailabilityVm)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameters = new Dictionary<string, string>();

                // Create a mapping of property names to their values
                var productProperties = new Dictionary<string, string>
                {
                    { nameof(productStockAvailabilityVm.PRODUCTNAME), productStockAvailabilityVm.PRODUCTNAME },
                    { nameof(productStockAvailabilityVm.PRODUCTCATEGORY), productStockAvailabilityVm.PRODUCTCATEGORY },
                    { nameof(productStockAvailabilityVm.PRODUCTFIRSTCATEGORY), productStockAvailabilityVm.PRODUCTFIRSTCATEGORY ?? "NA" },
                    { nameof(productStockAvailabilityVm.PRODUCTSECSUBCATEGORY), productStockAvailabilityVm.PRODUCTSECSUBCATEGORY ?? "NA" },
                    { nameof(productStockAvailabilityVm.UNIT), productStockAvailabilityVm.UNIT },
                    { nameof(productStockAvailabilityVm.AVAILABLEQTY), productStockAvailabilityVm.AVAILABLEQTY.ToString() },
                    { nameof(productStockAvailabilityVm.MINIMUMQTY), productStockAvailabilityVm.MINIMUMQTY.ToString() },
                    { nameof(productStockAvailabilityVm.DOMESTICQTY), productStockAvailabilityVm.DOMESTICQTY.ToString() },
                    { nameof(productStockAvailabilityVm.IMPORTEDQTY), productStockAvailabilityVm.IMPORTEDQTY.ToString() }
                };

                // Map template parameters to their values
                foreach (var param in templateParams)
                {
                    string paramValue = productProperties.TryGetValue(param.ParameterName, out string value)
                        ? value
                        : param.DefaultValue ?? "N/A";

                    parameters.Add(param.ParameterName, paramValue);

                    if (param.IsRequired == true && (string.IsNullOrEmpty(paramValue) || paramValue.Equals("0")))
                    {
                        _logger.LogWarning($"Required parameter {param.ParameterName} has no value for template {templateMasterId}");
                    }
                }

                return parameters;
            }
        }

        /// <summary>
        /// Enhanced method that returns NotificationParameterCollection for provider-specific handling
        /// Handles duplicate parameter names by processing each template parameter individually
        /// </summary>
        public PmsCore.Notifications.Models.NotificationParameterCollection GenerateLowStockParametersEnhanced(long templateMasterId, ProductStockAvailabilityVm productStockAvailabilityVm)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameterCollection = new PmsCore.Notifications.Models.NotificationParameterCollection();

                // Create a mapping of property names to their values
                var productProperties = new Dictionary<string, string>
                {
                    { "ProductName", productStockAvailabilityVm.PRODUCTNAME ?? "N/A" },
                    { "ProductCategory", productStockAvailabilityVm.PRODUCTCATEGORY ?? "N/A" },
                    { "ProductFirstSubCategory", productStockAvailabilityVm.PRODUCTFIRSTCATEGORY ?? "N/A" },
                    { "ProductSecSubCategory", productStockAvailabilityVm.PRODUCTSECSUBCATEGORY ?? "N/A" },
                    { "TotalQuantity", productStockAvailabilityVm.AVAILABLEQTY?.ToString() ?? "0" },
                    { "MinimumQuantity", productStockAvailabilityVm.MINIMUMQTY?.ToString() ?? "0" },
                    { "Unit", productStockAvailabilityVm.UNIT ?? "N/A" },
                    { "DomesticQuantity", productStockAvailabilityVm.DOMESTICQTY?.ToString() ?? "0" },
                    { "ImportedQuantity", productStockAvailabilityVm.IMPORTEDQTY?.ToString() ?? "0" },
                    { "UNIT", productStockAvailabilityVm.UNIT ?? "N/A" }, // Handle both "Unit" and "UNIT" parameter names
                    { "PRODUCTNAME", productStockAvailabilityVm.PRODUCTNAME ?? "N/A" },
                    { "PRODUCTCATEGORY", productStockAvailabilityVm.PRODUCTCATEGORY ?? "N/A" },
                    { "PRODUCTFIRSTCATEGORY", productStockAvailabilityVm.PRODUCTFIRSTCATEGORY ?? "N/A" },
                    { "PRODUCTSECSUBCATEGORY", productStockAvailabilityVm.PRODUCTSECSUBCATEGORY ?? "N/A" },
                    { "AVAILABLEQTY", productStockAvailabilityVm.AVAILABLEQTY?.ToString() ?? "0" },
                    { "MINIMUMQTY", productStockAvailabilityVm.MINIMUMQTY?.ToString() ?? "0" },
                    { "DOMESTICQTY", productStockAvailabilityVm.DOMESTICQTY?.ToString() ?? "0" },
                    { "IMPORTEDQTY", productStockAvailabilityVm.IMPORTEDQTY?.ToString() ?? "0" }
                };

                // Process each template parameter individually (allows duplicates)
                foreach (var param in templateParams)
                {
                    // Get the parameter value based on parameter name
                    string paramValue = productProperties.TryGetValue(param.ParameterName, out string value)
                        ? value
                        : param.DefaultValue ?? "N/A";

                    // Create a notification parameter for each template parameter entry
                    // This allows the same parameter name to appear multiple times with different sequences
                    var notificationParam = new PmsCore.Notifications.Models.NotificationParameter
                    {
                        Name = param.ParameterName,
                        Value = paramValue,
                        Sequence = param.Sequence ?? int.MaxValue,
                        IsRequired = param.IsRequired ?? false,
                        DefaultValue = param.DefaultValue
                    };

                    // Add each parameter individually (no duplicate key issues)
                    parameterCollection.Add(notificationParam);

                    if (param.IsRequired == true && (string.IsNullOrEmpty(paramValue) || paramValue.Equals("0")))
                    {
                        _logger.LogWarning("Required parameter {ParameterName} has no value for template {TemplateMasterId}", param.ParameterName, templateMasterId);
                    }
                }

                _logger.LogDebug("Generated {ParameterCount} parameters for template {TemplateMasterId}", parameterCollection.Count, templateMasterId);
                return parameterCollection;
            }
        }
        public async Task<ProductStockAvailabilityVm> GetProductStockAvailabilty(long productId)
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var query = (from p in db.ProductMasters
                             join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId
                             join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                             from pf in psf.DefaultIfEmpty()
                             join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                             from psc in pssc.DefaultIfEmpty()
                             join a in db.StockProductTables on p.ProductId equals a.ProductId
                             join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                             join r in db.RackMasters on b.RackId equals r.RackId
                             join s in db.StoreMasters on r.StoreId equals s.StoreId
                             where productId == p.ProductId && p.Disabled != true && p.MinimumQuantity > 0
                                   && b.InspectionType.ToLower() == "accepted" && b.Quantity > 0 && s.IsWorkInProgressStore == false
                             select new
                             {
                                 p.ProductId,
                                 p.ProductName,
                                 pr.ProductCategory,
                                 pf.ProductFirstSubCategory,
                                 psc.ProductSecSubCategory,
                                 b.Quantity,
                                 p.MinimumQuantity,
                                 a.Unit,
                                 a.StockProductId,
                                 a.StockId
                             }).ToList();

                if (!query.Any())
                    return null;

                var totalQuantity = query.Sum(x => x.Quantity);
                var minimumQuantity = query.First().MinimumQuantity;

                // Only return data if total quantity is less than minimum quantity
                if (totalQuantity >= minimumQuantity)
                    return null;

                var StockQuality = (from sm in db.StockMasters
                                    join sp in db.StockProductTables on sm.StockId equals sp.StockId
                                    join spa in db.StockProductAllocationTables on sp.StockProductId equals spa.StockProductId
                                    where query.Select(q => q.StockProductId).Contains(sp.StockProductId)
                                    group spa by new { sm.ProductQuality } into g
                                    select new
                                    {
                                        g.Key.ProductQuality,
                                        Quantity = g.Sum(x => x.Quantity)
                                    }).ToList();

                var DomesticQty = StockQuality.Where(x => x.ProductQuality == "DOMESTIC").Select(x => x.Quantity).FirstOrDefault() + StockQuality.Where(x => string.IsNullOrEmpty(x.ProductQuality)).Select(x => x.Quantity).FirstOrDefault();
                var ImportedQty = StockQuality.Where(x => x.ProductQuality == "IMPORTED").Select(x => x.Quantity).FirstOrDefault();

                return new ProductStockAvailabilityVm()
                {
                    ProductId = query.First().ProductId,
                    PRODUCTNAME = query.First().ProductName,
                    PRODUCTCATEGORY = query.First().ProductCategory,
                    PRODUCTFIRSTCATEGORY = query.First().ProductFirstSubCategory,
                    PRODUCTSECSUBCATEGORY = query.First().ProductSecSubCategory,
                    AVAILABLEQTY = totalQuantity,
                    MINIMUMQTY = minimumQuantity,
                    UNIT = query.First().Unit,
                    DOMESTICQTY = DomesticQty,
                    IMPORTEDQTY = ImportedQty
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product stock availability for ProductId: {ProductId}", productId);
                return new ProductStockAvailabilityVm();
            }
        }
        public async Task<EmailSettings> GetPrimaryProviderEmailConfig()
        {
            using (var db = new Models.pmsdbContext())
            {
                var emailConfig = await db.EmailConfigTables
                    .Where(e => !e.Disabled.GetValueOrDefault() && e.EmailConfigName == "Primary")
                    .FirstOrDefaultAsync();

                return emailConfig == null ? null : new EmailSettings
                {
                    ConfigName = emailConfig.EmailConfigName,
                    FromEmail = emailConfig.EmailConfigAccountId,
                    FromName = emailConfig.EmailConfigFromEmailDisplayName,
                    SmtpServer = emailConfig.EmailConfigSmtp,
                    AccessKey = emailConfig.EmailConfigFromEmailId,
                    SecretKey = emailConfig.EmailConfigPassword,
                    Region = "us-east-1",
                    Port = int.Parse(emailConfig.EmailConfigPort),
                    EnableSsl = emailConfig.EnableSsl.GetValueOrDefault(),
                    EnableRetry = emailConfig.RetryCount > 0,
                    MaxRetryAttempts = emailConfig.RetryCount,
                    MaxAttachmentSize = emailConfig.MaxAttachmentSize.GetValueOrDefault(),
                };
            }
        }
        public async Task<EmailSettings> GetSecondaryProviderEmailConfig()
        {
            using (var db = new Models.pmsdbContext())
            {
                var emailConfig = await db.EmailConfigTables
                    .Where(e => !e.Disabled.GetValueOrDefault() && e.EmailConfigName == "Secondary")
                    .FirstOrDefaultAsync();

                return emailConfig == null ? null : new EmailSettings
                {
                    ConfigName = emailConfig.EmailConfigName,
                    FromEmail = emailConfig.EmailConfigAccountId,
                    FromName = emailConfig.EmailConfigFromEmailDisplayName,
                    SmtpServer = emailConfig.EmailConfigSmtp,
                    AccessKey = emailConfig.EmailConfigFromEmailId,
                    SecretKey = emailConfig.EmailConfigPassword,
                    Region = "us-east-1",
                    Port = int.Parse(emailConfig.EmailConfigPort),
                    EnableSsl = emailConfig.EnableSsl.GetValueOrDefault(),
                    EnableRetry = emailConfig.RetryCount > 0,
                    MaxRetryAttempts = emailConfig.RetryCount,
                    MaxAttachmentSize = emailConfig.MaxAttachmentSize.GetValueOrDefault(),
                };
            }
        }
        public async Task<string> GenerateYieldSummaryReportPdfAndUploadToStorageAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                YieldReportRequestVm filter = new YieldReportRequestVm();
                filter.DateTo = toDate;
                filter.DateFrom = fromDate;
                filter.FabricProductId = 0;
                var data = new ReportDataFn(_globalData);
                var dataSet = data.YieldReport(filter);

                var pdfData = new YieldSummaryReportPdfAdapter(dataSet, filter.DateFrom.Value, filter.DateTo.Value);

                var fileName = $"YieldSummaryReport_{Guid.NewGuid():N}.pdf";

                if (pdfData.AllRecords.Count > 0)
                {
                    // Use the new WhatsApp-compatible upload method
                    var pdfUrl = await _pdfService.GeneratePdfAndUploadToWhatsAppStorageAsync(
                        pdfData,
                        fileName,
                        "yieldsummaryreport"  // Document type for hierarchical organization
                    );

                    _logger.LogInformation("Successfully generated and uploaded yield summary report PDF: {PdfUrl}", pdfUrl);
                    return pdfUrl;
                }

                _logger.LogWarning("No data found for yield summary report from {FromDate} to {ToDate}", fromDate, toDate);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating yield summary report PDF and uploading to storage");
                return null;
            }
        }
        public async Task<Dictionary<string, string>> GenerateYieldSummaryReportParameters(long templateMasterId, DateTime fromdate, DateTime todate)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameters = new Dictionary<string, string>();

                // Convert UTC dates to IST for proper display in WhatsApp message
                var fromDateIST = TimeZoneHelper.ConvertToTimeZone(fromdate, TimeZoneId.IndiaStandardTime);
                var toDateIST = TimeZoneHelper.ConvertToTimeZone(todate, TimeZoneId.IndiaStandardTime);

                // Create a mapping of property names to their values
                // IMPORTANT: Use the EXACT parameter names from the template, not the variable names
                var productProperties = new Dictionary<string, string>
                {
                    // Use the exact parameter names from the WhatsApp template with IST formatting
                    { "1", fromDateIST.ToString("dd-MMM-yyyy hh:mm tt") },
                    { "2", toDateIST.ToString("dd-MMM-yyyy hh:mm tt") }
                };

                // Map template parameters to their values
                foreach (var param in templateParams)
                {
                    string paramValue = productProperties.TryGetValue(param.Sequence == null || param.Sequence == 0 ? param.ParameterName : param.Sequence.ToString(), out string value)
                        ? value
                        : param.DefaultValue ?? "N/A";

                    parameters.Add(param.Sequence == null || param.Sequence == 0 ? param.ParameterName : param.Sequence.ToString(), paramValue);

                    if (param.IsRequired == true && (string.IsNullOrEmpty(paramValue) || paramValue.Equals("0")))
                    {
                        _logger.LogWarning($"Required parameter {param.ParameterName} has no value for template {templateMasterId}");
                    }
                }

                return parameters;
            }
        }

        public async Task<List<ScheduledNotification>> GetPendingScheduledNotifications()
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var currentTime = DateTime.UtcNow;

                // Get all active schedules that are due to run
                var schedules = await db.NotificationReportScheduleMappingTables
                    .Where(s => s.IsActive && !s.Disabled && s.NextRunTime.HasValue && s.NextRunTime <= currentTime)
                    .ToListAsync();

                var result = new List<ScheduledNotification>();

                foreach (var schedule in schedules)
                {
                    // Get the recipient information
                    var recipient = await GetRecipientById(schedule.NotificationGroupUserId ?? 0);
                    if (recipient == null) continue;

                    // Create parameters based on report type
                    var parameters = new Dictionary<string, string>();

                    if (schedule.ReportType == "YieldReportSummary")
                    {
                        // Calculate the 24-hour period from previous day 8:00 AM IST to current day 8:00 AM IST
                        var currentTimeIST = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);
                        var currentDay8AMIST = currentTimeIST.Date.AddHours(8);
                        var previousDay8AMIST = currentDay8AMIST.AddDays(-1);

                        // Use the exact parameter names from the WhatsApp template with IST formatting
                        parameters.Add("fromdate", previousDay8AMIST.ToString("dd-MMM-yyyy hh:mm tt"));
                        parameters.Add("todate", currentDay8AMIST.ToString("dd-MMM-yyyy hh:mm tt"));

                        _logger.LogInformation("Created parameters for YieldReportSummary: fromdate={FromDate}, todate={ToDate}",
                            parameters["fromdate"], parameters["todate"]);
                    }

                    result.Add(new ScheduledNotification
                    {
                        ReportId = schedule.ReportId,
                        ReportType = schedule.ReportType,
                        ReportName = schedule.ReportName,
                        NotificationGroupUserId = schedule.NotificationGroupUserId ?? 0,
                        TemplateMasterId = schedule.TemplateMasterId ?? 0,
                        CronExpression = schedule.CronExpression,
                        LastRunTime = schedule.LastRunTime,
                        NextRunTime = schedule.NextRunTime,
                        IsActive = schedule.IsActive,
                        TimeZone = schedule.TimeZone,
                        AddedBy = schedule.AddedBy,
                        AddedDate = schedule.AddedDate,
                        Disabled = schedule.Disabled,
                        Recipient = recipient,
                        Parameters = parameters
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending scheduled notifications");
                return new List<ScheduledNotification>();
            }
        }

        public async Task UpdateNextRunTime(long reportId, DateTime lastRunTime, DateTime nextRunTime)
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var schedule = await db.NotificationReportScheduleMappingTables.FindAsync(reportId);

                if (schedule != null)
                {
                    schedule.LastRunTime = lastRunTime;
                    schedule.NextRunTime = nextRunTime;
                    await db.SaveChangesAsync();

                    _logger.LogInformation("Updated schedule {ReportId} next run time to {NextRunTime}",
                        reportId, nextRunTime);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating next run time for report {ReportId}", reportId);
                throw;
            }
        }

        public async Task UpdateOnDemandNotificationStatus(long notificationGroupId)
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var notificationGroup = await db.NotificationGroupsTables.FindAsync(notificationGroupId);

                if (notificationGroup != null)
                {
                    // Update the LastTriggeredBy and LastTriggeredDate fields
                    // These fields need to be added to the NotificationGroupsTable
                    // For now, we'll just log that the notification was triggered
                    _logger.LogInformation("On-demand notification triggered for group {NotificationGroupId}",
                        notificationGroupId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating on-demand notification status for group {NotificationGroupId}",
                    notificationGroupId);
                throw;
            }
        }

        public async Task<List<NotificationRecipient>> GetNotificationsByTriggerType(string triggerType)
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var notifications = await db.NotificationGroupsTables
                    .Where(n => n.TriggerType == triggerType && !n.Disabled.GetValueOrDefault())
                    .Select(n => new NotificationRecipient
                    {
                        Id = n.NotificationGroupUserId,
                        Name = n.Name,
                        EmailId = n.Email,
                        MobileNumberString = n.MobileNumber,
                        EnableToEmail = n.EnableToEmail ?? false,
                        EnableCCEmail = n.EnableCcemail ?? false,
                        EnableBCCEmail = n.EnableBccemail ?? false,
                        IsWhatsAppNotificationEnabled = n.IsWhatsAppNotificationEnabled ?? false,
                        UserType = n.UserType,
                        UserMasterId = n.UserMasterId,
                        NotificationType = n.NotificationType,
                        ReportType = n.TriggerType,
                        WhatsAppTemplateMasterId = n.WhatsAppTemplateMasterId ?? 0
                    })
                    .ToListAsync();

                foreach (var notification in notifications)
                {
                    notification.MobileNumbers = !string.IsNullOrEmpty(notification.MobileNumberString)
                        ? notification.MobileNumberString.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList()
                        : new List<string>();
                }

                return notifications;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notifications by trigger type {TriggerType}", triggerType);
                return new List<NotificationRecipient>();
            }
        }

        public async Task<string> GenerateLowStockReportPdfAndUploadToStorageAsync()
        {
            try
            {
                var request = new LowStockReportRequestVm
                {
                    IncludeWIPStore = false,
                    GeneratedDate = DateTime.UtcNow
                };

                var stockData = new StockDataFn(_globalData);
                var lowStockItems = stockData.GetLowStockReportData(request);

                if (lowStockItems.Count == 0)
                {
                    _logger.LogInformation("No low stock items found for report generation");
                    return null;
                }

                var pdfData = new LowStockReportPdfAdapter(lowStockItems, request.GeneratedDate ?? DateTime.UtcNow);
                var fileName = $"LowStockReport_{Guid.NewGuid():N}.pdf";

                // Use the WhatsApp-compatible upload method
                var pdfUrl = await _pdfService.GeneratePdfAndUploadToWhatsAppStorageAsync(
                    pdfData,
                    fileName,
                    "lowstockreport"  // Document type for hierarchical organization
                );

                _logger.LogInformation("Successfully generated and uploaded low stock report PDF: {PdfUrl}", pdfUrl);
                return pdfUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating low stock report PDF and uploading to storage");
                return null;
            }
        }

        public async Task<Dictionary<string, string>> GenerateLowStockReportParameters(long templateMasterId)
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    var templateParams = db.NotificationTemplateParameterTables
                        .Where(x => x.TemplateMasterId == templateMasterId)
                        .OrderBy(x => x.Sequence ?? int.MaxValue)
                        .ThenBy(x => x.ParameterName)
                        .ToList();

                    _logger.LogInformation("Found template id {TemplateMasterId} with parameters: {Parameters}",
                        templateMasterId,
                        string.Join(", ", templateParams.Select(p => $"{p.ParameterName} (Sequence: {p.Sequence})")));

                    var parameters = new Dictionary<string, string>();
                    var currentTimeIST = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);

                    foreach (var param in templateParams)
                    {
                        // Determine the parameter key: use sequence number if available and > 0, otherwise use parameter name
                        string parameterKey = param.Sequence == null || param.Sequence == 0
                            ? param.ParameterName
                            : param.Sequence.ToString();

                        // Determine the parameter value based on the parameter name (not the key)
                        string parameterValue;
                        switch (param.ParameterName.ToLower())
                        {
                            case "reportname":
                            case "1":
                                parameterValue = "Low Stock Report";
                                break;
                            case "generateddate":
                            case "2":
                                parameterValue = currentTimeIST.ToString("dd-MMM-yyyy HH:mm tt");
                                break;
                            case "caption":
                                parameterValue = "Low Stock Report";
                                break;
                            default:
                                parameterValue = !string.IsNullOrEmpty(param.DefaultValue) ? param.DefaultValue : "N/A";
                                break;
                        }

                        // Add parameter using the determined key and value
                        parameters.Add(parameterKey, parameterValue);
                    }

                    _logger.LogInformation("Created parameters for LowStockReport: {Parameters}",
                        string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}")));

                    return parameters;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating LowStockReport parameters for template {TemplateMasterId}",
                    templateMasterId);
                throw;
            }
        }

        // Pending Returnable OutPass Notification Methods

        /// <summary>
        /// Generate PDF for single pending returnable outpass reminder
        /// </summary>
        public async Task<string> GeneratePendingReturnableOutPassReminderPdfAndUploadToStorageAsync(long outpassId)
        {
            try
            {
                var outpassData = new OutpassDataFn(_globalData);
                var pendingOutpass = outpassData.GetPendingReturnableOutPassById(outpassId);

                if (pendingOutpass == null)
                {
                    _logger.LogInformation("No pending returnable outpass found for ID: {OutpassId}", outpassId);
                    return null;
                }

                var pdfData = new PendingReturnableOutPassPdfAdapter(pendingOutpass, DateTime.UtcNow);
                var fileName = $"PendingReturnableOutPass_{pendingOutpass.OutpassNumber}_{Guid.NewGuid():N}.pdf";

                // Use the WhatsApp-compatible upload method
                var pdfUrl = await _pdfService.GeneratePdfAndUploadToWhatsAppStorageAsync(
                    pdfData,
                    fileName,
                    "pendingreturnable"  // Document type for hierarchical organization
                );

                _logger.LogInformation("Successfully generated and uploaded pending returnable outpass reminder PDF: {PdfUrl}", pdfUrl);
                return pdfUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating pending returnable outpass reminder PDF and uploading to storage");
                return null;
            }
        }

        /// <summary>
        /// Generate PDF for pending returnable outpass report
        /// </summary>
        public async Task<string> GeneratePendingReturnableOutPassReportPdfAndUploadToStorageAsync()
        {
            try
            {
                var request = new PendingReturnableOutPassRequestVm
                {
                    GeneratedDate = DateTime.UtcNow,
                    IncludeOverdueOnly = false
                };

                var outpassData = new OutpassDataFn(_globalData);
                var reportData = outpassData.GetPendingReturnableOutPassReportData(request);

                if (reportData.TotalPendingCount == 0)
                {
                    _logger.LogInformation("No pending returnable outpasses found for report generation");
                    return null;
                }

                var pdfData = new PendingReturnableOutPassReportPdfAdapter(reportData);
                var fileName = $"PendingReturnableOutPassReport_{Guid.NewGuid():N}.pdf";

                // Use the WhatsApp-compatible upload method
                var pdfUrl = await _pdfService.GeneratePdfAndUploadToWhatsAppStorageAsync(
                    pdfData,
                    fileName,
                    "pendingreturnablereport"  // Document type for hierarchical organization
                );

                _logger.LogInformation("Successfully generated and uploaded pending returnable outpass report PDF: {PdfUrl}", pdfUrl);
                return pdfUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating pending returnable outpass report PDF and uploading to storage");
                return null;
            }
        }

        /// <summary>
        /// Generate parameters for pending returnable outpass reminder notification
        /// </summary>
        public async Task<Dictionary<string, string>> GeneratePendingReturnableOutPassReminderParameters(long outpassId, long templateMasterId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameters = new Dictionary<string, string>();
                var currentTimeIST = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);

                var outpassData = new OutpassDataFn(_globalData);
                var pendingOutpass = outpassData.GetPendingReturnableOutPassById(outpassId);

                if (pendingOutpass == null)
                {
                    _logger.LogWarning("Pending returnable outpass not found for ID: {OutpassId}", outpassId);
                    return parameters;
                }
                var expectedReturnDateIST = TimeZoneHelper.ConvertToTimeZone(pendingOutpass.ExpectedReturnDate ?? DateTime.UtcNow, TimeZoneId.IndiaStandardTime);

                foreach (var param in templateParams)
                {
                    var key = param.Sequence.HasValue && param.Sequence > 0 ? param.Sequence.ToString() : param.ParameterName;
                    var value = param.ParameterName.ToLower() switch
                    {
                        "outpassnumber" => pendingOutpass.OutpassNumber ?? "",
                        "daysoverdue" => pendingOutpass.DaysOverdue.ToString(),
                        "expectedreturndate" => expectedReturnDateIST.ToString("dd-MMM-yyyy HH:mm tt"),
                        _ => param.DefaultValue ?? ""
                    };
                    parameters[key] = value;
                }

                return parameters;
            }
        }

        /// <summary>
        /// Generate parameters for pending returnable outpass report notification
        /// </summary>
        public async Task<Dictionary<string, string>> GeneratePendingReturnableOutPassReportParameters(long templateMasterId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameters = new Dictionary<string, string>();
                var currentTimeIST = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);

                var request = new PendingReturnableOutPassRequestVm
                {
                    GeneratedDate = DateTime.UtcNow,
                    IncludeOverdueOnly = false
                };

                var outpassData = new OutpassDataFn(_globalData);
                var reportData = outpassData.GetPendingReturnableOutPassReportData(request);

                foreach (var param in templateParams)
                {
                    var key = param.Sequence.HasValue && param.Sequence > 0 ? param.Sequence.ToString() : param.ParameterName;
                    var value = param.ParameterName.ToLower() switch
                    {
                        "totalpending" => reportData.TotalPendingCount.ToString(),
                        "overduecount" => reportData.OverdueCount.ToString(),
                        "ontimecount" => (reportData.TotalPendingCount - reportData.OverdueCount).ToString(),
                        "totalamount" => $"₹ {reportData.TotalPendingAmount:N2}",
                        "generateddate" => currentTimeIST.ToString("dd-MMM-yyyy HH:mm tt"),
                    };
                    parameters[key] = value;
                }

                return parameters;
            }
        }

        public async Task<List<long>> GetOverdueReturnableOutPassesForScheduledReminders()
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var currentTimeUtc = DateTime.UtcNow;

                _logger.LogInformation("Checking for overdue returnable out passes for scheduled reminders");

                // Get all customer notification subscriptions for overdue returnable out pass reminders
                var customerSubscriptions = await db.NotificationGroupsTables
                    .Where(ng => ng.UserType == "Customer"
                                && ng.Disabled != true
                                && ng.TriggerType.Contains("Scheduled")
                                && ng.NotificationType == "ReturnableOutPassReminder")
                    .ToListAsync();

                if (!customerSubscriptions.Any())
                {
                    _logger.LogInformation("No customer subscriptions found for overdue returnable out pass reminders");
                    return new List<long>();
                }

                _logger.LogInformation("Found {Count} customer subscriptions for overdue returnable out pass reminders", customerSubscriptions.Count);

                var overdueOutPassIds = new List<long>();

                foreach (var subscription in customerSubscriptions)
                {
                    try
                    {
                        // Check if it's time to send notification based on CRON schedule using shared service
                        if (!_schedulingService.IsTimeToSendNotification(
                            subscription.CronScheduleExpression,
                            subscription.TimeZone,
                            subscription.LastTriggeredDate,
                            currentTimeUtc,
                            10, // 10-minute execution window
                            subscription.NotificationGroupUserId))
                        {
                            continue;
                        }

                        _logger.LogInformation("Processing overdue reminders for customer {CustomerId} (subscription {SubscriptionId})",
                            subscription.UserMasterId, subscription.NotificationGroupUserId);

                        // Find all overdue returnable out passes for this customer
                        var overdueOutPasses = await db.OutpassMasters
                            .Where(op => op.OutpassToCustomerId == subscription.UserMasterId
                                        && op.OutpassType == "Returnable"
                                        && op.IsOutpassIn != true
                                        && op.Status == "Approved"
                                        && op.ExpectedReturnDate.HasValue
                                        && op.ExpectedReturnDate.Value < currentTimeUtc)
                            .Select(op => op.OutpassId)
                            .ToListAsync();

                        if (overdueOutPasses.Any())
                        {
                            _logger.LogInformation("Found {Count} overdue returnable out passes for customer {CustomerId}",
                                overdueOutPasses.Count, subscription.UserMasterId);

                            overdueOutPassIds.AddRange(overdueOutPasses);

                            // Update the notification schedule only when overdue outpasses exist and will be processed
                            await UpdateNotificationSchedule(subscription, currentTimeUtc);

                            _logger.LogInformation("Updated notification schedule for customer {CustomerId} after finding overdue outpasses",
                                subscription.UserMasterId);
                        }
                        else
                        {
                            _logger.LogInformation("No overdue returnable out passes found for customer {CustomerId}. Schedule not updated to allow future checks within same CRON period.",
                                subscription.UserMasterId);
                            // Do NOT update schedule when no overdue outpasses exist
                            // This allows future notifications within the same CRON period if outpasses become overdue
                        }
                    }
                    catch (Exception subscriptionEx)
                    {
                        _logger.LogError(subscriptionEx, "Error processing overdue reminders for customer subscription {SubscriptionId}",
                            subscription.NotificationGroupUserId);
                        // Continue processing other subscriptions
                    }
                }

                _logger.LogInformation("Total overdue returnable out passes found: {Count}", overdueOutPassIds.Count);
                return overdueOutPassIds.Distinct().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting overdue returnable out passes for scheduled reminders");
                return new List<long>();
            }
        }

        // CRON evaluation logic moved to shared SchedulingService

        /// <summary>
        /// Update the notification schedule after processing
        /// </summary>
        private async Task UpdateNotificationSchedule(Models.NotificationGroupsTable subscription, DateTime currentTimeUtc)
        {
            try
            {
                using var db = new Models.pmsdbContext();

                // Find the subscription in the database
                var dbSubscription = await db.NotificationGroupsTables
                    .FirstOrDefaultAsync(ng => ng.NotificationGroupUserId == subscription.NotificationGroupUserId);

                if (dbSubscription != null)
                {
                    // Update last triggered date
                    dbSubscription.LastTriggeredDate = currentTimeUtc;
                    dbSubscription.LastTriggeredBy = "System-Scheduler";

                    await db.SaveChangesAsync();
                    _logger.LogInformation("Updated notification schedule for subscription {SubscriptionId}", subscription.NotificationGroupUserId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating notification schedule for subscription {SubscriptionId}", subscription.NotificationGroupUserId);
                // Don't throw - this is not critical enough to stop processing
            }
        }

        // Costing Report methods
        public async Task<string> GenerateCostingReportPdfAndUploadToStorageAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // Get costing data using existing patterns
                var costingData = await GetCostingReportData(fromDate, toDate);
                if (costingData == null || !costingData.CostingData.Any())
                {
                    _logger.LogWarning("No costing data found for period {FromDate} to {ToDate}", fromDate, toDate);
                    return null;
                }

                var fileName = $"CostingReport_{Guid.NewGuid():N}.pdf";

                // Use the WhatsApp-compatible upload method
                var pdfUrl = await _pdfService.GeneratePdfAndUploadToWhatsAppStorageAsync(
                    costingData,
                    fileName,
                    "costingreport"  // Document type for hierarchical organization
                );

                _logger.LogInformation("Successfully generated and uploaded costing report PDF: {PdfUrl}", pdfUrl);
                return pdfUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating costing report PDF and uploading to storage");
                return null;
            }
        }

        public async Task<Dictionary<string, string>> GenerateCostingReportParameters(long templateMasterId, DateTime fromDate, DateTime toDate)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameters = new Dictionary<string, string>();

                // Convert UTC dates to IST for proper display
                var fromDateIST = TimeZoneHelper.ConvertToTimeZone(fromDate, TimeZoneId.IndiaStandardTime);
                var toDateIST = TimeZoneHelper.ConvertToTimeZone(toDate, TimeZoneId.IndiaStandardTime);

                // Create parameter mapping
                var parameterMapping = new Dictionary<string, string>
                {
                    { "generateddate", fromDateIST.ToString("dd-MMM-yyyy hh:mm tt") + " to " + toDateIST.ToString("dd-MMM-yyyy hh:mm tt") },
                    { "reportname", "Executive Costing Analysis Report" }
                };

                // Map template parameters to their values
                foreach (var param in templateParams)
                {
                    string paramValue = parameterMapping.TryGetValue(param.ParameterName, out string value)
                        ? value
                        : param.DefaultValue ?? "N/A";

                    parameters.Add(param.ParameterName, paramValue);

                    if (param.IsRequired == true && string.IsNullOrEmpty(paramValue))
                    {
                        _logger.LogWarning("Required parameter {ParameterName} has no value for template {TemplateMasterId}", param.ParameterName, templateMasterId);
                    }
                }

                return parameters;
            }
        }

        // Overhead Cost Reminder methods
        public async Task<List<OverheadCostReminderData>> GetOverdueOverheadCostData()
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    var currentDate = DateTime.Now.Date;
                    var currentYear = currentDate.Year;
                    var currentMonth = currentDate.Month;
                    var fifteenthOfCurrentMonth = new DateTime(currentYear, currentMonth, 15);

                    // Only check for reminders if we're past the 15th of the current month
                    if (currentDate <= fifteenthOfCurrentMonth)
                    {
                        return new List<OverheadCostReminderData>(); // No reminders needed yet
                    }

                    // Check if an overhead cost entry exists for the current month
                    // Following the FIFO pattern: get the most recent entry (OrderByDescending ApplicableOn)
                    var mostRecentOverheadEntry = await db.OverheadCostingTables
                        .OrderByDescending(x => x.ApplicableOn)
                        .FirstOrDefaultAsync();

                    if (mostRecentOverheadEntry == null)
                    {
                        // No overhead cost entries exist at all - this is critical
                        return new List<OverheadCostReminderData>
                        {
                            new OverheadCostReminderData
                            {
                                OverheadCostingId = 0,
                                LastUpdatedDate = DateTime.MinValue,
                                ApplicableOnDate = new DateTime(currentYear, currentMonth, 1), // First day of current month
                                DaysDelayed = (int)(currentDate - fifteenthOfCurrentMonth).TotalDays,
                                OverheadCost = 0
                            }
                        };
                    }

                    // Check if the most recent entry is for the current month
                    var mostRecentEntryDate = mostRecentOverheadEntry.ApplicableOn;

                    // If the most recent entry is from the current month, no reminder needed
                    if (mostRecentEntryDate.Year == currentYear && mostRecentEntryDate.Month == currentMonth)
                    {
                        return new List<OverheadCostReminderData>(); // Current month entry exists
                    }

                    // Most recent entry is from a previous month - reminder needed
                    var daysDelayed = (int)(currentDate - fifteenthOfCurrentMonth).TotalDays;

                    return new List<OverheadCostReminderData>
                    {
                        new OverheadCostReminderData
                        {
                            OverheadCostingId = mostRecentOverheadEntry.OverheadCostingId,
                            LastUpdatedDate = mostRecentOverheadEntry.UpdatedDate ?? mostRecentOverheadEntry.AddedDate ?? DateTime.MinValue,
                            ApplicableOnDate = mostRecentOverheadEntry.ApplicableOn,
                            DaysDelayed = daysDelayed,
                            OverheadCost = mostRecentOverheadEntry.OverheadCost
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting overdue overhead cost data");
                return new List<OverheadCostReminderData>();
            }
        }

        public async Task<Dictionary<string, string>> GenerateOverheadCostReminderParameters(long templateMasterId, DateTime lastUpdatedDate, DateTime applicableOnDate, int daysDelayed)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameters = new Dictionary<string, string>();

                // Convert dates to IST for proper display
                var lastUpdatedIST = TimeZoneHelper.ConvertToTimeZone(lastUpdatedDate, TimeZoneId.IndiaStandardTime);
                var applicableOnIST = TimeZoneHelper.ConvertToTimeZone(applicableOnDate, TimeZoneId.IndiaStandardTime);

                _logger.LogInformation("Found template id {TemplateMasterId} with parameters: {Parameters}",
                    templateMasterId,
                    string.Join(", ", templateParams.Select(p => $"{p.ParameterName} (Sequence: {p.Sequence})")));

                foreach (var param in templateParams)
                {
                    // Determine the parameter key: use sequence number if available and > 0, otherwise use parameter name
                    // This follows the established PMS notification system pattern for WhatsApp compatibility
                    string parameterKey = param.Sequence == null || param.Sequence == 0
                        ? param.ParameterName
                        : param.Sequence.ToString();

                    // Determine the parameter value based on the parameter name (not the key)
                    string parameterValue;
                    switch (param.ParameterName.ToLower())
                    {
                        case "last_updated_date":
                        case "1":
                            parameterValue = lastUpdatedIST.ToString("dd-MMM-yyyy HH:mm tt");
                            break;
                        case "applicable_on_date":
                        case "2":
                            parameterValue = applicableOnIST.ToString("dd-MMM-yyyy HH:mm tt");
                            break;
                        case "days_delayed":
                        case "3":
                            parameterValue = daysDelayed.ToString();
                            break;
                        default:
                            parameterValue = !string.IsNullOrEmpty(param.DefaultValue) ? param.DefaultValue : "N/A";
                            break;
                    }

                    // Add parameter using the determined key and value
                    parameters.Add(parameterKey, parameterValue);

                    if (param.IsRequired == true && string.IsNullOrEmpty(parameterValue))
                    {
                        _logger.LogWarning("Required parameter {ParameterName} has no value for template {TemplateMasterId}", param.ParameterName, templateMasterId);
                    }
                }

                _logger.LogInformation("Created parameters for OverheadCostReminder: {Parameters}",
                    string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}")));

                return parameters;
            }
        }

        public async Task UpdateNotificationLastTriggered(long notificationGroupUserId, DateTime currentTimeUtc)
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    var subscription = await db.NotificationGroupsTables
                        .FirstOrDefaultAsync(n => n.NotificationGroupUserId == notificationGroupUserId);

                    if (subscription != null)
                    {
                        subscription.LastTriggeredDate = currentTimeUtc;
                        subscription.LastTriggeredBy = "System-Scheduler";
                        await db.SaveChangesAsync();

                        _logger.LogInformation("Updated last triggered date for notification group {NotificationGroupUserId}", notificationGroupUserId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating notification last triggered date for group {NotificationGroupUserId}", notificationGroupUserId);
                // Don't throw - this is not critical enough to stop processing
            }
        }

        // Helper methods for costing report data
        private async Task<ICostingReportPdfData> GetCostingReportData(DateTime fromDate, DateTime toDate)
        {
            try
            {
                using (var db = new Models.pmsdbContext())
                {
                    // Get costing data from database - following existing patterns
                    // Include ThicknessMasters join to match GetTopSellingProducts pattern
                    var costingQuery = from c in db.SaleOrderCostingTables
                                       join so in db.SaleOrderTables on c.SaleOrderId equals so.SaleOrderId
                                       join cm in db.CustomerMasters on so.CustomerId equals cm.CustomerId
                                       join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId into sopGroup
                                       from sop in sopGroup.DefaultIfEmpty()
                                       join fcm in db.SaleFormulationCodeMasters on so.SaleFormulationCodeId equals fcm.SaleFormulationCodeId into fcmGroup
                                       from fcm in fcmGroup.DefaultIfEmpty()
                                       join pcm in db.ProductCategoryMasters on fcm.CategoryId equals pcm.ProductCategoryId into pcmGroup
                                       from pcm in pcmGroup.DefaultIfEmpty()
                                       join tk in db.ThicknessMasters on sop.Thick equals tk.ThicknessId into tkGroup
                                       from tk in tkGroup.DefaultIfEmpty()
                                       where c.AddedDate >= fromDate && c.AddedDate <= toDate
                                       select new CostingReportItem
                                       {
                                           SerialNo = 0, // Will be set later
                                           SaleOrderNo = so.SaleOrderNumber,
                                           CustomerName = cm.CustomerName,
                                           Alias = sop.ManufacturingProductName,
                                           Category = pcm.ProductCategory ?? "N/A",
                                           OrderQty = sop.OrderQuantity ?? 0,
                                           MfdQty = sop.ManufacturingQuantity ?? 0,
                                           RejectionPercent = c.Rejection ?? 0,
                                           SalePrice = sop.SalePrice ?? 0,
                                           // Use proper calculation logic matching CostingDataFn.cs
                                           TotalCostLm = (c.ProductionCostLm ?? 0) + (c.OverheadCost ?? 0) +
                                                        (c.PackagingCostPerUnit ?? 0) + (c.MiscellaneousCostPerUnit ?? 0),
                                           FinalTotalCost = ((c.ProductionCostLm ?? 0) + (c.OverheadCost ?? 0) +
                                                           (c.PackagingCostPerUnit ?? 0) + (c.MiscellaneousCostPerUnit ?? 0)) *
                                                           (sop.ManufacturingQuantity ?? 0),
                                           ProfitLossLm = (sop.SalePrice ?? 0) - ((c.ProductionCostLm ?? 0) + (c.OverheadCost ?? 0) +
                                                         (c.PackagingCostPerUnit ?? 0) + (c.MiscellaneousCostPerUnit ?? 0)),
                                           TotalProfitLoss = ((sop.ManufacturingQuantity ?? 0) * (sop.SalePrice ?? 0)) -
                                                           (((c.ProductionCostLm ?? 0) + (c.OverheadCost ?? 0) +
                                                            (c.PackagingCostPerUnit ?? 0) + (c.MiscellaneousCostPerUnit ?? 0)) *
                                                            (sop.ManufacturingQuantity ?? 0)),
                                           OverheadCost = c.OverheadCost ?? 0,
                                           CostingStatus = sop.CostingStatus ?? "Pending",
                                           SubmittedDate = c.AddedDate ?? DateTime.MinValue,
                                           SubmittedBy = c.AddedBy ?? "N/A",
                                           // Add fields needed for top products calculation following GetTopSellingProducts pattern
                                           FormulationCode = fcm.SaleFormulationCode ?? "N/A",
                                           ThicknessNumber = tk.ThicknessNumber ?? "N/A"
                                       };

                    var costingData = await costingQuery.ToListAsync();

                    // Set serial numbers
                    for (int i = 0; i < costingData.Count; i++)
                    {
                        costingData[i].SerialNo = i + 1;
                    }

                    // Calculate summary using proper field mappings
                    var summary = new CostingReportSummary
                    {
                        GrandTotalOrderQty = costingData.Sum(x => x.OrderQty),
                        GrandTotalManufacturedQty = costingData.Sum(x => x.MfdQty),
                        TotalAverageRejectionPercent = costingData.Any() ? costingData.Average(x => x.RejectionPercent) : 0,
                        // AverageOverheadCostLm should be average of overhead costs, not total costs
                        AverageOverheadCostLm = costingData.Any() ?
                            costingData.Where(x => x.OverheadCost > 0).Average(x => x.OverheadCost) : 0,
                        GrandTotalCost = costingData.Sum(x => x.FinalTotalCost),
                        AverageProfitLossLm = costingData.Any() ? costingData.Average(x => x.ProfitLossLm) : 0,
                        GrandTotalProfitLoss = costingData.Sum(x => x.TotalProfitLoss),
                        TotalRecords = costingData.Count
                    };

                    // Calculate analytics
                    var analytics = new CostingReportAnalytics
                    {
                        ProfitPercentage = summary.GrandTotalProfitLoss > 0 ?
                            (costingData.Count(x => x.TotalProfitLoss > 0) * 100.0m / costingData.Count) : 0,
                        OrderFulfillmentRate = summary.GrandTotalOrderQty > 0 ?
                            (summary.GrandTotalManufacturedQty * 100 / summary.GrandTotalOrderQty) : 0,
                        AverageOrderValue = costingData.Any() ? costingData.Average(x => x.FinalTotalCost) : 0
                    };

                    analytics.LossPercentage = analytics.ProfitPercentage > 0 ? 100 - analytics.ProfitPercentage : 0;

                    // Get top products following GetTopSellingProducts pattern
                    // Group by FormulationCode + ThicknessNumber (same as GetTopSellingProducts)
                    var topProducts = costingData
                        .Where(x => !string.IsNullOrEmpty(x.FormulationCode) && !string.IsNullOrEmpty(x.ThicknessNumber))
                        .GroupBy(x => new { x.FormulationCode, x.ThicknessNumber })
                        .Select(g => new TopProductItem
                        {
                            // Product identification: FormulationCode + "-" + ThicknessNumber (same format as GetTopSellingProducts chart)
                            ProductName = g.Key.FormulationCode + " - " + g.Key.ThicknessNumber,
                            SalesVolume = g.Sum(x => x.MfdQty), // Total manufactured quantity (same as TotalOrderQty in GetTopSellingProducts)
                            SalesValue = g.Sum(x => x.FinalTotalCost), // Total sales value
                            OrderCount = g.Count() // Number of orders for this product combination
                        })
                        .OrderByDescending(x => x.SalesVolume) // Order by sales volume (same as GetTopSellingProducts)
                        .Take(10) // Top 10 products
                        .ToList();

                    return new PmsCore.Notifications.Models.CostingReportPdfData
                    {
                        FromDate = fromDate,
                        ToDate = toDate,
                        CostingData = costingData,
                        Summary = summary,
                        Analytics = analytics,
                        TopProducts = topProducts
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting costing report data");
                return null;
            }
        }
    }
}

