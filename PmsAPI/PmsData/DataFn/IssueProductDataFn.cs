using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using static System.Net.WebRequestMethods;
using PmsCommon;
using Azure.Core;
using Microsoft.Graph;
using PmsCore.Notifications.Interfaces;
namespace PmsData.DataFn
{
    public class IssueProductDataFn
    {
        public GlobalDataEntity GlobalData;
        private readonly INotificationService _notificationService;
        public IssueProductDataFn(GlobalDataEntity gd, INotificationService notificationService)
        {
            GlobalData = gd;
            this._notificationService = notificationService;
        }
        public List<IssueProductTableVm> GetIssueProductRequests(IssueFilterVm filter)
        {
            if (filter.DateTo == null)
            {
                filter.DateTo = DateTime.Now;
            }
            if (filter.DateFrom == null)
            {
                filter.DateFrom = filter.DateTo.Value.AddDays(-3);
            }
            List<IssueProductTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from inv in db.IssueProductTables
                       join ist in db.IssueSlipTables on inv.IssueSlipId equals ist.IssueSlipId into istd
                       from ist in istd.DefaultIfEmpty()
                       join stp in db.StockProductTables on inv.StockProductId equals stp.StockProductId into stpd
                       from stp in stpd.DefaultIfEmpty()
                       join so in db.SaleOrderTables on inv.SaleOrderId equals so.SaleOrderId into sot
                       from so in sot.DefaultIfEmpty()
                       join stm in db.StockMasters on inv.StockId equals stm.StockId into fcms
                       from stm in fcms.DefaultIfEmpty()
                       join invo in db.InvoiceMasters on stm.InvoiceId equals invo.InvoiceId into fcmsin
                       from invo in fcmsin.DefaultIfEmpty()
                       join sup in db.SupplierMasters on invo.SupplierId equals sup.SupplierId into fcmsup
                       from sup in fcmsup.DefaultIfEmpty()
                       join r in db.RackMasters on inv.FromRack equals r.RackId
                       join p in db.ProductMasters on inv.ProductId equals p.ProductId
                       join fs in db.StoreMasters on inv.FromStore equals fs.StoreId
                       join ts in db.StoreMasters on inv.ToStore equals ts.StoreId
                       join fsd in db.DeptMasters on fs.DeptId equals fsd.DeptId
                       join tsd in db.DeptMasters on ts.DeptId equals tsd.DeptId
                       join fuser in db.UserStoreMappingTables on fs.StoreId equals fuser.StoreId
                       where fuser.Username == GlobalData.loggedInUser
                       && (filter.DateFrom == null || inv.CreatedDate >= filter.DateFrom)
                       && (filter.DateTo == null || inv.CreatedDate <= filter.DateTo)
                       && (filter.IssueId == 0 || filter.IssueId == inv.IssueId)
                       && (string.IsNullOrEmpty(filter.SaleOrderNumber) || filter.SaleOrderNumber == so.SaleOrderNumber)
                       && (string.IsNullOrEmpty(filter.Status) || inv.Status == filter.Status)
                       && (filter.ProductId == 0 || inv.ProductId == filter.ProductId)
                       && (filter.ProductCategoryId == 0 || filter.ProductCategoryId == p.ProductCategoryId)
                       && (filter.ProductFirstSubCategoryId == 0 || filter.ProductFirstSubCategoryId == p.ProductFirstSubCategoryId)
                       && (filter.ProductSecSubCategoryId == 0 || filter.ProductSecSubCategoryId == p.ProductSecSubCategoryId)
                       && (string.IsNullOrEmpty(filter.IssueSlipNumber) || filter.IssueSlipNumber == ist.IssueSlipNumber)
                       && (filter.FromStoreId == 0 || fs.StoreId == filter.FromStoreId)
                       && (filter.ToStoreId == 0 || ts.StoreId == filter.ToStoreId)
                       && (string.IsNullOrEmpty(filter.RequestedBy) || filter.RequestedBy == inv.CreatedBy)
                       && (string.IsNullOrEmpty(filter.ActionBy) || filter.ActionBy == inv.ActionBy)
                       && (string.IsNullOrEmpty(filter.BatchNo) || filter.BatchNo == stm.Batch)
                       select new IssueProductTableVm
                       {
                           IssueId = inv.IssueId,
                           SaleOrderId = inv.SaleOrderId,
                           SaleOrderNumber = so.SaleOrderNumber,
                           FromStore = inv.FromStore,
                           FromStoreName = fs.StoreName,
                           FromDept = fs.DeptId,
                           FromDeptName = fsd.DeptName,
                           ToStore = inv.ToStore,
                           ToStoreName = ts.StoreName,
                           ToDept = ts.DeptId,
                           ToDeptName = tsd.DeptName,
                           ProductId = inv.ProductId,
                           ProductCode = p.ProductCode,
                           ProductName = p.ProductName,
                           Quantity = inv.Quantity,
                           DemandQuantity = inv.DemandQuantity ?? 0,
                           Status = inv.Status,
                           Remark = inv.Remark,
                           CreatedBy = inv.CreatedBy,
                           CreatedDate = inv.CreatedDate,
                           AuthorizedAction = PmsCommon.PMSStatus.ToView,
                           isView = true,
                           isAction = true,
                           ActionBy = inv.ActionBy,
                           ActionDate = inv.ActionDate,
                           SKU = stp.Sku,
                           BarCode = stp.Barcode,
                           Batch = stm.Batch,
                           Unit = stp.Unit,
                           PerUnitPrice = stp.PricePerUnit,
                           ManufacturedDate = stp.ManufacturedDate,
                           ExpiryDate = stp.ExpiryDate,
                           StockId = stp.StockId,
                           StockProductId = inv.StockProductId,
                           SupplierId = invo.SupplierId,
                           SupplierName = sup.SupplierName,
                           InvoiceId = stm.InvoiceId,
                           InvoiceNumber = invo.InvoiceNumber,
                           FromRack = inv.FromRack,
                           FromRackName = r.RackName,
                           FromRackCode = r.RackCode,
                           IssueSlipNumber = ist.IssueSlipNumber ?? "Not Generated",
                           IssueSlipId = ist.IssueSlipId,
                           RequestType = inv.RequestType
                       }).ToList();

                List<IssueProductTableVm> res1 = (from inv in db.IssueProductTables
                                                  join ist in db.IssueSlipTables on inv.IssueSlipId equals ist.IssueSlipId into istd
                                                  from ist in istd.DefaultIfEmpty()
                                                  join stp in db.StockProductTables on inv.StockProductId equals stp.StockProductId into stpd
                                                  from stp in stpd.DefaultIfEmpty()
                                                  join so in db.SaleOrderTables on inv.SaleOrderId equals so.SaleOrderId into sot
                                                  from so in sot.DefaultIfEmpty()
                                                  join stm in db.StockMasters on inv.StockId equals stm.StockId into fcms
                                                  from stm in fcms.DefaultIfEmpty()
                                                  join invo in db.InvoiceMasters on stm.InvoiceId equals invo.InvoiceId into fcmsin
                                                  from invo in fcmsin.DefaultIfEmpty()
                                                  join sup in db.SupplierMasters on invo.SupplierId equals sup.SupplierId into fcmsup
                                                  from sup in fcmsup.DefaultIfEmpty()
                                                  join r in db.RackMasters on inv.FromRack equals r.RackId
                                                  join p in db.ProductMasters on inv.ProductId equals p.ProductId
                                                  join fs in db.StoreMasters on inv.FromStore equals fs.StoreId
                                                  join ts in db.StoreMasters on inv.ToStore equals ts.StoreId
                                                  join fsd in db.DeptMasters on fs.DeptId equals fsd.DeptId
                                                  join tsd in db.DeptMasters on ts.DeptId equals tsd.DeptId
                                                  join tuser in db.UserStoreMappingTables on ts.StoreId equals tuser.StoreId
                                                  where tuser.Username == GlobalData.loggedInUser
                                                    && (filter.DateFrom == null || inv.CreatedDate >= filter.DateFrom)
                                                    && (filter.DateTo == null || inv.CreatedDate <= filter.DateTo)
                                                    && (filter.IssueId == 0 || filter.IssueId == inv.IssueId)
                                                    && (string.IsNullOrEmpty(filter.SaleOrderNumber) || filter.SaleOrderNumber == so.SaleOrderNumber)
                                                    && (string.IsNullOrEmpty(filter.Status) || inv.Status == filter.Status)
                                                    && (filter.ProductId == 0 || inv.ProductId == filter.ProductId)
                                                    && (filter.ProductCategoryId == 0 || filter.ProductCategoryId == p.ProductCategoryId)
                                                    && (filter.ProductFirstSubCategoryId == 0 || filter.ProductFirstSubCategoryId == p.ProductFirstSubCategoryId)
                                                    && (filter.ProductSecSubCategoryId == 0 || filter.ProductSecSubCategoryId == p.ProductSecSubCategoryId)
                                                    && (string.IsNullOrEmpty(filter.IssueSlipNumber) || filter.IssueSlipNumber == ist.IssueSlipNumber)
                                                    && (filter.FromStoreId == 0 || fs.StoreId == filter.FromStoreId)
                                                    && (filter.ToStoreId == 0 || ts.StoreId == filter.ToStoreId)
                                                    && (string.IsNullOrEmpty(filter.RequestedBy) || filter.RequestedBy == inv.CreatedBy)
                                                    && (string.IsNullOrEmpty(filter.ActionBy) || filter.ActionBy == inv.ActionBy)
                                                    && (string.IsNullOrEmpty(filter.BatchNo) || filter.BatchNo == stm.Batch)
                                                  select new IssueProductTableVm
                                                  {
                                                      IssueId = inv.IssueId,
                                                      SaleOrderId = inv.SaleOrderId,
                                                      SaleOrderNumber = so.SaleOrderNumber,
                                                      FromStore = inv.FromStore,
                                                      FromStoreName = fs.StoreName,
                                                      FromDept = fs.DeptId,
                                                      FromDeptName = fsd.DeptName,
                                                      ToStore = inv.ToStore,
                                                      ToStoreName = ts.StoreName,
                                                      ToDept = ts.DeptId,
                                                      ToDeptName = tsd.DeptName,
                                                      ProductId = inv.ProductId,
                                                      ProductCode = p.ProductCode,
                                                      ProductName = p.ProductName,
                                                      Quantity = inv.Quantity,
                                                      DemandQuantity = inv.DemandQuantity,
                                                      Status = inv.Status,
                                                      Remark = inv.Remark,
                                                      CreatedBy = inv.CreatedBy,
                                                      CreatedDate = inv.CreatedDate,
                                                      AuthorizedAction = PmsCommon.PMSStatus.ToAction,
                                                      isAction = false,
                                                      isView = true,
                                                      ActionBy = inv.ActionBy,
                                                      ActionDate = inv.ActionDate,
                                                      SKU = stp.Sku,
                                                      BarCode = stp.Barcode,
                                                      Batch = stm.Batch,
                                                      Unit = stp.Unit,
                                                      PerUnitPrice = stp.PricePerUnit,
                                                      ManufacturedDate = stp.ManufacturedDate,
                                                      ExpiryDate = stp.ExpiryDate,
                                                      StockId = stp.StockId,
                                                      StockProductId = inv.StockProductId,
                                                      SupplierId = invo.SupplierId,
                                                      SupplierName = sup.SupplierName,
                                                      InvoiceId = stm.InvoiceId,
                                                      InvoiceNumber = invo.InvoiceNumber,
                                                      FromRack = inv.FromRack,
                                                      FromRackName = r.RackName,
                                                      FromRackCode = r.RackCode,
                                                      IssueSlipNumber = ist.IssueSlipNumber ?? "Not Generated",
                                                      IssueSlipId = ist.IssueSlipId,
                                                      RequestType = inv.RequestType
                                                  }).ToList();
                foreach (var item in res1)
                {
                    var data = res.FirstOrDefault(x => x.IssueId == item.IssueId);
                    if (data == null)
                    {
                        res.Add(item);
                    }
                    else
                    {
                        res.FirstOrDefault(x => x.IssueId == item.IssueId).isAction = true;
                    }
                }
            }
            return res.OrderByDescending(x => x.IssueId).ToList();
        }

        public ApiFunctionResponseVm IssueProductRequest(List<IssueProductTableVm> ipt)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (ipt[0].FromRack == ipt[0].ToRack)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "From and to rack should not be same");
                }
                foreach (var item in ipt)
                {
                    IssueProductTable spt = new IssueProductTable();
                    if (item.RequestType == "Simple")
                    {

                        spt.FromStore = item.FromStore;
                        spt.SaleOrderId = item.SaleOrderId;
                        spt.ToStore = item.ToStore;
                        spt.ToRack = item.ToRackId;
                        spt.ProductId = item.ProductId;
                        spt.DemandQuantity = item.Quantity;
                        // spt.Quantity = item.Quantity;
                        spt.Status = PmsCommon.PMSStatus.Pending;
                        spt.Remark = item.Remark;
                        spt.CreatedBy = String.IsNullOrEmpty(item.CreatedBy) ? "System" : item.CreatedBy;
                        spt.CreatedDate = System.DateTime.Now;
                        spt.FromRack = item.FromRack;
                        spt.RequestType = item.RequestType;
                    }
                    else
                    {
                        spt.FromStore = item.FromStore;
                        spt.SaleOrderId = item.SaleOrderId;
                        spt.ToStore = item.ToStore;
                        spt.ToRack = item.ToRackId;
                        spt.ProductId = item.ProductId;
                        spt.DemandQuantity = item.Quantity;
                        // spt.Quantity = item.Quantity;
                        spt.Status = PmsCommon.PMSStatus.Pending;
                        spt.Remark = item.Remark;
                        spt.CreatedBy = String.IsNullOrEmpty(item.CreatedBy) ? "System" : item.CreatedBy;
                        spt.CreatedDate = System.DateTime.Now;
                        spt.FromRack = item.FromRack;
                        spt.StockProductId = item.StockProductId;
                        spt.StockId = item.StockId;
                        spt.RequestType = item.RequestType;
                    }
                    db.IssueProductTables.Add(spt);
                }
                db.SaveChanges();
                var saleorderprod = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == ipt[0].SaleOrderId);
                if (saleorderprod != null)
                {
                    saleorderprod.SaleOrderStoreId = ipt[0].ToStore;
                    db.SaveChanges();
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Issue Request Submitted Successfully.");
        }

        public ApiFunctionResponseVm SaleOrderIssueProductRequest(List<IssueProductTableVm> ipt)
        {
            using (var db = new Models.pmsdbContext())
            {
                var reqqty = ipt[0].Quantity;
                foreach (var item in ipt)
                {
                    if (item.ProductId > 0)
                    {
                        var notexistrec = !db.IssueProductTables.Any(x => x.FromStore == item.FromStore
                            && x.SaleOrderId == item.SaleOrderId
                            && x.ToStore == item.ToStore
                            && x.ProductId == item.ProductId
                            && x.DemandQuantity == item.Quantity
                            && x.Status != PmsCommon.PMSStatus.Rejected
                            );
                        if (notexistrec)
                        {
                            IssueProductTable spt = new IssueProductTable();
                            spt.FromStore = item.FromStore;
                            spt.SaleOrderId = item.SaleOrderId;
                            spt.ToStore = item.ToStore;
                            spt.ProductId = item.ProductId;
                            spt.DemandQuantity = item.Quantity;
                            spt.Status = PmsCommon.PMSStatus.Pending;
                            spt.CreatedBy = GlobalData.loggedInUser;
                            spt.CreatedDate = System.DateTime.Now;
                            spt.FromRack = item.FromRack;
                            spt.StockProductId = item.StockProductId;
                            spt.StockId = item.StockId;
                            db.IssueProductTables.Add(spt);
                        }
                    }
                }
                db.SaveChanges();
                if (ipt[0].SaleOrderId > 0)
                {
                    List<FormulationCodeMixingRawMaterialTableVm> InsStock = GetStockForSaleOrder(ipt[0].SaleOrderId.Value);
                    List<long> productList = ipt.Select(x => x.ProductId).ToList();
                    if (productList.Count > 0)
                    {
                        InsStock = InsStock.Where(x => !productList.Contains(x.ProductId.Value)).ToList();
                    }
                    if (InsStock != null && InsStock.Count > 0)
                    {
                        foreach (var item in InsStock)
                        {
                            var notexistrec = !db.IssueProductTables.Any(x => x.FromStore == ipt[0].ToStore
                            && x.SaleOrderId == ipt[0].SaleOrderId
                            && x.ToStore == ipt[0].ToStore
                            && x.ProductId == item.ProductId.Value
                            && x.Quantity == item.Quantity
                            && x.Status != PmsCommon.PMSStatus.Rejected
                            );
                            if (notexistrec)
                            {
                                IssueProductTable spt = new IssueProductTable();
                                spt.FromStore = ipt[0].ToStore;
                                spt.SaleOrderId = ipt[0].SaleOrderId;
                                spt.ToStore = ipt[0].ToStore;
                                spt.ProductId = item.ProductId.Value;
                                spt.DemandQuantity = item.Quantity.Value;
                                spt.Quantity = item.Quantity.Value;
                                spt.Status = PmsCommon.PMSStatus.Approved;
                                spt.CreatedBy = GlobalData.loggedInUser;
                                spt.CreatedDate = System.DateTime.Now;
                                spt.FromRack = item.ProductStock.Count > 0 ? item.ProductStock.FirstOrDefault(x => x.StoreId == ipt[0].ToStore).RackId : 0;
                                spt.StockProductId = item.ProductStock.Count > 0 ? item.ProductStock.FirstOrDefault(x => x.StoreId == ipt[0].ToStore).StockProductId : 0;
                                spt.StockId = item.ProductStock.Count > 0 ? item.ProductStock.FirstOrDefault(x => x.StoreId == ipt[0].ToStore).StockId : 0;
                                spt.ToRack = item.ProductStock.Count > 0 ? item.ProductStock.FirstOrDefault(x => x.StoreId == ipt[0].ToStore).RackId : 0;
                                spt.ToNewStockProductId = item.ProductStock.Count > 0 ? item.ProductStock.FirstOrDefault(x => x.StoreId == ipt[0].ToStore).StockProductId : 0;
                                spt.ToNewStockId = item.ProductStock.Count > 0 ? item.ProductStock.FirstOrDefault(x => x.StoreId == ipt[0].ToStore).StockId : 0;
                                db.IssueProductTables.Add(spt);
                            }
                        }
                        db.SaveChanges();

                    }

                    var saleorderprod = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == ipt[0].SaleOrderId);
                    var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == ipt[0].SaleOrderId);
                    if (so != null)
                    {
                        so.Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialRequested;
                        saleorderprod.SaleOrderStoreId = ipt[0].ToStore;

                        db.SaveChanges();
                        if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.RawMaterialRequested))
                        {
                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                            {
                                SaleOrderId = so.SaleOrderId,
                                Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialRequested,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = System.DateTime.Now,
                                WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                            });
                            db.SaveChanges();
                        }
                    }
                    var pendingrecs = db.IssueProductTables.Where(x => x.SaleOrderId == ipt[0].SaleOrderId && x.Status == PmsCommon.PMSStatus.Pending).Count();
                    if (pendingrecs == 0)
                    {
                        if (so != null)
                        {
                            so.Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialIssued;
                            db.SaveChanges();
                            var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnInProduction" && x.ConfigValue == "true")).ToList();
                            if (enableSOStatusEmail.Count == 2)
                            {
                                var emailSaleOrderStatus = SaleOrderEmailStatus.StartingProduction;
                                _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(so.SaleOrderId, emailSaleOrderStatus);
                            }
                        }
                        if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.RawMaterialIssued))
                        {
                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                            {
                                SaleOrderId = so.SaleOrderId,
                                Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialIssued,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = System.DateTime.Now,
                                WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                            });
                            db.SaveChanges();
                        }
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm SaleOrderInspectionProductRequest(SaleOrderTableVm request)
        {
            if (request.SaleOrderProduction.LMConstant == null || request.SaleOrderProduction.LMConstant == 0)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "LM Constant cannot be empty or 0");
            }
            if (request.SaleOrderProduction.ExtraProduction == null || request.SaleOrderProduction.ExtraProduction < 0)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Extra Production Percentage cannot be empty, enter 0 atleast.");
            }
            List<string> productList = new List<string>();
            using (var db = new Models.pmsdbContext())
            {
                //if (request.SendToConsumption)
                //{
                //    var a = GetOldStockForSaleOrder(request.SaleOrderId);
                //    var checkdata = new StockDataFn().GetProductWiseStoreRackstock().Where(x => x.StoreId == request.ConsumptionStoreId).ToList();
                //    var fb1 = db.SaleFormulationCodeMasters.FirstOrDefault(x => x.SaleFormulationCodeId == request.SaleFormulationCodeId);
                //    var checkFebQty = checkdata.Where(x => x.ProductId == fb1.FabricProductId && x.Quantity >= request.FormulationCode.FabricProductQty).FirstOrDefault();
                //    if (checkFebQty == null)
                //    {
                //        var cfq = checkdata.Where(x => x.ProductId == fb1.FabricProductId).OrderByDescending(x => x.Quantity).FirstOrDefault();
                //        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Insufficient stock of fabric product, only " + cfq.Quantity + " available");
                //    }
                //    foreach (var item in request.SaleOrderProduction.InspectionFormulationMixing)
                //    {
                //        foreach (var itm in item.MixingRawMaterial)
                //        {
                //            var checkqty = checkdata.Where(x => x.ProductId == itm.ProductId && x.Quantity >= itm.Quantity).FirstOrDefault();
                //            if (checkqty == null)
                //            {
                //                var cfq = checkdata.Where(x => x.ProductId == itm.ProductId).OrderByDescending(x => x.Quantity).FirstOrDefault();
                //                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Insufficient stock of " + itm.ProductName + " product, only " + cfq.Quantity + " available ");
                //            }
                //        }

                //    }
                //}
                long inscodeid = 0;
                if (request.SaleOrderId > 0)
                {
                    var fb = db.SaleFormulationCodeMasters.FirstOrDefault(x => x.SaleFormulationCodeId == request.SaleFormulationCodeId);

                    var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == request.SaleOrderId);
                    var sopro = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == request.SaleOrderId);
                    sopro.ExtraProduction = request.SaleOrderProduction.ExtraProduction;
                    sopro.Lmconstant = request.SaleOrderProduction.LMConstant;
                    var fabricProduct = request.FormulationCode.FabricProductId > 0 ? db.ProductMasters.FirstOrDefault(x => x.ProductId == request.FormulationCode.FabricProductId) : null;
                    if (fb != null)
                    {
                        InspectionSaleFormulationCodeMaster mm = new InspectionSaleFormulationCodeMaster();
                        mm.InspectionSaleFormulationCode = fb.SaleFormulationCode;
                        mm.AddedBy = GlobalData.loggedInUser;
                        mm.AddedDate = System.DateTime.Now;
                        mm.FabricProductId = request.FormulationCode.FabricProductId;
                        mm.PreSkinGsm = request.SaleOrderProduction.PreSkinGsm;
                        mm.SkinGsm = request.SaleOrderProduction.SkinGsm;
                        mm.FoamGsm = request.SaleOrderProduction.FoamGsm;
                        mm.AdhesiveGsm = request.SaleOrderProduction.AdhesiveGsm;
                        mm.FabricGsm = request.SaleOrderProduction.FabricGsm;
                        mm.TotalGsm = request.SaleOrderProduction.TotalGsm;
                        mm.ThicknessId = fb.ThicknessId;
                        mm.CategoryId = fb.CategoryId;
                        mm.FabricProductQty = request.FormulationCode.FabricProductQty;
                        mm.FabricWidthInMeter = fabricProduct?.WidthInMeter;
                        mm.ShiftSupervisorWorkerId = request.ShiftSupervisorWorkerId;
                        db.InspectionSaleFormulationCodeMasters.Add(mm);
                        db.SaveChanges();
                        so.Status = (int)PmsCommon.ESalesOrderStatus.Inspection;
                        db.SaveChanges();
                        inscodeid = mm.InspectionSaleFormulationCodeId;
                        if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == request.SaleOrderId && x.Status == (int)ESalesOrderStatus.Inspection))
                        {
                            db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                            {
                                SaleOrderId = request.SaleOrderId,
                                Status = (int)PmsCommon.ESalesOrderStatus.Inspection,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = System.DateTime.Now,
                                WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == request.SaleOrderId).WorkplanId
                            });
                            db.SaveChanges();
                        }
                    }
                }
                if (request.SaleOrderProduction.InspectionFormulationMixing != null)
                {
                    foreach (var item in request.SaleOrderProduction.InspectionFormulationMixing)
                    {
                        var totalQuantity = item.MixingRawMaterial.Sum(mrm => mrm.Quantity);
                        var totalScQuantity = item.MixingRawMaterial.Sum(mrm => mrm.Scquantity);

                        InspectionFormulationCodeMixingTable spl = new InspectionFormulationCodeMixingTable
                        {
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                            MixingId = item.MixingId,
                            SaleOrderId = request.SaleOrderId,
                            SaleFormulationCodeId = item.SaleFormulationCodeId,
                            InspectionSaleFormulationCodeId = inscodeid,
                            StdPasteRequirementQuantity = totalQuantity,
                            StdPasteRequirementScquantity = totalScQuantity
                        };
                        db.InspectionFormulationCodeMixingTables.Add(spl);
                        db.SaveChanges();
                        foreach (var itm in item.MixingRawMaterial)
                        {
                            InspectionFormulationCodeMixingRawMaterialTable spt = new InspectionFormulationCodeMixingRawMaterialTable
                            {
                                ProductId = itm.ProductId,
                                Quantity = itm.Quantity,
                                Unit = itm.Unit,
                                Price = itm.Price,
                                Scquantity = itm.Scquantity,
                                BaseQuantity = itm.BaseQuantity,
                                IsBaseMaterial = itm.IsBaseMaterial,
                                FormulationCodeMixingId = spl.FormulationCodeMixingId
                            };
                            db.InspectionFormulationCodeMixingRawMaterialTables.Add(spt);
                        }
                        db.SaveChanges();
                    }
                }
                // var saleorderproductionid = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == request.SaleOrderId).SaleOrderProductionId;
                // if (request.SaleOrderProduction.Lacquer != null)
                // {
                //     foreach (var item in request.SaleOrderProduction.Lacquer)
                //     {
                //         foreach (var itemraw in item.LacquerRawMaterial)
                //         {
                //             var data = db.SaleOrderProductionLacquerRawMaterialTables.FirstOrDefault(x => x.LacquerMasterId == itemraw.LacquerMasterId && x.ProductId == itemraw.ProductId && x.SaleOrderProductionId == saleorderproductionid && x.Removed != true);
                //             data.Quantity = itemraw.Quantity;
                //             db.SaveChanges();
                //         }
                //     }
                // }

                if (request.SendToConsumption)
                {
                    var InsStock = GetStockForSaleOrder(request.SaleOrderId);
                    foreach (var item in InsStock)
                    {

                        var itm = item.ProductStock
                            .Where(x => x.StoreId == request.ConsumptionStoreId)
                            .GroupBy(x => x.StoreId)
                            .Where(group => group.Sum(x => x.Quantity) >= item.Quantity)
                            .SelectMany(group => group)
                            .FirstOrDefault();
                        if (itm == null)
                        {
                            productList.Add(item.ProductName);
                        }
                    }
                    if (productList.Count() == 0)
                    {
                        if (request.SaleOrderId > 0)
                        {
                            var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == request.SaleOrderId);
                            var saleorderprod = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == request.SaleOrderId);
                            if (so != null)
                            {
                                so.Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialIssued;
                                saleorderprod.SaleOrderStoreId = request.ConsumptionStoreId;
                                db.SaveChanges();
                                var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnInProduction" && x.ConfigValue == "true")).ToList();
                                if (enableSOStatusEmail.Count == 2)
                                {
                                    var emailSaleOrderStatus = SaleOrderEmailStatus.StartingProduction;
                                    _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(so.SaleOrderId, emailSaleOrderStatus);
                                }
                            }
                            if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.RawMaterialIssued))
                            {
                                db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                {
                                    SaleOrderId = so.SaleOrderId,
                                    Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialIssued,
                                    AddedBy = GlobalData.loggedInUser,
                                    AddedDate = System.DateTime.Now,
                                    WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                                });
                                db.SaveChanges();
                            }
                        }
                    }

                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, productList.Distinct());
        }
        public ApiFunctionResponseVm CancelSaleOrderInspectionProductRequest(InspectionCancellationTrackingTableVm request)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var res = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == request.SaleOrderId);
                if (res != null)
                {
                    var isConsumptionCompleted = db.ConsumeStockProductMasters.Any(x => x.SaleOrderId == res.SaleOrderId);
                    if (isConsumptionCompleted)
                    {
                        transaction.Dispose();
                        return new ApiFunctionResponseVm(HttpStatusCode.Forbidden, "Inspection cannot be cancelled as raw material consumption was completed.");
                    }

                    var attempts = 1;
                    var lastCancelledIns = db.InspectionCancellationTrackingTables
                    .Where(x => x.SaleOrderId == request.SaleOrderId)
                    .OrderByDescending(y => y.CancelledDate)
                    .FirstOrDefault();

                    if (lastCancelledIns != null)
                    {
                        attempts = lastCancelledIns.AttemptNumber.Value + 1;
                    }

                    var newCancellation = new InspectionCancellationTrackingTable
                    {
                        SaleOrderId = request.SaleOrderId,
                        Reason = request.Reason,
                        AttemptNumber = attempts,
                        CancelledBy = GlobalData.loggedInUser,
                        CancelledDate = DateTime.Now,
                    };
                    db.InspectionCancellationTrackingTables.Add(newCancellation);
                    db.SaveChanges();

                    var InsMixinglist = db.InspectionFormulationCodeMixingTables.Where(x => x.SaleOrderId == request.SaleOrderId).ToList();
                    foreach (var InsMixing in InsMixinglist)
                    {
                        var InsRawMaterial = db.InspectionFormulationCodeMixingRawMaterialTables.Where(x => x.FormulationCodeMixingId == InsMixing.FormulationCodeMixingId).ToList();
                        db.InspectionFormulationCodeMixingRawMaterialTables.RemoveRange(InsRawMaterial);
                        db.SaveChanges();

                        var InsFCM = db.InspectionSaleFormulationCodeMasters.Where(x => x.InspectionSaleFormulationCodeId == InsMixing.InspectionSaleFormulationCodeId).ToList();
                        db.InspectionSaleFormulationCodeMasters.RemoveRange(InsFCM);
                        db.SaveChanges();

                        db.InspectionFormulationCodeMixingTables.Remove(InsMixing);
                        db.SaveChanges();
                    }
                }
                if (res.Status == (int)ESalesOrderStatus.Inspection)
                {
                    var sot = db.SaleOrderTimelineTables.Where(x => x.SaleOrderId == request.SaleOrderId && (x.Status == (int)ESalesOrderStatus.Inspection)).ToList();
                    db.SaleOrderTimelineTables.RemoveRange(sot);
                    db.SaveChanges();
                    res.Status = (int)ESalesOrderStatus.WorkPlan;
                    db.SaveChanges();
                }
                if (res.Status == (int)ESalesOrderStatus.RawMaterialRequested)
                {
                    var sot = db.SaleOrderTimelineTables.Where(x => x.SaleOrderId == request.SaleOrderId && (x.Status == (int)ESalesOrderStatus.Inspection || x.Status == (int)ESalesOrderStatus.RawMaterialRequested)).ToList();
                    db.SaleOrderTimelineTables.RemoveRange(sot);
                    db.SaveChanges();
                    res.Status = (int)ESalesOrderStatus.WorkPlan;
                    db.SaveChanges();
                }
                else if (res.Status == (int)ESalesOrderStatus.RawMaterialIssued)
                {
                    var issuelist = db.IssueProductTables.Where(x => x.SaleOrderId == request.SaleOrderId).ToList();
                    foreach (var item in issuelist)
                    {
                        item.SaleOrderId = null;
                    }
                    db.SaveChanges();

                    var sot = db.SaleOrderTimelineTables.Where(x => x.SaleOrderId == request.SaleOrderId && (x.Status == (int)ESalesOrderStatus.Inspection || x.Status == (int)ESalesOrderStatus.RawMaterialRequested || x.Status == (int)ESalesOrderStatus.RawMaterialIssued)).ToList();
                    db.SaleOrderTimelineTables.RemoveRange(sot);
                    db.SaveChanges();
                    res.Status = (int)ESalesOrderStatus.WorkPlan;
                    db.SaveChanges();
                }
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Inspection has been cancelled Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw;
            }
        }

        public ApiFunctionResponseVm ActionIssueProductRequest(IssueProductActionVm item)
        {
            if (item.ApprovalMode == "Barcode")
            {
                return ActionBarcodeLabelIssueRequest(item);
            }
            if (item.ApprovalMode == "Manual - Multi Batch")
            {
                return ActionMultiBatchIssueRequest(item);
            }

            using var db = new Models.pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                if (item.Status == PmsCommon.PMSStatus.Rejected)
                {
                    var resrem = db.IssueProductTables.FirstOrDefault(x => x.IssueId == item.IssueId);
                    resrem.Status = item.Status;
                    resrem.ActionBy = GlobalData.loggedInUser;
                    resrem.ActionDate = System.DateTime.Now;
                    db.SaveChanges();
                    if (resrem.SaleOrderId > 0)
                    {
                        var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == resrem.SaleOrderId);
                        if (so != null)
                        {
                            so.Status = (int)PmsCommon.ESalesOrderStatus.Inspection;
                            db.SaveChanges();
                        }
                    }
                    transaction.Commit();
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Request has been rejected Successfully");
                }
                var issueRec = db.IssueProductTables.FirstOrDefault(x => x.IssueId == item.IssueId);
                item.RackId = issueRec.ToRack.Value;
                if (item.FromRackId == issueRec.ToRack.Value)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "From and to rack in same Store cannot be same. This request can only be rejected.");
                }
                if (issueRec.RequestType == "Simple")
                {
                    issueRec.FromRack = item.FromRackId;
                    issueRec.StockId = item.StockId;
                    issueRec.StockProductId = item.StockProductId;
                }
                var removestock = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == issueRec.StockProductId && x.RackId == item.FromRackId);
                //var removestock = new StockProductAllocationTable
                //{
                //    Quantity = db.StockProductAllocationTables.Where(x => x.StockProductId == issueRec.StockProductId && x.sto == issueRec.FromRack).Sum(x => x.Quantity)
                //};
                if (removestock.Quantity < item.IssueQuantity)
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Quantity is greater than stock");

                removestock.Quantity = removestock.Quantity - item.IssueQuantity;

                var removestockproduct = db.StockProductTables.FirstOrDefault(x => x.StockProductId == issueRec.StockProductId);
                if (removestockproduct.Quantity < item.IssueQuantity)
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Quantity is greater than stock");

                removestockproduct.Quantity = removestockproduct.Quantity - item.IssueQuantity;
                db.SaveChanges();

                if (item.Status == PmsCommon.PMSStatus.Approved)
                {
                    var res = db.IssueProductTables.FirstOrDefault(x => x.IssueId == item.IssueId);
                    res.Status = item.Status;
                    res.ActionBy = GlobalData.loggedInUser;
                    res.ActionDate = DateTime.Now;
                    res.Quantity = item.IssueQuantity;
                    db.SaveChanges();

                    var itemstock = db.StockMasters.FirstOrDefault(x => x.StockId == issueRec.StockId);
                    var itemstockProduct = db.StockProductTables.FirstOrDefault(x => x.StockProductId == issueRec.StockProductId);

                    StockProductTable existingstockProduct = (from a in db.StockProductTables
                                                              join stm in db.StockMasters on a.StockId equals stm.StockId
                                                              join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                                                              where a.ProductId == itemstockProduct.ProductId && a.Barcode == itemstockProduct.Barcode
                                                              && stm.Batch == itemstock.Batch && b.RackId == item.RackId && b.InspectionType == PmsCommon.PMSStatus.Accepted
                                                              select a).FirstOrDefault();
                    StockMaster sm = new StockMaster();
                    StockProductTable spt = new StockProductTable();

                    if (existingstockProduct == null)
                    {
                        //Adding stock to Tostore
                        sm = new StockMaster();
                        sm.InvoiceId = itemstock.InvoiceId;
                        sm.StockDate = System.DateTime.Now;
                        sm.Batch = itemstock.Batch;
                        sm.InspectionCompleted = true;
                        sm.AllocationCompleted = true;
                        sm.IsQualityInspectionCompleted = true;
                        sm.QualityInspectionCompletedBy = itemstock.QualityInspectionCompletedBy;
                        sm.QualityInspectionCompletedDate = itemstock.QualityInspectionCompletedDate;
                        sm.ManageRejectedItemsCompleted = true;
                        sm.InspectionCompletedBy = itemstock.InspectionCompletedBy;
                        sm.InspectionCompletedDate = itemstock.InspectionCompletedDate;
                        sm.ProductQuality = itemstock.ProductQuality;
                        sm.AddedBy = itemstock.AddedBy;
                        sm.AddedDate = itemstock.AddedDate;
                        db.StockMasters.Add(sm);
                        db.SaveChanges();




                        spt = new StockProductTable();
                        spt.StockId = sm.StockId;
                        spt.ProductId = itemstockProduct.ProductId;
                        spt.Sku = itemstockProduct.Sku;
                        spt.Barcode = itemstockProduct.Barcode;
                        spt.Quantity = item.IssueQuantity;
                        spt.ManufacturedDate = itemstockProduct.ManufacturedDate;
                        spt.ExpiryDate = itemstockProduct.ExpiryDate;
                        spt.Unit = itemstockProduct.Unit;
                        spt.PricePerUnit = itemstockProduct.PricePerUnit;
                        spt.ShippingHandlingPerUnit = itemstockProduct.ShippingHandlingPerUnit;
                        spt.FreightPerUnit = itemstockProduct.FreightPerUnit;
                        spt.MiscPerUnit = itemstockProduct.MiscPerUnit;
                        spt.InvoicePricePerUnit = itemstockProduct.InvoicePricePerUnit;
                        spt.Grade = itemstockProduct.Grade;
                        spt.AcceptedQuantity = item.IssueQuantity;
                        db.StockProductTables.Add(spt);
                        db.SaveChanges();
                        var spa = new StockProductAllocationTable()
                        {
                            StockProductId = spt.StockProductId,
                            Quantity = item.IssueQuantity,
                            InspectionType = PmsCommon.PMSStatus.Accepted,
                            RackId = item.RackId
                        };
                        db.StockProductAllocationTables.Add(spa);
                        db.SaveChanges();
                    }
                    else
                    {
                        spt = existingstockProduct;
                        spt.Quantity = spt.Quantity + item.IssueQuantity;
                        // spt.AcceptedQuantity = spt.AcceptedQuantity + item.IssueQuantity;
                        sm = db.StockMasters.FirstOrDefault(x => x.StockId == spt.StockId);
                        var rackdata = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == spt.StockProductId && x.RackId == item.RackId && x.InspectionType == PmsCommon.PMSStatus.Accepted);
                        rackdata.Quantity = rackdata.Quantity + item.IssueQuantity;
                        db.SaveChanges();
                    }


                    var resrec = db.IssueProductTables.FirstOrDefault(x => x.IssueId == item.IssueId);
                    resrec.ToRack = item.RackId;
                    resrec.ToNewStockId = sm.StockId;
                    resrec.ToNewStockProductId = spt.StockProductId;
                    db.SaveChanges();

                }
                transaction.Commit();

                var emaillist = db.EmailGroupTables.Where(x => x.EmailGroupName == PMSEmailGroups.LowStockReportsGroup && x.Enabled == "true").Select(x => x.EmailId).ToArray();
                if (emaillist.Length > 0)
                {
                    ReportDataFn rfunc = new ReportDataFn(GlobalData);
                    _ = rfunc.LowProductQuantityNotification(issueRec.ProductId);
                }
                var config = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "EnableWhatsAppNotification" && x.ConfigValue == "true");
                if (config != null && _notificationService != null)
                {
                    _ = _notificationService.SendLowStockNotification(issueRec.ProductId);
                }

                var saleorderissueid = db.IssueProductTables.FirstOrDefault(x => x.IssueId == item.IssueId).SaleOrderId;
                var pendingrecs = db.IssueProductTables.Where(x => x.SaleOrderId == saleorderissueid && x.Status == PmsCommon.PMSStatus.Pending).Count();
                if (pendingrecs == 0)
                {
                    var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderissueid);
                    if (so != null)
                    {
                        so.Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialIssued;
                        db.SaveChanges();
                        var enableSOStatusEmail = db.ConfigTables.Where(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") || (x.ConfigItem == "EnableSaleOrderStatusEmailOnInProduction" && x.ConfigValue == "true")).ToList();
                        if (enableSOStatusEmail.Count == 2)
                        {
                            var emailSaleOrderStatus = SaleOrderEmailStatus.StartingProduction;
                            _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(so.SaleOrderId, emailSaleOrderStatus);
                        }
                    }
                    if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.RawMaterialIssued))
                    {
                        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                        {
                            SaleOrderId = so.SaleOrderId,
                            Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialIssued,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                            WorkPlanId = db.WorkPlanOrders.FirstOrDefault(x => x.OrderId == so.SaleOrderId).WorkplanId
                        });
                        db.SaveChanges();
                    }
                }
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Issue request approved successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "An error has occured. Please contact administrator. " + ex);
            }

        }
        private ApiFunctionResponseVm ActionBarcodeLabelIssueRequest(IssueProductActionVm item)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var issueRec = db.IssueProductTables.FirstOrDefault(x => x.IssueId == item.IssueId);
                        if (issueRec == null)
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Issue request not found");
                        }

                        var stockLabels = db.StockLabelTables
                            .Where(sl => item.StockLabelIds.Contains(sl.StockLabelId))
                            .ToList();

                        if (stockLabels.Any(sl => sl.InspectionStatus != PmsCommon.PMSStatus.Accepted))
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Some or all stock labels are not accepted. First get those stock labels accepted.");
                        }

                        var stockProductGroups = stockLabels.GroupBy(sl => sl.StockProductId).ToList();

                        decimal totalSelectedQuantity = stockLabels.Sum(sl => sl.Quantity ?? 0);
                        //IssueQuntity can be less than selected quantity.
                        DateTime actionDate = DateTime.Now;
                        foreach (var group in stockProductGroups)
                        {
                            var stockProductId = group.Key;
                            var labelsForThisStock = group.ToList();
                            var quantityForThisStock = labelsForThisStock.Sum(sl => sl.Quantity ?? 0);
                            var stockLabelIdsForThisStock = labelsForThisStock.Select(sl => sl.StockLabelId).ToList();



                            IssueProductTable requestToProcess;
                            if (stockProductId != item.StockProductId)
                            {
                                // Create new issue request
                                requestToProcess = CreateNewIssueRequest(issueRec, stockProductId, quantityForThisStock, labelsForThisStock, actionDate, null);
                                db.IssueProductTables.Add(requestToProcess);
                                db.SaveChanges();
                            }
                            else
                            {
                                // Update existing issue request
                                requestToProcess = issueRec;
                                UpdateExistingIssueRequest(requestToProcess, quantityForThisStock, issueRec.DemandQuantity.Value, labelsForThisStock, actionDate, null);
                            }

                            // Process the request
                            var (success, errorMessage) = ProcessIssueRequest(db, requestToProcess, labelsForThisStock, null, item);
                            if (!success)
                            {
                                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, errorMessage);
                            }
                        }

                        db.SaveChanges();

                        // Update sale order status
                        UpdateSaleOrderStatus(db, issueRec.SaleOrderId);

                        transaction.Commit();

                        var config = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "EnableWhatsAppNotification" && x.ConfigValue == "true");
                        if (config != null && _notificationService != null)
                        {
                            _ = _notificationService.SendLowStockNotification(issueRec.ProductId);
                        }
                        // Send low stock notification
                        SendLowStockNotification(db, issueRec.ProductId);

                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Issue request processed successfully via barcode.");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        // Log the exception
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error occurred while processing the barcode label issue request. " + ex);
                    }
                }
            }
        }

        private IssueProductTable CreateNewIssueRequest(IssueProductTable originalRequest, long stockProductId, decimal quantity, List<StockLabelTable> labels, DateTime actionDate, List<SelectedBatchVm> batches)
        {
            return new IssueProductTable
            {
                FromStore = originalRequest.FromStore,
                ToStore = originalRequest.ToStore,
                ProductId = originalRequest.ProductId,
                DemandQuantity = quantity,
                Quantity = quantity,
                FromRack = labels != null ? labels.First().CurrentRackId : batches.First().RackId,
                ToRack = originalRequest.ToRack,
                StockId = labels != null ? labels.First().StockId : batches.First().StockId,
                StockProductId = stockProductId,
                Status = PmsCommon.PMSStatus.Approved,
                CreatedBy = originalRequest.CreatedBy,
                CreatedDate = originalRequest.CreatedDate,
                ActionBy = GlobalData.loggedInUser,
                ActionDate = actionDate,
                SaleOrderId = originalRequest.SaleOrderId,
                RequestType = originalRequest.RequestType,
                ParentIssueId = originalRequest.IssueId,
                ApprovalMode = labels != null ? "Barcode" : "Manual - Multi Batch",
                Remark = originalRequest.Remark,
                IssueSlipId = originalRequest.IssueSlipId,
            };
        }

        private void UpdateExistingIssueRequest(IssueProductTable issueRec, decimal quantity, decimal demandQty, List<StockLabelTable> labels, DateTime actionDate, List<SelectedBatchVm> batches)
        {
            issueRec.FromRack = labels != null ? labels.First().CurrentRackId : batches.First().RackId;
            issueRec.StockId = labels != null ? labels.First().StockId : batches.First().StockId;
            issueRec.StockProductId = labels != null ? labels.First().StockProductId : batches.First().StockProductId;
            issueRec.Quantity = quantity;
            issueRec.DemandQuantity = demandQty;
            issueRec.Status = PmsCommon.PMSStatus.Approved;
            issueRec.ActionBy = GlobalData.loggedInUser;
            issueRec.ActionDate = actionDate;
            issueRec.ApprovalMode = labels != null ? "Barcode" : "Manual - Multi Batch";
        }

        private (bool, string) ProcessIssueRequest(pmsdbContext db, IssueProductTable issueRec, List<StockLabelTable> stockLabels, List<SelectedBatchVm> batches, IssueProductActionVm item)
        {
            StockMaster sm = null;
            StockProductTable spt = null;
            decimal totalIssuedQuantity = stockLabels != null ?
                stockLabels.Sum(l => l.Quantity ?? 0) :
                batches.Sum(b => b.Quantity);

            if (stockLabels != null)
            {
                // Update labels and record movements
                foreach (var label in stockLabels)
                {
                    // Update label location
                    label.CurrentStoreId = issueRec.ToStore;
                    label.CurrentRackId = issueRec.ToRack;

                    // Record label movement
                    StockDataFn stockDataFn = new(GlobalData);
                    stockDataFn.RecordLabelMovement(db, label.StockLabelId, issueRec.FromStore, issueRec.FromRack.Value,
                                            issueRec.ToStore, issueRec.ToRack.Value, "Issue Request Approved", issueRec.IssueId);

                    stockDataFn.UpdateStockLabelStatus(db, label.StockLabelId, StockLabelStatus.Transferred, "Transferred to new location.", issueRec.IssueId, "IssueProductTable");

                    // Update stock allocation for the source
                    var sourceAllocation = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == label.StockProductId && x.RackId == issueRec.FromRack);
                    if (sourceAllocation != null)
                    {
                        sourceAllocation.Quantity -= label.Quantity ?? 0;
                    }

                    // Update source stock product quantity
                    var sourceStockProduct = db.StockProductTables.FirstOrDefault(x => x.StockProductId == label.StockProductId);
                    if (sourceStockProduct != null)
                    {
                        sourceStockProduct.Quantity -= label.Quantity ?? 0;
                    }
                }
            }
            else // Batch processing
            {
                // Group by StockProductId and RackId to handle multiple batches correctly
                var batchGroups = batches.GroupBy(b => new { b.StockProductId, b.RackId });

                foreach (var group in batchGroups)
                {
                    var totalBatchQuantity = group.Sum(b => b.Quantity);

                    // Get and lock the source allocation
                    var sourceAllocation = db.StockProductAllocationTables
                        .FirstOrDefault(x => x.StockProductId == group.Key.StockProductId
                                           && x.RackId == group.Key.RackId);

                    if (sourceAllocation != null)
                    {
                        // Validate available quantity
                        if (sourceAllocation.Quantity < totalBatchQuantity)
                        {
                            return (false, $"Insufficient quantity in stock. Available: {sourceAllocation.Quantity}, Required: {totalBatchQuantity}");
                        }

                        // Update source allocation
                        sourceAllocation.Quantity -= totalBatchQuantity;
                    }
                    // Update source stock product
                    var sourceStockProduct = db.StockProductTables
                        .FirstOrDefault(x => x.StockProductId == group.Key.StockProductId);

                    if (sourceStockProduct != null)
                    {
                        // Validate stock product quantity
                        if (sourceStockProduct.Quantity < totalBatchQuantity)
                        {
                            return (false, $"Insufficient quantity in stock product. Available: {sourceStockProduct.Quantity}, Required: {totalBatchQuantity}");
                        }

                        sourceStockProduct.Quantity -= totalBatchQuantity;
                    }
                }
            }

            // Get the source stock master
            var sourceStockMaster = db.StockMasters.FirstOrDefault(x => x.StockId == issueRec.StockId);
            var itemstockProduct = db.StockProductTables.FirstOrDefault(x => x.StockProductId == issueRec.StockProductId);
            // Check if stock already exists in the destination
            StockProductTable existingStockProduct = (from a in db.StockProductTables
                                                      join stm in db.StockMasters on a.StockId equals stm.StockId
                                                      join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                                                      where a.ProductId == itemstockProduct.ProductId && stm.Batch == sourceStockMaster.Batch
                                                      && b.RackId == item.RackId && b.InspectionType == PmsCommon.PMSStatus.Accepted
                                                      select a).FirstOrDefault();
            // var existingStockProduct = db.StockProductTables
            //     .FirstOrDefault(x => x.ProductId == issueRec.ProductId &&
            //                          db.StockProductAllocationTables.Any(spa => spa.StockProductId == x.StockProductId &&
            //                                                                     spa.RackId == issueRec.ToRack &&
            //                                                                     spa.InspectionType == PmsCommon.PMSStatus.Accepted));

            if (existingStockProduct == null)
            {
                // Create new stock entries
                sm = new StockMaster
                {
                    InvoiceId = sourceStockMaster.InvoiceId,
                    StockDate = DateTime.Now,
                    Batch = sourceStockMaster.Batch,
                    InspectionCompleted = true,
                    AllocationCompleted = true,
                    IsQualityInspectionCompleted = sourceStockMaster.IsQualityInspectionCompleted,
                    QualityInspectionCompletedBy = sourceStockMaster.QualityInspectionCompletedBy,
                    QualityInspectionCompletedDate = sourceStockMaster.QualityInspectionCompletedDate,
                    ManageRejectedItemsCompleted = sourceStockMaster.ManageRejectedItemsCompleted,
                    InspectionCompletedBy = sourceStockMaster.InspectionCompletedBy,
                    InspectionCompletedDate = sourceStockMaster.InspectionCompletedDate,
                    ProductQuality = sourceStockMaster.ProductQuality,
                    AddedBy = sourceStockMaster.AddedBy,
                    AddedDate = sourceStockMaster.AddedDate
                };
                db.StockMasters.Add(sm);
                db.SaveChanges();

                spt = new StockProductTable
                {
                    StockId = sm.StockId,
                    ProductId = issueRec.ProductId,
                    Sku = itemstockProduct.Sku,
                    Barcode = itemstockProduct.Barcode,
                    Quantity = totalIssuedQuantity,
                    ManufacturedDate = itemstockProduct.ManufacturedDate,
                    ExpiryDate = itemstockProduct.ExpiryDate,
                    Unit = itemstockProduct.Unit,
                    PricePerUnit = itemstockProduct.PricePerUnit,
                    ShippingHandlingPerUnit = itemstockProduct.ShippingHandlingPerUnit,
                    FreightPerUnit = itemstockProduct.FreightPerUnit,
                    MiscPerUnit = itemstockProduct.MiscPerUnit,
                    InvoicePricePerUnit = itemstockProduct.InvoicePricePerUnit,
                    Grade = itemstockProduct.Grade,
                    AcceptedQuantity = totalIssuedQuantity
                };
                db.StockProductTables.Add(spt);
                db.SaveChanges();

                var spa = new StockProductAllocationTable
                {
                    StockProductId = spt.StockProductId,
                    Quantity = totalIssuedQuantity,
                    InspectionType = PmsCommon.PMSStatus.Accepted,
                    RackId = issueRec.ToRack.Value
                };
                db.StockProductAllocationTables.Add(spa);
                db.SaveChanges();
            }
            else
            {
                // Update existing stock
                existingStockProduct.Quantity += totalIssuedQuantity;
                // existingStockProduct.AcceptedQuantity += totalIssuedQuantity;
                var rackAllocation = db.StockProductAllocationTables
                    .FirstOrDefault(x => x.StockProductId == existingStockProduct.StockProductId &&
                                         x.RackId == issueRec.ToRack &&
                                         x.InspectionType == PmsCommon.PMSStatus.Accepted);
                if (rackAllocation != null)
                {
                    rackAllocation.Quantity += totalIssuedQuantity;
                }
                sm = db.StockMasters.FirstOrDefault(x => x.StockId == existingStockProduct.StockId);
                spt = existingStockProduct;
            }

            // Update AllocationId for all labels
            var newAllocation = db.StockProductAllocationTables
                .FirstOrDefault(x => x.StockProductId == spt.StockProductId && x.RackId == issueRec.ToRack);
            if (stockLabels != null)
            {
                foreach (var label in stockLabels)
                {
                    label.AllocationId = newAllocation?.AllocationId;
                    label.StockProductId = spt.StockProductId != label.StockProductId ? spt.StockProductId : label.StockProductId;
                    label.StockId = sm.StockId != label.StockId ? sm.StockId : label.StockId;
                }
            }

            // Update issue record
            issueRec.ToNewStockId = sm?.StockId;
            issueRec.ToNewStockProductId = spt?.StockProductId;
            db.SaveChanges();
            return (true, "");
        }

        private void SendLowStockNotification(pmsdbContext db, long productId)
        {
            var emaillist = db.EmailGroupTables
                .Where(x => x.EmailGroupName == PMSEmailGroups.LowStockReportsGroup && x.Enabled == "true")
                .Select(x => x.EmailId)
                .ToArray();

            if (emaillist.Length > 0)
            {
                ReportDataFn rfunc = new ReportDataFn(GlobalData);
                _ = rfunc.LowProductQuantityNotification(productId);
            }
        }

        private void UpdateSaleOrderStatus(pmsdbContext db, long? saleOrderId)
        {
            if (saleOrderId.HasValue)
            {
                var pendingrecs = db.IssueProductTables
                    .Count(x => x.SaleOrderId == saleOrderId && x.Status == PmsCommon.PMSStatus.Pending);

                if (pendingrecs == 0)
                {
                    var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleOrderId);
                    if (so != null)
                    {
                        so.Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialIssued;
                        db.SaveChanges();

                        var enableSOStatusEmail = db.ConfigTables
                            .Count(x => (x.ConfigItem == "EnableSaleOrderStatusEmail" && x.ConfigValue == "true") ||
                                        (x.ConfigItem == "EnableSaleOrderStatusEmailOnInProduction" && x.ConfigValue == "true")) == 2;

                        if (enableSOStatusEmail)
                        {
                            var emailSaleOrderStatus = SaleOrderEmailStatus.StartingProduction;
                            _ = new ReportDataFn(GlobalData).SendSaleOrderStatusUpdate(so.SaleOrderId, emailSaleOrderStatus);
                        }
                    }

                    if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == saleOrderId && x.Status == (int)PmsCommon.ESalesOrderStatus.RawMaterialIssued))
                    {
                        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                        {
                            SaleOrderId = saleOrderId.Value,
                            Status = (int)PmsCommon.ESalesOrderStatus.RawMaterialIssued,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now
                        });
                        db.SaveChanges();
                    }
                }
            }
        }
        public ApiFunctionResponseVm ActionMultiBatchIssueRequest(IssueProductActionVm item)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var issueRec = db.IssueProductTables.FirstOrDefault(x => x.IssueId == item.IssueId && x.Status == PmsCommon.PMSStatus.Pending);
                        if (issueRec == null)
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Issue request not found or it is already processed.");
                        }

                        // Group by StockProductId from SelectedBatches instead of StockLabelIds
                        var stockProductGroups = item.SelectedBatches
                            .GroupBy(sb => sb.StockProductId)
                            .ToList();
                        item.StockProductId = item.SelectedBatches.First().StockProductId;
                        decimal totalSelectedQuantity = item.SelectedBatches.Sum(sb => sb.Quantity);
                        DateTime actionDate = DateTime.Now;

                        foreach (var group in stockProductGroups)
                        {
                            var stockProductId = group.Key;
                            var quantityForThisStock = group.Sum(sb => sb.Quantity);
                            var batchesForThisStock = group.Where(sb => sb.StockProductId == stockProductId).ToList();



                            IssueProductTable requestToProcess;
                            if (stockProductId != item.StockProductId)
                            {
                                // Create new issue request
                                requestToProcess = CreateNewIssueRequest(issueRec, stockProductId, quantityForThisStock, null, actionDate, batchesForThisStock);
                                db.IssueProductTables.Add(requestToProcess);
                                db.SaveChanges();
                            }
                            else
                            {
                                // Update existing issue request
                                requestToProcess = issueRec;
                                UpdateExistingIssueRequest(requestToProcess, quantityForThisStock, issueRec.DemandQuantity.Value, null, actionDate, batchesForThisStock);
                            }

                            // Process the request
                            ProcessIssueRequest(db, requestToProcess, null, batchesForThisStock, item);
                        }

                        db.SaveChanges();

                        // Update sale order status
                        UpdateSaleOrderStatus(db, issueRec.SaleOrderId);

                        transaction.Commit();

                        var config = db.ConfigTables.FirstOrDefault(x => x.ConfigItem == "EnableWhatsAppNotification" && x.ConfigValue == "true");
                        if (config != null && _notificationService != null)
                        {
                            _ = _notificationService.SendLowStockNotification(issueRec.ProductId);
                        }

                        // Send low stock notification
                        SendLowStockNotification(db, issueRec.ProductId);

                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Issue request processed successfully via multiple batches.");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error occurred while processing the multi-batch issue request. " + ex);
                    }
                }
            }
        }
        public List<StockLabelMovementHistoryTableVm> GetLabelsForIssueRequest(long issueId)
        {
            using (var db = new Models.pmsdbContext())
            {
                // First, get the issue request and its potential child requests
                var issueRequests = db.IssueProductTables
                    .Where(i => i.IssueId == issueId || i.ParentIssueId == issueId)
                    .Select(i => i.IssueId)
                    .ToList();

                // Then, fetch the labels for all these issue requests
                return db.StockLabelMovementHistoryTables
                    .Where(m => issueRequests.Contains(m.IssueId))
                    .Select(m => new StockLabelMovementHistoryTableVm
                    {
                        StockLabelId = m.StockLabelId,
                        IssueId = m.IssueId
                    })
                    .Distinct()
                    .ToList();
            }
        }
        public ApiFunctionResponseVm AddIssueSlipRequest(List<IssueSlipRequestVm> model)
        {
            using var db = new Models.pmsdbContext();

            try
            {
                var date = DateTime.Today;
                var todaySlipCount = db.IssueSlipTables.Where(x => x.RequestedDate.Date == date).Count();

                string datePart = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("yyyy/MM/dd");
                var SlipSerialNo = $"ZIP-{datePart}/{todaySlipCount++}";

                IssueSlipTable ist = new()
                {
                    IssueSlipNumber = SlipSerialNo,
                    Requestedby = GlobalData.loggedInUser,
                    RequestedDate = DateTime.Now
                };
                db.IssueSlipTables.Add(ist);
                db.SaveChanges();

                var issueIds = model.Select(x => x.IssueId).ToList();

                // Then get child issue IDs using parent IDs
                var childIssueIds = (from i in db.IssueProductTables
                                     where i.ParentIssueId.HasValue
                                     && issueIds.Contains(i.ParentIssueId.Value)
                                     select i.IssueId);

                // Combine all issue IDs
                var allIssueIds = issueIds.Union(childIssueIds).ToList();

                var issueRecords = db.IssueProductTables
                                     .Where(x => allIssueIds.Contains(x.IssueId))
                                     .ToList();

                foreach (var item in allIssueIds)
                {
                    var issueRecord = issueRecords.FirstOrDefault(x => x.IssueId == item);
                    if (issueRecord != null)
                    {
                        issueRecord.IssueSlipId = ist.IssueSlipId;
                    }
                }
                db.SaveChanges();

                if (ist.IssueSlipId > 0)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, ist.IssueSlipId);
                }
                else
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "Issue Slip Generation Failed.");
                }
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public List<FormulationCodeMixingRawMaterialTableVm> GetStockForSaleOrderWithConsumptionstoreId(long saleorderId, long consumptionStoreId)
        {
            List<FormulationCodeMixingRawMaterialTableVm> InsStock = GetStockForSaleOrder(saleorderId);
            List<long> productList = new List<long>();
            foreach (var item in InsStock)
            {
                var itm = item.ProductStock
                            .Where(x => x.StoreId == consumptionStoreId)
                            .GroupBy(x => x.StoreId)
                            .Where(group => group.Sum(x => x.Quantity) >= item.Quantity)
                            .SelectMany(group => group)
                            .FirstOrDefault();


                if (itm == null)
                {
                    var notexistrec = false;
                    using (var db = new Models.pmsdbContext())
                    {
                        notexistrec = !db.IssueProductTables.Any(x => x.SaleOrderId == saleorderId
                           && x.ToStore == consumptionStoreId
                           && x.ProductId == item.ProductId
                           && x.Quantity >= item.Quantity
                           && x.Status != PmsCommon.PMSStatus.Rejected
                           );
                    }
                    if (notexistrec)
                        productList.Add(item.ProductId.Value);
                }
            }
            InsStock = InsStock.Where(x => productList.Contains(x.ProductId.Value)).ToList();
            return InsStock;
        }

        public List<FormulationCodeMixingRawMaterialTableVm> GetStockForSaleOrder(long saleorderId)
        {
            List<FormulationCodeMixingRawMaterialTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                res = (from op in db.InspectionFormulationCodeMixingRawMaterialTables
                       join s in db.InspectionFormulationCodeMixingTables on op.FormulationCodeMixingId equals s.FormulationCodeMixingId
                       join mix in db.MixingMasters on s.MixingId equals mix.MixingId
                       join so in db.SaleOrderTables on s.SaleOrderId equals so.SaleOrderId
                       join p in db.ProductMasters on op.ProductId equals p.ProductId
                       where s.SaleOrderId == saleorderId
                       select new FormulationCodeMixingRawMaterialTableVm
                       {
                           ProductId = op.ProductId,
                           ProductName = p.ProductName,
                           ProductCode = p.ProductCode,
                           Quantity = op.Quantity,
                           Scquantity = op.Scquantity,
                           AvgGsm = p.AvgGsm,
                           Unit = op.Unit,
                           Price = op.Price,
                           MixingName = mix.MixingName
                       }).ToList();
                var inscode = db.InspectionFormulationCodeMixingTables.Where(s => s.SaleOrderId == saleorderId).FirstOrDefault().InspectionSaleFormulationCodeId;
                var SaleFormulationCode = db.InspectionSaleFormulationCodeMasters.Where(x => x.InspectionSaleFormulationCodeId == inscode).FirstOrDefault();
                if (SaleFormulationCode != null)
                {
                    var FabPro = db.ProductMasters.Where(x => x.ProductId == SaleFormulationCode.FabricProductId).FirstOrDefault();
                    if (FabPro != null)
                    {
                        var it = new FormulationCodeMixingRawMaterialTableVm
                        {
                            ProductId = FabPro.ProductId,
                            ProductName = FabPro.ProductName,
                            ProductCode = FabPro.ProductCode,
                            Quantity = SaleFormulationCode.FabricProductQty,
                            Unit = FabPro.Unit,
                            AvgGsm = FabPro.AvgGsm,
                            Price = 0,
                            MixingName = "Fabric"
                        };
                        res.Add(it);
                    }
                }
                var laq = (from lr in db.SaleOrderProductionLacquerRawMaterialTables
                           join sop in db.SaleOrderProductionTables on lr.SaleOrderProductionId equals sop.SaleOrderProductionId
                           join so in db.SaleOrderTables on sop.SaleOrderId equals so.SaleOrderId
                           join p in db.ProductMasters on lr.ProductId equals p.ProductId
                           where so.SaleOrderId == saleorderId && lr.Removed != true
                           select new FormulationCodeMixingRawMaterialTableVm
                           {
                               ProductId = lr.ProductId,
                               ProductName = p.ProductName,
                               ProductCode = p.ProductCode,
                               Quantity = lr.Quantity,
                               AvgGsm = p.AvgGsm,
                               Unit = lr.Unit,
                               Price = lr.Price,
                               MixingName = "Lacquer"
                           }).Distinct().ToList();
                if (laq != null && laq.Count > 0)
                    res.AddRange(laq);

                foreach (var item in res)
                {
                    item.ProductStock = (from a in db.StockProductTables
                                         join stm in db.StockMasters on a.StockId equals stm.StockId into fcms
                                         from stm in fcms.DefaultIfEmpty()
                                         join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId into fcmsin
                                         from inv in fcmsin.DefaultIfEmpty()
                                         join sup in db.SupplierMasters on inv.SupplierId equals sup.SupplierId into fcmsup
                                         from sup in fcmsup.DefaultIfEmpty()
                                         join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                                         join p in db.ProductMasters on a.ProductId equals p.ProductId
                                         join r in db.RackMasters on b.RackId equals r.RackId
                                         join s in db.StoreMasters on r.StoreId equals s.StoreId
                                         where a.ProductId == item.ProductId && b.InspectionType.ToLower() == "accepted" && b.Quantity > 0
                                         select new IssueSaleOrderProductsStockVm
                                         {
                                             ProductId = a.ProductId,
                                             ProductName = p.ProductName,
                                             ProductCode = p.ProductCode,
                                             ProductType = p.ProductType,
                                             SKU = a.Sku,
                                             BarCode = a.Barcode,
                                             Unit = a.Unit,
                                             PerUnitPrice = a.PricePerUnit,
                                             Batch = stm.Batch,
                                             ManufacturedDate = a.ManufacturedDate,
                                             ExpiryDate = a.ExpiryDate,
                                             StockId = a.StockId,
                                             StockProductId = a.StockProductId,
                                             SupplierId = inv.SupplierId,
                                             SupplierName = sup.SupplierName,
                                             InvoiceId = stm.InvoiceId,
                                             InvoiceNumber = inv.InvoiceNumber,
                                             StoreId = r.StoreId,
                                             StoreName = s.StoreName,
                                             StoreCode = s.StoreCode,
                                             RackId = b.RackId,
                                             RackName = r.RackName,
                                             RackCode = r.RackCode,
                                             Quantity = b.Quantity,
                                             StockDate = stm.StockDate,
                                         }).ToList();

                }
            }
            return res;
        }

        public List<FormulationCodeMixingRawMaterialTableVm> GetOldStockForSaleOrder(long saleorderId)
        {
            List<FormulationCodeMixingRawMaterialTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                res = (from op in db.FormulationCodeMixingRawMaterialTables
                       join s in db.FormulationCodeMixingTables on op.FormulationCodeMixingId equals s.FormulationCodeMixingId
                       join so in db.SaleOrderTables on s.SaleFormulationCodeId equals so.SaleFormulationCodeId
                       join p in db.ProductMasters on op.ProductId equals p.ProductId
                       where so.SaleOrderId == saleorderId
                       select new FormulationCodeMixingRawMaterialTableVm
                       {
                           ProductId = op.ProductId,
                           ProductName = p.ProductName,
                           ProductCode = p.ProductCode,
                           Quantity = op.Quantity,
                           Scquantity = op.Scquantity,
                           AvgGsm = p.AvgGsm,
                           Unit = op.Unit,
                           Price = op.Price
                       }).ToList();
                foreach (var item in res)
                {
                    item.ProductStock = (from a in db.StockProductTables
                                         join stm in db.StockMasters on a.StockId equals stm.StockId into fcms
                                         from stm in fcms.DefaultIfEmpty()
                                         join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId into fcmsin
                                         from inv in fcmsin.DefaultIfEmpty()
                                         join sup in db.SupplierMasters on inv.SupplierId equals sup.SupplierId into fcmsup
                                         from sup in fcmsup.DefaultIfEmpty()
                                         join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                                         join p in db.ProductMasters on a.ProductId equals p.ProductId
                                         join r in db.RackMasters on b.RackId equals r.RackId
                                         join s in db.StoreMasters on r.StoreId equals s.StoreId
                                         where a.ProductId == item.ProductId && b.InspectionType.ToLower() == "accepted" && b.Quantity > 0
                                         select new IssueSaleOrderProductsStockVm
                                         {
                                             ProductId = a.ProductId,
                                             ProductName = p.ProductName,
                                             ProductCode = p.ProductCode,
                                             ProductType = p.ProductType,
                                             SKU = a.Sku,
                                             BarCode = a.Barcode,
                                             Unit = a.Unit,
                                             PerUnitPrice = a.PricePerUnit,
                                             Batch = stm.Batch,
                                             ManufacturedDate = a.ManufacturedDate,
                                             ExpiryDate = a.ExpiryDate,
                                             StockId = a.StockId,
                                             StockProductId = a.StockProductId,
                                             SupplierId = inv.SupplierId,
                                             SupplierName = sup.SupplierName,
                                             InvoiceId = stm.InvoiceId,
                                             InvoiceNumber = inv.InvoiceNumber,
                                             StoreId = r.StoreId,
                                             StoreName = s.StoreName,
                                             StoreCode = s.StoreCode,
                                             RackId = b.RackId,
                                             RackName = r.RackName,
                                             RackCode = r.RackCode,
                                             Quantity = b.Quantity,
                                             StockDate = stm.StockDate
                                         }).ToList();

                }
            }
            return res;
        }
        public List<IssueSaleOrderProductsStockVm> GetStockForProduct(long productId)
        {
            List<IssueSaleOrderProductsStockVm> res = null;
            using (var db = new Models.pmsdbContext())
            {

                res = (from a in db.StockProductTables
                       join stm in db.StockMasters on a.StockId equals stm.StockId into fcms
                       from stm in fcms.DefaultIfEmpty()
                       join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId into fcmsin
                       from inv in fcmsin.DefaultIfEmpty()
                       join sup in db.SupplierMasters on inv.SupplierId equals sup.SupplierId into fcmsup
                       from sup in fcmsup.DefaultIfEmpty()
                       join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                       join p in db.ProductMasters on a.ProductId equals p.ProductId
                       join r in db.RackMasters on b.RackId equals r.RackId
                       join s in db.StoreMasters on r.StoreId equals s.StoreId
                       where a.ProductId == productId && b.InspectionType.ToLower() == "accepted" && b.Quantity > 0
                       select new IssueSaleOrderProductsStockVm
                       {
                           ProductId = a.ProductId,
                           ProductName = p.ProductName,
                           ProductCode = p.ProductCode,
                           ProductType = p.ProductType,
                           SKU = a.Sku,
                           BarCode = a.Barcode,
                           Unit = a.Unit,
                           PerUnitPrice = a.PricePerUnit,
                           ManufacturedDate = a.ManufacturedDate,
                           ExpiryDate = a.ExpiryDate,
                           StockId = a.StockId,
                           Batch = stm.Batch,
                           StockProductId = a.StockProductId,
                           SupplierId = inv.SupplierId,
                           SupplierName = sup.SupplierName,
                           InvoiceId = stm.InvoiceId,
                           InvoiceNumber = inv.InvoiceNumber,
                           StoreId = r.StoreId,
                           StoreName = s.StoreName,
                           StoreCode = s.StoreCode,
                           RackId = b.RackId,
                           RackName = r.RackName,
                           RackCode = r.RackCode,
                           Quantity = b.Quantity,
                           StockDate = stm.StockDate,
                           ProductQuality = stm.ProductQuality
                       }).OrderByDescending(x => x.Quantity).ToList();
            }
            return res;
        }


        public List<IssueProductsSlipIdVm> GetIssueProductBySlipId(long IssueSlipId)
        {
            using (var db = new Models.pmsdbContext())
            {
                // First get parent issue IDs for the slip
                var parentIssueIds = (from i in db.IssueProductTables
                                      where i.IssueSlipId == IssueSlipId
                                      select i.IssueId);

                // Then get child issue IDs using parent IDs
                var childIssueIds = (from i in db.IssueProductTables
                                     where i.ParentIssueId.HasValue
                                     && parentIssueIds.Contains(i.ParentIssueId.Value)
                                     select i.IssueId);

                // Combine all issue IDs
                var allIssueIds = parentIssueIds.Union(childIssueIds).ToList();

                var result = (from inv in db.IssueProductTables
                              join ist in db.IssueSlipTables on inv.IssueSlipId equals ist.IssueSlipId into istd
                              from ist in istd.DefaultIfEmpty()
                              join so in db.SaleOrderTables on inv.SaleOrderId equals so.SaleOrderId into sot
                              from so in sot.DefaultIfEmpty()
                              join p in db.ProductMasters on inv.ProductId equals p.ProductId
                              join fs in db.StoreMasters on inv.FromStore equals fs.StoreId
                              join fr in db.RackMasters on inv.FromRack equals fr.RackId into frd
                              from fr in frd.DefaultIfEmpty()
                              join ts in db.StoreMasters on inv.ToStore equals ts.StoreId
                              join tr in db.RackMasters on inv.ToRack equals tr.RackId into trd
                              from tr in trd.DefaultIfEmpty()
                              join fsd in db.DeptMasters on fs.DeptId equals fsd.DeptId
                              join um in db.UserMasters on inv.CreatedBy equals um.Email
                              join umslip in db.UserMasters on ist.Requestedby equals umslip.Email into umslipd
                              from umslip in umslipd.DefaultIfEmpty()
                              join umapp in db.UserMasters on inv.ActionBy equals umapp.Email into umappd
                              from umapp in umappd.DefaultIfEmpty()
                              where allIssueIds.Contains(inv.IssueId)
                              select new IssueProductsSlipIdVm
                              {
                                  IssueId = inv.IssueId,
                                  ParentIssueId = inv.ParentIssueId,
                                  ApprovalMode = inv.ApprovalMode,
                                  SaleOrderId = inv.SaleOrderId.ToString(),
                                  SaleOrderNumber = so.SaleOrderNumber,
                                  FromStoreName = fs.StoreName,
                                  FromRackName = fr.RackName,
                                  ToStoreName = ts.StoreName,
                                  ToRackName = tr.RackName,
                                  Quantity = inv.Quantity,
                                  DemandQuantity = inv.DemandQuantity,
                                  IssueSlipNumber = ist.IssueSlipNumber,
                                  IssueSlipId = ist.IssueSlipId,
                                  SlipGeneratedBy = umslip.Name,
                                  SlipGeneratedDate = ist.RequestedDate,
                                  CreatedBy = um.Name,
                                  CreatedDate = inv.CreatedDate,
                                  ProductId = inv.ProductId,
                                  ProductName = p.ProductName,
                                  ProductCode = p.ProductCode,
                                  Unit = p.Unit,
                                  Remark = inv.Remark,
                                  ActionBy = umapp.Name,
                                  ActionDate = inv.ActionDate,
                                  RequestedDeptName = fsd.DeptName,
                                  Status = inv.Status
                              }).ToList();

                // Handle barcode label details
                if (result.Any(r => r.ApprovalMode == "Barcode"))
                {
                    var labelMovements = db.StockLabelMovementHistoryTables
                        .Where(m => allIssueIds.Contains(m.IssueId))
                        .Select(m => new { m.StockLabelId, m.IssueId })
                        .Distinct()
                        .ToList();

                    var stockLabels = db.StockLabelTables
                        .Where(sl => labelMovements.Select(lm => lm.StockLabelId).Contains(sl.StockLabelId)
                               && sl.IsActive == true)
                        .Select(sl => new
                        {
                            sl.StockLabelId,
                            sl.SerialNo,
                            sl.Quantity,
                            sl.PackagingUnit
                        })
                        .ToList();

                    var labelDetails = labelMovements
                        .Join(stockLabels,
                            lm => lm.StockLabelId,
                            sl => sl.StockLabelId,
                            (lm, sl) => new
                            {
                                lm.IssueId,
                                sl.StockLabelId,
                                sl.SerialNo,
                                sl.Quantity,
                                sl.PackagingUnit
                            })
                        .GroupBy(x => x.IssueId)
                        .ToDictionary(
                            g => g.Key,
                            g => g.Select(x => new StockLabelTableVm
                            {
                                StockLabelId = x.StockLabelId,
                                SerialNo = x.SerialNo,
                                Quantity = x.Quantity ?? 0,
                                PackagingUnit = x.PackagingUnit
                            }).ToList()
                        );

                    // Assign label details to respective issues
                    foreach (var item in result.Where(r => r.ApprovalMode == "Barcode"))
                    {
                        if (labelDetails.TryGetValue(item.IssueId, out var labels))
                        {
                            item.LabelDetails = labels.OrderBy(x => x.SerialNo).ToList();
                        }
                    }
                }

                // Group the results
                var groupedResult = result.GroupBy(item => new
                {
                    item.ProductId,
                    item.ProductName,
                    item.ProductCode,
                    item.IssueSlipId,
                    item.FromStoreName,
                    item.FromRackName,
                    item.ToStoreName,
                    item.ToRackName,
                    item.IssueSlipNumber,
                    item.CreatedBy,
                    item.CreatedDate,
                    item.Unit,
                    item.SlipGeneratedBy,
                    item.SlipGeneratedDate,
                    item.Remark,
                    item.ActionBy,
                    item.ActionDate,
                    item.RequestedDeptName,
                    item.Status,
                    item.ApprovalMode
                })
                .Select(group => new IssueProductsSlipIdVm
                {
                    ProductId = group.Key.ProductId,
                    ProductName = group.Key.ProductName,
                    ProductCode = group.Key.ProductCode,
                    Unit = group.Key.Unit,
                    IssueSlipId = group.Key.IssueSlipId,
                    SaleOrderId = ConvertToNullIfEmpty(string.Join(", ", group.Select(x => x.SaleOrderId).Distinct())),
                    SaleOrderNumber = ConvertToNullIfEmpty(string.Join(", ", group.Select(x => x.SaleOrderNumber).Distinct())),
                    FromStoreName = group.Key.FromStoreName,
                    FromRackName = group.Key.FromRackName,
                    ToStoreName = group.Key.ToStoreName,
                    ToRackName = group.Key.ToRackName,
                    Quantity = group.Sum(x => x.Quantity),
                    DemandQuantity = group.Where(x => !x.ParentIssueId.HasValue).Sum(x => x.DemandQuantity),
                    IssueSlipNumber = group.Key.IssueSlipNumber,
                    IssueIdList = ConvertToNullIfEmpty(string.Join(", ", group.Select(x => x.IssueId.ToString()).Distinct())),
                    CreatedBy = group.Key.CreatedBy,
                    CreatedDate = group.Key.CreatedDate,
                    SlipGeneratedBy = group.Key.SlipGeneratedBy,
                    SlipGeneratedDate = group.Key.SlipGeneratedDate,
                    Remark = group.Key.Remark,
                    ActionBy = group.Key.ActionBy,
                    ActionDate = group.Key.ActionDate,
                    RequestedDeptName = group.Key.RequestedDeptName,
                    Status = group.Key.Status,
                    LabelDetails = group.Key.ApprovalMode == "Barcode" ?
                        group.SelectMany(x => x.LabelDetails ?? new List<StockLabelTableVm>()).OrderBy(x => x.SerialNo).ToList() : null
                }).ToList();

                return groupedResult;
            }
        }

        private string ConvertToNullIfEmpty(string input)
        {
            return string.IsNullOrEmpty(input) ? null : input;
        }


    }
}
