using System.Collections.Generic;
using System.Linq;
using System.Net;
using PmsEntity.ViewModel;
using PmsData.Models;
using PmsCommon;
using System;


namespace PmsData.DataFn
{
    public class CalculationsDataFn
    {
        public FormulationProductCalculatePasteReqResponse GetFormulationProductCalculatePasteReqQuantity(FormulationProductCalculatePasteReqRequest request)
        {
            FormulationProductCalculatePasteReqResponse response = new();
            decimal prefor = (decimal)request.SaleOrderProduction.LMConstant / 1000;

            var PreSkinGsmPasteReq = request.SaleOrderProduction.PreSkinGsm * request.TotalProductionQty * prefor;
            var SkinGsmPasteReq = request.SaleOrderProduction.SkinGsm * request.TotalProductionQty * prefor;
            var FoamGsmPasteReq = request.SaleOrderProduction.FoamGsm * request.TotalProductionQty * prefor;
            var AdhesiveGsmPasteReq = request.SaleOrderProduction.AdhesiveGsm * request.TotalProductionQty * prefor;

            response.PreSkinGsmPasteReq = response.PreSkinGsmSCPasteReq = PreSkinGsmPasteReq;
            response.SkinGsmPasteReq = response.SkinGsmSCPasteReq = SkinGsmPasteReq;
            response.FoamGsmPasteReq = response.FoamGsmSCPasteReq = FoamGsmPasteReq;
            response.AdhesiveGsmPasteReq = response.AdhesiveGsmSCPasteReq = AdhesiveGsmPasteReq;


            return response;
        }
        public ApiFunctionResponseVm GetGSMFromPasteQuantity(FormulationProductCalculatePasteReqRequest request)
        {
            if (request.TotalProductionQty == 0 || request.SaleOrderProduction.ManufacturingQuantity == 0)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.FailedDependency, "Add at least one jumbo details to do the calculation.");
            }
            FormulationProductCalculatePasteReqResponse response = new();
            decimal prefor = (decimal)request.SaleOrderProduction.LMConstant / 1000;

            var PreSkinGsmPasteReq = request.SaleOrderProduction.PreSkinGsm * request.TotalProductionQty * prefor;
            var SkinGsmPasteReq = request.SaleOrderProduction.SkinGsm * request.TotalProductionQty * prefor;
            var FoamGsmPasteReq = request.SaleOrderProduction.FoamGsm * request.TotalProductionQty * prefor;
            var AdhesiveGsmPasteReq = request.SaleOrderProduction.AdhesiveGsm * request.TotalProductionQty * prefor;

            var UsedPreSkinPasteQuantity = PreSkinGsmPasteReq - request.PreSkinRemainingPasteScQty;
            var UsedSkinPasteQuantity = SkinGsmPasteReq - request.SkinRemainingPasteScQty;
            var UsedFoamPasteQuantity = FoamGsmPasteReq - request.FoamRemainingPasteScQty;
            var UsedAdhesivePasteQuantity = AdhesiveGsmPasteReq - request.AdhesiveRemainingPasteScQty;

            response.PreSkinGsmPasteReq = PreSkinGsmPasteReq;
            response.SkinGsmPasteReq = SkinGsmPasteReq;
            response.FoamGsmPasteReq = FoamGsmPasteReq;
            response.AdhesiveGsmPasteReq = AdhesiveGsmPasteReq;
            if (request.SaleOrderProduction.InspectionFormulationMixing.Count != 0 && request.SaleOrderProduction.ManufacturingQuantity > 0 && request.SaleOrderProduction.LMConstant > 0)
            {
                response.PreSkinScGsm = UsedPreSkinPasteQuantity / request.SaleOrderProduction.ManufacturingQuantity / (decimal)request.SaleOrderProduction.LMConstant * 1000;
                response.SkinScGsm = UsedSkinPasteQuantity / request.SaleOrderProduction.ManufacturingQuantity / (decimal)request.SaleOrderProduction.LMConstant * 1000;
                response.FoamScGsm = UsedFoamPasteQuantity / request.SaleOrderProduction.ManufacturingQuantity / (decimal)request.SaleOrderProduction.LMConstant * 1000;
                response.AdhesiveScGsm = UsedAdhesivePasteQuantity / request.SaleOrderProduction.ManufacturingQuantity / (decimal)request.SaleOrderProduction.LMConstant * 1000;
            }
            foreach (var item in request.SaleOrderProduction.InspectionFormulationMixing)
            {
                switch (item.MixingName.Trim().ToLower())
                {
                    case "pre skin":
                        if (item.StdPasteRequirementQuantity != null && item.StdPasteRequirementQuantity > 0 && item.StdPasteRequirementScquantity != null && item.StdPasteRequirementScquantity > 0)
                        {
                            response.PreSkinGsm = CalculateCostingGSMFromUsedQuantity(item.StdPasteRequirementQuantity.Value, item.StdPasteRequirementScquantity.Value,
                            request.PreSkinRemainingPasteScQty, request.SaleOrderProduction.ManufacturingQuantity.Value, request.SaleOrderProduction.LMConstant.Value, response.PreSkinScGsm.Value);
                        }
                        break;
                    case "skin":
                        if (item.StdPasteRequirementQuantity != null && item.StdPasteRequirementQuantity > 0 && item.StdPasteRequirementScquantity != null && item.StdPasteRequirementScquantity > 0)
                        {
                            response.SkinGsm = CalculateCostingGSMFromUsedQuantity(item.StdPasteRequirementQuantity.Value, item.StdPasteRequirementScquantity.Value,
                            request.SkinRemainingPasteScQty, request.SaleOrderProduction.ManufacturingQuantity.Value, request.SaleOrderProduction.LMConstant.Value, response.SkinScGsm.Value);
                        }
                        break;
                    case "foam":
                        if (item.StdPasteRequirementQuantity != null && item.StdPasteRequirementQuantity > 0 && item.StdPasteRequirementScquantity != null && item.StdPasteRequirementScquantity > 0)
                        {
                            response.FoamGsm = CalculateCostingGSMFromUsedQuantity(item.StdPasteRequirementQuantity.Value, item.StdPasteRequirementScquantity.Value,
                            request.FoamRemainingPasteScQty, request.SaleOrderProduction.ManufacturingQuantity.Value, request.SaleOrderProduction.LMConstant.Value, response.FoamScGsm.Value);
                        }
                        break;
                    case "adhesive":
                        if (item.StdPasteRequirementQuantity != null && item.StdPasteRequirementQuantity > 0 && item.StdPasteRequirementScquantity != null && item.StdPasteRequirementScquantity > 0)
                        {
                            response.AdhesiveGsm = CalculateCostingGSMFromUsedQuantity(item.StdPasteRequirementQuantity.Value, item.StdPasteRequirementScquantity.Value,
                            request.AdhesiveRemainingPasteScQty, request.SaleOrderProduction.ManufacturingQuantity.Value, request.SaleOrderProduction.LMConstant.Value, response.AdhesiveScGsm.Value);
                        }
                        break;
                }
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, response);
        }

        public decimal CalculateCostingGSMFromUsedQuantity(decimal StdPasteRequirementQuantity, decimal StdPasteRequirementScquantity, decimal? RemainingPasteScQuantity, decimal MfgQty, decimal LMConstant, decimal ScGSM)
        {
            decimal CostingGSM;
            decimal AdditionalPasteQty = 0;
            decimal UsedPasteScQuantity;
            decimal totalUsedPasteQuantity;
            decimal QuantityOfPerishedMaterialPasteUsed = 0;
            decimal QuantityOfPerishedMaterialPasteLeft = 0;

            if (RemainingPasteScQuantity < 0)
            {
                AdditionalPasteQty = Math.Abs(RemainingPasteScQuantity.Value);
                UsedPasteScQuantity = StdPasteRequirementScquantity;
            }
            else
            {
                UsedPasteScQuantity = StdPasteRequirementScquantity - RemainingPasteScQuantity.Value;
            }

            decimal PerishedMaterialQuantity = StdPasteRequirementQuantity - StdPasteRequirementScquantity;

            if (PerishedMaterialQuantity > 0)
            {
                decimal PrctgOfUsedPasteSCQuantity = CalculatePercentageOfUsedPasteSCQuantity(StdPasteRequirementScquantity, UsedPasteScQuantity);

                QuantityOfPerishedMaterialPasteUsed = CalculateActualQuantityOfPerishedMaterialForPasteUsed(PerishedMaterialQuantity, StdPasteRequirementScquantity, UsedPasteScQuantity);
                QuantityOfPerishedMaterialPasteLeft = CalculateActualQuantityOfPerishedMaterialForPasteLeft(PerishedMaterialQuantity, PrctgOfUsedPasteSCQuantity);
            }

            if (AdditionalPasteQty > 0)
            {
                totalUsedPasteQuantity = UsedPasteScQuantity + AdditionalPasteQty + (QuantityOfPerishedMaterialPasteUsed - QuantityOfPerishedMaterialPasteLeft);
            }
            else
            {
                totalUsedPasteQuantity = UsedPasteScQuantity + (QuantityOfPerishedMaterialPasteUsed - QuantityOfPerishedMaterialPasteLeft);
            }

            CostingGSM = CalculateGSMFromPasteQuantity(totalUsedPasteQuantity, MfgQty, LMConstant);
            return CostingGSM;
        }
        public decimal CalculateGSMFromPasteQuantity(decimal PasteQuantity, decimal TotalProductionQty, decimal LMConstant)
        {
            var GsmFromPaste = PasteQuantity / TotalProductionQty / (decimal)LMConstant * 1000;

            return GsmFromPaste;
        }
        public List<InspectionFormulationCodeMixingTableVm> GetFormulationProductCalculateMaterialsQuantity(List<InspectionFormulationCodeMixingTableVm> FCMixing, long saleOrderId, decimal? MfdQty)
        {
            var totalQuantitiesByMixingName = FCMixing
            .GroupBy(f => f.MixingName.Trim().ToLower())
            .ToDictionary(
            group => group.Key,
            group => group.Sum(f => f.MixingRawMaterial.Sum(m => m.BaseQuantity))
            );

            foreach (var formulation in FCMixing)
            {
                decimal? totalQuantity = totalQuantitiesByMixingName[formulation.MixingName.Trim().ToLower()];

                foreach (var raw in formulation.MixingRawMaterial)
                {
                    switch (formulation.MixingName.Trim().ToLower())
                    {
                        case "pre skin":
                            if (formulation.PreSkinGsmPasteReq > 0 && totalQuantity > 0)
                            {
                                raw.CalculatedQuantity = (formulation.PreSkinGsmPasteReq / totalQuantity) * raw.BaseQuantity;
                                raw.CalculatedSCQuantity = raw.CalculatedSCQuantity == 0 ? 0 : (formulation.PreSkinGsmPasteReq / totalQuantity) * raw.BaseQuantity;
                            }
                            break;
                        case "skin":
                            if (formulation.SkinGsmPasteReq > 0 && totalQuantity > 0)
                            {
                                if (raw.IsBaseMaterial != null && raw.IsBaseMaterial == true)
                                {
                                    raw.CalculatedQuantity = (formulation.SkinGsmPasteReq / totalQuantity) * 100;
                                    raw.CalculatedSCQuantity = raw.CalculatedSCQuantity == 0 ? 0 : (formulation.SkinGsmPasteReq / totalQuantity) * 100;
                                }
                                else
                                {
                                    raw.CalculatedQuantity = (formulation.SkinGsmPasteReq / totalQuantity) * raw.BaseQuantity;
                                    raw.CalculatedSCQuantity = raw.CalculatedSCQuantity == 0 ? 0 : (formulation.SkinGsmPasteReq / totalQuantity) * raw.BaseQuantity;
                                }
                            }
                            break;

                        case "foam":
                            if (formulation.FoamGsmPasteReq > 0 && totalQuantity > 0)
                            {
                                raw.CalculatedQuantity = (formulation.FoamGsmPasteReq / totalQuantity) * raw.BaseQuantity;
                                raw.CalculatedSCQuantity = raw.CalculatedSCQuantity == 0 ? 0 : (formulation.FoamGsmPasteReq / totalQuantity) * raw.BaseQuantity;
                            }
                            break;

                        case "adhesive":
                            if (formulation.AdhesiveGsmPasteReq > 0 && totalQuantity > 0)
                            {
                                raw.CalculatedQuantity = (formulation.AdhesiveGsmPasteReq / totalQuantity) * raw.BaseQuantity;
                                raw.CalculatedSCQuantity = raw.CalculatedSCQuantity == 0 ? 0 : (formulation.AdhesiveGsmPasteReq / totalQuantity) * raw.BaseQuantity;
                            }
                            break;
                        case "fabric":
                            if (raw.Unit.ToLower() == "kgs")
                            {
                                var FabricQty = GetFabricConversionMTRToKG(raw.ProductId.Value, saleOrderId);
                                if (FabricQty > 0)
                                {
                                    raw.CalculatedQuantity = FabricQty;
                                }
                                else
                                {
                                    raw.CalculatedQuantity = 0;
                                }
                            }
                            else
                            {
                                raw.CalculatedQuantity = MfdQty;
                            }
                            break;

                        default:
                            raw.CalculatedQuantity = raw.Quantity;
                            raw.CalculatedSCQuantity = raw.CalculatedSCQuantity == 0 ? 0 : raw.Quantity;
                            break;
                    }
                }
            }
            return FCMixing;
        }
        public decimal? GetFabricConversionMTRToKG(long productId, long saleOrderId)
        {
            using var db = new pmsdbContext();

            var ProductData = new ProductMaster();
            decimal? ConvertedQuantity = 0;
            decimal? FabricGSM = 0;
            decimal? FabricWidth = 0;

            var orderdata = (from so in db.SaleOrderTables
                             join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                             join ifcm in db.InspectionFormulationCodeMixingTables on so.SaleOrderId equals ifcm.SaleOrderId
                             join isfcm in db.InspectionSaleFormulationCodeMasters on ifcm.InspectionSaleFormulationCodeId equals isfcm.InspectionSaleFormulationCodeId
                             where so.SaleOrderId == saleOrderId
                             select new
                             {
                                 so.Status,
                                 sop.ManufacturingQuantity,
                                 isfcm.FabricGsm,
                                 isfcm.FabricWidthInMeter
                             }).FirstOrDefault();
            if (orderdata.FabricGsm == null || orderdata.FabricGsm > 0)
            {
                ProductData = db.ProductMasters.FirstOrDefault(x => x.ProductId == productId);
            }

            FabricGSM = orderdata.Status != (int)ESalesOrderStatus.WorkPlan ? orderdata.FabricGsm : ProductData.AvgGsm;
            FabricWidth = orderdata.Status != (int)ESalesOrderStatus.WorkPlan ? orderdata.FabricWidthInMeter : ProductData.WidthInMeter;

            if (ProductData.WidthInMeter != null && ProductData.WidthInMeter > 0 && ProductData.AvgGsm != null && ProductData.AvgGsm > 0)
            {
                ConvertedQuantity = orderdata.ManufacturingQuantity * FabricWidth * FabricGSM / 1000;
            }
            return ConvertedQuantity;
        }
        public decimal GetProductionLineSpeedIncAllJumbo(long? saleOrderId)
        {
            using var db = new pmsdbContext();

            var jumboData = db.WorkPlanJumboMasters.Where(x => x.SaleOrderId == saleOrderId).ToList();
            decimal LineSpeed = 0;
            List<decimal> linespeedlist = new();
            if (jumboData.Count > 0)
            {
                double TotalPrdMinutes = 0;
                decimal? TotalProductionQty = 0;
                foreach (var item in jumboData)
                {
                    if (item.JumboRollStartTime != null && item.JumboRollEndTime != null)
                    {
                        DateTime startTime = item.JumboRollStartTime.Value;
                        DateTime endTime = item.JumboRollEndTime.Value;

                        TimeSpan TimeDifference;
                        // Check if the end time is earlier than the start time, indicating a span over midnight
                        if (endTime < startTime)
                        {
                            TimeDifference = endTime.AddDays(1) - startTime;
                        }
                        else
                        {
                            TimeDifference = endTime - startTime;
                        }

                        TotalPrdMinutes += TimeDifference.TotalMinutes;
                        TotalProductionQty += item.JumboRolQty;

                        var linespeed = decimal.Round(item.JumboRolQty.Value / (decimal)TimeDifference.TotalMinutes, 2);
                        linespeedlist.Add(linespeed);
                    }
                }
                if (TotalPrdMinutes > 0)
                {
                    // LineSpeed = decimal.Round(TotalProductionQty.Value / (decimal)TotalPrdMinutes, 2);
                    LineSpeed = linespeedlist.Average();
                }

            }
            return LineSpeed;
        }
        public decimal? GetProductionLineSpeedIncAllJumbo(List<WorkPlanJumboMasterVm> JumboData)
        {
            var jumboData = JumboData;
            decimal LineSpeed = 0;
            List<decimal> LineSpeedList = new();
            if (jumboData.Count > 0)
            {
                double TotalPrdMinutes = 0;
                decimal? TotalProductionQty = 0;
                foreach (var item in jumboData)
                {
                    if (item.JumboRollStartTime != null && item.JumboRollEndTime != null)
                    {
                        TimeSpan? TimeDifference = item.JumboRollEndTime - item.JumboRollStartTime;
                        TotalPrdMinutes += TimeDifference.Value.TotalMinutes;
                        TotalProductionQty += item.JumboRolQty;

                        var SingleJumboSpeed = item.JumboRolQty.Value / (decimal)TimeDifference.Value.TotalMinutes;
                        LineSpeedList.Add(SingleJumboSpeed);
                    }
                }
                if (TotalPrdMinutes > 0)
                {
                    // LineSpeed = decimal.Round(TotalProductionQty.Value / (decimal)TotalPrdMinutes, 2);
                    LineSpeed = LineSpeedList.Average();
                }
            }
            return LineSpeed;
        }
        public static decimal CalculatePercentageOfUsedPasteSCQuantity(decimal initialSCQuantity, decimal usedSCQuantity)
        {
            // Prevent division by zero
            if (initialSCQuantity <= 0)
                return 0;

            decimal usedSCPercentage = (usedSCQuantity / initialSCQuantity) * 100;
            return usedSCPercentage;
        }
        public static decimal CalculateActualQuantityOfPerishedMaterialForPasteUsed(decimal perishedQuantity, decimal initialSCQuantity, decimal usedSCQuantity)
        {
            // Prevent division by zero
            if (initialSCQuantity <= 0)
                return 0;

            decimal usedSCPercentage = (usedSCQuantity / initialSCQuantity) * 100;
            // Calculate the quantity of the perished material for the paste that was used.
            decimal perishedQuantityForUsedPaste = (usedSCPercentage / 100) * perishedQuantity;

            return perishedQuantityForUsedPaste;
        }
        public static decimal CalculateActualQuantityOfPerishedMaterialForPasteLeft(decimal perishedQuantity, decimal usedSCPercentage)
        {
            decimal leftSCPercentage = 100 - usedSCPercentage;
            // Calculate the quantity of the perished material for the paste that was left.
            decimal perishedQuantityLeft = (leftSCPercentage / 100) * perishedQuantity;

            return perishedQuantityLeft;
        }
        public decimal GetOverheadforAllJumboPerSaleOrder(long saleOrderId)
        {
            using var db = new pmsdbContext();

            var jumboData = db.WorkPlanJumboMasters.Where(x => x.SaleOrderId == saleOrderId).ToList();
            List<decimal> ApplicableOverheadCostList = new();
            if (jumboData.Count > 0)
            {
                foreach (var item in jumboData)
                {
                    if (item.JumboRollStartTime != null && item.JumboRollEndTime != null)
                    {
                        DateTime startTime = item.JumboRollStartTime.Value;
                        DateTime endTime = item.JumboRollEndTime.Value;
                        decimal? PerHourAvgOverheadCost = 0;

                        // Check if the end time is earlier than the start time, indicating a span over midnight
                        if (endTime < startTime)
                        {
                            endTime = endTime.AddDays(1);
                        }

                        TimeSpan TimeDifference = endTime - startTime;

                        var JumboRollStartDateInIST = TimeZoneInfo.ConvertTimeFromUtc(item.JumboRollStartTime.Value, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).Date;
                        PerHourAvgOverheadCost = db.OverheadCostingTables
                        .Where(x => x.ApplicableOn == JumboRollStartDateInIST)
                        .Select(x => x.OverheadCost)
                        .SingleOrDefault();
                        if (PerHourAvgOverheadCost == null || PerHourAvgOverheadCost == 0)
                        {
                            PerHourAvgOverheadCost = db.OverheadCostingTables
                            .Where(x => x.ApplicableOn < JumboRollStartDateInIST)
                            .OrderByDescending(x => x.ApplicableOn)
                            .Select(x => x.OverheadCost)
                            .FirstOrDefault();
                        }
                        if (PerHourAvgOverheadCost == null || PerHourAvgOverheadCost == 0)
                        {
                            PerHourAvgOverheadCost = db.OverheadCostingTables
                            .OrderByDescending(x => x.ApplicableOn)
                            .Select(x => x.OverheadCost)
                            .FirstOrDefault();
                        }
                        // Prevent division by zero for time difference and line speed calculations
                        var totalMinutes = (decimal)TimeDifference.TotalMinutes;
                        if (totalMinutes <= 0 || item.JumboRolQty <= 0)
                        {
                            // Skip this jumbo if invalid time or quantity
                            continue;
                        }

                        var linespeed = decimal.Round(item.JumboRolQty.Value / totalMinutes, 2);

                        // Prevent division by zero for line speed
                        var PerJumboOverhead = linespeed > 0
                            ? PerHourAvgOverheadCost / 60 / linespeed
                            : 0;

                        ApplicableOverheadCostList.Add(PerJumboOverhead.Value);
                    }
                }
            }
            // Return average if list has items, otherwise return 0
            return ApplicableOverheadCostList.Any() ? ApplicableOverheadCostList.Average() : 0;
        }
    }
}
