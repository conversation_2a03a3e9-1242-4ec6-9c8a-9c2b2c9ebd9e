﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class ElementDataFn
    {
        public List<ElementMasterVm> GetAllElements()
        {
            List<ElementMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ElementMasters
                       select new ElementMasterVm
                       {
                           ElementId = a.ElementId,
                           ElementName = a.ElementName,
                           ElementCode = a.ElementCode,
                           ElementDesc = a.ElementDesc,
                           ElementAddedBy = a.ElementAddedBy,
                           ElementAddedDate = a.ElementAddedDate
                       }).OrderBy(x => x.ElementCode).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateElement(ElementMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.ElementId == 0)
                {
                    var rec = db.ElementMasters.Where(x => x.ElementCode == br.ElementCode).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                ElementMaster res = new ElementMaster();
                if (br.ElementId == 0)
                {
                    res.ElementName = br.ElementName;
                    res.ElementCode = br.ElementCode;
                    res.ElementDesc = br.ElementDesc;
                    res.ElementAddedBy = br.ElementAddedBy;
                    res.ElementAddedDate = System.DateTime.Now;
                    db.ElementMasters.Add(res);
                }
                else
                {
                    res = db.ElementMasters.Where(x => x.ElementId == br.ElementId).FirstOrDefault();
                    if (res != null)
                    {
                        res.ElementName = br.ElementName;
                        res.ElementCode = br.ElementCode;
                        res.ElementDesc = br.ElementDesc;
                        res.ElementAddedBy = br.ElementAddedBy;
                        res.ElementAddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
    }
}
