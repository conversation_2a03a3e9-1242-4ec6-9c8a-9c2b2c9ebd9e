﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class EmbossingDataFn
    {
        public List<EmbossingMasterVm> GetAllEmbossings()
        {
            List<EmbossingMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.EmbossingMasters
                       where a.Disabled != true
                       select new EmbossingMasterVm
                       {
                           EmbossingMasterId = a.EmbossingMasterId,
                           Name = a.Name,
                           Code = a.Code,
                           ImageName = a.ImageName,
                           Description = a.Description,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderBy(x => x.Code).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateEmbossing(EmbossingMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.EmbossingMasterId == 0)
                {
                    var rec = db.EmbossingMasters.Where(x => x.Code == br.Code && x.Disabled != true).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                EmbossingMaster res = new EmbossingMaster();
                if (br.EmbossingMasterId == 0)
                {

                    res.Name = br.Name;
                    res.Code = br.Code;
                    res.ImageName = br.ImageName;
                    res.Description = br.Description;
                    res.AddedBy = br.AddedBy;
                    res.AddedDate = System.DateTime.Now;
                    db.EmbossingMasters.Add(res);
                }
                else
                {
                    res = db.EmbossingMasters.Where(x => x.EmbossingMasterId == br.EmbossingMasterId).FirstOrDefault();
                    if (res != null)
                    {
                        res.Name = br.Name;
                        res.Code = br.Code;
                        res.ImageName = br.ImageName;
                        res.Description = br.Description;
                        res.AddedBy = br.AddedBy;
                        res.AddedDate = System.DateTime.Now;
                        res.EmbossingMasterId = br.EmbossingMasterId;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DeleteEmbossing(EmbossingMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        EmbossingMaster item = db.EmbossingMasters.FirstOrDefault(x => x.EmbossingMasterId == param.EmbossingMasterId);
                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
