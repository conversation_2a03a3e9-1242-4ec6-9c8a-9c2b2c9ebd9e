﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsCommon;
using Azure.Storage.Sas;
using PmsData.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;

namespace PmsData.DataFn
{
    public class CommonDataFn
    {
        public GlobalDataEntity GlobalData;
        public CommonDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<MeasureUnitMasterVm> GetMeasureUnits()
        {
            List<MeasureUnitMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.MeasureUnitMasters
                       where a.Disabled == false
                       select new MeasureUnitMasterVm
                       {
                           Id = a.Id,
                           Unit = a.Unit,
                           UnitName = a.UnitName,
                           UnitType = a.UnitType,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateMeasureUnit(MeasureUnitMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.Id == 0)
                {
                    var rec = db.MeasureUnitMasters.Where(x => x.Unit == br.Unit).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                MeasureUnitMaster res = new MeasureUnitMaster();
                if (br.Id == 0)
                {
                    res.Unit = br.Unit;
                    res.UnitName = br.UnitName;
                    res.UnitType = br.UnitType;
                    res.AddedBy = br.AddedBy;
                    res.AddedDate = System.DateTime.Now;
                    res.Disabled = false;
                    db.MeasureUnitMasters.Add(res);
                }
                else
                {
                    res = db.MeasureUnitMasters.Where(x => x.Id == br.Id).FirstOrDefault();
                    if (res != null)
                    {
                        res.Unit = br.Unit;
                        res.UnitName = br.UnitName;
                        res.UnitType = br.UnitType;
                        res.AddedBy = br.AddedBy;
                        res.AddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public ApiFunctionResponseVm DisableMeasureUnit(int id)
        {
            using var db = new Models.pmsdbContext();
            var res = db.MeasureUnitMasters.Where(x => x.Id == id).FirstOrDefault();
            if (res != null)
            {
                res.Disabled = true;
                res.DisabledDate = System.DateTime.Now;
                res.DisabledById = db.UserMasters.Where(x => x.Email == GlobalData.loggedInUser).Select(x => x.UserId).FirstOrDefault();
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Measure Unit not found");
        }
        public List<PackagingTypeMasterVm> GetAllPackagingTypes()
        {
            List<PackagingTypeMasterVm> res = null;
            using var db = new Models.pmsdbContext();

            res = db.PackagingTypeMasters
            .Include(a => a.AddedBy)
            .Where(x => x.Disabled == false)
            .Select(a => new PackagingTypeMasterVm
            {
                PackagingTypeId = a.PackagingTypeId,
                PackagingTypeName = a.PackagingTypeName,
                PackagingTypeCode = a.PackagingTypeCode,
                AddedBy = new UserMasterBasicVm
                {
                    Name = a.AddedBy.Name,
                    Email = a.AddedBy.Email
                },
                AddedDate = a.AddedDate
            }).ToList();

            return res;
        }

        public ApiFunctionResponseVm AddUpdatePackagingType(PackagingTypeMasterVm br)
        {
            using var db = new Models.pmsdbContext();
            if (br.PackagingTypeId == 0)
            {
                var rec = db.PackagingTypeMasters.Where(x => x.PackagingTypeName == br.PackagingTypeName && x.Disabled == false).FirstOrDefault();
                if (rec != null)
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Packaging Type already exists");
            }
            PackagingTypeMaster res = new PackagingTypeMaster();
            if (br.PackagingTypeId == 0)
            {
                res.PackagingTypeName = br.PackagingTypeName;
                res.PackagingTypeCode = br.PackagingTypeCode;
                res.AddedById = db.UserMasters.Where(x => x.Email == GlobalData.loggedInUser).Select(x => x.UserId).FirstOrDefault();
                res.AddedDate = System.DateTime.Now;
                res.Disabled = false;
                db.PackagingTypeMasters.Add(res);
            }
            else
            {
                res = db.PackagingTypeMasters.Where(x => x.PackagingTypeId == br.PackagingTypeId).FirstOrDefault();
                if (res != null)
                {
                    res.PackagingTypeName = br.PackagingTypeName;
                    res.PackagingTypeCode = br.PackagingTypeCode;
                    res.AddedById = db.UserMasters.Where(x => x.Email == GlobalData.loggedInUser).Select(x => x.UserId).FirstOrDefault();
                    res.AddedDate = System.DateTime.Now;
                }
            }
            db.SaveChanges();
            return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
        }
        public ApiFunctionResponseVm DisablePackagingType(int id)
        {
            using var db = new Models.pmsdbContext();
            var res = db.PackagingTypeMasters.Where(x => x.PackagingTypeId == id).FirstOrDefault();
            if (res != null)
            {
                res.Disabled = true;
                res.DisabledDate = System.DateTime.Now;
                res.DisabledById = db.UserMasters.Where(x => x.Email == GlobalData.loggedInUser).Select(x => x.UserId).FirstOrDefault();
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Packaging Type not found");
        }

        public StorageTokenVm GetStorageTokenForInvoice()
        {
            var storageAccountName = KeyVault.GetKeyValue("StorageAccountName");
            var storageAccountKey = KeyVault.GetKeyValue("StorageAccountKey");
            var storageContainerName = KeyVault.GetKeyValue("InvoiceStorageContainerName");
            string storageAccountHost = string.Format("https://{0}.blob.core.windows.net/{1}", storageAccountName, storageContainerName);
            Uri res = CommonFunctions.GetServiceSasUriForContainer(storageAccountName, storageAccountKey, storageContainerName);
            return new StorageTokenVm
            {
                StorageAccountHost = string.Format("https://{0}.blob.core.windows.net", storageAccountName),
                StorageAccountName = storageAccountName,
                StorageContainerName = storageContainerName,
                StorageAccountToken = res.Query
            };
        }

        public StorageTokenVm GetStorageTokenForPOUpload()
        {
            var storageAccountName = KeyVault.GetKeyValue("StorageAccountName");
            var storageAccountKey = KeyVault.GetKeyValue("StorageAccountKey");
            var storageContainerName = "purchaseorder";
            string storageAccountHost = string.Format("https://{0}.blob.core.windows.net/{1}", storageAccountName, storageContainerName);
            Uri res = CommonFunctions.GetServiceSasUriForContainer(storageAccountName, storageAccountKey, storageContainerName);
            return new StorageTokenVm
            {
                StorageAccountHost = string.Format("https://{0}.blob.core.windows.net", storageAccountName),
                StorageAccountName = storageAccountName,
                StorageContainerName = storageContainerName,
                StorageAccountToken = res.Query
            };
        }
        public StorageTokenVm GetStorageTokenForGate()
        {
            var storageAccountName = KeyVault.GetKeyValue("StorageAccountName");
            var storageAccountKey = KeyVault.GetKeyValue("StorageAccountKey");
            var storageContainerName = "gate"; // KeyVault.GetKeyValue("InvoiceStorageContainerName");
            string storageAccountHost = string.Format("https://{0}.blob.core.windows.net/{1}", storageAccountName, storageContainerName);
            Uri res = CommonFunctions.GetServiceSasUriForContainer(storageAccountName, storageAccountKey, storageContainerName);
            return new StorageTokenVm
            {
                StorageAccountHost = string.Format("https://{0}.blob.core.windows.net", storageAccountName),
                StorageAccountName = storageAccountName,
                StorageContainerName = storageContainerName,
                StorageAccountToken = res.Query
            };
        }

        public List<string> GetCurrency()
        {
            var currencyList = new List<string>();
            currencyList.Add("INR");
            currencyList.Add("USD");
            currencyList.Add("EUR");
            currencyList.Add("AED");
            currencyList.Add("ALL");
            currencyList.Add("AMD");
            currencyList.Add("AOA");
            currencyList.Add("ARS");
            currencyList.Add("AUD");
            currencyList.Add("AZN");
            currencyList.Add("BAM");
            currencyList.Add("BBD");
            currencyList.Add("BGN");
            currencyList.Add("BHD");
            currencyList.Add("BMD");
            currencyList.Add("BOB");
            currencyList.Add("BRL");
            currencyList.Add("BWP");
            currencyList.Add("BYN");
            currencyList.Add("CAD");
            currencyList.Add("CDF");
            currencyList.Add("CHF");
            currencyList.Add("CLP");
            currencyList.Add("CNY");
            currencyList.Add("COP");
            currencyList.Add("CRC");
            currencyList.Add("CVE");
            currencyList.Add("CZK");
            currencyList.Add("DKK");
            currencyList.Add("DOP");
            currencyList.Add("DZD");
            currencyList.Add("EGP");
            currencyList.Add("FJD");
            currencyList.Add("GBP");
            currencyList.Add("GEL");
            currencyList.Add("GHS");
            currencyList.Add("GIP");
            currencyList.Add("GTQ");
            currencyList.Add("GYD");
            currencyList.Add("HKD");
            currencyList.Add("HNL");
            currencyList.Add("HRK");
            currencyList.Add("HUF");
            currencyList.Add("IDR");
            currencyList.Add("ILS");
            currencyList.Add("IMP");
            currencyList.Add("IQD");
            currencyList.Add("ISK");
            currencyList.Add("JMD");
            currencyList.Add("JOD");
            currencyList.Add("JPY");
            currencyList.Add("KES");
            currencyList.Add("KGS");
            currencyList.Add("KHR");
            currencyList.Add("KRW");
            currencyList.Add("KWD");
            currencyList.Add("KYD");
            currencyList.Add("KZT");
            currencyList.Add("LAK");
            currencyList.Add("LBP");
            currencyList.Add("LKR");
            currencyList.Add("LYD");
            currencyList.Add("MAD");
            currencyList.Add("MDL");
            currencyList.Add("MGA");
            currencyList.Add("MKD");
            currencyList.Add("MMK");
            currencyList.Add("MNT");
            currencyList.Add("MOP");
            currencyList.Add("MRU");
            currencyList.Add("MUR");
            currencyList.Add("MVR");
            currencyList.Add("MWK");
            currencyList.Add("MXN");
            currencyList.Add("MYR");
            currencyList.Add("MZN");
            currencyList.Add("NAD");
            currencyList.Add("NGN");
            currencyList.Add("NIO");
            currencyList.Add("NOK");
            currencyList.Add("NZD");
            currencyList.Add("OMR");
            currencyList.Add("PAB");
            currencyList.Add("PEN");
            currencyList.Add("PGK");
            currencyList.Add("PHP");
            currencyList.Add("PKR");
            currencyList.Add("PLN");
            currencyList.Add("PYG");
            currencyList.Add("QAR");
            currencyList.Add("RON");
            currencyList.Add("RSD");
            currencyList.Add("RUB");
            currencyList.Add("RWF");
            currencyList.Add("SAR");
            currencyList.Add("SEK");
            currencyList.Add("SGD");
            currencyList.Add("SZL");
            currencyList.Add("THB");
            currencyList.Add("TJS");
            currencyList.Add("TMT");
            currencyList.Add("TND");
            currencyList.Add("TRY");
            currencyList.Add("TTD");
            currencyList.Add("TWD");
            currencyList.Add("TZS");
            currencyList.Add("UAH");
            currencyList.Add("UGX");
            currencyList.Add("UYU");
            currencyList.Add("UZS");
            currencyList.Add("VEF");
            currencyList.Add("VND");
            currencyList.Add("XAF");
            currencyList.Add("XCD");
            currencyList.Add("XOF");
            currencyList.Add("ZAR");
            currencyList.Add("ZMW");
            currencyList.Add("ZWD");
            return currencyList;

        }
        public (string SerialNo, string ShortCode) GenerateSerialNoWithShortCode(pmsdbContext db)
        {
            int currentYear = DateTime.Now.Year;
            string yearPart = currentYear.ToString().Substring(2);
            string monthPart = DateTime.Now.Month.ToString("D2");
            string dayPart = DateTime.Now.Day.ToString("D2");

            string yearMonthPart = yearPart + monthPart + dayPart;

            // Get the highest serial number for the current year, month and day
            var lastSerialNo = db.StockLabelTables
                .Where(sl => sl.SerialNo.StartsWith(yearMonthPart))
                .OrderByDescending(sl => sl.SerialNo)
                .Select(sl => sl.SerialNo)
                .FirstOrDefault();

            int series = lastSerialNo != null ? int.Parse(lastSerialNo[6..]) + 1 : 1;
            string serialNo = $"{yearMonthPart}{series:D8}";

            // Generate short code synchronously
            string shortCode = GenerateShortCode(db, serialNo);

            return (serialNo, shortCode);
        }

        public string GenerateShortCode(pmsdbContext db, string serialNo)
        {
            const string ALPHA = "ABCDEFGHJKLMNPQRSTUVWXYZ"; // Excluding I, O
            const string NUMERIC = "23456789"; // Excluding 0, 1

            // First attempt: YY + Alpha + Numeric (4 chars)
            string year = serialNo.Substring(0, 2);
            string remaining = serialNo.Substring(2);
            int hash = Math.Abs(remaining.GetHashCode());

            string alpha = ALPHA[hash % ALPHA.Length].ToString();
            string numeric = NUMERIC[hash % NUMERIC.Length].ToString();
            string shortCode = $"{year}{alpha}{numeric}";

            if (!db.StockLabelTables.Any(x => x.ShortCode == shortCode))
                return shortCode;

            // Second attempt: 3 letters + 2 numbers (5 chars)
            string letters = new string(Enumerable.Range(0, 3)
                .Select(i => ALPHA[(hash + i) % ALPHA.Length])
                .ToArray());
            string numbers = new string(Enumerable.Range(0, 2)
                .Select(i => NUMERIC[(hash + i) % NUMERIC.Length])
                .ToArray());
            shortCode = $"{letters}{numbers}";

            if (!db.StockLabelTables.Any(x => x.ShortCode == shortCode))
                return shortCode;

            // Final attempt: Base36 with collision handling
            string baseCode = ToBase36String(long.Parse(serialNo));
            string finalCode = baseCode.Substring(0, Math.Min(6, baseCode.Length));

            int suffix = 1;
            string candidateCode = finalCode;

            while (db.StockLabelTables.Any(x => x.ShortCode == candidateCode))
            {
                candidateCode = $"{finalCode}{suffix}";
                suffix++;

                if (suffix > 99)
                    throw new InvalidOperationException($"Unable to generate unique short code for serial: {serialNo}");
            }

            return candidateCode;
        }

        private string ToBase36String(long value)
        {
            const string Base36Chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

            if (value == 0) return "0";

            var sb = new StringBuilder();
            while (value > 0)
            {
                sb.Insert(0, Base36Chars[(int)(value % 36)]);
                value /= 36;
            }
            return sb.ToString();
        }
    }
}
