﻿using PmsCommon;
using PmsData.Models;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace PmsData.DataFn
{
    public class WorkPlanOrderFn
    {
        public List<WorkPlanOrderVm> GetWorkPlanOrderByWorkplanId(long WorkPlanId)
        {
            List<WorkPlanOrderVm> vmModel = new List<WorkPlanOrderVm>();
            using (var db = new pmsdbContext())
            {
                vmModel = (from x in db.WorkPlanOrders
                           where x.WorkplanId == WorkPlanId
                           select new WorkPlanOrderVm
                           {
                               WorkplanId = x.WorkplanId,
                               OrderId = x.OrderId,
                               WorkPlanOrdersId = x.WorkPlanOrdersId,
                               SaleOrder = (from s in db.SaleOrderTables
                                            join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                                            join pcm in db.ProductCategoryMasters on s.CategoryId equals pcm.ProductCategoryId into spc
                                            from pcm in spc.DefaultIfEmpty()
                                            join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                                            from cust in scu.DefaultIfEmpty()
                                            join c in db.ColorMasters on a.ColorId equals c.ColorId into cagr
                                            from c in cagr.DefaultIfEmpty()
                                            join pr in db.ProductMasters on a.ProductId equals pr.ProductId into pra
                                            from pr in pra.DefaultIfEmpty()
                                            join slpe in db.SaleOrderPostProcessEmbossingTables on s.SaleOrderId equals slpe.SaleOrderId into slpea
                                            from slpe in slpea.DefaultIfEmpty()
                                            join slpp in db.SaleOrderPostProcessPrintTables on s.SaleOrderId equals slpp.SaleOrderId into slppa
                                            from slpp in slppa.DefaultIfEmpty()
                                            join slpv in db.SaleOrderPostProcessVacuumTables on s.SaleOrderId equals slpv.SaleOrderId into slpva
                                            from slpv in slpva.DefaultIfEmpty()
                                            join slpl in db.SaleOrderPostProcessLacquerTables on s.SaleOrderId equals slpl.SaleOrderId into slpla
                                            from slpl in slpla.DefaultIfEmpty()
                                            join slpt in db.SaleOrderPostProcessTumblingTables on s.SaleOrderId equals slpt.SaleOrderId into slpta
                                            from slpt in slpta.DefaultIfEmpty()
                                            where s.SaleOrderId == x.OrderId
                                            select new SaleOrderTableVm
                                            {
                                                SaleOrderId = s.SaleOrderId,
                                                SaleOrderDate = s.SaleOrderDate,
                                                SaleOrderNumber = s.SaleOrderNumber,
                                                SaleOrderType = s.SaleOrderType,
                                                SaleOrderCode = s.SaleOrderCode,
                                                CostingAdded = s.CostingAdded,
                                                CustomerId = s.CustomerId,
                                                CustomerName = cust.CustomerName,
                                                CategoryId = s.CategoryId,
                                                Category = pcm.ProductCategory,
                                                AddedBy = s.AddedBy,
                                                AddedDate = s.AddedDate,
                                                DeliveryDate = s.DeliveryDate,
                                                Remarks = s.Remarks,
                                                SaleOrderStatus = s.SaleOrderStatus,
                                                IsRawMaterialIssued = s.IsRawMaterialIssued,
                                                Status = (PmsCommon.ESalesOrderStatus)s.Status,
                                                IsJumboRequired = s.IsJumboRequired,
                                                ProformaInvoiceId = s.ProformaInvoiceId,
                                                ProductionCompletionRemarks = s.ProductionCompletionRemarks,

                                                SaleOrderPostProcessOrder = (from sopp in db.SaleOrderPostProcessOrderTables
                                                                             where sopp.SaleOrderId == s.SaleOrderId && sopp.Removed != true
                                                                             select new SaleOrderPostProcessOrderTableVm
                                                                             {
                                                                                 SaleOrderId = sopp.SaleOrderId,
                                                                                 Rank = sopp.Rank,
                                                                                 PostProcessName = sopp.PostProcessName
                                                                             }).OrderBy(x => x.Rank).ToList(),
                                                FormulationCode = (from s in db.SaleFormulationCodeMasters
                                                                   join c in db.ProductCategoryMasters on s.CategoryId equals c.ProductCategoryId
                                                                   join ab in db.ProductMasters on s.FabricProductId equals ab.ProductId
                                                                   where s.SaleFormulationCodeId == s.SaleFormulationCodeId
                                                                   select new SaleFormulationCodeMasterVm
                                                                   {
                                                                       SaleFormulationCodeId = s.SaleFormulationCodeId,
                                                                       SaleFormulationCode = s.SaleFormulationCode,
                                                                       FabricProductId = s.FabricProductId,
                                                                       FabricProductName = ab.ProductName,
                                                                       AddedBy = s.AddedBy,
                                                                       AddedDate = s.AddedDate,
                                                                       PreSkinGsm = s.PreSkinGsm,
                                                                       SkinGsm = s.SkinGsm,
                                                                       FoamGsm = s.FoamGsm,
                                                                       AdhesiveGsm = s.AdhesiveGsm,
                                                                       FabricGsm = s.FabricGsm,
                                                                       TotalGsm = s.TotalGsm,
                                                                       ThicknessId = s.ThicknessId,
                                                                       CategoryId = s.CategoryId,
                                                                       FabricProductQty = s.FabricProductQty,
                                                                       CategoryName = c.ProductCategory
                                                                   }).FirstOrDefault(),
                                                SaleOrderProduction = new SaleOrderProductionTableVm
                                                {
                                                    SaleOrderProductionId = a.SaleOrderProductionId,
                                                    ProductId = a.ProductId,
                                                    ProductName = pr.ProductName,
                                                    ProductCode = pr.ProductCode,
                                                    ManufacturingProductName = a.ManufacturingProductName,
                                                    ManufacturingProductCode = a.ManufacturingProductCode,
                                                    Lot = a.Lot,
                                                    Batch = a.Batch,
                                                    OrderQuantity = a.OrderQuantity,
                                                    LMConstant = a.Lmconstant,
                                                    ExtraProduction = a.ExtraProduction,
                                                    ManufacturingQuantity = a.ManufacturingQuantity,
                                                    Unit = a.Unit,
                                                    ColorId = a.ColorId,
                                                    ColorName = c.ColorName,
                                                    Barcode = a.Barcode,
                                                    ProductionStatus = a.ProductionStatus,
                                                    CostingStatus = a.CostingStatus,
                                                    SlippagePercent = a.SlippagePercent,
                                                    TotalCost = a.TotalCost,
                                                    ProcessFormulationCode = a.ProcessFormulationCode,
                                                    MixingFormulationCode = a.MixingFormulationCode,
                                                    AddedBy = a.AddedBy,
                                                    AddedDate = a.AddedDate,
                                                    PreSkinGsm = a.PreSkinGsm,
                                                    SkinGsm = a.SkinGsm,
                                                    AdhesiveGsm = a.AdhesiveGsm,
                                                    FoamGsm = a.FoamGsm,
                                                    FabricGsm = a.FabricGsm,
                                                    SaleOrderPostProcessPrint = new SaleOrderPostProcessPrintTableVm
                                                    {
                                                        SaleOrderPostProcessPrintId = slpp == null ? 0 : slpp.SaleOrderPostProcessPrintId,
                                                        SaleOrderId = slpp == null ? 0 : slpp.SaleOrderId,
                                                        ReceivedQuantity = slpp == null ? 0 : slpp.ReceivedQuantity,
                                                        PrintCompletedQuantity = slpp == null ? 0 : slpp.PrintCompletedQuantity,
                                                        PrintMeasurementUnit = slpp == null ? null : slpp.PrintMeasurementUnit,
                                                        PrintRack = slpp == null ? null : slpp.PrintRack,
                                                        PrintWastageQuantity = slpp == null ? null : slpp.PrintWastageQuantity,
                                                        PrintStatus = slpp == null ? null : slpp.PrintStatus,
                                                        AddedBy = slpp == null ? null : slpp.AddedBy,
                                                        AddedDate = slpp == null ? null : slpp.AddedDate,
                                                    },
                                                    SaleOrderPostProcessEmbossing = new SaleOrderPostProcessEmbossingTableVm
                                                    {
                                                        SaleOrderPostProcessEmbossingId = slpe == null ? 0 : slpe.SaleOrderPostProcessEmbossingId,
                                                        SaleOrderId = slpe == null ? 0 : slpe.SaleOrderId,
                                                        ReceivedQuantity = slpe == null ? 0 : slpe.ReceivedQuantity,
                                                        EmbossingCompletedQuantity = slpe == null ? 0 : slpe.EmbossingCompletedQuantity,
                                                        EmbossingMeasurementUnit = slpe == null ? null : slpe.EmbossingMeasurementUnit,
                                                        EmbossingRack = slpe == null ? null : slpe.EmbossingRack,
                                                        EmbossingWastageQuantity = slpe == null ? null : slpe.EmbossingWastageQuantity,
                                                        EmbossingStatus = slpe == null ? null : slpe.EmbossingStatus,
                                                        AddedBy = slpe == null ? null : slpe.AddedBy,
                                                        AddedDate = slpe == null ? null : slpe.AddedDate,
                                                    },
                                                    SaleOrderPostProcessVacuum = new SaleOrderPostProcessVacuumTableVm
                                                    {
                                                        SaleOrderPostProcessVacuumId = slpv == null ? 0 : slpv.SaleOrderPostProcessVacuumId,
                                                        SaleOrderId = slpv == null ? 0 : slpv.SaleOrderId,
                                                        ReceivedQuantity = slpv == null ? 0 : slpv.ReceivedQuantity,
                                                        VacuumCompletedQuantity = slpv == null ? 0 : slpv.VacuumCompletedQuantity,
                                                        VacuumMeasurementUnit = slpv == null ? null : slpv.VacuumMeasurementUnit,
                                                        VacuumRack = slpv == null ? null : slpv.VacuumRack,
                                                        VacuumWastageQuantity = slpv == null ? null : slpv.VacuumWastageQuantity,
                                                        VacuumStatus = slpv == null ? null : slpv.VacuumStatus,
                                                        AddedBy = slpv == null ? null : slpv.AddedBy,
                                                        AddedDate = slpv == null ? null : slpv.AddedDate,
                                                    },
                                                    SaleOrderPostProcessLacquer = new SaleOrderPostProcessLacquerTableVm
                                                    {
                                                        SaleOrderPostProcessLacquerId = slpl == null ? 0 : slpl.SaleOrderPostProcessLacquerId,
                                                        SaleOrderId = slpl == null ? 0 : slpl.SaleOrderId,
                                                        ReceivedQuantity = slpl == null ? 0 : slpl.ReceivedQuantity,
                                                        LacquerCompletedQuantity = slpl == null ? 0 : slpl.LacquerCompletedQuantity,
                                                        LacquerMeasurementUnit = slpl == null ? null : slpl.LacquerMeasurementUnit,
                                                        LacquerRack = slpl == null ? null : slpl.LacquerRack,
                                                        LacquerWastageQuantity = slpl == null ? null : slpl.LacquerWastageQuantity,
                                                        LacquerStatus = slpl == null ? null : slpl.LacquerStatus,
                                                        AddedBy = slpl == null ? null : slpl.AddedBy,
                                                        AddedDate = slpl == null ? null : slpl.AddedDate,
                                                    },
                                                    SaleOrderPostProcessTumbling = new SaleOrderPostProcessTumblingTableVm
                                                    {
                                                        SaleOrderPostProcessTumblingId = slpt == null ? 0 : slpt.SaleOrderPostProcessTumblingId,
                                                        SaleOrderId = slpt == null ? 0 : slpt.SaleOrderId,
                                                        ReceivedQuantity = slpt == null ? 0 : slpt.ReceivedQuantity,
                                                        TumblingCompletedQuantity = slpt == null ? 0 : slpt.TumblingCompletedQuantity,
                                                        TumblingMeasurementUnit = slpt == null ? null : slpt.TumblingMeasurementUnit,
                                                        TumblingRack = slpt == null ? null : slpt.TumblingRack,
                                                        TumblingWastageQuantity = slpt == null ? null : slpt.TumblingWastageQuantity,
                                                        TumblingStatus = slpt == null ? null : slpt.TumblingStatus,
                                                        AddedBy = slpt == null ? null : slpt.AddedBy,
                                                        AddedDate = slpt == null ? null : slpt.AddedDate,
                                                    },
                                                }
                                            }).OrderByDescending(x => x.SaleOrderId).FirstOrDefault()

                           }).OrderByDescending(x => x.WorkPlanOrdersId).ToList();

                foreach (var res in vmModel)
                {
                    res.SaleOrder.IsPrintRequired = db.SaleOrderProductionPrintMasters.Any(x => x.SaleOrderProductionId == res.SaleOrder.SaleOrderProduction.SaleOrderProductionId && x.Removed != true);
                    res.SaleOrder.IsEmbossingRequired = db.SaleOrderProductionEmbossingMasters.Any(x => x.SaleOrderProductionId == res.SaleOrder.SaleOrderProduction.SaleOrderProductionId && x.Removed != true);
                    res.SaleOrder.IsLacquerRequired = db.SaleOrderProductionLacquerRawMaterialTables.Any(x => x.SaleOrderProductionId == res.SaleOrder.SaleOrderProduction.SaleOrderProductionId && x.Removed != true);
                    res.SaleOrder.IsTumblingRequired = db.SaleOrderProductionTumblingMasters.Any(x => x.SaleOrderProductionId == res.SaleOrder.SaleOrderProduction.SaleOrderProductionId && x.Removed != true);
                    res.SaleOrder.IsVacuumRequired = db.SaleOrderProductionVacuumMasters.Any(x => x.SaleOrderProductionId == res.SaleOrder.SaleOrderProduction.SaleOrderProductionId && x.Removed != true);
                    res.SaleOrder.SaleOrderProduction.InspectionFormulationMixing = (from la in db.InspectionFormulationCodeMixingTables
                                                                                     join a in db.MixingMasters on la.MixingId equals a.MixingId
                                                                                     where la.SaleOrderId == res.SaleOrder.SaleOrderId
                                                                                     select new InspectionFormulationCodeMixingTableVm
                                                                                     {
                                                                                         FormulationCodeMixingId = la.FormulationCodeMixingId,
                                                                                         SaleFormulationCodeId = la.SaleFormulationCodeId,
                                                                                         GSM = (a.MixingName == "PRE SKIN" ? res.SaleOrder.SaleOrderProduction.PreSkinGsm : (a.MixingName == "ADHESIVE" ? res.SaleOrder.SaleOrderProduction.AdhesiveGsm : (a.MixingName == "FOAM" ? res.SaleOrder.SaleOrderProduction.FoamGsm : (a.MixingName == "SKIN" ? res.SaleOrder.SaleOrderProduction.SkinGsm : 0)))),
                                                                                         AddedDate = la.AddedDate,
                                                                                         AddedBy = la.AddedBy,
                                                                                         MixingId = la.MixingId,
                                                                                         MixingName = a.MixingName,
                                                                                         StdPasteRequirementQuantity = la.StdPasteRequirementQuantity,
                                                                                         StdPasteRequirementScquantity = la.StdPasteRequirementScquantity,
                                                                                         MixingRawMaterial = (from op in db.InspectionFormulationCodeMixingRawMaterialTables
                                                                                                              join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                                                                              where op.FormulationCodeMixingId == la.FormulationCodeMixingId
                                                                                                              select new InspectionFormulationCodeMixingRawMaterialTableVm
                                                                                                              {
                                                                                                                  FormulationCodeMixingRawMaterialId = op.FormulationCodeMixingRawMaterialId,
                                                                                                                  FormulationCodeMixingId = op.FormulationCodeMixingId,
                                                                                                                  ProductId = op.ProductId,
                                                                                                                  ProductName = p.ProductName,
                                                                                                                  ProductCode = p.ProductCode,
                                                                                                                  Quantity = op.Quantity,
                                                                                                                  Scquantity = op.Scquantity,
                                                                                                                  AvgGsm = p.AvgGsm,
                                                                                                                  Unit = op.Unit,
                                                                                                                  Price = 0,
                                                                                                              }).ToList()
                                                                                     }).ToList();
                    var insdata = (from isfcm in db.InspectionSaleFormulationCodeMasters
                                   join ifcm in db.InspectionFormulationCodeMixingTables on isfcm.InspectionSaleFormulationCodeId equals ifcm.InspectionSaleFormulationCodeId
                                   join mm in db.MixingMasters on ifcm.MixingId equals mm.MixingId
                                   where ifcm.SaleOrderId == res.SaleOrder.SaleOrderId
                                   select new InspectionSaleFormulationCodeMasterVm
                                   {
                                       PreSkinGsm = isfcm.PreSkinGsm,
                                       SkinGsm = isfcm.SkinGsm,
                                       FoamGsm = isfcm.FoamGsm,
                                       AdhesiveGsm = isfcm.AdhesiveGsm,
                                       FabricGsm = isfcm.FabricGsm,
                                       MixingName = mm.MixingName
                                   }).ToList();
                    foreach (var item in res.SaleOrder.SaleOrderProduction.InspectionFormulationMixing)
                    {
                        if (res.SaleOrder.SaleOrderProduction.LMConstant != null || res.SaleOrder.SaleOrderProduction.LMConstant > 0)
                        {
                            decimal prefor = res.SaleOrder.SaleOrderProduction.LMConstant.Value / 1000;
                            var totalqtywithExtra = res.SaleOrder.SaleOrderProduction.OrderQuantity + (res.SaleOrder.SaleOrderProduction.OrderQuantity * res.SaleOrder.SaleOrderProduction.ExtraProduction) / 100;
                            var preSkinItem = insdata.FirstOrDefault(x => x.MixingName.ToLower() == "pre skin");
                            item.PreSkinGsmPasteReq = (preSkinItem?.PreSkinGsm ?? 0) > 0 ? preSkinItem.PreSkinGsm * totalqtywithExtra * prefor : 0;

                            var skinItem = insdata.FirstOrDefault(x => x.MixingName.ToLower() == "skin");
                            item.SkinGsmPasteReq = (skinItem?.SkinGsm ?? 0) > 0 ? skinItem.SkinGsm * totalqtywithExtra * prefor : 0;

                            var foamItem = insdata.FirstOrDefault(x => x.MixingName.ToLower() == "foam");
                            item.FoamGsmPasteReq = (foamItem?.FoamGsm ?? 0) > 0 ? foamItem.FoamGsm * totalqtywithExtra * prefor : 0;

                            var adhesiveItem = insdata.FirstOrDefault(x => x.MixingName.ToLower() == "adhesive");
                            item.AdhesiveGsmPasteReq = (adhesiveItem?.AdhesiveGsm ?? 0) > 0 ? adhesiveItem.AdhesiveGsm * totalqtywithExtra * prefor : 0;

                            var FabricItem = insdata.FirstOrDefault(x => x.FabricGsm > 0);
                            item.FabricGSM = (FabricItem?.FabricGsm ?? 0) > 0 ? FabricItem.FabricGsm : 0;
                        }
                    }
                }
            }
            return vmModel;
        }
        public List<WorkPlanOrderVm> GetPostProcessWorkPlanOrderByWorkplanId(long WorkPlanId)
        {
            List<WorkPlanOrderVm> vmModel = new List<WorkPlanOrderVm>();
            using (var db = new pmsdbContext())
            {
                vmModel = (from x in db.WorkPlanOrders
                           where x.WorkplanId == WorkPlanId
                           select new WorkPlanOrderVm
                           {
                               WorkplanId = x.WorkplanId,
                               OrderId = x.OrderId,
                               WorkPlanOrdersId = x.WorkPlanOrdersId,
                               SaleOrder = (from s in db.SaleOrderTables
                                            join a in db.SaleOrderProductionTables on s.SaleOrderId equals a.SaleOrderId
                                            join cust in db.CustomerMasters on s.CustomerId equals cust.CustomerId into scu
                                            from cust in scu.DefaultIfEmpty()
                                            join slpe in db.SaleOrderPostProcessEmbossingTables on s.SaleOrderId equals slpe.SaleOrderId into slpea
                                            from slpe in slpea.DefaultIfEmpty()
                                            join slpp in db.SaleOrderPostProcessPrintTables on s.SaleOrderId equals slpp.SaleOrderId into slppa
                                            from slpp in slppa.DefaultIfEmpty()
                                            join slpv in db.SaleOrderPostProcessVacuumTables on s.SaleOrderId equals slpv.SaleOrderId into slpva
                                            from slpv in slpva.DefaultIfEmpty()
                                            join slpl in db.SaleOrderPostProcessLacquerTables on s.SaleOrderId equals slpl.SaleOrderId into slpla
                                            from slpl in slpla.DefaultIfEmpty()
                                            join slpt in db.SaleOrderPostProcessTumblingTables on s.SaleOrderId equals slpt.SaleOrderId into slpta
                                            from slpt in slpta.DefaultIfEmpty()
                                            where s.SaleOrderId == x.OrderId
                                            select new SaleOrderTableVm
                                            {
                                                SaleOrderId = s.SaleOrderId,
                                                SaleOrderDate = s.SaleOrderDate,
                                                SaleOrderNumber = s.SaleOrderNumber,
                                                CustomerName = cust.CustomerName,
                                                Status = (PmsCommon.ESalesOrderStatus)s.Status,
                                                SaleOrderPostProcessOrder = (from sopp in db.SaleOrderPostProcessOrderTables
                                                                             where sopp.SaleOrderId == s.SaleOrderId && sopp.Removed != true
                                                                             select new SaleOrderPostProcessOrderTableVm
                                                                             {
                                                                                 SaleOrderId = sopp.SaleOrderId,
                                                                                 Rank = sopp.Rank,
                                                                                 PostProcessName = sopp.PostProcessName
                                                                             }).OrderBy(x => x.Rank).ToList(),

                                                SaleOrderProductionPostProcess = new SaleOrderProductionPostProcessVm
                                                {
                                                    SaleOrderProductionId = a.SaleOrderProductionId,
                                                    ManufacturingProductName = a.ManufacturingProductName,

                                                    SaleOrderPostProcessPrint = (from slpp in db.SaleOrderPostProcessPrintTables
                                                                                 where slpp.SaleOrderId == s.SaleOrderId
                                                                                 select new SaleOrderPostProcessPrintTableVm
                                                                                 {
                                                                                     SaleOrderPostProcessPrintId = slpp.SaleOrderPostProcessPrintId,
                                                                                     SaleOrderId = slpp.SaleOrderId,
                                                                                     ReceivedQuantity = slpp.ReceivedQuantity,
                                                                                     PrintCompletedQuantity = slpp.PrintCompletedQuantity,
                                                                                     PrintMeasurementUnit = slpp.PrintMeasurementUnit,
                                                                                     PrintRack = slpp.PrintRack,
                                                                                     PrintWastageQuantity = slpp.PrintWastageQuantity,
                                                                                     PrintStatus = slpp.PrintStatus,
                                                                                     AddedBy = slpp.AddedBy,
                                                                                     AddedDate = slpp.AddedDate,
                                                                                     Rank = slpp.Rank
                                                                                 }).ToList(),

                                                    SaleOrderPostProcessEmbossing = (from slpe in db.SaleOrderPostProcessEmbossingTables
                                                                                     where slpe.SaleOrderId == s.SaleOrderId
                                                                                     select new SaleOrderPostProcessEmbossingTableVm
                                                                                     {
                                                                                         SaleOrderPostProcessEmbossingId = slpe.SaleOrderPostProcessEmbossingId,
                                                                                         SaleOrderId = slpe.SaleOrderId,
                                                                                         ReceivedQuantity = slpe.ReceivedQuantity,
                                                                                         EmbossingCompletedQuantity = slpe.EmbossingCompletedQuantity,
                                                                                         EmbossingMeasurementUnit = slpe.EmbossingMeasurementUnit,
                                                                                         EmbossingRack = slpe.EmbossingRack,
                                                                                         EmbossingWastageQuantity = slpe.EmbossingWastageQuantity,
                                                                                         EmbossingStatus = slpe.EmbossingStatus,
                                                                                         AddedBy = slpe.AddedBy,
                                                                                         AddedDate = slpe.AddedDate,
                                                                                         Rank = slpe.Rank
                                                                                     }).ToList(),

                                                    SaleOrderPostProcessVacuum = (from slpv in db.SaleOrderPostProcessVacuumTables
                                                                                  where slpv.SaleOrderId == s.SaleOrderId
                                                                                  select new SaleOrderPostProcessVacuumTableVm
                                                                                  {
                                                                                      SaleOrderPostProcessVacuumId = slpv.SaleOrderPostProcessVacuumId,
                                                                                      SaleOrderId = slpv.SaleOrderId,
                                                                                      ReceivedQuantity = slpv.ReceivedQuantity,
                                                                                      VacuumCompletedQuantity = slpv.VacuumCompletedQuantity,
                                                                                      VacuumMeasurementUnit = slpv.VacuumMeasurementUnit,
                                                                                      VacuumRack = slpv.VacuumRack,
                                                                                      VacuumWastageQuantity = slpv.VacuumWastageQuantity,
                                                                                      VacuumStatus = slpv.VacuumStatus,
                                                                                      AddedBy = slpv.AddedBy,
                                                                                      AddedDate = slpv.AddedDate,
                                                                                      Rank = slpv.Rank
                                                                                  }).ToList(),

                                                    SaleOrderPostProcessLacquer = (from slpl in db.SaleOrderPostProcessLacquerTables
                                                                                   where slpl.SaleOrderId == s.SaleOrderId
                                                                                   select new SaleOrderPostProcessLacquerTableVm
                                                                                   {
                                                                                       SaleOrderPostProcessLacquerId = slpl.SaleOrderPostProcessLacquerId,
                                                                                       SaleOrderId = slpl.SaleOrderId,
                                                                                       ReceivedQuantity = slpl.ReceivedQuantity,
                                                                                       LacquerCompletedQuantity = slpl.LacquerCompletedQuantity,
                                                                                       LacquerMeasurementUnit = slpl.LacquerMeasurementUnit,
                                                                                       LacquerRack = slpl.LacquerRack,
                                                                                       LacquerWastageQuantity = slpl.LacquerWastageQuantity,
                                                                                       LacquerStatus = slpl.LacquerStatus,
                                                                                       AddedBy = slpl.AddedBy,
                                                                                       AddedDate = slpl.AddedDate,
                                                                                       Rank = slpl.Rank
                                                                                   }).ToList(),

                                                    SaleOrderPostProcessTumbling = (from slpt in db.SaleOrderPostProcessTumblingTables
                                                                                    where slpt.SaleOrderId == s.SaleOrderId
                                                                                    select new SaleOrderPostProcessTumblingTableVm
                                                                                    {
                                                                                        SaleOrderPostProcessTumblingId = slpt.SaleOrderPostProcessTumblingId,
                                                                                        SaleOrderId = slpt.SaleOrderId,
                                                                                        ReceivedQuantity = slpt.ReceivedQuantity,
                                                                                        TumblingCompletedQuantity = slpt.TumblingCompletedQuantity,
                                                                                        TumblingMeasurementUnit = slpt.TumblingMeasurementUnit,
                                                                                        TumblingRack = slpt.TumblingRack,
                                                                                        TumblingWastageQuantity = slpt.TumblingWastageQuantity,
                                                                                        TumblingStatus = slpt.TumblingStatus,
                                                                                        AddedBy = slpt.AddedBy,
                                                                                        AddedDate = slpt.AddedDate,
                                                                                        Rank = slpt.Rank
                                                                                    }).ToList(),

                                                }
                                            }).OrderByDescending(x => x.SaleOrderId).FirstOrDefault()

                           }).OrderByDescending(x => x.WorkPlanOrdersId).ToList();
            }
            return vmModel;
        }
        public List<WorkPlanOrderVm> GetWorkPlanOrderByWorkplanIdForConsume(long WorkPlanId)
        {
            List<ESalesOrderStatus> stsList = new()
            {
                ESalesOrderStatus.NotYet,
                ESalesOrderStatus.WorkPlan,
                ESalesOrderStatus.Inspection,
                ESalesOrderStatus.RawMaterialRequested
            };

            List<WorkPlanOrderVm> vmModel = new List<WorkPlanOrderVm>();
            using (var db = new pmsdbContext())
            {
                vmModel = (from x in db.WorkPlanOrders
                           join cspm in db.ConsumeStockProductMasters on x.OrderId equals cspm.SaleOrderId into cspmd
                           from cspm in cspmd.DefaultIfEmpty()
                           join so in db.SaleOrderTables on x.OrderId equals so.SaleOrderId into sod
                           from so in sod.DefaultIfEmpty()
                           where x.WorkplanId == WorkPlanId
                           && cspm.SaleOrderId == null
                           && !stsList.Contains((ESalesOrderStatus)so.Status)
                           select new WorkPlanOrderVm
                           {
                               WorkplanId = x.WorkplanId,
                               OrderId = x.OrderId,
                               WorkPlanOrdersId = x.WorkPlanOrdersId,
                               SaleOrder = (from s in db.SaleOrderTables
                                            where s.SaleOrderId == x.OrderId
                                            select new SaleOrderTableVm
                                            {
                                                SaleOrderId = s.SaleOrderId,
                                                SaleOrderNumber = s.SaleOrderNumber,
                                                SaleOrderType = s.SaleOrderType
                                            }).OrderByDescending(x => x.SaleOrderId).FirstOrDefault()

                           }).OrderByDescending(x => x.WorkPlanOrdersId).ToList();
            }
            return vmModel;
        }
    }
}
