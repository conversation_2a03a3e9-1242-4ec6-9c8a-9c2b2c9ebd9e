﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;

namespace PmsData.DataFn
{
    public class SuppliertDataFn
    {
        public List<SupplierMasterVm> GetAllSuppliers()
        {
            List<SupplierMasterVm> res = null;
            using (var db = new pmsdbContext())
            {
                res = (from a in db.SupplierMasters
                       select new SupplierMasterVm
                       {
                           SupplierId = a.SupplierId,
                           SupplierName = a.SupplierName,
                           ContactPersonName = a.ContactPersonName,
                           Email = a.Email,
                           SupplierContactNumber = a.SupplierContactNumber,
                           ContactPersonNumber = a.ContactPersonNumber,
                           Address = a.Address,
                           Gst = a.Gst
                       }).OrderBy(x => x.SupplierName).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateSupplier(SupplierMasterVm supplier)
        {
            using (var db = new Models.pmsdbContext())
            {
                SupplierMaster res = new SupplierMaster();
                if (supplier.SupplierId == 0)
                {
                    res.SupplierId = supplier.SupplierId;
                    res.SupplierName = supplier.SupplierName;
                    res.ContactPersonName = supplier.ContactPersonName;
                    res.Email = supplier.Email;
                    res.SupplierContactNumber = supplier.SupplierContactNumber;
                    res.ContactPersonNumber = supplier.ContactPersonNumber;
                    res.Address = supplier.Address;
                    res.Gst = supplier.Gst;
                    db.SupplierMasters.Add(res);
                }
                else
                {
                    res = db.SupplierMasters.Where(x => x.SupplierId == supplier.SupplierId).FirstOrDefault();
                    if (res != null)
                    {
                        res.SupplierId = supplier.SupplierId;
                        res.SupplierName = supplier.SupplierName;
                        res.ContactPersonName = supplier.ContactPersonName;
                        res.Email = supplier.Email;
                        res.SupplierContactNumber = supplier.SupplierContactNumber;
                        res.ContactPersonNumber = supplier.ContactPersonNumber;
                        res.Address = supplier.Address;
                        res.Gst = supplier.Gst;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm DeleteSupplier(long supplierId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var res = db.SupplierMasters.Where(x => x.SupplierId == supplierId).FirstOrDefault();
                if (res != null)
                {
                    db.SupplierMasters.Remove(res);
                    db.SaveChanges();
                }
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Product removed successfully");
            }
        }
    }
}
