﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using PmsData.ViewModel;
using System.Text.RegularExpressions;

namespace PmsData.DataFn
{
    public class MBFormulationDataFn
    {
        public GlobalDataEntity GlobalData;
        public MBFormulationDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<MbformulationMasterVm> GetAllMBFormulationMaster()
        {
            List<MbformulationMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.MbformulationMasters
                       select new MbformulationMasterVm
                       {
                           MbformulationId = a.MbformulationId,
                           MbformulationName = a.MbformulationName,
                           BatchSize = a.BatchSize,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           FinalProductId = a.FinalProductId,
                           MbformulationRawMaterial = (from op in db.MbformulationRawMaterialMasters
                                                       join p in db.ProductMasters on op.RawMaterialProductId equals p.ProductId
                                                       where op.MbformulationId == a.MbformulationId
                                                       select new MbformulationRawMaterialMasterVm
                                                       {
                                                           MbformulationRawMaterialId = op.MbformulationRawMaterialId,
                                                           MbformulationId = a.MbformulationId,
                                                           RawMaterialProductId = op.RawMaterialProductId,
                                                           RawMaterialProductName = p.ProductName,
                                                           RawMaterialProductCode = p.ProductCode,
                                                           QuantityInKg = op.QuantityInKg,
                                                           QuantityBatchSizeInKg = op.QuantityBatchSizeInKg,
                                                           AddedBy = op.AddedBy,
                                                           AddedDate = op.AddedDate,
                                                       }).ToList()
                       }).OrderByDescending(x => x.MbformulationId).ToList();
            }
            return res;
        }
        public ApiFunctionResponseVm AddMBFormulationMaster(MbformulationMasterVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        ProductMaster res = new ProductMaster();
                        if (mix.FinalProductId == 0)
                        {
                            res.ProductName = mix.MbformulationName;
                            //res.ProductType = product.ProductType;
                            //res.ProductCode = product.ProductCode;
                            res.Unit = "Kgs";
                            res.ProductCategoryId = mix.ProductCategoryId;
                            res.ProductFirstSubCategoryId = mix.ProductFirstSubCategoryId;
                            res.ProductSecSubCategoryId = mix.ProductSecSubCategoryId;
                            //res.MinimumQuantity = product.MinimumQuantity;
                            //res.AvgGsm = product.AvgGsm;
                            res.AddedBy = GlobalData.loggedInUser;
                            res.AddedDate = System.DateTime.Now;
                            db.ProductMasters.Add(res);
                            db.SaveChanges();
                        }

                        MbformulationMaster mm = new MbformulationMaster
                        {
                            MbformulationName = mix.MbformulationName,
                            BatchSize = mix.BatchSize,
                            FinalProductId = res.ProductId,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now,
                        };

                        db.MbformulationMasters.Add(mm);
                        db.SaveChanges();
                        foreach (var op in mix.MbformulationRawMaterial)
                        {
                            MbformulationRawMaterialMaster spt = new MbformulationRawMaterialMaster
                            {
                                MbformulationId = mm.MbformulationId,
                                RawMaterialProductId = op.RawMaterialProductId,
                                QuantityInKg = op.QuantityInKg,
                                QuantityBatchSizeInKg = op.QuantityBatchSizeInKg,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                            };
                            db.MbformulationRawMaterialMasters.Add(spt);
                        }
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Selected Pigment MB Added Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }

        public ApiFunctionResponseVm UpdateMBFormulationMaster(MbformulationProductTableVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        MbformulationMaster mm = db.MbformulationMasters.FirstOrDefault(x => x.MbformulationId == mix.MbformulationId);
                        mm.BatchSize = mix.BatchSize;
                        mm.AddedBy = GlobalData.loggedInUser;
                        mm.AddedDate = System.DateTime.Now;
                        db.SaveChanges();
                        var mmrawmaterial = db.MbformulationRawMaterialMasters.Where(x => x.MbformulationId == mix.MbformulationId).ToList();
                        foreach (var mmpr in mmrawmaterial)
                        {
                            if (!mix.MbformulationRawMaterialProduct.Any(x => x.RawMaterialProductId == mmpr.RawMaterialProductId))
                            {
                                db.MbformulationRawMaterialMasters.Remove(mmpr);
                                db.SaveChanges();
                            }
                        }
                        foreach (var rawpr in mix.MbformulationRawMaterialProduct)
                        {
                            MbformulationRawMaterialMaster spt = mmrawmaterial.FirstOrDefault(x => x.RawMaterialProductId == rawpr.RawMaterialProductId);
                            if (spt == null)
                            {
                                spt = new MbformulationRawMaterialMaster
                                {
                                    MbformulationId = mix.MbformulationId,
                                    RawMaterialProductId = rawpr.RawMaterialProductId,
                                    QuantityInKg = rawpr.QuantityInKg,
                                    QuantityBatchSizeInKg = rawpr.QuantityBatchSizeInKg,
                                    AddedBy = GlobalData.loggedInUser,
                                    AddedDate = DateTime.Now,
                                };
                                db.MbformulationRawMaterialMasters.Add(spt);
                            }
                            else
                            {
                                spt.RawMaterialProductId = rawpr.RawMaterialProductId;
                                spt.QuantityInKg = rawpr.QuantityInKg;
                                spt.QuantityBatchSizeInKg = rawpr.QuantityBatchSizeInKg;
                                spt.AddedBy = GlobalData.loggedInUser;
                                spt.AddedDate = DateTime.Now;
                            }
                            db.SaveChanges();
                        }

                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = mix.MbformulationId,
                            TableName = "MbformulationMasters",
                            EntityName = "MbformulationMasters",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                        });
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Selected Pigment MB Updated Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }

        }

        public MbformulationProductTableVm GetMBFormulationProductById(long id)
        {
            MbformulationProductTableVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.MbformulationProductTables
                       where a.MbformulationProductId == id
                       select new MbformulationProductTableVm
                       {
                           MbformulationProductId = a.MbformulationId,
                           MbformulationProductName = a.MbformulationProductName,
                           MbformulationId = a.MbformulationId,
                           Batch = a.Batch,
                           FinalProductId = a.FinalProductId,
                           BatchSize = a.BatchSize,
                           Quantity = a.Quantity,
                           StockId = a.StockId,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           MbformulationRawMaterialProduct = (from op in db.MbformulationRawMaterialProductTables
                                                              join p in db.ProductMasters on op.RawMaterialProductId equals p.ProductId
                                                              where op.MbformulationProductId == a.MbformulationProductId
                                                              select new MbformulationRawMaterialProductTableVm
                                                              {
                                                                  MbformulationRawMaterialProductId = op.MbformulationRawMaterialProductId,
                                                                  MbformulationProductId = a.MbformulationProductId,
                                                                  RawMaterialProductId = op.RawMaterialProductId,
                                                                  RawMaterialProductName = p.ProductName,
                                                                  RawMaterialProductCode = p.ProductCode,
                                                                  QuantityInKg = op.QuantityInKg,
                                                                  QuantityBatchSizeInKg = op.QuantityBatchSizeInKg,
                                                                  AddedBy = op.AddedBy,
                                                                  AddedDate = op.AddedDate,
                                                              }).ToList()
                       }).FirstOrDefault();
            }
            return res;
        }
        public ApiFunctionResponseVm AddMBFormulationProduct(MbformulationProductTableVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        StockDataFn sdf = new StockDataFn(GlobalData);
                        //bool rawMaterialAvailable = true;
                        var stockdata = sdf.GetProductWiseStorestock();
                        ProductMaster res = new ProductMaster();
                        //foreach (var op in mix.MbformulationRawMaterialProduct)
                        //{
                        //    if(!stockdata.Any(x=>x.ProductId == op.RawMaterialProductId && x.Quantity >= op.QuantityInKg))
                        //    {
                        //        rawMaterialAvailable = false;
                        //    }
                        //}
                        //if(!rawMaterialAvailable)
                        //{
                        //    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Raw material is not available in the selected store");
                        //}
                        if (mix.MbformulationId == 0)
                        {
                            if (mix.FinalProductId == 0)
                            {
                                // var prodcat = db.ProductCategoryMasters.FirstOrDefault(x => x.ProductCategory == "MbFormulation");
                                // if (prodcat == null)
                                // {
                                //     prodcat = new ProductCategoryMaster
                                //     {
                                //         ProductCategory = "MbFormulation",
                                //         ProductCategoryDesc = "MbFormulation",
                                //         ProductType = "Raw",
                                //         ProductCategoryAddedBy = GlobalData.loggedInUser,
                                //         ProductCategoryAddedDate = System.DateTime.Now
                                //     };
                                //     db.ProductCategoryMasters.Add(prodcat);
                                //     db.SaveChanges();
                                // }
                                res.ProductName = mix.MbformulationProductName;
                                res.Unit = "Kgs";
                                res.ProductCode = Regex.Replace(mix.MbformulationProductName, @"\s", "");
                                res.ProductCategoryId = mix.ProductCategoryId;
                                res.ProductFirstSubCategoryId = mix.ProductFirstSubCategoryId;
                                res.ProductSecSubCategoryId = mix.ProductSecSubCategoryId;
                                res.ProductType = "Raw";
                                //res.ProductCategoryId = prodcat.ProductCategoryId;
                                res.AddedBy = GlobalData.loggedInUser;
                                res.AddedDate = System.DateTime.Now;
                                db.ProductMasters.Add(res);
                                db.SaveChanges();
                            }

                            MbformulationMaster mmmaster = new MbformulationMaster
                            {
                                MbformulationName = mix.MbformulationProductName,
                                BatchSize = mix.BatchSize,
                                FinalProductId = res.ProductId,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                            };
                            mix.FinalProductId = mmmaster.FinalProductId;
                            mix.MbformulationId = mmmaster.MbformulationId;
                            db.MbformulationMasters.Add(mmmaster);
                            db.SaveChanges();
                            foreach (var op in mix.MbformulationRawMaterialProduct)
                            {
                                MbformulationRawMaterialMaster sptmaster = new MbformulationRawMaterialMaster
                                {
                                    MbformulationId = mmmaster.MbformulationId,
                                    RawMaterialProductId = op.RawMaterialProductId,
                                    QuantityInKg = op.QuantityInKg,
                                    QuantityBatchSizeInKg = op.QuantityBatchSizeInKg,
                                    AddedBy = GlobalData.loggedInUser,
                                    AddedDate = DateTime.Now,
                                };
                                db.MbformulationRawMaterialMasters.Add(sptmaster);
                            }
                            db.SaveChanges();
                        }
                        if (mix.IsMasterUpdate == true)
                        {
                            UpdateMBFormulationMaster(mix);
                        }

                        var supplier = db.SupplierMasters.FirstOrDefault(x => x.SupplierName == "Internal_PigmentMB" && x.Disabled != true);
                        if (supplier == null)
                        {
                            supplier = new SupplierMaster
                            {
                                SupplierName = "Internal_PigmentMB",
                                Email = "Internal",
                                Address = "Internal"
                            };
                            db.SupplierMasters.Add(supplier);
                            db.SaveChanges();
                        }
                        InvoiceMaster im = new InvoiceMaster();
                        im.InvoiceNumber = "Internal_" + (db.InvoiceMasters.OrderByDescending(x => x.InvoiceId).First().InvoiceId + 1);
                        im.SupplierId = supplier.SupplierId;
                        db.InvoiceMasters.Add(im);
                        db.SaveChanges();

                        StockMaster sm = new StockMaster();
                        sm.InvoiceId = im.InvoiceId;
                        sm.InspectionCompleted = true;
                        //sm.IsOpeningStock = true;
                        sm.AllocationCompleted = true;
                        sm.ManageRejectedItemsCompleted = true;
                        sm.StockDate = System.DateTime.Now;
                        sm.AddedBy = GlobalData.loggedInUser;
                        sm.AddedDate = System.DateTime.Now;
                        db.StockMasters.Add(sm);
                        db.SaveChanges();
                        sm.Batch = DateTime.Now.ToString("dd/MM/yyyy") + "/" + sm.StockId;
                        db.SaveChanges();

                        StockProductTable spt = new StockProductTable();
                        spt.StockId = sm.StockId;
                        spt.ProductId = mix.FinalProductId.Value;
                        //spt.Sku = item.Sku;
                        spt.Barcode = "N/A";
                        spt.Quantity = mix.Quantity;
                        spt.ManufacturedDate = DateTime.Now;
                        spt.ExpiryDate = DateTime.Now.AddDays(30);
                        spt.Unit = "Kgs";
                        //spt.PricePerUnit = item.PricePerUnit;
                        spt.Grade = "N/A";
                        spt.AcceptedQuantity = mix.Quantity;
                        db.StockProductTables.Add(spt);
                        db.SaveChanges();
                        var spa = new StockProductAllocationTable()
                        {
                            StockProductId = spt.StockProductId,
                            Quantity = mix.Quantity.Value,
                            InspectionType = PmsCommon.PMSStatus.Accepted,
                            RackId = mix.RackId
                        };
                        db.StockProductAllocationTables.Add(spa);
                        db.SaveChanges();

                        MbformulationProductTable mm = new MbformulationProductTable
                        {
                            MbformulationId = mix.MbformulationId,
                            MbformulationProductName = mix.MbformulationProductName,
                            Batch = sm.Batch,
                            FinalProductId = mix.FinalProductId,
                            BatchSize = mix.BatchSize,
                            Quantity = mix.Quantity,
                            StockId = sm.StockId,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now,
                        };

                        db.MbformulationProductTables.Add(mm);
                        db.SaveChanges();
                        var consumeObj = new ConsumeStockProductDataFn(GlobalData);
                        decimal? tq = 0;
                        decimal? tc = 0;

                        foreach (var op in mix.MbformulationRawMaterialProduct)
                        {
                            MbformulationRawMaterialProductTable mbr = new MbformulationRawMaterialProductTable
                            {
                                MbformulationProductId = mm.MbformulationProductId,
                                RawMaterialProductId = op.RawMaterialProductId,
                                QuantityInKg = op.QuantityInKg,
                                QuantityBatchSizeInKg = op.QuantityBatchSizeInKg,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                            };
                            db.MbformulationRawMaterialProductTables.Add(mbr);
                            foreach (var item in op.ConsumeStockForRawMaterial)
                            {
                                var re = db.StockProductTables.FirstOrDefault(x => x.StockProductId == item.StockProductId);
                                tq = tq + item.Quantity;
                                tc = tc + (item.Quantity * re.PricePerUnit);
                            }
                            consumeObj.AddUpdateConsumeStockProduct(op.ConsumeStockForRawMaterial);
                        }
                        db.SaveChanges();
                        spt.PricePerUnit = tc / tq;
                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Pigment MB Added Successfully.");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
    }
}
