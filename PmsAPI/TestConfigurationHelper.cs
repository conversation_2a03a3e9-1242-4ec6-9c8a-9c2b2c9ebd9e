using System;
using PmsCommon;

namespace TestConfiguration
{
    /// <summary>
    /// Simple test program to verify configuration helper is working
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Configuration Helper Test ===");
            Console.WriteLine();

            // Test development environment detection
            Console.WriteLine($"Is Development Environment: {ConfigurationHelper.IsDevelopmentEnvironment()}");
            Console.WriteLine();

            // Test dev connection string retrieval
            var devConnectionString = ConfigurationHelper.GetDevConnectionString();
            if (!string.IsNullOrEmpty(devConnectionString))
            {
                Console.WriteLine("✅ Dev Connection String: FOUND");
                Console.WriteLine($"   Length: {devConnectionString.Length} characters");
                Console.WriteLine($"   Contains 'pms-mssqlserver-dev': {devConnectionString.Contains("pms-mssqlserver-dev")}");
            }
            else
            {
                Console.WriteLine("❌ Dev Connection String: NOT FOUND");
            }
            Console.WriteLine();

            // Test debug info
            Console.WriteLine("=== Debug Info ===");
            Console.WriteLine(ConfigurationHelper.GetConnectionStringsDebugInfo());
            Console.WriteLine();

            // Test environment variables
            Console.WriteLine("=== Environment Variables ===");
            Console.WriteLine($"ASPNETCORE_ENVIRONMENT: {Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}");
            Console.WriteLine($"AZURE_FUNCTIONS_ENVIRONMENT: {Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT")}");
            Console.WriteLine($"FUNCTIONS_EXTENSION_VERSION: {Environment.GetEnvironmentVariable("FUNCTIONS_EXTENSION_VERSION")}");
            Console.WriteLine();

            Console.WriteLine("=== Test Complete ===");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
