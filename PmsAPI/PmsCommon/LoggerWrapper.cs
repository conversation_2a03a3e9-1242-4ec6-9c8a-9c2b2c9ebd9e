using Microsoft.Extensions.Logging;
using System;

namespace PmsCommon
{
    /// <summary>
    /// A simple wrapper around ILogger to adapt it to ILogger<T>
    /// </summary>
    public class LoggerWrapper<T> : ILogger<T>
    {
        private readonly ILogger _innerLogger;

        public LoggerWrapper(ILogger innerLogger)
        {
            _innerLogger = innerLogger ?? throw new ArgumentNullException(nameof(innerLogger));
        }

        public IDisposable BeginScope<TState>(TState state)
        {
            return _innerLogger.BeginScope(state);
        }

        public bool IsEnabled(LogLevel logLevel)
        {
            return _innerLogger.IsEnabled(logLevel);
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            _innerLogger.Log(logLevel, eventId, state, exception, formatter);
        }
    }
}
