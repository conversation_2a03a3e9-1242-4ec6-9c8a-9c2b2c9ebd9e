using System.Text;
using System.IO;
namespace PmsCommon
{
    public static class CompanyDetails
    {
        public static string CompanyName { get; } = "Zaibunco Industries PVT. LTD.";
        public static string CompanyContact { get; } = "+91-7084423456";
        public static string CompanyEmail { get; } = "<EMAIL>";
        public static string CompanyAddress { get; } = "Head Office :- 611, BLOCK C, DEFENCE COLONY <br/> JAJMAU  KANPUR 208010, Uttar Pradesh, India <br/> Factory Add:- 955, SHESHPUR NARI <br/> BANTHER UNNAO 209862, Uttar Pradesh, India";
        public static string CompanyAddress1 { get; } = "Head Office :- 611, BLOCK C, DEFENCE COLONY";
        public static string CompanyAddress2 { get; } = "JAJMAU  KANPUR 208010, Uttar Pradesh, India";
        public static string CompanyAddress3 { get; } = "Factory Add:- 955, SHESHPUR NARI";
        public static string CompanyAddress4 { get; } = "BANTHER UNNAO 209862, Uttar Pradesh, India";
        public static string CompanyGSTIN { get; } = "09AADCK8892N1ZK";
        public static string CompanyShortAddress { get; } = "955, SHESHPUR NARI, BANTHAR, UNNAO,";
        public static string CompanyShortAddress2ndLine { get; } = "Uttar Pradesh - 209862, India";
        public static string CompanyHeadOfficeAddress { get; } = "611, BLOCK C, DEFENCE COLONY, JAJMAU, KANPUR, Uttar Pradesh - 208010, India";
        public static string CompanyFactoryAddress { get; } = "955, SHESHPUR NARI, BANTHER, UNNAO, Uttar Pradesh - 209862, India";
    }
}