using System;
using System.Net.Http;
using PmsCommon;

public static class ImageHelpers
{
    public static byte[] GetLogoBytes(string companyLogoPath, string LogoContainer)
    {
        try
        {
            var storageAccountName = KeyVault.GetKeyValue("StorageAccountName");
            var storageAccountKey = KeyVault.GetKeyValue("StorageAccountKey");
            var storageContainerName = LogoContainer;
            string storageAccountHost = string.Format("https://{0}.blob.core.windows.net/{1}", storageAccountName, storageContainerName);
            Uri res = CommonFunctions.GetServiceSasUriForBlob(storageAccountName, storageAccountKey, storageContainerName, companyLogoPath);
            var logoUrl = $"{storageAccountHost}/{companyLogoPath}{res.Query}";
            using (var client = new HttpClient())
            {
                return client.GetByteArrayAsync(logoUrl).Result;
            }
        }
        catch (Exception ex)
        {
            return null;
        }
    }
}