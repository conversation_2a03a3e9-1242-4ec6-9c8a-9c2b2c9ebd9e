using Microsoft.Extensions.Configuration;
using System;
using System.IO;

namespace PmsCommon
{
    /// <summary>
    /// Configuration Helper for reading connection strings and app settings
    /// Following PMS common utility patterns
    /// </summary>
    public static class ConfigurationHelper
    {
        private static IConfiguration _configuration;

        /// <summary>
        /// Initialize configuration from local.settings.json or appsettings.json
        /// </summary>
        static ConfigurationHelper()
        {
            try
            {
                var builder = new ConfigurationBuilder();

                // Try to find local.settings.json first (Azure Functions)
                var localSettingsPath = Path.Combine(Directory.GetCurrentDirectory(), "local.settings.json");
                if (File.Exists(localSettingsPath))
                {
                    builder.AddJsonFile("local.settings.json", optional: true, reloadOnChange: true);
                }

                // Try appsettings.json as fallback
                var appSettingsPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                if (File.Exists(appSettingsPath))
                {
                    builder.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                }

                // Add environment variables
                builder.AddEnvironmentVariables();

                _configuration = builder.Build();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing configuration: {ex.Message}");
                // Create empty configuration as fallback
                _configuration = new ConfigurationBuilder().Build();
            }
        }

        /// <summary>
        /// Get development connection string from configuration
        /// </summary>
        /// <returns>Development connection string or null if not found</returns>
        public static string GetDevConnectionString()
        {
            try
            {
                // Try to get from ConnectionStrings section first
                var connectionString = _configuration.GetConnectionString("DevConnectionString");
                
                if (!string.IsNullOrEmpty(connectionString))
                {
                    return connectionString;
                }

                // Try to get from Values section (Azure Functions pattern)
                connectionString = _configuration["Values:DevConnectionString"];
                
                if (!string.IsNullOrEmpty(connectionString))
                {
                    return connectionString;
                }

                // Try direct key
                connectionString = _configuration["DevConnectionString"];
                
                return connectionString;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading dev connection string: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Get any configuration value by key
        /// </summary>
        /// <param name="key">Configuration key</param>
        /// <returns>Configuration value or null if not found</returns>
        public static string GetConfigValue(string key)
        {
            try
            {
                return _configuration[key];
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading config value '{key}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Check if we're running in development environment
        /// </summary>
        /// <returns>True if development environment</returns>
        public static bool IsDevelopmentEnvironment()
        {
            try
            {
                var environment = _configuration["ASPNETCORE_ENVIRONMENT"] ?? 
                                _configuration["Values:ASPNETCORE_ENVIRONMENT"] ??
                                _configuration["AZURE_FUNCTIONS_ENVIRONMENT"] ??
                                _configuration["Values:AZURE_FUNCTIONS_ENVIRONMENT"];

                return !string.IsNullOrEmpty(environment) && 
                       environment.Equals("Development", StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get connection string section for debugging
        /// </summary>
        /// <returns>All connection strings for debugging</returns>
        public static string GetConnectionStringsDebugInfo()
        {
            try
            {
                var connectionStrings = _configuration.GetSection("ConnectionStrings");
                var debugInfo = "Connection Strings:\n";
                
                foreach (var item in connectionStrings.GetChildren())
                {
                    debugInfo += $"  {item.Key}: {(string.IsNullOrEmpty(item.Value) ? "NULL" : "***CONFIGURED***")}\n";
                }

                return debugInfo;
            }
            catch (Exception ex)
            {
                return $"Error getting debug info: {ex.Message}";
            }
        }
    }
}
