using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using PmsCore.PDFGeneration.Models;

public abstract class BaseDocument : IDocument
{
    protected readonly PdfConfiguration Configuration;

    protected BaseDocument(PdfConfiguration configuration)
    {
        Configuration = configuration;
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;
    
    public abstract void Compose(IDocumentContainer container);

    protected IContainer ComposeHeader(IContainer container)
    {
        container.Row(row =>
        {
            if (!string.IsNullOrEmpty(Configuration.CompanyLogo))
            {
                row.AutoItem().Height(50).Image(Configuration.CompanyLogo);
            }
        });
        return container;
    }
}