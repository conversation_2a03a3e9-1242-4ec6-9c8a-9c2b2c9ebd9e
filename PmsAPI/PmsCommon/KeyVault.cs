﻿using System;
using System.Text;
using Microsoft.Azure.KeyVault;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;

namespace PmsCommon
{
    public static class KeyVault
    {
        public static string KeyVaultUrl { get { return Environment.GetEnvironmentVariable("KeyVaultURL"); } }
        public static string GetKeyValue(string key)
        {
            var client = new SecretClient(new Uri(KeyVaultUrl), new DefaultAzureCredential());
            var secret = client.GetSecret(key).Value;
            return Convert.ToString(secret.Value);
        }
    }
}
