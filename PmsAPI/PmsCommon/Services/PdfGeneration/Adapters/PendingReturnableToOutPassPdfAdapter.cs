using System;
using System.Collections.Generic;
using System.Linq;
using PmsCore.PDFGeneration.Models;

namespace PmsCommon.Services.PdfGeneration.Adapters
{
    /// <summary>
    /// Adapter to convert IPendingReturnableOutPassPdfData to IOutPassPdfData
    /// This allows reusing the standard OutPassDocument for reminder notifications
    /// </summary>
    public class PendingReturnableToOutPassPdfAdapter : IOutPassPdfData
    {
        private readonly IPendingReturnableOutPassPdfData _pendingData;

        public PendingReturnableToOutPassPdfAdapter(IPendingReturnableOutPassPdfData pendingData)
        {
            _pendingData = pendingData ?? throw new ArgumentNullException(nameof(pendingData));
        }

        public string DocumentType => "OutPass";
        public long OutpassId => _pendingData.OutpassId;
        public string OutpassNumber => _pendingData.OutpassNumber ?? string.Empty;
        public string OutpassTo => _pendingData.OutpassTo ?? string.Empty;
        public DateTime? OutpassDate => _pendingData.OutpassDate;
        public string OutpassType => "Returnable"; // Default for pending returnable outpasses
        public string Purpose => _pendingData.Purpose ?? string.Empty;
        public string AddedBy => _pendingData.AddedBy ?? string.Empty;
        public DateTime? AddedDate => _pendingData.AddedDate;
        public string Status => _pendingData.Status ?? string.Empty;
        public string Remark => _pendingData.Remark ?? string.Empty;

        public IEnumerable<IOutPassItemData> OutpassItems =>
            _pendingData.OutpassItems?.Select(item => new PendingReturnableToOutPassItemAdapter(item)) ?? Enumerable.Empty<IOutPassItemData>();
    }

    /// <summary>
    /// Adapter to convert IPendingReturnableOutPassItemData to IOutPassItemData
    /// </summary>
    public class PendingReturnableToOutPassItemAdapter : IOutPassItemData
    {
        private readonly IPendingReturnableOutPassItemData _item;

        public PendingReturnableToOutPassItemAdapter(IPendingReturnableOutPassItemData item)
        {
            _item = item ?? throw new ArgumentNullException(nameof(item));
        }

        public long OutpassItemId => _item.OutpassItemId;
        public long? ProductId => _item.ProductId;
        public string ProductName => _item.ProductName ?? string.Empty;
        public decimal? Quantity => _item.Quantity;
        public string Unit => _item.Unit ?? string.Empty;
        public decimal? Amount => _item.Amount;
        public decimal? Total => (_item.Quantity ?? 0) * (_item.Amount ?? 0);
        public string BatchNo => _item.BatchNo ?? string.Empty;
    }
}
