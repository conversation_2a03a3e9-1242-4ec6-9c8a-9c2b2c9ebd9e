using QuestPDF.Fluent;
using PmsCore.PDFGeneration.Interfaces;
using PmsCore.PDFGeneration.Models;
using PmsCore.Notifications.Models;
using Microsoft.Extensions.Logging;
using PmsCommon;
using PmsCommon.Services.PdfGeneration.Documents;
using PmsCommon.Services.PdfGeneration.Adapters;
using System;
using System.IO;
using System.Threading.Tasks;
using QuestPDF.Infrastructure;
using PmsCore.DataAccessRepository.Interfaces;
using Microsoft.Extensions.Options;
using System.Linq;

public class QuestPdfService : IPdfService
{
    private readonly PdfConfiguration _config;
    private readonly ILogger<QuestPdfService> _logger;
    private readonly GlobalDataEntity _globalData;
    private readonly IDataAccessRepository _dataAccessRepository;
    private readonly IStorageRepository _storageRepository;

    public QuestPdfService(
        IOptions<PdfConfiguration> config,
        ILogger<QuestPdfService> logger,
        GlobalDataEntity globalData,
        IDataAccessRepository dataAccessRepository,
        IStorageRepository storageRepository)
    {
        _config = config.Value;
        _logger = logger;
        _globalData = globalData;
        _dataAccessRepository = dataAccessRepository;
        _storageRepository = storageRepository;
        QuestPDF.Settings.License = LicenseType.Community;
        QuestPDF.Settings.EnableDebugging = true; // Enable debugging for layout issues
    }

    public async Task<byte[]> GeneratePdfAsync(IPdfDocumentData data)
    {
        try
        {
            var document = GetDocumentByType(data);
            return document.GeneratePdf();
        }
        catch (QuestPDF.Drawing.Exceptions.DocumentLayoutException ex)
        {
            _logger.LogError(ex, "PDF Layout Error for document type {DocumentType}. Enable QuestPDF debugging for more details.", data.DocumentType);
            throw new InvalidOperationException($"PDF layout error for {data.DocumentType}: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating PDF for document type {DocumentType}", data.DocumentType);
            throw;
        }
    }

    private IDocument GetDocumentByType(IPdfDocumentData data)
    {
        return data switch
        {
            IPurchaseOrderPdfData po => new PurchaseOrderDocument(po, _config, _logger),
            IYieldReportSummeryModel yr => new YieldReportSummaryDocument(yr.AllRecords.ToList(), _config, yr.FromDate, yr.ToDate),
            ILowStockReportPdfData ls => new LowStockReportDocument(ls, _config),
            IPendingReturnableOutPassPdfData pro => new OutPassDocument(
                new PendingReturnableToOutPassPdfAdapter(pro),
                _config,
                isOverdueReminder: true,
                daysOverdue: pro.DaysOverdue),
            IPendingReturnableOutPassReportPdfData prr => new PendingReturnableOutPassReportDocument(prr, _config),
            IOutPassPdfData op => new OutPassDocument(op, _config),
            ICostingReportPdfData cr => new CostingReportDocument(cr, _config),
            // Add other document types here
            _ => throw new ArgumentException($"Unsupported document type: {data.GetType().Name}")
        };
    }

    public async Task<string> GeneratePdfAndUploadToStorageAsync(IPdfDocumentData data, string fileName)
    {
        try
        {
            var pdfBytes = await GeneratePdfAsync(data);

            var blobUri = CommonFunctions.UploadToBlob(
                _config.StorageContainerName,
                fileName,
                pdfBytes
            );

            return blobUri;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating and uploading PDF");
            throw;
        }
    }

    /// <summary>
    /// Generates PDF and uploads to WhatsApp-compatible storage without SAS token
    /// </summary>
    /// <param name="data">The PDF document data</param>
    /// <param name="fileName">The file name</param>
    /// <param name="documentType">The document type for path organization</param>
    /// <returns>Direct blob URL without SAS token for WhatsApp compatibility</returns>
    public async Task<string> GeneratePdfAndUploadToWhatsAppStorageAsync(IPdfDocumentData data, string fileName, string documentType)
    {
        try
        {
            var pdfBytes = await GeneratePdfAsync(data);

            // Use the new overload with WhatsApp-specific settings
            var blobUri = CommonFunctions.UploadToBlob(
                new MemoryStream(pdfBytes),
                fileName,
                documentType,
                appendSasToken: false  // No SAS token for WhatsApp compatibility
            );

            return blobUri;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating and uploading PDF for WhatsApp");
            throw;
        }
    }
}