using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using PmsCore.Notifications.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using PmsCore.PDFGeneration.Models;
using PmsCommon;
using ScottPlot;
using Colors = QuestPDF.Helpers.Colors;

namespace PmsCommon.Services.PdfGeneration.Documents
{
    public class CostingReportDocument : IDocument
    {
        private readonly ICostingReportPdfData _data;
        private readonly PdfConfiguration _config;

        public CostingReportDocument(ICostingReportPdfData data, PdfConfiguration config)
        {
            _data = data;
            _config = config;
        }

        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Size(PageSizes.A4.Landscape());
                    page.Margin(15);
                    page.DefaultTextStyle(x => x.FontSize(8));

                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);
                    page.Footer().Element(ComposeFooter);
                });
        }

        void ComposeHeader(IContainer container)
        {
            var istTime = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);
            var fromDateIST = TimeZoneHelper.ConvertToTimeZone(_data.FromDate, TimeZoneId.IndiaStandardTime);
            var toDateIST = TimeZoneHelper.ConvertToTimeZone(_data.ToDate, TimeZoneId.IndiaStandardTime);

            container.Row(row =>
            {
                // Empty space on left (if no logo)
                var leftWidth = 150f;

                // Logo on the left
                if (_config.CompanyLogoNew != null)
                {
                    var logoBytes = ImageHelpers.GetLogoBytes(_config.CompanyLogoNew, _config.LogoContainer);
                    if (logoBytes != null)
                    {
                        row.ConstantItem(leftWidth)
                           .Height(70)
                           .Padding(10)
                           .Image(logoBytes)
                           .FitArea();
                    }
                }
                else
                {
                    // Keep space even if no logo
                    row.ConstantItem(leftWidth);
                }

                // Title and date in center of remaining space
                row.RelativeItem().Column(column =>
                {
                    column.Item().AlignCenter().Text(CompanyDetails.CompanyName)
                        .FontSize(16).Bold();
                    column.Item().AlignCenter().Text("Production Costing Report")
                        .FontSize(14).Bold()
                        .FontColor(Colors.Blue.Medium);
                    column.Item().AlignCenter().PaddingTop(5)
                        .Text($"Period: {fromDateIST:dd-MMM-yyyy} to {toDateIST:dd-MMM-yyyy}")
                        .FontSize(10);
                    column.Item().AlignCenter().PaddingTop(5)
                        .Text($"Total Records: {_data.Summary.TotalRecords}")
                        .FontSize(10).SemiBold();
                    column.Item().PaddingVertical(5);
                });

                // Empty space on right to balance the layout
                row.ConstantItem(leftWidth);
            });
        }

        void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {
                // Executive Summary Section
                column.Item().Element(ComposeExecutiveSummary);
                column.Item().PaddingVertical(5); // Reduced padding

                // Visual Analytics Section
                column.Item().Element(ComposeAnalytics);
                column.Item().PaddingVertical(5); // Reduced padding

                // Top Products Section - Use page break before if needed
                column.Item().PageBreak();
                column.Item().Element(ComposeTopProducts);
                column.Item().PaddingVertical(5); // Reduced padding

                // Detailed Data Table
                column.Item().Element(ComposeDetailedTable);
            });
        }

        void ComposeExecutiveSummary(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().Text("Executive Summary")
                    .FontSize(16)
                    .Bold()
                    .FontColor(Colors.Blue.Medium);

                column.Item().PaddingTop(10).Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn(2);
                        columns.RelativeColumn(1);
                        columns.RelativeColumn(2);
                        columns.RelativeColumn(1);
                    });

                    // Header
                    table.Header(header =>
                    {
                        header.Cell().Element(CellStyle).Text("Metric").Bold();
                        header.Cell().Element(CellStyle).Text("Value").Bold();
                        header.Cell().Element(CellStyle).Text("Metric").Bold();
                        header.Cell().Element(CellStyle).Text("Value").Bold();
                    });

                    // Data rows
                    table.Cell().Element(CellStyle).Text("Grand Total Order Qty");
                    table.Cell().Element(CellStyle).Text($"{_data.Summary.GrandTotalOrderQty:N0} MTRs");
                    table.Cell().Element(CellStyle).Text("Grand Total Cost");
                    table.Cell().Element(CellStyle).Text($"₹{_data.Summary.GrandTotalCost:N2}");

                    table.Cell().Element(CellStyle).Text("Grand Total Manufactured Qty");
                    table.Cell().Element(CellStyle).Text($"{_data.Summary.GrandTotalManufacturedQty:N0} MTRs");
                    table.Cell().Element(CellStyle).Text("Average Profit/Loss /LM");
                    table.Cell().Element(CellStyle).Text($"₹{_data.Summary.AverageProfitLossLm:N2}");

                    table.Cell().Element(CellStyle).Text("Total Average Rejection %");
                    table.Cell().Element(CellStyle).Text($"{_data.Summary.TotalAverageRejectionPercent:N2}%");
                    table.Cell().Element(CellStyle).Text("Grand Total Profit/Loss");
                    table.Cell().Element(CellStyle).Text($"₹{_data.Summary.GrandTotalProfitLoss:N2}");

                    table.Cell().Element(CellStyle).Text("Average Overhead Cost /LM");
                    table.Cell().Element(CellStyle).Text($"₹{_data.Summary.AverageOverheadCostLm:N2}");
                    table.Cell().Element(CellStyle).Text("");
                    table.Cell().Element(CellStyle).Text("");
                });
            });
        }

        void ComposeAnalytics(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().Text("Visual Analytics")
                    .FontSize(14) // Smaller title
                    .Bold()
                    .FontColor(Colors.Blue.Medium);

                column.Item().PaddingTop(5).Row(row => // Reduced padding
                {
                    // Left side - Profit vs Loss Distribution with visual chart
                    row.RelativeItem().Column(col =>
                    {
                        col.Item().Text("Profit vs Loss Distribution")
                            .FontSize(10) // Smaller font
                            .SemiBold();

                        // Visual pie chart representation
                        col.Item().PaddingTop(5).AlignCenter() // Reduced padding
                            .Element(container => ComposePieChart(container, _data.Analytics.ProfitPercentage, _data.Analytics.LossPercentage));

                        col.Item().PaddingTop(5).Column(textCol => // Reduced padding
                        {
                            textCol.Item().Text($"Profit: {_data.Analytics.ProfitPercentage:N1}%")
                                .FontColor(Colors.Green.Medium)
                                .FontSize(9); // Smaller font

                            textCol.Item().Text($"Loss: {_data.Analytics.LossPercentage:N1}%")
                                .FontColor(Colors.Red.Medium)
                                .FontSize(9); // Smaller font
                        });
                    });

                    // Right side - Order Fulfillment Analysis with bar chart
                    row.RelativeItem().Column(col =>
                    {
                        col.Item().Text("Order Fulfillment Analysis")
                            .FontSize(10) // Smaller font
                            .SemiBold();

                        // Visual bar chart representation
                        col.Item().PaddingTop(5).AlignCenter() // Reduced padding
                            .Element(container => ComposeBarChart(container, _data.Analytics.OrderFulfillmentRate));

                        col.Item().PaddingTop(5).Column(textCol => // Reduced padding
                        {
                            textCol.Item().Text($"Fulfillment Rate: {_data.Analytics.OrderFulfillmentRate:N1}%")
                                .FontSize(9); // Smaller font
                            textCol.Item().Text($"Average Order Value: ₹{_data.Analytics.AverageOrderValue:N2}")
                                .FontSize(9); // Smaller font
                        });
                    });
                });
            });
        }

        void ComposePieChart(IContainer container, decimal profitPercentage, decimal lossPercentage)
        {
            container.Width(100).Height(100) // Reduced size significantly
                .Svg(size =>
                {
                    try
                    {
                        ScottPlot.Plot plot = new();

                        // Prepare data for pie chart
                        var slices = new List<PieSlice>();

                        if (profitPercentage > 0)
                        {
                            slices.Add(new PieSlice
                            {
                                Value = (double)profitPercentage,
                                FillColor = new ScottPlot.Color(Colors.Green.Medium.Hex),
                                Label = $"{profitPercentage:N0}%" // Simplified labels
                            });
                        }

                        if (lossPercentage > 0)
                        {
                            slices.Add(new PieSlice
                            {
                                Value = (double)lossPercentage,
                                FillColor = new ScottPlot.Color(Colors.Red.Medium.Hex),
                                Label = $"{lossPercentage:N0}%" // Simplified labels
                            });
                        }

                        // Add remaining percentage if any
                        var remainingPercentage = 100 - profitPercentage - lossPercentage;
                        if (remainingPercentage > 0)
                        {
                            slices.Add(new PieSlice
                            {
                                Value = (double)remainingPercentage,
                                FillColor = new ScottPlot.Color(Colors.Grey.Lighten2.Hex),
                                Label = $"{remainingPercentage:N0}%" // Simplified labels
                            });
                        }

                        if (slices.Any())
                        {
                            var pie = plot.Add.Pie(slices.ToArray());
                            pie.DonutFraction = 0.3; // Smaller donut for better fit
                            pie.SliceLabelDistance = 1.1; // Closer labels
                            pie.LineColor = ScottPlot.Colors.White;
                            pie.LineWidth = 0.5f; // Thinner lines

                            // Style the labels with smaller font
                            foreach (var pieSlice in pie.Slices)
                            {
                                pieSlice.LabelStyle.FontName = "Arial";
                                pieSlice.LabelStyle.FontSize = 8; // Much smaller font
                                pieSlice.LabelStyle.Bold = false; // Remove bold to save space
                            }
                        }

                        plot.Axes.Frameless();
                        plot.HideGrid();
                        plot.Layout.Frameless();

                        return plot.GetSvgXml(100, 100); // Smaller fixed size
                    }
                    catch
                    {
                        // Fallback to simple rectangle if ScottPlot fails
                        return "<svg width='100' height='100'><rect width='100' height='100' fill='lightgray'/><text x='50' y='50' text-anchor='middle' fill='black' font-size='10'>Chart Error</text></svg>";
                    }
                });
        }

        void ComposeBarChart(IContainer container, decimal fulfillmentRate)
        {
            container.Width(200).Height(100) // Reduced size significantly
                .Svg(size =>
                {
                    try
                    {
                        ScottPlot.Plot plot = new();

                        // Create bar chart data
                        var bars = new Bar[]
                        {
                            new()
                            {
                                Position = 1,
                                Value = (double)fulfillmentRate,
                                FillColor = new ScottPlot.Color(Colors.Blue.Medium.Hex),
                                LineWidth = 0,
                                Size = 0.5 // Smaller bar
                            }
                        };

                        plot.Add.Bars(bars);

                        // Customize the chart with proper axis labels
                        plot.Axes.Bottom.TickGenerator = new ScottPlot.TickGenerators.NumericManual(new Tick[]
                        {
                            new(1, "Fulfillment") // Shorter label
                        });

                        plot.Axes.Bottom.MajorTickStyle.Length = 0;
                        plot.Axes.Bottom.TickLabelStyle.FontName = "Arial";
                        plot.Axes.Bottom.TickLabelStyle.FontSize = 8; // Smaller font
                        plot.Axes.Bottom.TickLabelStyle.Bold = false;

                        // Set proper axis labels with smaller fonts
                        plot.Axes.Bottom.Label.Text = "Order Fulfillment Rate";
                        plot.Axes.Bottom.Label.FontName = "Arial";
                        plot.Axes.Bottom.Label.FontSize = 9; // Smaller font
                        plot.Axes.Bottom.Label.Bold = false;

                        plot.Axes.Left.Label.Text = "Percentage (%)";
                        plot.Axes.Left.Label.FontName = "Arial";
                        plot.Axes.Left.Label.FontSize = 9; // Smaller font
                        plot.Axes.Left.Label.Bold = false;
                        plot.Axes.SetLimitsY(0, 100);

                        // Add value label on top of bar
                        var textLabel = plot.Add.Text($"{fulfillmentRate:N1}%", 1, (double)fulfillmentRate + 3);
                        textLabel.LabelStyle.FontSize = 10; // Smaller font
                        textLabel.LabelStyle.Bold = false;
                        textLabel.LabelStyle.FontName = "Arial";

                        plot.Grid.XAxisStyle.IsVisible = false;
                        plot.Axes.Margins(bottom: 0.2f, top: 0.15f, left: 0.2f, right: 0.1f);
                        plot.Layout.Frameless();

                        return plot.GetSvgXml(200, 100); // Smaller fixed size
                    }
                    catch
                    {
                        // Fallback to simple rectangle if ScottPlot fails
                        return "<svg width='200' height='100'><rect width='200' height='100' fill='lightblue'/><text x='100' y='50' text-anchor='middle' fill='black' font-size='10'>Chart Error</text></svg>";
                    }
                });
        }

        void ComposeTopProducts(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().Text("Top 10 Products by Sales Volume")
                    .FontSize(14) // Smaller title
                    .Bold()
                    .FontColor(Colors.Blue.Medium);

                if (_data.TopProducts?.Any() == true)
                {
                    // Professional bar chart for top 5 products
                    column.Item().PaddingTop(5).Column(chartColumn => // Reduced padding
                    {
                        chartColumn.Item().PaddingBottom(5).Text("Top 5 Products - Sales Volume Comparison") // Reduced padding
                            .FontSize(10) // Smaller font
                            .SemiBold();

                        var topFive = _data.TopProducts.Take(5).ToList();

                        if (topFive.Any())
                        {
                            chartColumn.Item().Width(400).Height(150) // Further reduced size
                                .Svg(size =>
                                {
                                    try
                                    {
                                        ScottPlot.Plot plot = new();

                                        // Create horizontal bar chart data
                                        var bars = new List<Bar>();
                                        for (int i = 0; i < topFive.Count; i++)
                                        {
                                            var product = topFive[i];
                                            bars.Add(new Bar
                                            {
                                                Position = i + 1,
                                                Value = (double)product.SalesVolume,
                                                FillColor = new ScottPlot.Color(i == 0 ? Colors.Blue.Darken1.Hex : Colors.Blue.Lighten1.Hex),
                                                LineWidth = 0,
                                                Size = 0.6 // Reduced size for better fit
                                            });
                                        }

                                        plot.Add.Bars(bars.ToArray());

                                        // Create custom tick labels with product names
                                        var ticks = new List<Tick>();
                                        for (int i = 0; i < topFive.Count; i++)
                                        {
                                            var productName = topFive[i].ProductName.Length > 15 ?
                                                topFive[i].ProductName[..15] + "..." : topFive[i].ProductName;
                                            ticks.Add(new Tick(i + 1, productName));
                                        }

                                        plot.Axes.Bottom.TickGenerator = new ScottPlot.TickGenerators.NumericManual(ticks.ToArray());
                                        plot.Axes.Bottom.MajorTickStyle.Length = 0;
                                        plot.Axes.Bottom.TickLabelStyle.FontName = "Arial";
                                        plot.Axes.Bottom.TickLabelStyle.FontSize = 7; // Smaller font
                                        plot.Axes.Bottom.TickLabelStyle.Rotation = -25; // Less rotation

                                        // Proper axis labels with smaller fonts
                                        plot.Axes.Bottom.Label.Text = "Products";
                                        plot.Axes.Bottom.Label.FontName = "Arial";
                                        plot.Axes.Bottom.Label.FontSize = 9; // Smaller font
                                        plot.Axes.Bottom.Label.Bold = false;

                                        plot.Axes.Left.Label.Text = "Sales Volume (MTRs)";
                                        plot.Axes.Left.Label.FontName = "Arial";
                                        plot.Axes.Left.Label.FontSize = 9; // Smaller font
                                        plot.Axes.Left.Label.Bold = false;

                                        // Add value labels on top of bars
                                        for (int i = 0; i < topFive.Count; i++)
                                        {
                                            var product = topFive[i];
                                            var text = plot.Add.Text($"{product.SalesVolume:N0}", i + 1, (double)product.SalesVolume + (double)product.SalesVolume * 0.03);
                                            text.LabelStyle.FontSize = 7; // Much smaller font
                                            text.LabelStyle.Bold = false;
                                            text.LabelStyle.FontName = "Arial";
                                            text.LabelAlignment = Alignment.MiddleCenter;
                                        }

                                        plot.Grid.XAxisStyle.IsVisible = false;
                                        plot.Axes.Margins(bottom: 0.3f, top: 0.1f, left: 0.15f, right: 0.05f);
                                        plot.Layout.Frameless();

                                        return plot.GetSvgXml(400, 150); // Much smaller size
                                    }
                                    catch
                                    {
                                        // Fallback to simple rectangle if ScottPlot fails
                                        return "<svg width='400' height='150'><rect width='400' height='150' fill='lightblue'/><text x='200' y='75' text-anchor='middle' fill='black' font-size='10'>Chart Error</text></svg>";
                                    }
                                });
                        }
                    });

                    // Detailed table
                    column.Item().PaddingTop(20).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.ConstantColumn(30);
                            columns.RelativeColumn(3);
                            columns.RelativeColumn(2);
                            columns.RelativeColumn(2);
                            columns.RelativeColumn(1);
                        });

                        table.Header(header =>
                        {
                            header.Cell().Element(CellStyle).Text("Rank").Bold();
                            header.Cell().Element(CellStyle).Text("Product Name").Bold();
                            header.Cell().Element(CellStyle).Text("Sales Volume").Bold();
                            header.Cell().Element(CellStyle).Text("Sales Value").Bold();
                            header.Cell().Element(CellStyle).Text("Orders").Bold();
                        });

                        for (int i = 0; i < Math.Min(10, _data.TopProducts.Count); i++)
                        {
                            var product = _data.TopProducts[i];
                            table.Cell().Element(CellStyle).Text($"{i + 1}");
                            table.Cell().Element(CellStyle).Text(product.ProductName);
                            table.Cell().Element(CellStyle).Text($"{product.SalesVolume:N0} MTRs");
                            table.Cell().Element(CellStyle).Text($"₹{product.SalesValue:N2}");
                            table.Cell().Element(CellStyle).Text($"{product.OrderCount}");
                        }
                    });
                }
                else
                {
                    column.Item().PaddingTop(10).Text("No product data available")
                        .FontColor(Colors.Grey.Medium);
                }
            });
        }

        void ComposeDetailedTable(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().Text("Detailed Costing Data")
                    .FontSize(14) // Reduced font size for consistency
                    .Bold()
                    .FontColor(Colors.Blue.Medium);

                if (_data.CostingData?.Any() == true)
                {
                    // Use PageBreak before table to ensure it starts on a fresh page if needed
                    column.Item().PageBreak();

                    column.Item().PaddingTop(5).Table(table => // Reduced padding
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.ConstantColumn(25); // S.No.
                            columns.RelativeColumn(1); // Sale Order No.
                            columns.RelativeColumn(3); // Customer Name
                            columns.RelativeColumn(2); // Alias
                            columns.RelativeColumn(1); // Category
                            columns.RelativeColumn(1); // Order QTY
                            columns.RelativeColumn(1); // MFD Qty
                            columns.RelativeColumn(1); // Rejection %
                            columns.RelativeColumn(1); // Sale Price
                            columns.RelativeColumn(1); // Total Cost /LM
                            columns.RelativeColumn(1); // Final Total Cost
                            columns.RelativeColumn(1); // Profit/Loss /LM
                            columns.RelativeColumn(1); // Total Profit/Loss
                        });

                        // Configure table to repeat headers on each page and keep rows together
                        table.ExtendLastCellsToTableBottom();

                        // Header that will be repeated on each page
                        table.Header(header =>
                        {
                            header.Cell().Element(CellStyle).Text("S.No.").Bold();
                            header.Cell().Element(CellStyle).Text("Sale Order No.").Bold();
                            header.Cell().Element(CellStyle).Text("Customer Name").Bold();
                            header.Cell().Element(CellStyle).Text("Alias").Bold();
                            header.Cell().Element(CellStyle).Text("Category").Bold();
                            header.Cell().Element(CellStyle).Text("Order QTY").Bold();
                            header.Cell().Element(CellStyle).Text("MFD Qty").Bold();
                            header.Cell().Element(CellStyle).Text("Rejection %").Bold();
                            header.Cell().Element(CellStyle).Text("Sale Price").Bold();
                            header.Cell().Element(CellStyle).Text("Total Cost /LM").Bold();
                            header.Cell().Element(CellStyle).Text("Final Total Cost").Bold();
                            header.Cell().Element(CellStyle).Text("Profit/Loss /LM").Bold();
                            header.Cell().Element(CellStyle).Text("Total Profit/Loss").Bold();
                        });

                        foreach (var item in _data.CostingData)
                        {
                            table.Cell().Element(CellStyle).Text($"{item.SerialNo}");
                            table.Cell().Element(CellStyle).Text(item.SaleOrderNo);
                            table.Cell().Element(CellStyle).Text(item.CustomerName);
                            table.Cell().Element(CellStyle).Text(item.Alias);
                            table.Cell().Element(CellStyle).Text(item.Category);
                            table.Cell().Element(CellStyle).Text($"{item.OrderQty:N0}");
                            table.Cell().Element(CellStyle).Text($"{item.MfdQty:N0}");
                            table.Cell().Element(CellStyle).Text($"{item.RejectionPercent:N2}%");
                            table.Cell().Element(CellStyle).Text($"₹{item.SalePrice:N2}");
                            table.Cell().Element(CellStyle).Text($"₹{item.TotalCostLm:N2}");
                            table.Cell().Element(CellStyle).Text($"₹{item.FinalTotalCost:N2}");

                            // Color-code profit/loss for better readability
                            var profitLossColor = item.ProfitLossLm >= 0 ? Colors.Green.Darken1 : Colors.Red.Darken1;
                            var totalProfitLossColor = item.TotalProfitLoss >= 0 ? Colors.Green.Darken1 : Colors.Red.Darken1;

                            table.Cell().Element(CellStyle).Text($"₹{item.ProfitLossLm:N2}")
                                .FontColor(profitLossColor).Bold();
                            table.Cell().Element(CellStyle).Text($"₹{item.TotalProfitLoss:N2}")
                                .FontColor(totalProfitLossColor).Bold();
                        }
                    });
                }
                else
                {
                    column.Item().PaddingTop(10).Text("No costing data available")
                        .FontColor(Colors.Grey.Medium);
                }
            });
        }

        void ComposeFooter(IContainer container)
        {
            var istTime = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);

            container.Row(row =>
            {
                // Generated on text (centered)
                row.RelativeItem().AlignCenter().Text(text =>
                {
                    text.Span("Generated on: ").FontSize(9);
                    text.Span(istTime.ToString("dd-MMM-yyyy HH:mm")).FontSize(9);
                });

                // Page numbers (right aligned)
                row.RelativeItem().AlignRight().Text(text =>
                {
                    text.CurrentPageNumber().FontSize(9);
                    text.Span(" / ").FontSize(9);
                    text.TotalPages().FontSize(9);
                });
            });
        }

        static IContainer CellStyle(IContainer container)
        {
            return container
                .Border(1)
                .BorderColor(Colors.Grey.Lighten2)
                .Padding(5)
                .AlignCenter()
                .AlignMiddle();
        }
    }
}
