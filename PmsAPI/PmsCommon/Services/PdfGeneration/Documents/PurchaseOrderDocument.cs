using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using PmsCore.PDFGeneration.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
namespace PmsCommon.Services.PdfGeneration.Documents
{
    public class PurchaseOrderDocument : BaseDocument
    {
        private readonly IPurchaseOrderPdfData _po;
        private readonly PdfConfiguration _config;
        private readonly ILogger _logger;
        public PurchaseOrderDocument(IPurchaseOrderPdfData po, PdfConfiguration configuration, ILogger logger)
            : base(configuration)
        {
            _po = po;
            _config = configuration;
            _logger = logger;
        }

        public override void Compose(IDocumentContainer container)
        {
            container.Page(page =>
            {
                page.Margin(20);
                page.Size(PageSizes.A4);

                page.Header().ShowOnce().Element(ComposeHeaderSection);

                // Combine content and footer in main content area
                page.Content().Element(container =>
                {
                    container.Column(column =>
                    {
                        // Main content
                        column.Item().Element(ComposeContent);

                        // Spacer to push footer to bottom
                        column.Item().ExtendHorizontal().Height(50);

                        // Footer content
                        column.Item().Column(footer =>
                        {
                            footer.Item().AlignCenter().Text($"For any enquiry please contact {_po.ContactPersonName} {_po.ContactPersonNumber}");

                            footer.Item().PaddingTop(20).Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn();
                                    columns.RelativeColumn();
                                    columns.RelativeColumn();
                                });

                                table.Cell().Column(column =>
                                {
                                    column.Item().Text("PREPARED BY:").Bold();
                                    column.Item().Padding(20).Text(_po.AddedByName).Italic().FontFamily("Comic Sans MS");
                                    column.Item().Text("Auth Signatory");
                                });

                                table.Cell().Column(column =>
                                {
                                    column.Item().Text("APPROVED BY:").Bold();
                                    column.Item().Padding(20).Text(_po.ApprovedByName?.ToUpper()).Italic().FontFamily("Comic Sans MS");
                                    column.Item().Text("Auth Signatory");
                                });

                                table.Cell().Column(column =>
                                {
                                    column.Item().Text("DIRECTOR'S SIGNATURE").Bold();
                                    column.Item().PaddingTop(50).Text("Auth Signatory");
                                });
                            });

                            if (!string.IsNullOrEmpty(_po.ApprovedByName))
                            {
                                footer.Item().AlignCenter().PaddingTop(10)
                                    .Text("NOTE: This is a digitally generated and approved document. No physical sign required.")
                                    .FontSize(9);
                            }

                            footer.Item().AlignCenter()
                                .Text("Powered By: KanzenFlow")
                                .FontSize(9);
                        });
                    });
                });
            });
        }

        private void ComposeHeaderSection(IContainer container)
        {
            container.Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(2);  // Company details
                    columns.RelativeColumn(1);  // PO details
                });

                // Company Details - Left Column
                table.Cell().PaddingBottom(10).Column(column =>
                {
                    column.Item().Row(row =>
                    {
                        var logoBytes = ImageHelpers.GetLogoBytes(_config.CompanyLogo, _config.LogoContainer);
                        if (logoBytes != null)
                        {
                            row.AutoItem()
                               .Width(150)
                               .Height(60)
                               .PaddingBottom(10)
                               .Image(logoBytes)
                               .FitArea();
                        }
                    });
                    column.Item().Text(CompanyDetails.CompanyName).Bold().FontSize(18);
                    column.Item().Text(CompanyDetails.CompanyAddress1);
                    column.Item().Text(CompanyDetails.CompanyAddress2);
                    column.Item().Text(CompanyDetails.CompanyAddress3);
                    column.Item().Text(CompanyDetails.CompanyAddress4);
                    column.Item().Text("Contact: " + CompanyDetails.CompanyContact);
                    column.Item().Text("Email: " + CompanyDetails.CompanyEmail);
                    column.Item().Text("GSTIN: " + CompanyDetails.CompanyGSTIN);

                    // Vendor Details
                    column.Item().PaddingTop(10).Text("VENDOR").Bold();
                    column.Item().Text(_po.SupplierName.ToUpper()).Bold();
                    column.Item().Text(_po.SupplierAddress);
                    column.Item().Text("Contact: " + _po.SupplierContactNumber);
                    column.Item().Text("Email: " + _po.SupplierEmail);
                    column.Item().Text("GSTIN: " + _po.SupplierGSTIN);
                });

                // PO Details - Right Column
                table.Cell().Column(column =>
                {
                    column.Item().Text("PURCHASE ORDER").SemiBold().FontSize(18);
                    column.Item().PaddingTop(10).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn();
                            columns.RelativeColumn();
                        });

                        table.Cell().Text("PO No");
                        table.Cell().Text(_po.Ponumber);

                        table.Cell().Text("PO Date");
                        table.Cell().Text(_po.PocreationDate?.ToString("dd-MMM-yyyy"));

                        table.Cell().Text("Delivery Date");
                        table.Cell().Text(_po.DeliveryDate?.ToString("dd-MMM-yyyy"));

                        table.Cell().Text("Payment Terms");
                        table.Cell().Text(_po.PaymentTerm);

                        table.Cell().Text("Transport");
                        table.Cell().Text(_po.TransportName);

                        table.Cell().Text("PO Type");
                        table.Cell().Text(_po.Status);
                    });
                });
            });
        }

        private void ComposeContent(IContainer container)
        {
            container.PaddingVertical(10).Column(column =>
            {
                // Products Table
                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(30);     // #
                        columns.RelativeColumn(3);      // Description
                        columns.RelativeColumn(1);      // QTY
                        columns.RelativeColumn(1);      // UNIT
                        columns.RelativeColumn(1);      // PRICE
                        columns.RelativeColumn(2);    // AMOUNT (INR)
                        columns.RelativeColumn(1);      // IGST/TAX
                    });

                    // Header
                    table.Header(header =>
                    {
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(2).Text("#").Bold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(2).Text("Description").Bold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(2).Text("QTY").Bold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(2).Text("UNIT").Bold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(2).Text("PRICE").Bold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(2).Text("AMOUNT (INR)").Bold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(2).Text("IGST/TAX").Bold();
                    });

                    // Products
                    var index = 1;
                    foreach (var product in _po.PurchaseOrderProduct)
                    {
                        table.Cell().Border(1).BorderColor(Colors.Grey.Medium).Padding(2).Text(index.ToString());
                        table.Cell().Border(1).BorderColor(Colors.Grey.Medium).Padding(2).Text(product.ProductName);
                        table.Cell().Border(1).BorderColor(Colors.Grey.Medium).Padding(2).AlignRight().Text(product.Quantity?.ToString("N2"));
                        table.Cell().Border(1).BorderColor(Colors.Grey.Medium).Padding(2).Text(product.Unit);
                        table.Cell().Border(1).BorderColor(Colors.Grey.Medium).Padding(2).AlignRight().Text(product.Rate?.ToString("N2"));
                        table.Cell().Border(1).BorderColor(Colors.Grey.Medium).Padding(2).AlignRight().Text(product.Amount?.ToString("N2"));
                        table.Cell().Border(1).BorderColor(Colors.Grey.Medium).Padding(2).AlignCenter().Text(product.Igst ?? "N/A");
                        index++;
                    }
                });

                // Totals and Comments Section
                column.Item().PaddingTop(10).Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn(3);  // Left side - Comments
                        columns.RelativeColumn(2);  // Right side - Totals
                    });

                    // Left side - Amount in words and comments
                    table.Cell().Column(column =>
                    {
                        column.Item().Text($"(In words) (INR)-{_po.AmountInWords}").Bold();
                        column.Item().PaddingTop(5).PaddingBottom(5).Text("Other Comments or Special Instruction:").Bold();
                        column.Item().Border(1).MinHeight(70).Padding(4).Text(_po.Remarks);
                    });

                    // Right side - Totals
                    table.Cell().PaddingLeft(20).Table(totalsTable =>
                    {
                        totalsTable.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(2);  // Label
                            columns.RelativeColumn(1);  // Value
                        });

                        AddTotalRowContent(totalsTable, "Sub Total", _po.PototalAmount.ToString("N2"));
                        AddTotalRowContent(totalsTable, "Total IGST/TAX", _po.PototalTax == 0 ? "N/A" : _po.PototalTax.ToString("N2"));
                        AddTotalRowContent(totalsTable, "S & H", "0.00");
                        AddTotalRowContent(totalsTable, "Freight & Insurance", "0.00");
                        AddTotalRowContent(totalsTable, "Other", "0.00");
                        AddTotalRowContent(totalsTable, "Net Amount(INR)", _po.Pograndtotal.ToString("N2"), true);
                    });
                });

                // Notes
                column.Item().PaddingTop(5).Text("1. Test report should be accompanied with material without which we will not accept the consignment.");
                column.Item().Text("2. It is necessary to attach the purchase order with invoice/challan.");
            });
        }

        private static void AddTotalRowContent(TableDescriptor table, string label, string value, bool isFinal = false)
        {
            if (isFinal)
            {
                table.Cell().BorderTop(1).Text(label).Bold();
                table.Cell().BorderTop(1).AlignRight().Text(value).Bold();
            }
            else
            {
                table.Cell().Text(label);
                table.Cell().AlignRight().Text(value);
            }
        }
    }
}