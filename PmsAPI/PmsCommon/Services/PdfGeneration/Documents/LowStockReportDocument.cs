using System;
using System.Collections.Generic;
using System.Linq;
using PmsCommon;
using PmsCore.PDFGeneration.Models;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace PmsCommon.Services.PdfGeneration.Documents
{
    public class LowStockReportDocument : IDocument
    {
        private readonly ILowStockReportPdfData _data;
        private readonly PdfConfiguration _config;

        public LowStockReportDocument(ILowStockReportPdfData data, PdfConfiguration configuration)
        {
            _data = data;
            _config = configuration;
        }

        public void Compose(IDocumentContainer container)
        {
            var istTime = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);

            container.Page(page =>
            {
                page.Margin(20);
                page.Size(PageSizes.A4.Portrait());

                page.Header().Element(ComposeHeader);
                page.Content().Element(ComposeContent);
                page.Footer().Row(row =>
                {
                    // Generated on text (centered)
                    row.RelativeItem().AlignCenter().Text(text =>
                    {
                        text.Span("Generated on: ").FontSize(9);
                        text.Span(istTime.ToString("dd-MMM-yyyy HH:mm")).FontSize(9);
                    });

                    // Page numbers (right aligned)
                    row.RelativeItem().AlignRight().Text(text =>
                    {
                        text.CurrentPageNumber().FontSize(9);
                        text.Span(" / ").FontSize(9);
                        text.TotalPages().FontSize(9);
                    });
                });
            });
        }

        private void ComposeHeader(IContainer container)
        {
            container.Row(row =>
            {
                // Empty space on left (if no logo)
                var leftWidth = 150f;

                // Logo on the left
                if (_config.CompanyLogoNew != null)
                {
                    var logoBytes = ImageHelpers.GetLogoBytes(_config.CompanyLogoNew, _config.LogoContainer);
                    if (logoBytes != null)
                    {
                        row.ConstantItem(leftWidth)
                           .Height(70)
                           .Padding(10)
                           .Image(logoBytes)
                           .FitArea();
                    }
                }
                else
                {
                    // Keep space even if no logo
                    row.ConstantItem(leftWidth);
                }

                var generatedDateIST = TimeZoneHelper.ConvertToTimeZone(_data.GeneratedDate, TimeZoneId.IndiaStandardTime);

                // Title and date in center of remaining space
                row.RelativeItem().Column(column =>
                {
                    column.Item().AlignCenter().Text(CompanyDetails.CompanyName)
                        .FontSize(16).Bold();
                    column.Item().AlignCenter().Text("Low Stock Report")
                        .FontSize(14).Bold();
                    column.Item().AlignCenter().PaddingTop(5)
                        .Text($"Generated on: {generatedDateIST:dd-MMM-yyyy HH:mm tt}")
                        .FontSize(12);
                    column.Item().AlignCenter().PaddingTop(5)
                        .Text($"Total Items Below Minimum Level: {_data.TotalItemsCount}")
                        .FontSize(12).SemiBold();
                    column.Item().PaddingVertical(5);
                });

                // Empty space on right to balance the layout
                row.ConstantItem(leftWidth);
            });
        }

        private void ComposeContent(IContainer container)
        {
            container.PaddingTop(20).Column(column =>
            {
                // Filter data into three categories
                var adequateStockItems = _data.LowStockItems
                    .Where(item => item.TotalAvailableQty > (item.MinimumLevel * 0.5m))
                    .ToList();

                var lowStockItems = _data.LowStockItems
                    .Where(item => item.TotalAvailableQty <= (item.MinimumLevel * 0.5m) && item.TotalAvailableQty > 0)
                    .ToList();

                var outOfStockItems = _data.LowStockItems
                    .Where(item => item.TotalAvailableQty == 0)
                    .ToList();

                // Table 1: Adequate Stock (Yellow Header)
                column.Item().Element(container => ComposeStockTable(container, adequateStockItems,
                    "Adequate Stock - Items with more than 50% of minimum level quantity",
                    Colors.Yellow.Lighten2));

                // Add spacing between tables
                column.Item().PaddingTop(20);

                // Table 2: Low Stock - Risk of Stock Out (Red Header)
                column.Item().Element(container => ComposeStockTable(container, lowStockItems,
                    "Low Stock - Risk of Stock Out - Items with less than 50% of minimum level but still in stock",
                    Colors.Red.Lighten2));

                // Add spacing between tables
                column.Item().PaddingTop(20);

                // Table 3: Out of Stock (Red Header)
                column.Item().Element(container => ComposeStockTable(container, outOfStockItems,
                    "Out of Stock - Items with zero available quantity",
                    Colors.Red.Lighten2));
            });
        }

        private static void ComposeStockTable(IContainer container, List<ILowStockReportItemData> items, string headerText, string headerColor)
        {
            container.Column(column =>
            {
                // Table header with description
                column.Item().Background(headerColor).Padding(8).Text(headerText)
                    .FontSize(12).SemiBold().FontColor(Colors.Black);

                // Check if there are items to display
                if (items == null || !items.Any())
                {
                    column.Item().Border(0.5f).BorderColor(Colors.Grey.Darken2).Padding(10)
                        .AlignCenter().Text("No items found").FontSize(10).FontColor(Colors.Grey.Darken1);
                    return;
                }

                // Create the data table
                column.Item().Table(table =>
                {
                    // Define columns
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(30); // S.No.
                        columns.RelativeColumn(3); // Product Name (multi-line)
                        columns.RelativeColumn(1); // Minimum Level
                        columns.RelativeColumn(1); // Total Available Qty
                        columns.RelativeColumn(1); // Domestic Qty
                        columns.RelativeColumn(1); // Imported Qty
                    });

                    // Table header row
                    table.Header(header =>
                    {
                        header.Cell().Element(DataHeaderCellStyle).Text("S.No.");
                        header.Cell().Element(DataHeaderCellStyle).Text("Product Details");
                        header.Cell().Element(DataHeaderCellStyle).Text("Minimum Level");
                        header.Cell().Element(DataHeaderCellStyle).Text("Total Available Qty");
                        header.Cell().Element(DataHeaderCellStyle).Text("Domestic Qty");
                        header.Cell().Element(DataHeaderCellStyle).Text("Imported Qty");
                    });

                    // Data rows
                    int SNo = 1;
                    foreach (var item in items)
                    {
                        table.Cell().Element(StyleCell).AlignCenter().Text(SNo.ToString());
                        table.Cell().Element(StyleCell).Column(productColumn =>
                        {
                            productColumn.Item().Text(item.ProductName).SemiBold();
                            if (!string.IsNullOrEmpty(item.ProductType))
                                productColumn.Item().Text(item.ProductType).FontSize(9).FontColor(Colors.Grey.Darken1);
                            if (!string.IsNullOrEmpty(item.ProductCategory))
                                productColumn.Item().Text(item.ProductCategory).FontSize(9).FontColor(Colors.Grey.Darken1);
                            if (!string.IsNullOrEmpty(item.ProductFirstSubCategory))
                                productColumn.Item().Text(item.ProductFirstSubCategory).FontSize(9).FontColor(Colors.Grey.Darken1);
                            if (!string.IsNullOrEmpty(item.ProductSecSubCategory))
                                productColumn.Item().Text(item.ProductSecSubCategory).FontSize(9).FontColor(Colors.Grey.Darken1);
                        });

                        table.Cell().Element(StyleCell).AlignRight().Text($"{item.MinimumLevel:N2} {item.Unit}");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{item.TotalAvailableQty:N2} {item.Unit}");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{item.DomesticQty:N2} {item.Unit}");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{item.ImportedQty:N2} {item.Unit}");
                        SNo++;
                    }
                });
            });
        }

        private static IContainer DataHeaderCellStyle(IContainer container) =>
            container.DefaultTextStyle(x => x.SemiBold())
                .Border(0.5f)
                .Padding(2)
                .BorderColor(Colors.Grey.Darken2)
                .Background(Colors.Grey.Lighten3);

        private static IContainer StyleCell(IContainer container) => container
            .DefaultTextStyle(x => x.FontSize(8))
            .Border(0.5f)
            .BorderColor(Colors.Black)
            .PaddingVertical(5)
            .PaddingHorizontal(5)
            .AlignLeft()
            .AlignMiddle();
    }
}
