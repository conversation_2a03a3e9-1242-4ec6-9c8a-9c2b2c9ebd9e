using System.Linq;
using PmsCore.PDFGeneration.Models;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace PmsCommon.Services.PdfGeneration.Documents
{
    /// <summary>
    /// PDF document for OutPass generation
    /// </summary>
    public class OutPassDocument : IDocument
    {
        private readonly IOutPassPdfData _data;
        private readonly bool _isOverdueReminder;
        private readonly int _daysOverdue;

        public OutPassDocument(IOutPassPdfData data, PdfConfiguration configuration, bool isOverdueReminder = false, int daysOverdue = 0)
        {
            _data = data;
            _isOverdueReminder = isOverdueReminder;
            _daysOverdue = daysOverdue;
            // Configuration parameter kept for future extensibility
        }

        public void Compose(IDocumentContainer container)
        {
            container.Page(page =>
            {
                page.Margin(20);
                page.Size(PageSizes.A4.Portrait());

                page.Header().Element(ComposeHeader);
                page.Content().Element(ComposeContent);
                page.Footer().Element(ComposeFooter);
            });
        }

        private void ComposeHeader(IContainer container)
        {
            container.Column(column =>
            {
                // Company Header
                column.Item().Border(1).Padding(10).Column(headerColumn =>
                {
                    headerColumn.Item().AlignCenter().Text(CompanyDetails.CompanyName)
                        .FontSize(16).Bold();
                    headerColumn.Item().AlignCenter().Text($"HEAD OFFICE: {CompanyDetails.CompanyHeadOfficeAddress}")
                        .FontSize(10);
                    headerColumn.Item().AlignCenter().Text($"FACTORY ADDRESS: {CompanyDetails.CompanyFactoryAddress}")
                        .FontSize(10);
                });

                // OutPass Title
                column.Item().Border(1).BorderTop(0).Padding(8).AlignCenter()
                    .Text($"SOFTWARE GENERATED MATERIAL OUT PASS NO.: {_data.OutpassNumber}")
                    .FontSize(13).Bold();
            });
        }

        private void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {
                // OutPass Details Section
                column.Item().Element(ComposeOutPassDetails);

                // Items Section
                column.Item().PaddingTop(10).Element(ComposeItemsTable);

                // Overdue Warning Section (only for reminder notifications)
                if (_isOverdueReminder)
                {
                    column.Item().PaddingTop(20).Element(ComposeOverdueWarning);
                }
            });
        }

        private void ComposeOutPassDetails(IContainer container)
        {
            container.Border(1).BorderTop(0).Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.ConstantColumn(120);
                    columns.RelativeColumn();
                    columns.ConstantColumn(120);
                    columns.RelativeColumn();
                });

                // Row 1: OutPass To and OutPass Date
                table.Cell().Border(1).Padding(5).Text("OUTPASS TO").FontSize(10).Bold();
                table.Cell().Border(1).Padding(5).Text(_data.OutpassTo).FontSize(10).Bold();
                table.Cell().Border(1).Padding(5).Text("OUTPASS DATE").FontSize(10).Bold();
                table.Cell().Border(1).Padding(5).Text(_data.OutpassDate?.ToString("dd-MMM-yyyy") ?? "").FontSize(10).Bold();

                // Row 2: OutPass Type and Purpose
                table.Cell().Border(1).Padding(5).Text("OUTPASS TYPE").FontSize(10).Bold();
                table.Cell().Border(1).Padding(5).Text(_data.OutpassType).FontSize(10).Bold();
                table.Cell().Border(1).Padding(5).Text("PURPOSE").FontSize(10).Bold();
                table.Cell().Border(1).Padding(5).Text(_data.Purpose).FontSize(10).Bold();

                // Row 3: Status (only for overdue reminders) - matching original print format
                if (_isOverdueReminder)
                {
                    table.Cell().Border(1).Padding(5).Text("STATUS").FontSize(10).Bold();
                    table.Cell().Border(1).Padding(5).Text("OVERDUE").FontSize(10).Bold().FontColor(Colors.Red.Darken2);
                    table.Cell().Border(1).Padding(5).Text("REMARK").FontSize(10).Bold();
                    table.Cell().Border(1).Padding(5).Text(_data.Remark).FontSize(10).Bold();
                }
                else
                {
                    table.Cell().Border(1).Padding(5).Text("STATUS").FontSize(10).Bold();
                    table.Cell().Border(1).Padding(5).Text(_data.Status).FontSize(10).Bold();
                    table.Cell().Border(1).Padding(5).Text("REMARK").FontSize(10).Bold();
                    table.Cell().Border(1).Padding(5).Text(_data.Remark).FontSize(10).Bold();
                }
            });
        }

        private void ComposeItemsTable(IContainer container)
        {
            // Step 1: Group items by ProductId (or ProductName if ProductId is null) - matching Angular logic exactly
            var productGroups = _data.OutpassItems
                .GroupBy(item =>
                {
                    if (item.ProductId != null && item.ProductId > 0)
                        return $"pid-{item.ProductId}";
                    else
                        return $"pname-{item.ProductName ?? "unknown"}";
                })
                .Select(group =>
                {
                    var items = group.ToList();
                    var uniqueUnits = items.Select(x => x.Unit).Distinct().Where(u => !string.IsNullOrEmpty(u));
                    var units = string.Join(", ", uniqueUnits);

                    // Calculate total quantity and amount
                    var totalQuantity = items.Sum(x => x.Quantity ?? 0);

                    // Calculate average amount only considering items with non-zero Amount values (matching Angular logic)
                    var itemsWithNonZeroAmount = items.Where(x => (x.Amount ?? 0) > 0).ToList();
                    var totalBatchAmount = itemsWithNonZeroAmount.Count > 0
                        ? itemsWithNonZeroAmount.Sum(x => x.Amount ?? 0) / itemsWithNonZeroAmount.Count
                        : 0;

                    var totalAmount = totalBatchAmount * totalQuantity;

                    // Get batch information
                    var batches = items
                        .Where(x => !string.IsNullOrEmpty(x.BatchNo?.Trim()))
                        .Select(x => new { BatchNo = x.BatchNo, Quantity = x.Quantity ?? 0 })
                        .ToList();

                    // If no batches with batch numbers, add a default entry with empty batch number
                    if (batches.Count == 0 && items.Count > 0)
                    {
                        batches.Add(new { BatchNo = "", Quantity = 0m });
                    }

                    return new
                    {
                        ProductId = group.Key.StartsWith("pid-") ? long.Parse(group.Key.Substring(4)) : 0,
                        ProductName = items.First().ProductName,
                        TotalQuantity = totalQuantity,
                        TotalBatchAmount = totalBatchAmount,
                        Units = units,
                        TotalAmount = totalAmount,
                        Batches = batches
                    };
                })
                .ToList();

            // Step 2: Group by unit type to create separate tables (matching Angular logic exactly)
            var unitGroups = productGroups
                .GroupBy(item =>
                {
                    var unitType = item.Units.Split(',').FirstOrDefault()?.Trim();
                    return string.IsNullOrEmpty(unitType) ? "N/A" : unitType;
                })
                .Select(unitGroup => new
                {
                    UnitType = unitGroup.Key,
                    Items = unitGroup.ToList(),
                    TotalQuantity = unitGroup.Sum(x => x.TotalQuantity),
                    TotalAmount = unitGroup.Sum(x => x.TotalAmount)
                })
                .OrderBy(x => x.UnitType)
                .ToList();

            // Step 3: Create separate tables for each unit group (matching Angular HTML exactly)
            container.Column(column =>
            {
                foreach (var unitGroup in unitGroups)
                {
                    // Only show table if there are items
                    if (unitGroup.Items.Count > 0)
                    {
                        column.Item().PaddingBottom(20).Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.ConstantColumn(40);   // S.NO
                                columns.RelativeColumn(3);    // RAW MATERIAL
                                columns.ConstantColumn(80);   // QTY
                                columns.ConstantColumn(60);   // UNIT
                                columns.ConstantColumn(80);   // AMOUNT
                                columns.ConstantColumn(100);  // TOTAL AMOUNT
                            });

                            // Header
                            table.Header(header =>
                            {
                                header.Cell().Border(1).Background(Colors.Grey.Lighten3).Padding(5)
                                    .Text("S.NO").FontSize(10).Bold();
                                header.Cell().Border(1).Background(Colors.Grey.Lighten3).Padding(5)
                                    .Text("RAW MATERIAL").FontSize(10).Bold();
                                header.Cell().Border(1).Background(Colors.Grey.Lighten3).Padding(5)
                                    .Text("QTY").FontSize(10).Bold();
                                header.Cell().Border(1).Background(Colors.Grey.Lighten3).Padding(5)
                                    .Text("UNIT").FontSize(10).Bold();
                                header.Cell().Border(1).Background(Colors.Grey.Lighten3).Padding(5)
                                    .Text("AMOUNT").FontSize(10).Bold();
                                header.Cell().Border(1).Background(Colors.Grey.Lighten3).Padding(5)
                                    .Text("TOTAL AMOUNT").FontSize(10).Bold();
                            });

                            // Items for this unit group
                            for (int i = 0; i < unitGroup.Items.Count; i++)
                            {
                                var item = unitGroup.Items[i];

                                table.Cell().Border(1).Padding(5).AlignCenter()
                                    .Text((i + 1).ToString()).FontSize(10);

                                table.Cell().Border(1).Padding(5).Column(itemColumn =>
                                {
                                    itemColumn.Item().Text(item.ProductName).FontSize(10).Bold();

                                    // Show batch details as sub-items (matching Angular format exactly)
                                    foreach (var batch in item.Batches)
                                    {
                                        if (!string.IsNullOrEmpty(batch.BatchNo))
                                        {
                                            itemColumn.Item().PaddingTop(2).Text($"({batch.BatchNo} - Qty: {batch.Quantity:F2})")
                                                .FontSize(8).Italic().FontColor(Colors.Grey.Darken1);
                                        }
                                    }
                                });

                                table.Cell().Border(1).Padding(5).AlignCenter()
                                    .Text(item.TotalQuantity.ToString("F2")).FontSize(10);

                                table.Cell().Border(1).Padding(5).AlignCenter()
                                    .Text(unitGroup.UnitType).FontSize(10);

                                table.Cell().Border(1).Padding(5).AlignRight()
                                    .Text($"₹ {item.TotalBatchAmount:F2}").FontSize(10);

                                table.Cell().Border(1).Padding(5).AlignRight()
                                    .Text($"₹ {item.TotalAmount:F2}").FontSize(10);
                            }

                            // Total Row for this unit group
                            table.Cell().ColumnSpan(2).Border(1).Background(Colors.Grey.Lighten4).Padding(5)
                                .AlignRight().Text("TOTAL:").FontSize(10).Bold();
                            table.Cell().Border(1).Background(Colors.Grey.Lighten4).Padding(5)
                                .AlignCenter().Text(unitGroup.TotalQuantity.ToString("F2")).FontSize(10).Bold();
                            table.Cell().Border(1).Background(Colors.Grey.Lighten4).Padding(5)
                                .AlignCenter().Text(unitGroup.UnitType).FontSize(10).Bold();
                            table.Cell().Border(1).Background(Colors.Grey.Lighten4).Padding(5);
                            table.Cell().Border(1).Background(Colors.Grey.Lighten4).Padding(5)
                                .AlignRight().Text($"₹ {unitGroup.TotalAmount:F2}").FontSize(10).Bold();
                        });
                    }
                }

                // If no unit groups, show a message
                if (unitGroups.Count == 0)
                {
                    column.Item().PaddingTop(20).Text("No items found in this OutPass.")
                        .FontSize(12).FontColor(Colors.Orange.Darken2);
                }
            });
        }

        private void ComposeOverdueWarning(IContainer container)
        {
            container.Background(Colors.Red.Lighten4).Border(1).Padding(15).Column(column =>
            {
                column.Item().AlignCenter().Text("⚠️ OVERDUE NOTIFICATION")
                    .FontSize(14).Bold().FontColor(Colors.Red.Darken2);

                column.Item().PaddingTop(5).AlignCenter()
                    .Text($"This OutPass is OVERDUE by {_daysOverdue} days. Please arrange for immediate return.")
                    .FontSize(12).Bold().FontColor(Colors.Red.Darken1);

                column.Item().PaddingTop(5).AlignCenter()
                    .Text("Status: OVERDUE")
                    .FontSize(11).Bold().FontColor(Colors.Red.Darken2);
            });
        }

        private void ComposeFooter(IContainer container)
        {
            container.PaddingTop(30).Column(column =>
            {
                // Signatures
                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                    });

                    table.Cell().PaddingLeft(20).Column(leftColumn =>
                    {
                        leftColumn.Item().Text("PREPARED BY:").FontSize(10).Bold();
                        leftColumn.Item().PaddingTop(10).Text(_data.AddedBy)
                            .FontSize(10).Bold().Italic();
                    });

                    table.Cell().Padding(10).Column(rightColumn =>
                    {
                        rightColumn.Item().Text("APPROVED BY:").FontSize(10).Bold();
                        rightColumn.Item().PaddingTop(25);
                        rightColumn.Item().Text("Authorized Signatory").FontSize(10);
                    });
                });

                // Powered By (matching original format)
                column.Item().PaddingTop(20).AlignCenter()
                    .Text("Powered By: KanzenFlow").FontSize(10);
            });
        }
    }
}
