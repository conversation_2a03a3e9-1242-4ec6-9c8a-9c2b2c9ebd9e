using System;
using System.Collections.Generic;
using System.Linq;
using PmsCommon;
using PmsCore.DataAccessRepository.Models;
using PmsCore.PDFGeneration.Models;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

public class YieldReportSummaryDocument : IDocument
{
    private readonly List<YieldReportDto> _data;
    private readonly DateTime _fromDate;
    private readonly DateTime _toDate;
    private readonly PdfConfiguration _config;

    public YieldReportSummaryDocument(List<YieldReportDto> data, PdfConfiguration configuration, DateTime fromDate, DateTime toDate)
    {
        _data = data;
        _fromDate = fromDate;
        _toDate = toDate;
        _config = configuration;
    }

    public void Compose(IDocumentContainer container)
    {
        var IstTime = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);
        container.Page(page =>
        {
            page.Margin(20);
            page.Size(PageSizes.A4.Landscape());

            page.Header().Element(ComposeHeader);
            page.Content().Element(ComposeContent);
            page.Footer().Row(row =>
            {
                // Generated on text (centered)
                row.RelativeItem().AlignCenter().Text(text =>
                {
                    text.Span("Generated on: ").FontSize(9);
                    text.Span(IstTime.ToString("dd-MMM-yyyy HH:mm")).FontSize(9);
                });

                // Page numbers (right aligned)
                row.RelativeItem().AlignRight().Text(text =>
                {
                    text.CurrentPageNumber().FontSize(9);
                    text.Span(" / ").FontSize(9);
                    text.TotalPages().FontSize(9);
                });
            });
        });
    }

    private void ComposeHeader(IContainer container)
    {
        container.Row(row =>
        {
            // Empty space on left (if no logo)
            var leftWidth = 150f;

            // Logo on the left
            if (_config.CompanyLogoNew != null)
            {
                var logoBytes = ImageHelpers.GetLogoBytes(_config.CompanyLogoNew, _config.LogoContainer);
                if (logoBytes != null)
                {
                    row.ConstantItem(leftWidth)
                       .Height(90)
                       .Padding(10)
                       .Image(logoBytes)
                       .FitArea();
                }
            }
            else
            {
                // Keep space even if no logo
                row.ConstantItem(leftWidth);
            }

            var fromDateIST = TimeZoneHelper.ConvertToTimeZone(_fromDate, TimeZoneId.IndiaStandardTime);
            var toDateIST = TimeZoneHelper.ConvertToTimeZone(_toDate, TimeZoneId.IndiaStandardTime);
            // Title and date in center of remaining space
            row.RelativeItem().Column(column =>
            {
                column.Item().AlignCenter().Text(CompanyDetails.CompanyName)
                    .FontSize(20).Bold();
                column.Item().AlignCenter().Text("Yield Report Summary")
                    .FontSize(20).Bold();
                column.Item().AlignCenter().PaddingTop(5)
                    .Text($"Period: {fromDateIST:dd-MMM-yyyy hh:mm tt} to {toDateIST:dd-MMM-yyyy hh:mm tt}")
                    .FontSize(12);
                column.Item().PaddingVertical(5);
            });

            // Empty space on right to balance the layout
            row.ConstantItem(leftWidth);
        });
    }

    private void ComposeContent(IContainer container)
    {
        var summary = CalculateSummary();

        container.Column(column =>
        {
            column.Item().AlignCenter().Padding(20)
                .Text("Executive Summary")
                .Bold().FontSize(14);

            // Summary Statistics
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });

                AddSummaryRow(table, "Total Orders Qty", $"{summary.OrderQty:N2} Mtrs");
                AddSummaryRow(table, "Total PRD MTR", $"{summary.PrdMtr:N2} Mtrs");
                AddSummaryRow(table, "Over Production", $"{summary.OverProdPer:N2}%");
                AddSummaryRow(table, "Average Yield", $"{summary.YieldPer:N2}%");
                AddSummaryRow(table, "Total First Grade", $"{summary.First:N2} Mtrs");
                AddSummaryRow(table, "Total A Grade", $"{summary.AGrade:N2} Mtrs");
                AddSummaryRow(table, "Total Lot Grade", $"{summary.Lot:N2} Mtrs");
                AddSummaryRow(table, "Total NS Grade", $"{summary.NS:N2} Mtrs");
                AddSummaryRow(table, "Total Cut PC Grade", $"{summary.CutPc:N2} Mtrs");
                AddSummaryRow(table, "Total Sample Grade", $"{summary.Sample:N2} Mtrs");
                AddSummaryRow(table, "Total Wastage", $"{summary.Scrap:N2} Mtrs");
                AddSummaryRow(table, "Total Grand Total", $"{summary.GrandTotal:N2} Mtrs");
            });

            // Orders Below Target
            var lowYieldOrders = _data.Where(x =>
            {
                // Calculate yield percentage safely - if ActualQuantity is null or zero, consider it as 0% yield (below target)
                var yieldPercentage = (x.ActualQuantity ?? 0) > 0
                    ? ((x.FirstGrade ?? 0) + (x.AGrade ?? 0) + (x.SampleQuantity ?? 0)) * 100 / (x.ActualQuantity ?? 0)
                    : 0;

                // Check over production safely - if SaleOrderQuantity is null or zero, skip this check
                var isOverProduction = (x.SaleOrderQuantity ?? 0) > 0
                    ? (x.ManufacturingQuantity ?? 0) > (x.SaleOrderQuantity ?? 0) * 1.05m
                    : false;

                return yieldPercentage < 90 || isOverProduction;
            }).ToList();

            if (lowYieldOrders.Any())
            {
                column.Item().PaddingTop(10)
                    .LineHorizontal(1)
                    .LineColor(Colors.Grey.Medium);
                column.Item().PaddingTop(20).PaddingBottom(10)
                    .Text("Orders Below 90% Yield or PRD MTR is above 5% of Order QTY:-")
                    .Bold().FontSize(14);

                column.Item().Table(table =>
                {
                    // Define columns with specific widths
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(40);      // S.No.
                        columns.RelativeColumn(3);       // Inspection Date
                        columns.RelativeColumn(2);       // Sale Order No
                        columns.RelativeColumn(3);       // Customer
                        columns.RelativeColumn(4);       // Sale Order Code
                        columns.RelativeColumn(3);       // Fabric
                        columns.RelativeColumn(2);       // Order Qty
                        columns.RelativeColumn(2);       // PRD Qty
                        columns.RelativeColumn(2);       // Over PRD %
                        columns.RelativeColumn(2);       // Yield %
                        columns.RelativeColumn(2);       // 1st
                        columns.RelativeColumn(2);       // A
                        columns.RelativeColumn(2);       // Grand Total
                    });

                    // Header
                    table.Header(header =>
                    {
                        header.Cell().Element(HeaderCellStyle).Text("S.No.");
                        header.Cell().Element(HeaderCellStyle).Text("Inspected Date");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("Order No");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("Customer");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("Sale Order Code");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("Fabric");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("Order Qty");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("PRD Qty");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("Over PRD %");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("Yield %");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("1st");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("A");
                        header.Cell().Element(HeaderCellStyle).Padding(2).Text("Grand Total");
                    });

                    int sNo = 1;
                    foreach (var order in lowYieldOrders)
                    {
                        // Calculate yield percentage safely
                        var yield = (order.ActualQuantity ?? 0) > 0
                            ? ((order.FirstGrade ?? 0) + (order.AGrade ?? 0) + (order.SampleQuantity ?? 0)) * 100 / (order.ActualQuantity ?? 0)
                            : 0;

                        // Calculate over production percentage safely
                        var overProdPerItem = (order.SaleOrderQuantity ?? 0) > 0
                            ? ((order.ManufacturingQuantity ?? 0) - (order.SaleOrderQuantity ?? 0)) / (order.SaleOrderQuantity ?? 0) * 100
                            : 0;

                        var grandTotal = (order.FirstGrade ?? 0) + (order.AGrade ?? 0) + (order.LOTGrade ?? 0) + (order.NSGrade ?? 0) +
                            (order.CUTPCGrade ?? 0) + (order.SampleQuantity ?? 0) + (order.FILMGrade ?? 0) + (order.WASTEGrade ?? 0);

                        var InspectedDateIST = order.AddedDate.HasValue
                            ? TimeZoneHelper.ConvertToTimeZone(order.AddedDate.Value.ToUniversalTime(), TimeZoneId.IndiaStandardTime)
                            : DateTime.MinValue;

                        table.Cell().Element(StyleCell).AlignCenter().Text(sNo.ToString());
                        table.Cell().Element(StyleCell).Text(InspectedDateIST == DateTime.MinValue ? "N/A" : InspectedDateIST.ToString("dd-MMM-yyyy HH:mm:ss tt"));
                        table.Cell().Element(StyleCell).Text(order.SaleOrderNumber ?? "N/A");
                        table.Cell().Element(StyleCell).Text(order.CustomerName ?? "N/A");
                        table.Cell().Element(StyleCell).Text(order.SaleOrderCode ?? "N/A");
                        table.Cell().Element(StyleCell).Text(order.FabricName ?? "N/A");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{order.SaleOrderQuantity ?? 0:N2}");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{order.ManufacturingQuantity ?? 0:N2}");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{overProdPerItem:N2}");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{yield:N2}%");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{order.FirstGrade ?? 0:N2}");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{order.AGrade ?? 0:N2}");
                        table.Cell().Element(StyleCell).AlignRight().Text($"{grandTotal:N2}");

                        sNo++;
                    }
                });
            }
        });
    }

    private static void AddSummaryRow(TableDescriptor table, string label, string value)
    {
        table.Cell().BorderBottom(0.5f).BorderColor(Colors.Grey.Lighten2)
            .PaddingVertical(5).Text(label).Bold();
        table.Cell().BorderBottom(0.5f).BorderColor(Colors.Grey.Lighten2)
            .PaddingVertical(5).Text(value);
    }
    private static IContainer HeaderCellStyle(IContainer container) =>
        container.DefaultTextStyle(x => x.SemiBold())
            .Border(0.5f)
            .Padding(2)
            .BorderColor(Colors.Grey.Darken2)
            .Background(Colors.Grey.Lighten3);

    private static IContainer StyleCell(IContainer container) => container
        .DefaultTextStyle(x => x.FontSize(8))
        .Border(0.5f)
        .BorderColor(Colors.Black)
        .PaddingVertical(5)
        .PaddingHorizontal(5)
        .AlignLeft()
        .AlignMiddle();



    private YieldSummary CalculateSummary()
    {
        var summary = new YieldSummary();
        foreach (var item in _data)
        {
            summary.OrderQty += item.SaleOrderQuantity ?? 0;
            summary.PrdMtr += item.ManufacturingQuantity ?? 0;
            summary.First += item.FirstGrade ?? 0;
            summary.AGrade += item.AGrade ?? 0;
            summary.Lot += item.LOTGrade ?? 0;
            summary.NS += item.NSGrade ?? 0;
            summary.CutPc += item.CUTPCGrade ?? 0;
            summary.Sample += item.SampleQuantity ?? 0;
            summary.WasteGrade += item.WASTEGrade ?? 0;
            summary.FilmGrade += item.FILMGrade ?? 0;
        }

        // Calculate over production percentage safely
        summary.OverProdPer = summary.OrderQty > 0
            ? (summary.PrdMtr - summary.OrderQty) / summary.OrderQty * 100
            : 0;

        summary.Scrap = summary.FilmGrade + summary.WasteGrade;

        // Calculate yield percentage safely
        summary.YieldPer = summary.PrdMtr > 0
            ? (summary.First + summary.AGrade + summary.Sample) * 100 / summary.PrdMtr
            : 0;
        summary.GrandTotal = summary.First + summary.AGrade + summary.Lot + summary.NS +
                           summary.CutPc + summary.Sample + summary.WasteGrade + summary.FilmGrade;

        return summary;
    }

    private class YieldSummary
    {
        public decimal OrderQty { get; set; }
        public decimal PrdMtr { get; set; }
        public decimal OverProdPer { get; set; }
        public decimal First { get; set; }
        public decimal AGrade { get; set; }
        public decimal Lot { get; set; }
        public decimal NS { get; set; }
        public decimal CutPc { get; set; }
        public decimal YieldPer { get; set; }
        public decimal Sample { get; set; }
        public decimal Scrap { get; set; }
        public decimal GrandTotal { get; set; }
        public decimal WasteGrade { get; set; }
        public decimal FilmGrade { get; set; }
    }
}