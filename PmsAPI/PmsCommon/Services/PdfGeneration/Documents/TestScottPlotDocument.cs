using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using ScottPlot;
using System.Collections.Generic;
using System.Linq;
using Colors = QuestPDF.Helpers.Colors;

namespace PmsCommon.Services.PdfGeneration.Documents
{
    public class TestScottPlotDocument : IDocument
    {
        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(2, Unit.Centimetre);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(12));

                page.Header()
                    .Text("ScottPlot Integration Test")
                    .SemiBold().FontSize(20).FontColor(Colors.Blue.Medium);

                page.Content()
                    .PaddingVertical(1, Unit.Centimetre)
                    .Column(column =>
                    {
                        // Test Pie Chart
                        column.Item().Text("Professional Pie Chart").FontSize(16).SemiBold();
                        column.Item().PaddingTop(10).Height(150).Width(150).AlignCenter()
                            .Svg(size =>
                            {
                                ScottPlot.Plot plot = new();

                                var slices = new PieSlice[]
                                {
                                    new() { Value = 65, FillColor = new ScottPlot.Color(Colors.Green.Medium.Hex), Label = "Profit (65%)" },
                                    new() { Value = 25, FillColor = new ScottPlot.Color(Colors.Red.Medium.Hex), Label = "Loss (25%)" },
                                    new() { Value = 10, FillColor = new ScottPlot.Color(Colors.Grey.Lighten2.Hex), Label = "Break-even (10%)" }
                                };

                                var pie = plot.Add.Pie(slices);
                                pie.DonutFraction = 0.4;
                                pie.SliceLabelDistance = 1.3;
                                pie.LineColor = ScottPlot.Colors.White;
                                pie.LineWidth = 2;

                                plot.Axes.Frameless();
                                plot.HideGrid();
                                plot.Layout.Frameless();

                                return plot.GetSvgXml((int)size.Width, (int)size.Height);
                            });

                        // Test Bar Chart
                        column.Item().PaddingTop(20).Text("Professional Bar Chart").FontSize(16).SemiBold();
                        column.Item().PaddingTop(10).Height(150).Width(300).AlignCenter()
                            .Svg(size =>
                            {
                                ScottPlot.Plot plot = new();

                                var bars = new Bar[]
                                {
                                    new() { Position = 1, Value = 85.5, FillColor = new ScottPlot.Color(Colors.Blue.Medium.Hex), Size = 0.6 }
                                };

                                plot.Add.Bars(bars);

                                plot.Axes.Bottom.TickGenerator = new ScottPlot.TickGenerators.NumericManual(new Tick[]
                                {
                                    new(1, "Fulfillment Rate")
                                });

                                plot.Axes.Left.Label.Text = "Percentage (%)";
                                plot.Axes.SetLimitsY(0, 100);

                                plot.Add.Text("85.5%", 1, 87)
                                    .LabelStyle.FontSize = 14;

                                plot.Grid.XAxisStyle.IsVisible = false;
                                plot.Layout.Frameless();

                                return plot.GetSvgXml((int)size.Width, (int)size.Height);
                            });

                        // Test Horizontal Bar Chart
                        column.Item().PaddingTop(20).Text("Professional Horizontal Bar Chart").FontSize(16).SemiBold();
                        column.Item().PaddingTop(10).Height(200).Width(400).AlignCenter()
                            .Svg(size =>
                            {
                                ScottPlot.Plot plot = new();

                                var products = new[] { "Product A", "Product B", "Product C", "Product D", "Product E" };
                                var values = new[] { 1200, 950, 800, 650, 500 };

                                var bars = new List<Bar>();
                                for (int i = 0; i < products.Length; i++)
                                {
                                    bars.Add(new Bar
                                    {
                                        Position = i + 1,
                                        Value = values[i],
                                        FillColor = new ScottPlot.Color(i == 0 ? Colors.Blue.Darken1.Hex : Colors.Blue.Lighten1.Hex),
                                        Size = 0.7
                                    });
                                }

                                plot.Add.Bars(bars.ToArray());

                                var ticks = new List<Tick>();
                                for (int i = 0; i < products.Length; i++)
                                {
                                    ticks.Add(new Tick(i + 1, products[i]));
                                }

                                plot.Axes.Bottom.TickGenerator = new ScottPlot.TickGenerators.NumericManual(ticks.ToArray());
                                plot.Axes.Bottom.TickLabelStyle.Rotation = -45;
                                plot.Axes.Left.Label.Text = "Sales Volume (MTRs)";

                                for (int i = 0; i < values.Length; i++)
                                {
                                    plot.Add.Text($"{values[i]:N0}", i + 1, values[i] + values[i] * 0.02)
                                        .LabelStyle.FontSize = 10;
                                }

                                plot.Grid.XAxisStyle.IsVisible = false;
                                plot.Axes.Margins(bottom: 0.2f, top: 0.1f);
                                plot.Layout.Frameless();

                                return plot.GetSvgXml((int)size.Width, (int)size.Height);
                            });
                    });

                page.Footer()
                    .AlignCenter()
                    .Text(x =>
                    {
                        x.Span("Generated with ScottPlot - ");
                        x.CurrentPageNumber();
                    });
            });
        }
    }
}
