using System;
using System.Linq;
using PmsCommon;
using PmsCore.PDFGeneration.Models;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace PmsCommon.Services.PdfGeneration.Documents
{
    /// <summary>
    /// PDF document for pending returnable outpass report (comprehensive report)
    /// </summary>
    public class PendingReturnableOutPassReportDocument : IDocument
    {
        private readonly IPendingReturnableOutPassReportPdfData _data;
        private readonly PdfConfiguration _config;

        public PendingReturnableOutPassReportDocument(IPendingReturnableOutPassReportPdfData data, PdfConfiguration configuration)
        {
            _data = data;
            _config = configuration;
        }

        public void Compose(IDocumentContainer container)
        {
            var istTime = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);

            container.Page(page =>
            {
                page.Margin(20);
                page.Size(PageSizes.A4.Landscape());

                page.Header().Element(ComposeHeader);
                page.Content().Element(ComposeContent);
                page.Footer().Row(row =>
                {
                    // Generated on text (centered)
                    row.RelativeItem().AlignCenter().Text(text =>
                    {
                        text.Span("Generated on: ").FontSize(9);
                        text.Span(istTime.ToString("dd-MMM-yyyy HH:mm")).FontSize(9);
                    });

                    // Page numbers (right aligned)
                    row.RelativeItem().AlignRight().Text(text =>
                    {
                        text.CurrentPageNumber().FontSize(9);
                        text.Span(" / ").FontSize(9);
                        text.TotalPages().FontSize(9);
                    });
                });
            });
        }

        private void ComposeHeader(IContainer container)
        {
            var leftWidth = 100f;

            container.Row(row =>
            {
                // Empty space on left (if no logo)
                var leftWidth = 150f;

                // Logo on the left
                if (_config.CompanyLogoNew != null)
                {
                    var logoBytes = ImageHelpers.GetLogoBytes(_config.CompanyLogoNew, _config.LogoContainer);
                    if (logoBytes != null)
                    {
                        row.ConstantItem(leftWidth)
                           .Height(70)
                           .Padding(10)
                           .Image(logoBytes)
                           .FitArea();
                    }
                }
                else
                {
                    // Keep space even if no logo
                    row.ConstantItem(leftWidth);
                }

                var generatedDateIST = TimeZoneHelper.ConvertToTimeZone(_data.GeneratedDate, TimeZoneId.IndiaStandardTime);

                // Title and date in center of remaining space
                row.RelativeItem().Column(column =>
                {
                    column.Item().AlignCenter().Text(CompanyDetails.CompanyName)
                        .FontSize(16).Bold();
                    column.Item().AlignCenter().Text("Pending Returnable OutPass Report")
                        .FontSize(14).Bold();
                    column.Item().AlignCenter().PaddingTop(5)
                        .Text($"Generated on: {generatedDateIST:dd-MMM-yyyy HH:mm tt}")
                        .FontSize(12);
                    column.Item().AlignCenter().PaddingTop(5)
                        .Text($"Total Pending: {_data.TotalPendingCount} | Overdue: {_data.OverdueCount}")
                        .FontSize(12).SemiBold();
                    column.Item().PaddingVertical(5);
                });

                // Empty space on right to balance the layout
                row.ConstantItem(leftWidth);
            });
        }

        private void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {
                // Summary Section
                column.Item().Element(ComposeSummary);

                // Report Table
                column.Item().PaddingTop(20).Element(ComposeReportTable);
            });
        }

        private void ComposeSummary(IContainer container)
        {
            container.Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });

                // Header
                table.Header(header =>
                {
                    header.Cell().ColumnSpan(4).Background(Colors.Grey.Lighten3)
                        .Padding(8).Text("Summary").FontSize(14).Bold();
                });

                // Summary data
                table.Cell().Border(1).Padding(8).AlignCenter().Column(column =>
                {
                    column.Item().Text("Total Pending").FontSize(12).Bold();
                    column.Item().Text(_data.TotalPendingCount.ToString()).FontSize(16).Bold().FontColor(Colors.Blue.Medium);
                });

                table.Cell().Border(1).Padding(8).AlignCenter().Column(column =>
                {
                    column.Item().Text("Overdue").FontSize(12).Bold();
                    column.Item().Text(_data.OverdueCount.ToString()).FontSize(16).Bold().FontColor(Colors.Red.Medium);
                });

                table.Cell().Border(1).Padding(8).AlignCenter().Column(column =>
                {
                    column.Item().Text("On Time").FontSize(12).Bold();
                    column.Item().Text((_data.TotalPendingCount - _data.OverdueCount).ToString()).FontSize(16).Bold().FontColor(Colors.Green.Medium);
                });

                table.Cell().Border(1).Padding(8).AlignCenter().Column(column =>
                {
                    column.Item().Text("Total Amount").FontSize(12).Bold();
                    column.Item().Text($"₹ {_data.TotalPendingAmount:N2}").FontSize(16).Bold().FontColor(Colors.Orange.Medium);
                });
            });
        }

        private void ComposeReportTable(IContainer container)
        {
            container.Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.ConstantColumn(30);   // S.No
                    columns.RelativeColumn(2);    // OutPass Number
                    columns.RelativeColumn(3);    // OutPass To
                    columns.RelativeColumn(2);    // OutPass Date
                    columns.RelativeColumn(2);    // Expected Return
                    columns.RelativeColumn(2);    // Purpose
                    columns.RelativeColumn(2);    // Created By
                    columns.RelativeColumn(1);    // Items
                    columns.RelativeColumn(2);    // Amount
                    columns.RelativeColumn(1);    // Days
                    columns.RelativeColumn(1);    // Status
                });

                // Header
                table.Header(header =>
                {
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("S.No").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("OutPass No").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("OutPass To").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("OutPass Date").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("Expected Return").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("Purpose").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("Created By").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("Items").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("Amount").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("Days").Bold().FontSize(10);
                    header.Cell().Background(Colors.Grey.Lighten3).Border(1).Padding(4).Text("Status").Bold().FontSize(10);
                });

                var outpasses = _data.PendingOutPasses.ToList();
                for (int i = 0; i < outpasses.Count; i++)
                {
                    var outpass = outpasses[i];
                    var backgroundColor = outpass.IsOverdue ? Colors.Red.Lighten4 : Colors.White;

                    table.Cell().Background(backgroundColor).Border(1).Padding(4).Text((i + 1).ToString()).FontSize(9);
                    table.Cell().Background(backgroundColor).Border(1).Padding(4).Text(outpass.OutpassNumber ?? "").FontSize(9);
                    table.Cell().Background(backgroundColor).Border(1).Padding(4).Text(outpass.OutpassTo ?? "").FontSize(9);
                    table.Cell().Background(backgroundColor).Border(1).Padding(4).Text(outpass.OutpassDate?.ToString("dd-MMM-yyyy") ?? "").FontSize(9);
                    table.Cell().Background(backgroundColor).Border(1).Padding(4).Text(outpass.ExpectedReturnDate?.ToString("dd-MMM-yyyy") ?? "").FontSize(9);
                    table.Cell().Background(backgroundColor).Border(1).Padding(4).Text(outpass.Purpose ?? "").FontSize(9);
                    table.Cell().Background(backgroundColor).Border(1).Padding(4).Text(outpass.AddedBy ?? "").FontSize(9);
                    table.Cell().Background(backgroundColor).Border(1).Padding(4).AlignCenter().Text(outpass.ItemCount.ToString()).FontSize(9);
                    table.Cell().Background(backgroundColor).Border(1).Padding(4).AlignRight().Text($"₹ {outpass.TotalAmount:N2}").FontSize(9);

                    // Days overdue with color coding
                    if (outpass.IsOverdue)
                    {
                        table.Cell().Background(backgroundColor).Border(1).Padding(4).AlignCenter()
                            .Text(outpass.DaysOverdue.ToString()).FontSize(9).Bold().FontColor(Colors.Red.Darken2);
                    }
                    else
                    {
                        table.Cell().Background(backgroundColor).Border(1).Padding(4).AlignCenter()
                            .Text("0").FontSize(9).FontColor(Colors.Green.Medium);
                    }

                    // Status with color coding
                    var statusColor = outpass.IsOverdue ? Colors.Red.Darken2 : Colors.Green.Medium;
                    var statusText = outpass.IsOverdue ? "OVERDUE" : "PENDING";
                    table.Cell().Background(backgroundColor).Border(1).Padding(4).AlignCenter()
                        .Text(statusText).FontSize(9).Bold().FontColor(statusColor);
                }
            });
        }
    }
}
