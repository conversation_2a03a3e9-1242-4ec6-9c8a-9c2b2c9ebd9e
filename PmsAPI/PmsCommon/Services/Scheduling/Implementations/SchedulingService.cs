using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using PmsCommon.Services.Scheduling.Interfaces;
using Cronos;
using System;
using System.Threading.Tasks;

namespace PmsCommon.Services.Scheduling.Implementations
{
    public class SchedulingService : ISchedulingService
    {
        private readonly ILogger<SchedulingService> _logger;

        public SchedulingService(ILogger<SchedulingService> logger)
        {
            _logger = logger;
        }

        public bool IsTimeToSendNotification(
            string cronExpression,
            string timeZone,
            DateTime? lastTriggeredDate,
            DateTime currentTimeUtc,
            int executionWindowMinutes = 10,
            long? subscriptionId = null)
        {
            try
            {
                // If no CRON expression, assume it's time to send
                if (string.IsNullOrEmpty(cronExpression))
                {
                    _logger.LogInformation("No CRON expression found for subscription {SubscriptionId}, allowing notification",
                        subscriptionId);
                    return true;
                }

                // Parse CRON expression
                var cron = CronExpression.Parse(cronExpression);
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timeZone ?? "Asia/Kolkata");

                // Convert current UTC time to the subscription's timezone
                var currentTimeInTimeZone = TimeZoneInfo.ConvertTime(currentTimeUtc, tz);

                _logger.LogDebug("Checking CRON schedule for subscription {SubscriptionId}: Expression='{CronExpression}', CurrentTime={CurrentTime}, TimeZone={TimeZone}",
                    subscriptionId, cronExpression, currentTimeInTimeZone, tz.Id);

                // If no last triggered date, check if current time is within execution window of any CRON occurrence today
                if (!lastTriggeredDate.HasValue)
                {
                    _logger.LogDebug("No last triggered date for subscription {SubscriptionId}, checking today's CRON occurrences",
                        subscriptionId);

                    return IsWithinTodaysCronWindow(cron, currentTimeInTimeZone, executionWindowMinutes, subscriptionId);
                }

                // Convert last triggered date from UTC to subscription's timezone
                var lastTriggeredInTimeZone = TimeZoneInfo.ConvertTime(lastTriggeredDate.Value, tz);

                _logger.LogDebug("Last triggered date for subscription {SubscriptionId}: {LastTriggeredDate} (local timezone)",
                    subscriptionId, lastTriggeredInTimeZone);

                // Get next occurrence after last triggered time
                // Convert to UTC kind for Cronos compatibility
                var lastTriggeredAsUtcKind = DateTime.SpecifyKind(lastTriggeredInTimeZone, DateTimeKind.Utc);
                var nextOccurrenceAsUtc = cron.GetNextOccurrence(lastTriggeredAsUtcKind);

                // Convert back to local timezone
                DateTime? nextOccurrence = nextOccurrenceAsUtc.HasValue
                    ? DateTime.SpecifyKind(nextOccurrenceAsUtc.Value, DateTimeKind.Unspecified)
                    : null;

                if (!nextOccurrence.HasValue)
                {
                    _logger.LogWarning("No next occurrence found for CRON expression '{CronExpression}' after {LastTriggered}",
                        cronExpression, lastTriggeredInTimeZone);
                    return false;
                }

                // Check if we're within the execution window of the next occurrence
                var isWithinWindow = currentTimeInTimeZone >= nextOccurrence.Value &&
                                   currentTimeInTimeZone <= nextOccurrence.Value.AddMinutes(executionWindowMinutes);

                _logger.LogDebug("CRON evaluation for subscription {SubscriptionId}: NextOccurrence={NextOccurrence}, CurrentTime={CurrentTime}, WithinWindow={WithinWindow}",
                    subscriptionId, nextOccurrence.Value, currentTimeInTimeZone, isWithinWindow);

                return isWithinWindow;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking notification schedule for subscription {SubscriptionId} with CRON '{CronExpression}'",
                    subscriptionId, cronExpression);
                // If there's an error parsing CRON, assume it's time to send to avoid missing notifications
                return true;
            }
        }

        public async Task UpdateScheduleLastTriggered(long scheduleId, DateTime currentTimeUtc, string tableName, string idColumnName, object dbContext)
        {
            try
            {
                // Use reflection to work with the database context without direct dependency
                var dbType = dbContext.GetType();

                if (tableName == "NotificationReportScheduleMappingTable")
                {
                    var dbSetProperty = dbType.GetProperty("NotificationReportScheduleMappingTables");
                    if (dbSetProperty != null)
                    {
                        var dbSet = dbSetProperty.GetValue(dbContext);
                        var findMethod = dbSet.GetType().GetMethod("FirstOrDefaultAsync", new[] { typeof(System.Linq.Expressions.Expression<>) });

                        // For now, we'll skip the database update in the shared service
                        // The calling code should handle the database update directly
                        _logger.LogInformation("Schedule update requested for {TableName} ID {ScheduleId} - handled by calling code", tableName, scheduleId);
                    }
                }
                else if (tableName == "NotificationGroupsTable")
                {
                    _logger.LogInformation("Subscription update requested for {TableName} ID {ScheduleId} - handled by calling code", tableName, scheduleId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating schedule for {TableName} ID {ScheduleId}", tableName, scheduleId);
                // Don't throw - this is not critical enough to stop processing
            }
        }

        public DateTime? CalculateNextRunTime(string cronExpression, string timeZone, DateTime fromTime)
        {
            try
            {
                if (string.IsNullOrEmpty(cronExpression))
                    return null;

                var cron = CronExpression.Parse(cronExpression);
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timeZone ?? "Asia/Kolkata");

                // Ensure fromTime is in UTC for Cronos library
                var fromTimeUtc = fromTime.Kind == DateTimeKind.Utc ? fromTime : DateTime.SpecifyKind(fromTime, DateTimeKind.Utc);

                // Convert fromTime to the specified timezone for CRON evaluation context
                var fromTimeInTimeZone = TimeZoneInfo.ConvertTime(fromTimeUtc, tz);

                _logger.LogDebug("Calculating next run time for CRON '{CronExpression}' in timezone '{TimeZone}'. FromTime: {FromTimeUTC} UTC -> {FromTimeLocal} {TimeZone}",
                    cronExpression, timeZone, fromTimeUtc, fromTimeInTimeZone, tz.Id);

                // Create a virtual "local time" DateTime with UTC kind for Cronos
                // This represents the local time as if it were UTC
                var fromTimeAsUtcKind = DateTime.SpecifyKind(fromTimeInTimeZone, DateTimeKind.Utc);

                // Get next occurrence treating the local time as UTC
                var nextOccurrenceAsUtc = cron.GetNextOccurrence(fromTimeAsUtcKind);

                if (nextOccurrenceAsUtc.HasValue)
                {
                    // Convert the result back: treat the "UTC" result as local time and convert to actual UTC
                    var nextOccurrenceLocal = DateTime.SpecifyKind(nextOccurrenceAsUtc.Value, DateTimeKind.Unspecified);
                    var nextRunTimeUtc = TimeZoneInfo.ConvertTimeToUtc(nextOccurrenceLocal, tz);

                    _logger.LogDebug("Next CRON occurrence: {NextOccurrenceLocal} {TimeZone} -> {NextRunTimeUTC} UTC",
                        nextOccurrenceLocal, tz.Id, nextRunTimeUtc);

                    return nextRunTimeUtc;
                }

                _logger.LogDebug("No next occurrence found for CRON '{CronExpression}' in timezone '{TimeZone}'", cronExpression, timeZone);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating next run time for CRON '{CronExpression}' in timezone '{TimeZone}'", cronExpression, timeZone);
                return null;
            }
        }

        /// <summary>
        /// Check if current time is within execution window of any CRON occurrence for today
        /// </summary>
        private bool IsWithinTodaysCronWindow(CronExpression cronExpression, DateTime currentTimeInTimeZone, int executionWindowMinutes, long? subscriptionId)
        {
            try
            {
                var startOfDay = currentTimeInTimeZone.Date;
                var endOfDay = startOfDay.AddDays(1);

                _logger.LogDebug("Checking today's CRON occurrences for subscription {SubscriptionId} between {StartOfDay} and {EndOfDay}",
                    subscriptionId, startOfDay, endOfDay);

                // Check all occurrences for today
                // Convert to UTC kind for Cronos compatibility
                var startOfDayAsUtcKind = DateTime.SpecifyKind(startOfDay, DateTimeKind.Utc);
                var occurrenceAsUtc = cronExpression.GetNextOccurrence(startOfDayAsUtcKind);
                DateTime? occurrence = occurrenceAsUtc.HasValue
                    ? DateTime.SpecifyKind(occurrenceAsUtc.Value, DateTimeKind.Unspecified)
                    : null;
                var occurrenceCount = 0;

                while (occurrence.HasValue && occurrence.Value < endOfDay && occurrenceCount < 50) // Safety limit
                {
                    occurrenceCount++;

                    // Check if current time is within execution window after scheduled time
                    var isWithinWindow = currentTimeInTimeZone >= occurrence.Value &&
                                       currentTimeInTimeZone <= occurrence.Value.AddMinutes(executionWindowMinutes);

                    _logger.LogDebug("CRON occurrence #{Count} for subscription {SubscriptionId}: {Occurrence}, WithinWindow={WithinWindow}",
                        occurrenceCount, subscriptionId, occurrence.Value, isWithinWindow);

                    if (isWithinWindow)
                    {
                        _logger.LogInformation("Current time {CurrentTime} is within execution window for CRON occurrence {Occurrence} (subscription {SubscriptionId})",
                            currentTimeInTimeZone, occurrence.Value, subscriptionId);
                        return true;
                    }

                    // Get next occurrence (add 1 minute to avoid infinite loop on same occurrence)
                    var nextTimeAsUtcKind = DateTime.SpecifyKind(occurrence.Value.AddMinutes(1), DateTimeKind.Utc);
                    var nextOccurrenceAsUtc = cronExpression.GetNextOccurrence(nextTimeAsUtcKind);
                    occurrence = nextOccurrenceAsUtc.HasValue
                        ? DateTime.SpecifyKind(nextOccurrenceAsUtc.Value, DateTimeKind.Unspecified)
                        : null;
                }

                _logger.LogDebug("No matching CRON window found for subscription {SubscriptionId}. Checked {Count} occurrences for today",
                    subscriptionId, occurrenceCount);

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking today's CRON windows for subscription {SubscriptionId}", subscriptionId);
                return false;
            }
        }
    }
}
