using System;
using System.Threading.Tasks;

namespace PmsCommon.Services.Scheduling.Interfaces
{
    public interface ISchedulingService
    {
        /// <summary>
        /// Check if it's time to send notification based on CRON schedule with execution window tolerance
        /// </summary>
        /// <param name="cronExpression">CRON expression string</param>
        /// <param name="timeZone">Timezone for CRON evaluation</param>
        /// <param name="lastTriggeredDate">Last time notification was triggered (UTC)</param>
        /// <param name="currentTimeUtc">Current UTC time</param>
        /// <param name="executionWindowMinutes">Execution window tolerance in minutes (default: 10)</param>
        /// <param name="subscriptionId">Subscription ID for logging</param>
        /// <returns>True if notification should be sent</returns>
        bool IsTimeToSendNotification(
            string cronExpression,
            string timeZone,
            DateTime? lastTriggeredDate,
            DateTime currentTimeUtc,
            int executionWindowMinutes = 10,
            long? subscriptionId = null);

        /// <summary>
        /// Update the last triggered date for a schedule
        /// </summary>
        /// <param name="scheduleId">Schedule ID to update</param>
        /// <param name="currentTimeUtc">Current UTC time to set as last triggered</param>
        /// <param name="tableName">Table name (NotificationReportScheduleMappingTable or NotificationGroupsTable)</param>
        /// <param name="idColumnName">ID column name (ReportId or NotificationGroupUserId)</param>
        /// <param name="dbContext">Database context to use for the update</param>
        Task UpdateScheduleLastTriggered(long scheduleId, DateTime currentTimeUtc, string tableName, string idColumnName, object dbContext);

        /// <summary>
        /// Calculate next run time based on CRON expression
        /// </summary>
        /// <param name="cronExpression">CRON expression string</param>
        /// <param name="timeZone">Timezone for calculation</param>
        /// <param name="fromTime">Calculate from this time (UTC)</param>
        /// <returns>Next run time in UTC</returns>
        DateTime? CalculateNextRunTime(string cronExpression, string timeZone, DateTime fromTime);
    }
}
