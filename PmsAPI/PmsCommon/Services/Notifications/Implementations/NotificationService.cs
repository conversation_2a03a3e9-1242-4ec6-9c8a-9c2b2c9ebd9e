using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using PmsCore.Notifications.Interfaces;
using PmsCore.Notifications.Models;
using PmsCommon.Services.Scheduling.Interfaces;
using System.Text;
using System.Text.Json;
using QuestPDF.Fluent;
using PmsCore.DataAccessRepository.Models;
using System.Net.Http;
namespace PmsCommon.Services.Notifications.Implementations
{
    public class NotificationService : INotificationService
    {
        private readonly INotificationRepository _notificationRepository;
        private readonly IEmailService _emailService;
        private readonly IWhatsAppService _whatsAppService;
        private readonly ILogger<NotificationService> _logger;
        private readonly INotificationTrackingService _trackingService;
        private readonly ISchedulingService _schedulingService;



        public NotificationService(
        INotificationRepository notificationRepository,
        IEmailService emailService,
        IWhatsAppService whatsAppService,
        ILogger<NotificationService> logger,
        INotificationTrackingService trackingService,
        ISchedulingService schedulingService)
        {
            _notificationRepository = notificationRepository;
            _emailService = emailService;
            _whatsAppService = whatsAppService;
            _logger = logger;
            _trackingService = trackingService;
            _schedulingService = schedulingService;
        }

        public async Task SendScheduledReportToRecipient(string reportType, long recipientId, string triggerType)
        {
            try
            {
                _logger.LogInformation("Sending scheduled report {ReportType} to recipient {RecipientId} with trigger type {TriggerType}",
                    reportType, recipientId, triggerType);

                // Handle specific report types that have custom implementations
                if (reportType.Equals("LowStockReport", StringComparison.OrdinalIgnoreCase))
                {
                    await SendLowStockReportNotification(triggerType, recipientId);
                    return;
                }
                if (reportType.Equals("ReturnableOutPassReport", StringComparison.OrdinalIgnoreCase))
                {
                    await SendPendingReturnableOutPassReportNotification(triggerType, recipientId);
                    return;
                }
                if (reportType.Equals("ExecutiveCostingReport", StringComparison.OrdinalIgnoreCase))
                {
                    // Calculate the date range for ExecutiveCostingReport (24-hour period from previous day 8:00 AM IST to current day 8:00 AM IST)
                    var currentTimeIST = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);
                    var currentDay8AMIST = currentTimeIST.Date.AddHours(8);
                    var previousDay8AMIST = currentDay8AMIST.AddDays(-1);
                    var fromDate = TimeZoneHelper.ConvertToUtc(previousDay8AMIST, TimeZoneId.IndiaStandardTime);
                    var toDate = TimeZoneHelper.ConvertToUtc(currentDay8AMIST, TimeZoneId.IndiaStandardTime);
                    await SendCostingReportPDFNotification(fromDate, toDate, triggerType, recipientId);
                    return;
                }
                if (reportType.Equals("YieldReportSummary", StringComparison.OrdinalIgnoreCase))
                {
                    // Calculate the date range for YieldReport (24-hour period from previous day 8:00 AM IST to current day 8:00 AM IST)
                    var currentTimeIST = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);
                    var currentDay8AMIST = currentTimeIST.Date.AddHours(8);
                    var previousDay8AMIST = currentDay8AMIST.AddDays(-1);
                    var fromDate = TimeZoneHelper.ConvertToUtc(previousDay8AMIST, TimeZoneId.IndiaStandardTime);
                    var toDate = TimeZoneHelper.ConvertToUtc(currentDay8AMIST, TimeZoneId.IndiaStandardTime);

                    await SendYieldReportSummaryWhatsApp(fromDate, toDate, triggerType, recipientId);
                    return;
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send scheduled report {ReportType} to recipient {RecipientId}", reportType, recipientId);
                throw;
            }
        }

        public async Task SendSaleOrderStatusNotification(long saleOrderId, long stageId)
        {
            try
            {
                var (template, parameters) = await _notificationRepository.GetSaleOrderStatusNotification(saleOrderId, stageId);

                if (template == null)
                {
                    _logger.LogWarning("No template found for sale order status notification. OrderId: {SaleOrderId}, Stage: {StageId}",
                        saleOrderId, stageId);
                    return;
                }

                var recipients = await _notificationRepository.GetReportRecipients("SaleOrderStatus", "SaleOrderStatusUpdate");

                foreach (var recipient in recipients)
                {
                    try
                    {
                        if (recipient.EnableToEmail)
                        {
                            await _emailService.SendEmailAsync(new EmailMessage
                            {
                                To = new[] { recipient.EmailId },
                                Subject = template.Subject,
                                Body = BuildEmailBody(parameters),
                                IsHtml = true
                            });

                            // Generate a unique notification message ID for email
                            var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                            // Track email notification
                            await _trackingService.TrackNotification(new NotificationTrackingModel
                            {
                                NotificationType = "SaleOrderStatus",
                                RecipientId = recipient.Id,
                                MessageContent = BuildEmailBody(parameters),
                                MessageType = "StatusUpdate",
                                Status = "Sent",
                                SentTime = DateTime.UtcNow,
                                NotificationGroupUserId = recipient.Id,
                                NotificationMessageId = emailNotificationMessageId,
                                RecipientEmail = recipient.EmailId
                            });
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            var whatsAppResponse = await _whatsAppService.SendTemplateMessageAsync(
                                template.WhatsAppProviderTemplateId.Value,
                                recipient.MobileNumbers,
                                parameters
                            );

                            // Generate a unique notification message ID for WhatsApp
                            var whatsAppNotificationMessageId = $"WA-{Guid.NewGuid():N}";

                            // Track the notification for each mobile number individually
                            foreach (var mobileNumber in recipient.MobileNumbers)
                            {
                                await _trackingService.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "SaleOrderStatus",
                                    RecipientId = recipient.Id,
                                    MasterTemplateId = template.WhatsAppProviderTemplateId ?? 0,
                                    ProviderMessageId = whatsAppResponse?.MessageId,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    ErrorMessage = whatsAppResponse?.ErrorMessage,
                                    MessageType = "StatusUpdate",
                                    Status = whatsAppResponse?.Success ?? false ? "Sent" : "Failed",
                                    SentTime = DateTime.UtcNow,
                                    NotificationGroupUserId = recipient.Id,
                                    NotificationMessageId = whatsAppNotificationMessageId,
                                    RecipientMobileNumber = mobileNumber
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send sale order status notification to recipient {RecipientId}. OrderId: {SaleOrderId}",
                            recipient.Id, saleOrderId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send sale order status notification. OrderId: {SaleOrderId}, Stage: {StageId}",
                    saleOrderId, stageId);
                throw;
            }
        }
        private async Task<ReportData> GenerateReportData(string reportType)
        {
            try
            {
                var reportData = await _notificationRepository.GenerateReportData(reportType);
                if (reportData == null)
                {
                    _logger.LogWarning("No data found for report type: {ReportType}", reportType);
                    return null;
                }

                return reportData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate report data for type {ReportType}", reportType);
                throw;
            }
        }
        /// <summary>
        /// Sends a notification to a recipient with the specified parameters
        /// </summary>
        /// <param name="notificationType">Type of notification (e.g., "WhatsApp", "Email")</param>
        /// <param name="recipientId">ID of the recipient</param>
        /// <param name="parameters">Dictionary of parameters to include in the notification</param>
        /// <param name="messageType">Type of message (e.g., "Alert", "Report", "OnDemand")</param>
        /// <param name="templateId">ID of the template to use</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task SendNotification(string notificationType, long recipientId, Dictionary<string, string> parameters, string messageType, long templateId)
        {
            try
            {
                _logger.LogInformation("Sending {NotificationType} notification to recipient {RecipientId}", notificationType, recipientId);

                // Get the recipient details
                var recipient = await _notificationRepository.GetRecipientById(recipientId)
                    ?? throw new ArgumentException($"Recipient not found: {recipientId}");

                // Check rate limits
                var withinLimits = await _notificationRepository.CheckRateLimit(notificationType, recipientId);
                if (!withinLimits)
                {
                    var limits = await _notificationRepository.GetNotificationLimits(notificationType);
                    _logger.LogWarning("Rate limit exceeded for {NotificationType} notification to recipient {RecipientId}. " +
                        "Daily limit: {DailyLimit}, Monthly limit: {MonthlyLimit}",
                        notificationType, recipientId, limits.MaxDailyMessages, limits.MaxMonthlyMessages);

                    // Generate a unique notification message ID for rate limit tracking
                    var rateLimitNotificationMessageId = $"RL-{Guid.NewGuid():N}";

                    // Get the recipient details to include contact information
                    var recipientContacts = recipient.MobileNumbers.Any()
                        ? recipient.MobileNumbers.First()
                        : null;

                    // Track the rate limit exceeded event
                    await _trackingService.TrackNotification(new NotificationTrackingModel
                    {
                        NotificationType = notificationType,
                        RecipientId = recipientId,
                        MessageType = messageType,
                        Status = "RateLimitExceeded",
                        ErrorMessage = $"Daily/Monthly limit exceeded for {notificationType}",
                        SentTime = DateTime.UtcNow,
                        NotificationGroupUserId = recipientId,
                        NotificationMessageId = rateLimitNotificationMessageId,
                        RecipientMobileNumber = recipientContacts,
                        RecipientEmail = recipient.EmailId
                    });

                    return; // Exit without sending notification
                }

                // Validate template access if applicable
                if (templateId > 0)
                {
                    var allowedTemplates = await _notificationRepository.GetRecipientTemplateIds(recipientId, notificationType);
                    if (!allowedTemplates.Contains(templateId))
                    {
                        _logger.LogWarning("Template {TemplateId} is not allowed for recipient {RecipientId}", templateId, recipientId);
                        throw new ArgumentException($"Template {templateId} is not allowed for recipient {recipientId}");
                    }
                }

                // Send the notification based on recipient preferences
                WhatsAppResponse whatsAppResponse = null;

                if (recipient.IsWhatsAppNotificationEnabled &&
                    (notificationType.Equals("WhatsApp", StringComparison.OrdinalIgnoreCase) ||
                     notificationType.Equals("All", StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogInformation("Sending WhatsApp notification to recipient {RecipientId}", recipient.Id);

                    whatsAppResponse = await _whatsAppService.SendTemplateMessageAsync(
                        templateId,
                        recipient.MobileNumbers,
                        parameters
                    );

                    // Generate a unique notification message ID for WhatsApp
                    var notificationMessageId = $"WA-{Guid.NewGuid():N}";

                    // Track the notification for each mobile number
                    foreach (var mobileNumber in recipient.MobileNumbers)
                    {
                        await _trackingService.TrackNotification(new NotificationTrackingModel
                        {
                            NotificationType = notificationType,
                            RecipientId = recipient.Id,
                            MasterTemplateId = templateId,
                            ProviderMessageId = whatsAppResponse?.MessageId,
                            MessageContent = JsonSerializer.Serialize(parameters),
                            ErrorMessage = whatsAppResponse?.ErrorMessage,
                            MessageType = messageType,
                            Status = whatsAppResponse?.Success ?? false ? "Sent" : "Failed",
                            SentTime = DateTime.UtcNow,
                            NotificationGroupUserId = recipient.Id,
                            NotificationMessageId = notificationMessageId,
                            RecipientMobileNumber = mobileNumber
                        });
                    }

                    if (!whatsAppResponse.Success)
                    {
                        _logger.LogError("Failed to send WhatsApp notification. Error: {ErrorMessage}", whatsAppResponse.ErrorMessage);
                    }
                }

                if (recipient.EnableToEmail &&
                    (notificationType.Equals("Email", StringComparison.OrdinalIgnoreCase) ||
                     notificationType.Equals("All", StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogInformation("Sending email notification to recipient {RecipientId}", recipient.Id);

                    await _emailService.SendEmailAsync(new EmailMessage
                    {
                        To = new[] { recipient.EmailId },
                        Subject = $"{notificationType} Notification",
                        Body = BuildEmailBody(parameters),
                        IsHtml = true
                    });

                    // Generate a unique notification message ID for email
                    var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                    // Track email notification
                    await _trackingService.TrackNotification(new NotificationTrackingModel
                    {
                        NotificationType = notificationType,
                        RecipientId = recipient.Id,
                        MessageContent = BuildEmailBody(parameters),
                        MessageType = messageType,
                        Status = "Sent",
                        SentTime = DateTime.UtcNow,
                        NotificationGroupUserId = recipient.Id,
                        NotificationMessageId = emailNotificationMessageId,
                        RecipientEmail = recipient.EmailId
                    });
                }

                // Note: Rate limit tracking is handled by the tracking service

                _logger.LogInformation("Successfully sent {NotificationType} notification to recipient {RecipientId}",
                    notificationType, recipientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send {NotificationType} notification to recipient {RecipientId}",
                    notificationType, recipientId);
                throw;
            }
        }
        public async Task SendLowStockNotification(long productId)
        {
            try
            {
                var stockAvail = await _notificationRepository.GetProductStockAvailabilty(productId);
                if (stockAvail != null)
                {
                    var recipients = await _notificationRepository.GetReportRecipients("LowStock", null, "Event");
                    if (recipients == null)
                    {
                        _logger.LogWarning("No recipients found for low stock notification. ProductId: {ProductId}", productId);
                        return;
                    }
                    if (recipients.Any(r => r.WhatsAppTemplateMasterId == 0))
                    {
                        _logger.LogWarning("No WhatsApp template found for low stock notification. ProductId: {ProductId}", productId);
                        return;
                    }

                    // Generate a unique notification message ID for WhatsApp
                    var whatsAppNotificationMessageId = $"WA-{Guid.NewGuid():N}";

                    foreach (var recipient in recipients.Where(r => r.NotificationType == "LowStock"))
                    {
                        try
                        {
                            var (providerTemplate, config, settings) = await _notificationRepository.GetWhatsAppTemplateAndConfig(recipient.WhatsAppTemplateMasterId);


                            WhatsAppResponse whatsAppResponse = null;
                            if (recipient.EnableToEmail)
                            {
                                var parameters = await _notificationRepository.GenerateLowStockParameters(productId, recipient.WhatsAppTemplateMasterId, stockAvail);
                                await _emailService.SendEmailAsync(new EmailMessage
                                {
                                    To = new[] { recipient.EmailId },
                                    Subject = $"Low Stock Alert - {parameters["ProductName"]}",
                                    Body = BuildEmailBody(parameters),
                                    IsHtml = true
                                });

                                // Generate a unique notification message ID for email
                                var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                                // Track email notification with the recipient's email address
                                await _trackingService.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "LowStock",
                                    RecipientId = recipient.Id,
                                    MessageContent = BuildEmailBody(parameters),
                                    MessageType = "Alert",
                                    Status = "Sent", // Email status is always "Sent" at this point
                                    SentTime = DateTime.UtcNow,
                                    NotificationGroupUserId = recipient.Id,
                                    NotificationMessageId = emailNotificationMessageId,
                                    RecipientEmail = recipient.EmailId
                                });
                            }

                            if (recipient.IsWhatsAppNotificationEnabled)
                            {
                                // Use enhanced parameter collection for provider-specific handling
                                var parameterCollection = _notificationRepository.GenerateLowStockParametersEnhanced(recipient.WhatsAppTemplateMasterId, stockAvail);

                                whatsAppResponse = await _whatsAppService.SendTemplateMessageAsync(
                                    recipient.WhatsAppTemplateMasterId,
                                    recipient.MobileNumbers,
                                    parameterCollection
                                );
                                if (!whatsAppResponse.Success)
                                {
                                    _logger.LogError("Failed to send WhatsApp template message. Error: {ErrorMessage}", whatsAppResponse.ErrorMessage);
                                }

                                // Track the notification for each mobile number individually
                                foreach (var mobileNumber in recipient.MobileNumbers)
                                {
                                    await _trackingService.TrackNotification(new NotificationTrackingModel
                                    {
                                        NotificationType = "LowStock",
                                        RecipientId = recipient.Id,
                                        MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                                        ProviderMessageId = whatsAppResponse?.MessageId,
                                        MessageContent = JsonSerializer.Serialize(providerTemplate.ProviderName == "Brevo" ? parameterCollection.ToNamedParameterDictionary() : parameterCollection.ToNumberedParameterDictionary()),
                                        ErrorMessage = whatsAppResponse?.ErrorMessage,
                                        MessageType = "Alert",
                                        Status = whatsAppResponse?.Success ?? false ? "Sent" : "Failed",
                                        SentTime = DateTime.UtcNow,
                                        NotificationGroupUserId = recipient.Id,
                                        NotificationMessageId = whatsAppNotificationMessageId,
                                        RecipientMobileNumber = mobileNumber
                                    });
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to send low stock notification to recipient {RecipientId}", recipient.Id);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send low stock notification for ProductId: {ProductId}", productId);
                throw;
            }
        }
        public async Task SendYieldReportSummaryWhatsApp(DateTime fromDate, DateTime toDate, string TriggerType, long? recipientId = null)
        {
            try
            {
                _logger.LogInformation("Generating and sending yield report summary for period {FromDate} to {ToDate}, trigger type: {TriggerType}, recipientId: {RecipientId}",
                    fromDate.ToString("yyyy-MM-dd"), toDate.ToString("yyyy-MM-dd"), TriggerType, recipientId?.ToString() ?? "ALL");

                // Generate the PDF and get the URL
                var pdfUrl = await _notificationRepository.GenerateYieldSummaryReportPdfAndUploadToStorageAsync(fromDate, toDate);
                if (pdfUrl == null)
                {
                    _logger.LogWarning("Failed to generate yield report PDF");
                    return;
                }

                _logger.LogInformation("Successfully generated yield report PDF: {PdfUrl}", pdfUrl);

                // Get recipients - either specific recipient or all recipients for YieldReportSummary notification type
                List<NotificationRecipient> recipients;
                if (recipientId.HasValue)
                {
                    // Get specific recipient
                    var recipient = await _notificationRepository.GetRecipientById(recipientId.Value);
                    if (recipient == null)
                    {
                        _logger.LogWarning("Recipient {RecipientId} not found for yield report summary notification", recipientId.Value);
                        return;
                    }
                    recipients = new List<NotificationRecipient> { recipient };
                    _logger.LogInformation("Sending yield report to specific recipient {RecipientId}: {RecipientName}", recipientId.Value, recipient.Name);
                }
                else
                {
                    // Get all recipients for YieldReportSummary notification type
                    recipients = await _notificationRepository.GetReportRecipients("Reports", "YieldReportSummary", TriggerType);
                    if (recipients == null || !recipients.Any())
                    {
                        _logger.LogWarning("No recipients found for yield report summary notification");
                        return;
                    }
                    _logger.LogInformation("Sending yield report to {RecipientCount} recipients", recipients.Count);
                }
                // Generate a unique notification message ID for WhatsApp
                var notificationMessageId = $"WA-{Guid.NewGuid():N}";

                // Send to each recipient
                foreach (var recipient in recipients)
                {
                    try
                    {
                        //Fetch parameters from template parameter master
                        var parameters = await _notificationRepository.GenerateYieldSummaryReportParameters(recipient.WhatsAppTemplateMasterId, fromDate, toDate);
                        if (parameters == null)
                        {
                            _logger.LogWarning("Failed to generate parameters for yield report summary notification");
                            continue;
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            _logger.LogInformation("Sending yield report to recipient {RecipientId} via WhatsApp", recipient.Id);

                            var response = await _whatsAppService.SendTemplateDocumentMessageAsync(
                                recipient.WhatsAppTemplateMasterId,
                                recipient.MobileNumbers,
                                parameters,
                                pdfUrl
                            );

                            // Track the notification for each mobile number
                            foreach (var mobileNumber in recipient.MobileNumbers)
                            {
                                await _trackingService.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "YieldReportSummary",
                                    RecipientId = recipient.Id,
                                    MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                                    ProviderMessageId = response?.MessageId,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    ErrorMessage = response?.ErrorMessage,
                                    MessageType = "Report",
                                    Status = response?.Success ?? false ? "Sent" : "Failed",
                                    SentTime = DateTime.UtcNow,
                                    NotificationGroupUserId = recipient.Id,
                                    NotificationMessageId = notificationMessageId,
                                    RecipientMobileNumber = mobileNumber
                                });
                            }

                            if (!response.Success)
                            {
                                _logger.LogError("Failed to send WhatsApp document message. Error: {ErrorMessage}", response.ErrorMessage);
                            }
                        }

                        // Also send via email if enabled
                        if (recipient.EnableToEmail)
                        {
                            _logger.LogInformation("Sending yield report to recipient {RecipientId} via Email", recipient.Id);

                            await _emailService.SendEmailAsync(new EmailMessage
                            {
                                To = new[] { recipient.EmailId },
                                Subject = $"Yield Report Summary ({fromDate:dd-MMM-yyyy} to {toDate:dd-MMM-yyyy})",
                                Body = $"<p>Please find the Yield Report Summary for the period {fromDate:dd-MMM-yyyy} to {toDate:dd-MMM-yyyy}.</p>" +
                                       $"<p>You can download the report from <a href='{pdfUrl}'>here</a>.</p>",
                                IsHtml = true
                            });

                            // Generate a unique notification message ID for email
                            var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                            // Track email notification
                            await _trackingService.TrackNotification(new NotificationTrackingModel
                            {
                                NotificationType = "YieldReportSummary",
                                RecipientId = recipient.Id,
                                MessageContent = $"Yield Report Summary for {fromDate:dd-MMM-yyyy} to {toDate:dd-MMM-yyyy}",
                                MessageType = "Report",
                                Status = "Sent",
                                SentTime = DateTime.UtcNow,
                                NotificationGroupUserId = recipient.Id,
                                NotificationMessageId = emailNotificationMessageId,
                                RecipientEmail = recipient.EmailId
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send yield report to recipient {RecipientId}", recipient.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send yield report WhatsApp notification");
                throw;
            }
        }



        public async Task TriggerOnDemandNotification(long notificationGroupId, Dictionary<string, string> parameters)
        {
            try
            {
                _logger.LogInformation("Triggering on-demand notification for group {NotificationGroupId}", notificationGroupId);

                // Get the notification group details
                var recipient = await _notificationRepository.GetRecipientById(notificationGroupId)
                    ?? throw new ArgumentException($"Notification group not found: {notificationGroupId}");

                // Verify this is an on-demand notification
                // The TriggerType might be stored in NotificationType or as a separate property
                // depending on the database schema
                var triggerType = recipient.NotificationType;
                if (triggerType != "OnDemand" && !triggerType.Contains("OnDemand"))
                {
                    _logger.LogWarning("Notification group {NotificationGroupId} is not configured for on-demand notifications. Type: {TriggerType}",
                        notificationGroupId, triggerType);
                    throw new ArgumentException($"Notification group {notificationGroupId} is not configured for on-demand notifications");
                }

                // Send the notification based on recipient preferences
                WhatsAppResponse whatsAppResponse = null;

                if (recipient.IsWhatsAppNotificationEnabled)
                {
                    _logger.LogInformation("Sending on-demand WhatsApp notification to recipient {RecipientId}", recipient.Id);

                    whatsAppResponse = await _whatsAppService.SendTemplateMessageAsync(
                        recipient.WhatsAppTemplateMasterId,
                        recipient.MobileNumbers,
                        parameters
                    );

                    // Generate a unique notification message ID for WhatsApp
                    var notificationMessageId = $"WA-{Guid.NewGuid():N}";

                    // Track the notification for each mobile number
                    foreach (var mobileNumber in recipient.MobileNumbers)
                    {
                        await _trackingService.TrackNotification(new NotificationTrackingModel
                        {
                            NotificationType = "OnDemand",
                            RecipientId = recipient.Id,
                            MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                            ProviderMessageId = whatsAppResponse?.MessageId,
                            MessageContent = JsonSerializer.Serialize(parameters),
                            ErrorMessage = whatsAppResponse?.ErrorMessage,
                            MessageType = "OnDemand",
                            Status = whatsAppResponse?.Success ?? false ? "Sent" : "Failed",
                            SentTime = DateTime.UtcNow,
                            NotificationGroupUserId = notificationGroupId,
                            NotificationMessageId = notificationMessageId,
                            RecipientMobileNumber = mobileNumber
                        });
                    }

                    if (!whatsAppResponse.Success)
                    {
                        _logger.LogError("Failed to send on-demand WhatsApp notification. Error: {ErrorMessage}", whatsAppResponse.ErrorMessage);
                    }
                }

                if (recipient.EnableToEmail)
                {
                    _logger.LogInformation("Sending on-demand email notification to recipient {RecipientId}", recipient.Id);

                    await _emailService.SendEmailAsync(new EmailMessage
                    {
                        To = new[] { recipient.EmailId },
                        Subject = "On-Demand Notification",
                        Body = BuildEmailBody(parameters),
                        IsHtml = true
                    });

                    // Generate a unique notification message ID for email
                    var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                    // Track email notification
                    await _trackingService.TrackNotification(new NotificationTrackingModel
                    {
                        NotificationType = "OnDemand",
                        RecipientId = recipient.Id,
                        MessageContent = BuildEmailBody(parameters),
                        MessageType = "OnDemand",
                        Status = "Sent",
                        SentTime = DateTime.UtcNow,
                        NotificationGroupUserId = notificationGroupId,
                        NotificationMessageId = emailNotificationMessageId,
                        RecipientEmail = recipient.EmailId
                    });
                }

                // Update the LastTriggeredBy and LastTriggeredDate in the database
                await _notificationRepository.UpdateOnDemandNotificationStatus(notificationGroupId);

                _logger.LogInformation("Successfully triggered on-demand notification for group {NotificationGroupId}", notificationGroupId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to trigger on-demand notification for group {NotificationGroupId}", notificationGroupId);
                throw;
            }
        }

        private string BuildEmailBody(Dictionary<string, string> parameters)
        {
            try
            {
                var sb = new StringBuilder();
                sb.Append("<html><body>");
                sb.Append("<h2>Notification Details</h2>");
                sb.Append("<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>");
                sb.Append("<tr><th>Parameter</th><th>Value</th></tr>");

                foreach (var param in parameters)
                {
                    sb.Append("<tr>");
                    sb.Append($"<td>{param.Key}</td>");

                    // Check if the value is a URL
                    if (Uri.TryCreate(param.Value, UriKind.Absolute, out Uri uriResult) &&
                        (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps))
                    {
                        sb.Append($"<td><a href='{param.Value}'>View/Download</a></td>");
                    }
                    else
                    {
                        sb.Append($"<td>{param.Value}</td>");
                    }

                    sb.Append("</tr>");
                }

                sb.Append("</table>");
                sb.Append("<p>This is an automated notification from the PMS system.</p>");
                sb.Append("</body></html>");

                return sb.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building email body");
                return "<p>Notification details could not be displayed.</p>";
            }
        }

        public async Task SendLowStockReportNotification(string triggerType, long? recipientId = null)
        {
            try
            {
                _logger.LogInformation("Generating and sending low stock report notification for trigger type: {TriggerType}, recipientId: {RecipientId}",
                    triggerType, recipientId?.ToString() ?? "ALL");

                // Generate the PDF and get the URL
                var pdfUrl = await _notificationRepository.GenerateLowStockReportPdfAndUploadToStorageAsync();
                if (pdfUrl == null)
                {
                    _logger.LogWarning("Failed to generate low stock report PDF or no low stock items found");
                    return;
                }

                // Get recipients based on whether specific recipient is requested
                List<NotificationRecipient> recipients;
                if (recipientId.HasValue)
                {
                    var recipient = await _notificationRepository.GetRecipientById(recipientId.Value);
                    recipients = recipient != null ? new List<NotificationRecipient> { recipient } : new List<NotificationRecipient>();
                    _logger.LogInformation("Processing low stock report for specific recipient {RecipientId}", recipientId.Value);
                }
                else
                {
                    recipients = await _notificationRepository.GetReportRecipients("Reports", "LowStockReport", triggerType);
                    _logger.LogInformation("Processing low stock report for all recipients with trigger type: {TriggerType}", triggerType);
                }

                if (recipients == null || !recipients.Any())
                {
                    _logger.LogWarning("No recipients found for low stock report notifications with trigger type: {TriggerType}, recipientId: {RecipientId}",
                        triggerType, recipientId?.ToString() ?? "ALL");
                    return;
                }

                _logger.LogInformation("Found {RecipientCount} recipients for low stock report notification", recipients.Count);

                // Send to each recipient
                foreach (var recipient in recipients)
                {
                    try
                    {
                        // Fetch parameters from template parameter master
                        var parameters = await _notificationRepository.GenerateLowStockReportParameters(recipient.WhatsAppTemplateMasterId);
                        if (parameters == null)
                        {
                            _logger.LogWarning("Failed to generate parameters for low stock report notification");
                            continue;
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            _logger.LogInformation("Sending low stock report to recipient {RecipientId} via WhatsApp", recipient.Id);

                            var response = await _whatsAppService.SendTemplateDocumentMessageAsync(
                                recipient.WhatsAppTemplateMasterId,
                                recipient.MobileNumbers,
                                parameters,
                                pdfUrl
                            );

                            if (response.Success)
                            {
                                _logger.LogInformation("Successfully sent low stock report WhatsApp notification to recipient {RecipientId}", recipient.Id);

                                // Track the notification
                                await _notificationRepository.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "WhatsApp",
                                    MessageType = "LowStockReport",
                                    RecipientId = recipient.Id,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                                    Status = "Sent",
                                    ProviderMessageId = response.MessageId,
                                    SentTime = DateTime.UtcNow,
                                    NotificationMessageId = $"WA-{Guid.NewGuid():N}",
                                    RecipientMobileNumber = string.Join(",", recipient.MobileNumbers)
                                });
                            }
                            else
                            {
                                _logger.LogError("Failed to send low stock report WhatsApp notification to recipient {RecipientId}: {Error}",
                                    recipient.Id, response.ErrorMessage);
                            }
                        }

                        // Send email notification if enabled
                        if (recipient.EnableToEmail && !string.IsNullOrEmpty(recipient.EmailId))
                        {
                            _logger.LogInformation("Sending low stock report to recipient {RecipientId} via Email", recipient.Id);

                            try
                            {
                                // Download PDF for email attachment
                                using var httpClient = new HttpClient();
                                var pdfBytes = await httpClient.GetByteArrayAsync(pdfUrl);

                                var emailMessage = new EmailMessage
                                {
                                    To = new[] { recipient.EmailId },
                                    Subject = "Low Stock Report",
                                    Body = $"<p>Dear {recipient.Name},</p><p>Please find attached the Low Stock Report generated on {parameters.GetValueOrDefault("generateddate", DateTime.Now.ToString("dd-MMM-yyyy HH:mm"))}.</p><p>Best regards,<br/>PMS System</p>",
                                    IsHtml = true
                                };

                                await _emailService.SendEmailWithAttachmentAsync(
                                    emailMessage,
                                    pdfBytes,
                                    "LowStockReport.pdf",
                                    "application/pdf"
                                );

                                _logger.LogInformation("Successfully sent low stock report email notification to recipient {RecipientId}", recipient.Id);

                                // Track the email notification
                                await _notificationRepository.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "Email",
                                    MessageType = "LowStockReport",
                                    RecipientId = recipient.Id,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    MasterTemplateId = 0, // Email doesn't use template master
                                    Status = "Sent",
                                    SentTime = DateTime.UtcNow,
                                    NotificationMessageId = $"EM-{Guid.NewGuid():N}",
                                    RecipientEmail = recipient.EmailId
                                });
                            }
                            catch (Exception emailEx)
                            {
                                _logger.LogError(emailEx, "Failed to send low stock report email notification to recipient {RecipientId}", recipient.Id);
                            }
                        }
                    }
                    catch (Exception recipientEx)
                    {
                        _logger.LogError(recipientEx, "Error processing low stock report notification for recipient {RecipientId}", recipient.Id);
                    }
                }

                _logger.LogInformation("Completed sending low stock report notifications for trigger type: {TriggerType}", triggerType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending low stock report notification for trigger type: {TriggerType}", triggerType);
                throw;
            }
        }

        public async Task SendPendingReturnableOutPassReminderNotification(long outpassId, string triggerType)
        {
            try
            {
                _logger.LogInformation("Generating and sending pending returnable outpass reminder notification for outpass ID: {OutpassId}, trigger type: {TriggerType}", outpassId, triggerType);

                // Generate the PDF and get the URL
                var pdfUrl = await _notificationRepository.GeneratePendingReturnableOutPassReminderPdfAndUploadToStorageAsync(outpassId);
                if (pdfUrl == null)
                {
                    _logger.LogWarning("Failed to generate pending returnable outpass reminder PDF for outpass ID: {OutpassId}", outpassId);
                    return;
                }

                // Get recipients for the pending returnable outpass reminder
                var recipients = await _notificationRepository.GetReportRecipients("ReturnableOutPassReminder", null, triggerType);
                if (recipients == null || !recipients.Any())
                {
                    _logger.LogWarning("No recipients found for pending returnable outpass reminder notification");
                    return;
                }

                _logger.LogInformation("Found {RecipientCount} recipients for pending returnable outpass reminder notification", recipients.Count);

                // Send to each recipient
                foreach (var recipient in recipients)
                {
                    try
                    {
                        // Fetch parameters from template parameter master
                        var parameters = await _notificationRepository.GeneratePendingReturnableOutPassReminderParameters(outpassId, recipient.WhatsAppTemplateMasterId);
                        if (parameters == null)
                        {
                            _logger.LogWarning("Failed to generate parameters for pending returnable outpass reminder notification");
                            continue;
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            _logger.LogInformation("Sending pending returnable outpass reminder to recipient {RecipientId} via WhatsApp", recipient.Id);

                            var response = await _whatsAppService.SendTemplateDocumentMessageAsync(
                                recipient.WhatsAppTemplateMasterId,
                                recipient.MobileNumbers,
                                parameters,
                                pdfUrl
                            );

                            if (response.Success)
                            {
                                _logger.LogInformation("Successfully sent pending returnable outpass reminder WhatsApp notification to recipient {RecipientId}", recipient.Id);

                                // Track the notification
                                await _notificationRepository.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "WhatsApp",
                                    MessageType = "ReturnableOutPassReminder",
                                    RecipientId = recipient.Id,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                                    Status = "Sent",
                                    ProviderMessageId = response.MessageId,
                                    SentTime = DateTime.UtcNow,
                                    NotificationMessageId = $"WA-{Guid.NewGuid():N}",
                                    RecipientMobileNumber = string.Join(",", recipient.MobileNumbers)
                                });
                            }
                            else
                            {
                                _logger.LogError("Failed to send pending returnable outpass reminder WhatsApp notification to recipient {RecipientId}: {Error}",
                                    recipient.Id, response.ErrorMessage);
                            }
                        }

                        // Send email notification if enabled
                        if (recipient.EnableToEmail && !string.IsNullOrEmpty(recipient.EmailId))
                        {
                            _logger.LogInformation("Sending pending returnable outpass reminder to recipient {RecipientId} via Email", recipient.Id);

                            try
                            {
                                // Download PDF for email attachment
                                using var httpClient = new HttpClient();
                                var pdfBytes = await httpClient.GetByteArrayAsync(pdfUrl);

                                var emailMessage = new EmailMessage
                                {
                                    To = new[] { recipient.EmailId },
                                    Subject = "Pending Returnable OutPass Reminder",
                                    Body = $"<p>Dear {recipient.Name},</p><p>Please find attached the Pending Returnable OutPass Reminder generated on {parameters.GetValueOrDefault("generateddate", DateTime.Now.ToString("dd-MMM-yyyy HH:mm"))}.</p><p>Best regards,<br/>PMS System</p>",
                                    IsHtml = true
                                };

                                await _emailService.SendEmailWithAttachmentAsync(
                                    emailMessage,
                                    pdfBytes,
                                    "PendingReturnableOutPassReminder.pdf",
                                    "application/pdf"
                                );

                                _logger.LogInformation("Successfully sent pending returnable outpass reminder email notification to recipient {RecipientId}", recipient.Id);

                                // Track the email notification
                                await _notificationRepository.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "Email",
                                    MessageType = "ReturnableOutPassReminder",
                                    RecipientId = recipient.Id,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    MasterTemplateId = 0, // Email doesn't use template master
                                    Status = "Sent",
                                    SentTime = DateTime.UtcNow,
                                    NotificationMessageId = $"EM-{Guid.NewGuid():N}",
                                    RecipientEmail = recipient.EmailId
                                });
                            }
                            catch (Exception emailEx)
                            {
                                _logger.LogError(emailEx, "Failed to send pending returnable outpass reminder email notification to recipient {RecipientId}", recipient.Id);
                            }
                        }
                    }
                    catch (Exception recipientEx)
                    {
                        _logger.LogError(recipientEx, "Error processing pending returnable outpass reminder notification for recipient {RecipientId}", recipient.Id);
                    }
                }

                _logger.LogInformation("Completed sending pending returnable outpass reminder notifications for outpass ID: {OutpassId}, trigger type: {TriggerType}", outpassId, triggerType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendPendingReturnableOutPassReminderNotification for outpass ID: {OutpassId}, trigger type: {TriggerType}", outpassId, triggerType);
                throw;
            }
        }

        public async Task SendPendingReturnableOutPassReportNotification(string triggerType, long? recipientId = null)
        {
            try
            {
                _logger.LogInformation("Generating and sending pending returnable outpass report notification for trigger type: {TriggerType}, recipientId: {RecipientId}",
                    triggerType, recipientId?.ToString() ?? "ALL");

                // Generate the PDF and get the URL
                var pdfUrl = await _notificationRepository.GeneratePendingReturnableOutPassReportPdfAndUploadToStorageAsync();
                if (pdfUrl == null)
                {
                    _logger.LogWarning("Failed to generate pending returnable outpass report PDF or no pending outpasses found");
                    return;
                }

                // Get recipients based on whether specific recipient is requested
                List<NotificationRecipient> recipients;
                if (recipientId.HasValue)
                {
                    var recipient = await _notificationRepository.GetRecipientById(recipientId.Value);
                    recipients = recipient != null ? new List<NotificationRecipient> { recipient } : new List<NotificationRecipient>();
                    _logger.LogInformation("Processing pending returnable outpass report for specific recipient {RecipientId}", recipientId.Value);
                }
                else
                {
                    recipients = await _notificationRepository.GetReportRecipients("Reports", "ReturnableOutPassReport", triggerType);
                    _logger.LogInformation("Processing pending returnable outpass report for all recipients with trigger type: {TriggerType}", triggerType);
                }

                if (recipients == null || !recipients.Any())
                {
                    _logger.LogWarning("No recipients found for pending returnable outpass report notifications with trigger type: {TriggerType}, recipientId: {RecipientId}",
                        triggerType, recipientId?.ToString() ?? "ALL");
                    return;
                }

                _logger.LogInformation("Found {RecipientCount} recipients for pending returnable outpass report notification", recipients.Count);

                // Send to each recipient
                foreach (var recipient in recipients)
                {
                    try
                    {
                        // Fetch parameters from template parameter master
                        var parameters = await _notificationRepository.GeneratePendingReturnableOutPassReportParameters(recipient.WhatsAppTemplateMasterId);
                        if (parameters == null)
                        {
                            _logger.LogWarning("Failed to generate parameters for pending returnable outpass report notification");
                            continue;
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            _logger.LogInformation("Sending pending returnable outpass report to recipient {RecipientId} via WhatsApp", recipient.Id);

                            var response = await _whatsAppService.SendTemplateDocumentMessageAsync(
                                recipient.WhatsAppTemplateMasterId,
                                recipient.MobileNumbers,
                                parameters,
                                pdfUrl
                            );

                            if (response.Success)
                            {
                                _logger.LogInformation("Successfully sent pending returnable outpass report WhatsApp notification to recipient {RecipientId}", recipient.Id);

                                // Track the notification
                                await _notificationRepository.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "WhatsApp",
                                    MessageType = "ReturnableOutPassReport",
                                    RecipientId = recipient.Id,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                                    Status = "Sent",
                                    ProviderMessageId = response.MessageId,
                                    SentTime = DateTime.UtcNow,
                                    NotificationMessageId = $"WA-{Guid.NewGuid():N}",
                                    RecipientMobileNumber = string.Join(",", recipient.MobileNumbers)
                                });
                            }
                            else
                            {
                                _logger.LogError("Failed to send pending returnable outpass report WhatsApp notification to recipient {RecipientId}: {Error}",
                                    recipient.Id, response.ErrorMessage);
                            }
                        }

                        // Send email notification if enabled
                        if (recipient.EnableToEmail && !string.IsNullOrEmpty(recipient.EmailId))
                        {
                            _logger.LogInformation("Sending pending returnable outpass report to recipient {RecipientId} via Email", recipient.Id);

                            try
                            {
                                // Download PDF for email attachment
                                using var httpClient = new HttpClient();
                                var pdfBytes = await httpClient.GetByteArrayAsync(pdfUrl);

                                var emailMessage = new EmailMessage
                                {
                                    To = new[] { recipient.EmailId },
                                    Subject = "Pending Returnable OutPass Report",
                                    Body = $"<p>Dear {recipient.Name},</p><p>Please find attached the Pending Returnable OutPass Report generated on {parameters.GetValueOrDefault("generateddate", DateTime.Now.ToString("dd-MMM-yyyy HH:mm"))}.</p><p>Best regards,<br/>PMS System</p>",
                                    IsHtml = true
                                };

                                await _emailService.SendEmailWithAttachmentAsync(
                                    emailMessage,
                                    pdfBytes,
                                    "PendingReturnableOutPassReport.pdf",
                                    "application/pdf"
                                );

                                _logger.LogInformation("Successfully sent pending returnable outpass report email notification to recipient {RecipientId}", recipient.Id);

                                // Track the email notification
                                await _notificationRepository.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "Email",
                                    MessageType = "ReturnableOutPassReport",
                                    RecipientId = recipient.Id,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    MasterTemplateId = 0, // Email doesn't use template master
                                    Status = "Sent",
                                    SentTime = DateTime.UtcNow,
                                    NotificationMessageId = $"EM-{Guid.NewGuid():N}",
                                    RecipientEmail = recipient.EmailId
                                });
                            }
                            catch (Exception emailEx)
                            {
                                _logger.LogError(emailEx, "Failed to send pending returnable outpass report email notification to recipient {RecipientId}", recipient.Id);
                            }
                        }
                    }
                    catch (Exception recipientEx)
                    {
                        _logger.LogError(recipientEx, "Error processing pending returnable outpass report notification for recipient {RecipientId}", recipient.Id);
                    }
                }

                _logger.LogInformation("Completed sending pending returnable outpass report notifications for trigger type: {TriggerType}", triggerType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending pending returnable outpass report notification for trigger type: {TriggerType}", triggerType);
                throw;
            }
        }

        public async Task SendScheduledOverdueReturnableOutPassReminders()
        {
            try
            {
                _logger.LogInformation("Starting scheduled overdue returnable out pass reminder processing");

                // Get overdue outpass data from the repository
                var overdueData = await _notificationRepository.GetOverdueReturnableOutPassesForScheduledReminders();

                if (!overdueData.Any())
                {
                    _logger.LogInformation("No overdue returnable out passes found for scheduled reminders");
                    return;
                }

                _logger.LogInformation("Found {Count} overdue returnable out passes for scheduled reminders", overdueData.Count);

                // Send reminder notification for each overdue out pass
                foreach (var outPassId in overdueData)
                {
                    try
                    {
                        await SendPendingReturnableOutPassReminderNotification(outPassId, "Scheduled");
                        _logger.LogInformation("Sent scheduled overdue reminder for OutPass {OutPassId}", outPassId);
                    }
                    catch (Exception outPassEx)
                    {
                        _logger.LogError(outPassEx, "Failed to send scheduled overdue reminder for OutPass {OutPassId}", outPassId);
                        // Continue processing other out passes
                    }
                }

                _logger.LogInformation("Completed scheduled overdue returnable out pass reminder processing");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendScheduledOverdueReturnableOutPassReminders");
                throw;
            }
        }

        public async Task SendCostingReportPDFNotification(DateTime fromDate, DateTime toDate, string triggerType, long? recipientId = null)
        {
            try
            {
                _logger.LogInformation("Generating and sending costing report PDF for period {FromDate} to {ToDate}, trigger type: {TriggerType}, recipientId: {RecipientId}",
                    fromDate.ToString("yyyy-MM-dd"), toDate.ToString("yyyy-MM-dd"), triggerType, recipientId?.ToString() ?? "ALL");

                // Generate the PDF and get the URL
                var pdfUrl = await _notificationRepository.GenerateCostingReportPdfAndUploadToStorageAsync(fromDate, toDate);
                if (pdfUrl == null)
                {
                    _logger.LogWarning("Failed to generate costing report PDF");
                    return;
                }

                _logger.LogInformation("Successfully generated costing report PDF: {PdfUrl}", pdfUrl);

                // Get recipients - either specific recipient or all recipients for CostingReportPDF notification type
                List<NotificationRecipient> recipients;
                if (recipientId.HasValue)
                {
                    // Get specific recipient
                    var recipient = await _notificationRepository.GetRecipientById(recipientId.Value);
                    if (recipient == null)
                    {
                        _logger.LogWarning("Recipient {RecipientId} not found for costing report PDF notification", recipientId.Value);
                        return;
                    }
                    recipients = new List<NotificationRecipient> { recipient };
                    _logger.LogInformation("Sending costing report to specific recipient {RecipientId}: {RecipientName}", recipientId.Value, recipient.Name);
                }
                else
                {
                    // Get all recipients for CostingReportPDF notification type
                    recipients = await _notificationRepository.GetReportRecipients("Reports", "ExecutiveCostingReport", triggerType);
                    if (recipients == null || !recipients.Any())
                    {
                        _logger.LogWarning("No recipients found for costing report PDF notification");
                        return;
                    }
                    _logger.LogInformation("Sending costing report to {RecipientCount} recipients", recipients.Count);
                }

                // Generate a unique notification message ID for WhatsApp
                var notificationMessageId = $"WA-{Guid.NewGuid():N}";

                // Send to each recipient
                foreach (var recipient in recipients)
                {
                    try
                    {
                        // Fetch parameters from template parameter master
                        var parameters = await _notificationRepository.GenerateCostingReportParameters(recipient.WhatsAppTemplateMasterId, fromDate, toDate);
                        if (parameters == null)
                        {
                            _logger.LogWarning("Failed to generate parameters for costing report PDF notification");
                            continue;
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            _logger.LogInformation("Sending costing report to recipient {RecipientId} via WhatsApp", recipient.Id);

                            var response = await _whatsAppService.SendTemplateDocumentMessageAsync(
                                recipient.WhatsAppTemplateMasterId,
                                recipient.MobileNumbers,
                                parameters,
                                pdfUrl
                            );

                            // Track the notification for each mobile number
                            foreach (var mobileNumber in recipient.MobileNumbers)
                            {
                                await _trackingService.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "WhatsApp",
                                    RecipientId = recipient.Id,
                                    MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                                    ProviderMessageId = response?.MessageId,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    ErrorMessage = response?.ErrorMessage,
                                    MessageType = "ExecutiveCostingReport",
                                    Status = response?.Success ?? false ? "Sent" : "Failed",
                                    SentTime = DateTime.UtcNow,
                                    NotificationGroupUserId = recipient.Id,
                                    NotificationMessageId = notificationMessageId,
                                    RecipientMobileNumber = mobileNumber
                                });
                            }

                            if (!response.Success)
                            {
                                _logger.LogError("Failed to send WhatsApp document message. Error: {ErrorMessage}", response.ErrorMessage);
                            }
                        }

                        // Also send via email if enabled
                        if (recipient.EnableToEmail)
                        {
                            _logger.LogInformation("Sending costing report to recipient {RecipientId} via Email", recipient.Id);

                            await _emailService.SendEmailAsync(new EmailMessage
                            {
                                To = new[] { recipient.EmailId },
                                Subject = $"Costing Report PDF ({fromDate:dd-MMM-yyyy} to {toDate:dd-MMM-yyyy})",
                                Body = $"<p>Please find the Costing Report PDF for the period {fromDate:dd-MMM-yyyy} to {toDate:dd-MMM-yyyy}.</p>" +
                                       $"<p>You can download the report from <a href='{pdfUrl}'>here</a>.</p>",
                                IsHtml = true
                            });

                            // Generate a unique notification message ID for email
                            var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                            // Track email notification
                            await _trackingService.TrackNotification(new NotificationTrackingModel
                            {
                                NotificationType = "ExecutiveCostingReport",
                                RecipientId = recipient.Id,
                                MessageContent = $"Costing Report PDF for {fromDate:dd-MMM-yyyy} to {toDate:dd-MMM-yyyy}",
                                MessageType = "Report",
                                Status = "Sent",
                                SentTime = DateTime.UtcNow,
                                NotificationGroupUserId = recipient.Id,
                                NotificationMessageId = emailNotificationMessageId,
                                RecipientEmail = recipient.EmailId
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send costing report to recipient {RecipientId}", recipient.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send costing report PDF notification");
                throw;
            }
        }

        public async Task SendOverheadCostUpdateReminderNotification(string triggerType, long? recipientId = null)
        {
            try
            {
                _logger.LogInformation("Sending overhead cost update reminder notification, trigger type: {TriggerType}, recipientId: {RecipientId}",
                    triggerType, recipientId?.ToString() ?? "ALL");

                // Get overdue overhead cost data
                var overdueData = await _notificationRepository.GetOverdueOverheadCostData();
                if (overdueData == null || !overdueData.Any())
                {
                    _logger.LogInformation("No overdue overhead cost updates found");
                    return;
                }

                _logger.LogInformation("Found {Count} overdue overhead cost entries", overdueData.Count);

                // Get recipients - either specific recipient or all recipients for OverheadCostUpdateReminder notification type
                List<NotificationRecipient> recipients;
                if (recipientId.HasValue)
                {
                    // Get specific recipient
                    var recipient = await _notificationRepository.GetRecipientById(recipientId.Value);
                    if (recipient == null)
                    {
                        _logger.LogWarning("Recipient {RecipientId} not found for overhead cost reminder notification", recipientId.Value);
                        return;
                    }
                    recipients = new List<NotificationRecipient> { recipient };
                    _logger.LogInformation("Sending overhead cost reminder to specific recipient {RecipientId}: {RecipientName}", recipientId.Value, recipient.Name);
                }
                else
                {
                    // Get all recipients for OverheadCostUpdateReminder notification type
                    recipients = await _notificationRepository.GetReportRecipients("OverheadCostUpdateReminder", null, triggerType);
                    if (recipients == null || !recipients.Any())
                    {
                        _logger.LogWarning("No recipients found for overhead cost reminder notification");
                        return;
                    }
                    _logger.LogInformation("Sending overhead cost reminder to {RecipientCount} recipients", recipients.Count);
                }

                // Generate a unique notification message ID for WhatsApp
                var notificationMessageId = $"WA-{Guid.NewGuid():N}";

                // Send to each recipient
                foreach (var recipient in recipients)
                {
                    try
                    {
                        // Generate parameters for the most overdue entry
                        var mostOverdueEntry = overdueData.OrderByDescending(x => x.DaysDelayed).First();
                        var parameters = await _notificationRepository.GenerateOverheadCostReminderParameters(
                            recipient.WhatsAppTemplateMasterId,
                            mostOverdueEntry.LastUpdatedDate,
                            mostOverdueEntry.ApplicableOnDate,
                            mostOverdueEntry.DaysDelayed);

                        if (parameters == null)
                        {
                            _logger.LogWarning("Failed to generate parameters for overhead cost reminder notification");
                            continue;
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            _logger.LogInformation("Sending overhead cost reminder to recipient {RecipientId} via WhatsApp", recipient.Id);

                            var response = await _whatsAppService.SendTemplateMessageAsync(
                                recipient.WhatsAppTemplateMasterId,
                                recipient.MobileNumbers,
                                parameters
                            );

                            // Track the notification for each mobile number
                            foreach (var mobileNumber in recipient.MobileNumbers)
                            {
                                await _trackingService.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "WhatsApp",
                                    RecipientId = recipient.Id,
                                    MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                                    ProviderMessageId = response?.MessageId,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    ErrorMessage = response?.ErrorMessage,
                                    MessageType = "OverheadCostUpdateReminder",
                                    Status = response?.Success ?? false ? "Sent" : "Failed",
                                    SentTime = DateTime.UtcNow,
                                    NotificationGroupUserId = recipient.Id,
                                    NotificationMessageId = notificationMessageId,
                                    RecipientMobileNumber = mobileNumber
                                });
                            }

                            if (!response.Success)
                            {
                                _logger.LogError("Failed to send WhatsApp reminder message. Error: {ErrorMessage}", response.ErrorMessage);
                            }
                        }

                        // Also send via email if enabled
                        if (recipient.EnableToEmail)
                        {
                            _logger.LogInformation("Sending overhead cost reminder to recipient {RecipientId} via Email", recipient.Id);

                            await _emailService.SendEmailAsync(new EmailMessage
                            {
                                To = new[] { recipient.EmailId },
                                Subject = "Overhead Cost Update Reminder",
                                Body = $"<p>Dear {recipient.Name},</p>" +
                                       $"<p>This is a reminder that overhead costs need to be updated.</p>" +
                                       $"<p>Last Updated: {mostOverdueEntry.LastUpdatedDate:dd-MMM-yyyy}</p>" +
                                       $"<p>Applicable On: {mostOverdueEntry.ApplicableOnDate:dd-MMM-yyyy}</p>" +
                                       $"<p>Days Delayed: {mostOverdueEntry.DaysDelayed}</p>" +
                                       $"<p>Please update the overhead costs as soon as possible.</p>" +
                                       $"<p>Best regards,<br/>PMS System</p>",
                                IsHtml = true
                            });

                            // Generate a unique notification message ID for email
                            var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                            // Track email notification
                            await _trackingService.TrackNotification(new NotificationTrackingModel
                            {
                                NotificationType = "OverheadCostUpdateReminder",
                                RecipientId = recipient.Id,
                                MessageContent = $"Overhead Cost Update Reminder - {mostOverdueEntry.DaysDelayed} days delayed",
                                MessageType = "Reminder",
                                Status = "Sent",
                                SentTime = DateTime.UtcNow,
                                NotificationGroupUserId = recipient.Id,
                                NotificationMessageId = emailNotificationMessageId,
                                RecipientEmail = recipient.EmailId
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send overhead cost reminder to recipient {RecipientId}", recipient.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send overhead cost update reminder notification");
                throw;
            }
        }

        public async Task SendScheduledOverheadCostUpdateReminders(string triggerType)
        {
            try
            {
                _logger.LogInformation("Processing {TriggerType} overhead cost update reminders", triggerType);

                // Get all scheduled subscriptions for overhead cost reminders using existing method
                var currentTimeUtc = DateTime.UtcNow;
                var customerSubscriptions = await _notificationRepository.GetReportRecipients("OverheadCostUpdateReminder", null, triggerType);

                if (customerSubscriptions == null || !customerSubscriptions.Any())
                {
                    _logger.LogInformation("No scheduled subscriptions found for {TriggerType} overhead cost update reminders", triggerType);
                    return;
                }

                _logger.LogInformation("Found {Count} customer subscriptions for {TriggerType} overhead cost update reminders", customerSubscriptions.Count, triggerType);

                foreach (var subscription in customerSubscriptions)
                {
                    try
                    {
                        // Check if it's time to send notification based on CRON schedule using shared service
                        if (!_schedulingService.IsTimeToSendNotification(
                            subscription.CronScheduleExpression,
                            subscription.TimeZone,
                            subscription.LastTriggeredDate,
                            currentTimeUtc,
                            10, // 10-minute execution window
                            subscription.Id))
                        {
                            continue;
                        }

                        _logger.LogInformation("Processing overhead cost reminder for subscription {SubscriptionId}",
                            subscription.Id);

                        // Send the reminder notification
                        await SendOverheadCostUpdateReminderNotification(triggerType, subscription.Id);

                        // Update the last triggered date
                        await _notificationRepository.UpdateNotificationLastTriggered(subscription.Id, currentTimeUtc);

                        _logger.LogInformation("Successfully sent {TriggerType} overhead cost reminder for subscription {SubscriptionId}",
                            triggerType,
                            subscription.Id);
                    }
                    catch (Exception subscriptionEx)
                    {
                        _logger.LogError(subscriptionEx, "Failed to process {TriggerType} overhead cost reminder for subscription {SubscriptionId}",
                            triggerType,
                            subscription.Id);
                        // Continue processing other subscriptions
                    }
                }

                _logger.LogInformation("Completed {TriggerType} overhead cost update reminder processing", triggerType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendScheduledOverheadCostUpdateReminders");
                throw;
            }
        }
    }
}
