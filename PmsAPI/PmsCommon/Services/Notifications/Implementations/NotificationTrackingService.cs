using Microsoft.Extensions.Logging;
using PmsCore.Notifications.Interfaces;
using PmsCore.Notifications.Models;
using System;
using System.Threading.Tasks;

namespace PmsCommon.Services.Notifications.Implementations
{
    public class NotificationTrackingService : INotificationTrackingService
    {
        private readonly ILogger<NotificationTrackingService> _logger;
        private readonly INotificationDataAccess _dataAccess;

        public NotificationTrackingService(
            ILogger<NotificationTrackingService> logger,
            INotificationDataAccess dataAccess)
        {
            _logger = logger;
            _dataAccess = dataAccess;
        }

        public async Task TrackNotification(NotificationTrackingModel tracking)
        {
            try
            {
                await _dataAccess.TrackNotification(tracking);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking notification");
                throw;
            }
        }

        public async Task UpdateNotificationStatus(string providerMessageId, string status, DateTime? deliveredTime = null, DateTime? readTime = null)
        {
            try
            {
                await _dataAccess.UpdateNotificationStatus(providerMessageId, status, deliveredTime, readTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating notification status by provider message ID {ProviderMessageId}", providerMessageId);
                throw;
            }
        }

        public async Task<bool> CheckRateLimit(string notificationType, long recipientId)
        {
            try
            {
                return await _dataAccess.CheckRateLimit(notificationType, recipientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking rate limit");
                throw;
            }
        }
    }
}