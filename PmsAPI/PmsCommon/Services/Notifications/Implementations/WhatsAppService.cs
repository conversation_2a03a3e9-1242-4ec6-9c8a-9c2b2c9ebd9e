using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PmsCore.Notifications.Interfaces;
using PmsCore.Notifications.Models;
using RestSharp;
using Azure.Communication.Messages;
using Azure.Identity;
using Azure;
using Azure.Communication.Messages.Models.Channels;
namespace PmsCommon.Services.Notifications.Implementations
{
    public class WhatsAppService : IWhatsAppService
    {
        // private readonly HttpClient _httpClient;
        private readonly ILogger<WhatsAppService> _logger;
        private readonly WhatsAppSettings _settings;
        private readonly INotificationDataAccess _dataAccess;

        public WhatsAppService(
            // HttpClient httpClient,
            ILogger<WhatsAppService> logger,
            IOptions<WhatsAppSettings> settings,
            INotificationDataAccess dataAccess)
        {
            // _httpClient = httpClient;
            _logger = logger;
            _settings = settings.Value;
            _dataAccess = dataAccess;
            // _httpClient.Timeout = TimeSpan.FromSeconds(_settings.RequestTimeoutSeconds);
        }

        /// <summary>
        /// Sends a WhatsApp template message to one or more recipients
        /// </summary>
        /// <param name="templateId">The ID of the template to use</param>
        /// <param name="mobileNumbers">List of mobile numbers to send the message to</param>
        /// <param name="parameters">Dictionary of parameters to include in the template</param>
        /// <returns>Response indicating success or failure</returns>
        public async Task<WhatsAppResponse> SendTemplateMessageAsync(long? templateId, List<string> mobileNumbers, Dictionary<string, string> parameters)
        {
            try
            {
                _logger.LogInformation("Sending WhatsApp template message to {MobileNumbers} with template {TemplateId}",
                    string.Join(", ", mobileNumbers), templateId);

                // Get template and configuration details
                var (template, config, settings) = await _dataAccess.GetWhatsAppTemplateAndConfig(templateId);

                // Validate template and configuration
                if (template == null)
                {
                    throw new Exception($"WhatsApp template {templateId} not found or disabled");
                }

                if (config == null)
                {
                    throw new Exception($"WhatsApp configuration for provider {template.ProviderName} not found");
                }

                if (settings == null)
                {
                    throw new Exception($"WhatsApp settings not found for template {templateId}");
                }

                // Detect which provider to use based on configuration
                if (config.ProviderName.Equals("AzureCommunicationServices", StringComparison.OrdinalIgnoreCase))
                {
                    return await SendAzureTemplateMessageAsync(template, config, parameters, mobileNumbers);
                }
                else if (config.ProviderName.Equals("Brevo", StringComparison.OrdinalIgnoreCase))
                {
                    return await SendBrevoTemplateMessageAsync(template, config, parameters, mobileNumbers);
                }
                else
                {
                    // Default implementation for backward compatibility
                    return await SendDefaultTemplateMessageAsync(template, config, parameters, mobileNumbers);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send WhatsApp template message to {MobileNumbers}", string.Join(", ", mobileNumbers));
                throw;
            }
        }

        /// <summary>
        /// Enhanced method that accepts NotificationParameterCollection for provider-specific parameter handling
        /// </summary>
        public async Task<WhatsAppResponse> SendTemplateMessageAsync(long? templateId, List<string> mobileNumbers, PmsCore.Notifications.Models.NotificationParameterCollection parameterCollection)
        {
            try
            {
                _logger.LogInformation("Sending WhatsApp template message to {MobileNumbers} with template {TemplateId}",
                    string.Join(", ", mobileNumbers), templateId);

                // Get template and configuration details
                var (template, config, settings) = await _dataAccess.GetWhatsAppTemplateAndConfig(templateId);

                // Validate template and configuration
                if (template == null)
                {
                    throw new Exception($"WhatsApp template {templateId} not found or disabled");
                }

                if (config == null)
                {
                    throw new Exception($"WhatsApp configuration for provider {template.ProviderName} not found");
                }

                if (settings == null)
                {
                    throw new Exception($"WhatsApp settings not found for template {templateId}");
                }

                // Convert parameters based on provider type
                Dictionary<string, string> parameters;
                if (config.ProviderName.Equals("AzureCommunicationServices", StringComparison.OrdinalIgnoreCase))
                {
                    // Use numbered parameters for Azure
                    parameters = parameterCollection.ToNumberedParameterDictionary();
                    return await SendAzureTemplateMessageAsync(template, config, parameters, mobileNumbers);
                }
                else if (config.ProviderName.Equals("Brevo", StringComparison.OrdinalIgnoreCase))
                {
                    // Use named parameters for Brevo
                    parameters = parameterCollection.ToNamedParameterDictionary();
                    return await SendBrevoTemplateMessageAsync(template, config, parameters, mobileNumbers);
                }
                else
                {
                    // Default to named parameters for backward compatibility
                    parameters = parameterCollection.ToNamedParameterDictionary();
                    return await SendDefaultTemplateMessageAsync(template, config, parameters, mobileNumbers);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending WhatsApp template message");
                throw;
            }
        }

        /// <summary>
        /// Sends a template message using Azure Communication Services
        /// </summary>
        private async Task<WhatsAppResponse> SendAzureTemplateMessageAsync(WhatsAppTemplate template, WhatsAppConfig config, Dictionary<string, string> parameters, List<string> mobileNumbers)
        {
            try
            {
                _logger.LogInformation("Using Azure Communication Services provider for template message");

                // Configure authentication
                var endpoint = new Uri(config.ApiEndpoint);
                var credential = new DefaultAzureCredential();

                // Instantiate the client
                var notificationMessagesClient = new NotificationMessagesClient(endpoint, credential);

                var channelRegistrationId = new Guid(config.ProviderKey);

                // Create template message with bindings
                WhatsAppMessageTemplateBindings bindings = new();

                // Create template message
                MessageTemplate templateMessage = new(template.ProviderTemplateName, template.Language ?? "en");

                // Log the expected parameter count from the template (if available)
                _logger.LogInformation("Adding parameters to template {TemplateName}. Parameter count: {ParameterCount}",
                    template.ProviderTemplateName, parameters.Count);

                // Add all parameters from the dictionary to the template
                _logger.LogInformation("Template parameters to be added: {Parameters}",
                    string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}")));

                // Add all parameters to Values collection (each parameter only once)
                foreach (var param in parameters)
                {
                    if (string.IsNullOrEmpty(param.Key))
                    {
                        _logger.LogError("Parameter key is null or empty. Value: {Value}", param.Value);
                        continue; // Skip this parameter to avoid the error
                    }

                    _logger.LogDebug("Adding parameter: {Key}={Value}", param.Key, param.Value);
                    var paramValue = new MessageTemplateText(param.Key, param.Value);
                    templateMessage.Values.Add(paramValue);
                }

                // Set up bindings - parameter "1" goes to both header and body
                var headerParam = parameters.FirstOrDefault(p => p.Key == "1");
                if (headerParam.Key != null && !string.IsNullOrEmpty(headerParam.Value))
                {
                    bindings.Header.Add(new("1"));
                    _logger.LogDebug("Added header binding for parameter: {Key}", headerParam.Key);
                }

                // Add all parameters to body bindings
                foreach (var param in parameters)
                {
                    if (string.IsNullOrEmpty(param.Key))
                        continue;

                    bindings.Body.Add(new(param.Key));
                    _logger.LogDebug("Added body binding for parameter: {Key}", param.Key);
                }

                templateMessage.Bindings = bindings;

                // Process each mobile number
                var responses = new List<WhatsAppResponse>();
                foreach (var mobileNumber in mobileNumbers)
                {
                    try
                    {
                        var recipientList = new List<string> { mobileNumber };

                        // Assemble template message
                        var templateContent = new TemplateNotificationContent(channelRegistrationId, recipientList, templateMessage);

                        // Send template message
                        Response<SendMessageResult> sendTemplateMessageResult = await notificationMessagesClient.SendAsync(templateContent);

                        // Get the message ID from the first receipt
                        var messageId = sendTemplateMessageResult.Value.Receipts.Count > 0
                            ? sendTemplateMessageResult.Value.Receipts[0].MessageId
                            : Guid.NewGuid().ToString();

                        responses.Add(new WhatsAppResponse
                        {
                            Success = true,
                            MessageId = messageId
                        });

                        _logger.LogInformation("Successfully sent Azure WhatsApp template message to {MobileNumber}", mobileNumber);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send Azure WhatsApp template message to {MobileNumber}", mobileNumber);
                        responses.Add(new WhatsAppResponse
                        {
                            Success = false,
                            ErrorCode = "500",
                            ErrorMessage = $"Exception: {ex.Message}"
                        });
                    }
                }

                // If all messages failed, throw an exception
                if (responses.All(r => !r.Success))
                {
                    throw new Exception("Failed to send Azure WhatsApp template message to all recipients");
                }

                // Return success if at least one message was sent successfully
                return responses.FirstOrDefault(r => r.Success) ?? responses.First();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendAzureTemplateMessageAsync");
                throw;
            }
        }

        /// <summary>
        /// Sends a template message using Brevo provider
        /// </summary>
        private async Task<WhatsAppResponse> SendBrevoTemplateMessageAsync(WhatsAppTemplate template, WhatsAppConfig config, Dictionary<string, string> parameters, List<string> mobileNumbers)
        {
            try
            {
                _logger.LogInformation("Using Brevo provider for template message");

                // For Brevo, we need to construct a specific payload
                var message = new
                {
                    contactNumbers = mobileNumbers,
                    templateId = template.ProviderTemplateId,
                    senderNumber = config.RegisteredSenderNumber,
                    Params = parameters  // Using the same property name as in WhatsAppMessage for consistency
                };

                string jsonMessage = JsonSerializer.Serialize(message);

                // Use the existing SendMessageAsync method with the Brevo-specific payload
                return await SendMessageAsync(jsonMessage, config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendBrevoTemplateMessageAsync");
                throw;
            }
        }

        /// <summary>
        /// Default implementation for backward compatibility with existing code
        /// </summary>
        private async Task<WhatsAppResponse> SendDefaultTemplateMessageAsync(WhatsAppTemplate template, WhatsAppConfig config, Dictionary<string, string> parameters, List<string> mobileNumbers)
        {
            try
            {
                _logger.LogInformation("Using default provider implementation for template message");

                // Create components for the template
                var components = new List<WhatsAppTemplateComponent>
                {
                    new()
                    {
                        Type = "body",
                        Parameters = parameters.Select(p => new WhatsAppTemplateParameter
                        {
                            Type = "text",
                            Text = p.Value
                        }).ToList()
                    }
                };

                // Create the message payload
                var message = new WhatsAppMessage
                {
                    contactNumbers = mobileNumbers,
                    templateId = template.ProviderTemplateId,
                    senderNumber = config.RegisteredSenderNumber,
                    Params = parameters  // Using the same property name as in WhatsAppMessage for consistency
                };

                string jsonMessage = JsonSerializer.Serialize(message);

                // Send the message using the existing method
                return await SendMessageAsync(jsonMessage, config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendDefaultTemplateMessageAsync");
                throw;
            }
        }

        public async Task<WhatsAppResponse> SendCustomMessageAsync(string mobileNumber, string message)
        {
            try
            {
                var config = await _dataAccess.GetDefaultWhatsAppConfig()
                    ?? throw new Exception("No active WhatsApp configuration found");

                var content = new
                {
                    messaging_product = "whatsapp",
                    to = mobileNumber,
                    from = config.RegisteredSenderNumber,
                    type = "text",
                    text = new { body = message }
                };

                string jsonMessage = JsonSerializer.Serialize(content);

                return await SendMessageAsync(jsonMessage, config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send WhatsApp custom message to {MobileNumber}", mobileNumber);
                throw;
            }
        }

        private async Task<WhatsAppResponse> SendMessageAsync(string message, WhatsAppConfig config)
        {
            var baseApiUrl = config.ApiEndpoint;
            var fullUrl = baseApiUrl + "whatsapp/sendMessage";
            var options = new RestClientOptions(fullUrl);
            var client = new RestClient(options);
            var request = new RestRequest("");
            request.AddHeader("api-key", $"{config.ProviderKey}");
            request.AddHeader("accept", "application/json");
            request.AddHeader("content-type", "application/json");

            request.AddJsonBody(message, false);

            return await SendRequestWithRetryAsync(request, client);
        }
        public async Task<WhatsAppResponse> SendTemplateDocumentMessageAsync(long? templateId, List<string> mobileNumbers, Dictionary<string, string> parameters, string documentUrl)
        {
            try
            {
                _logger.LogInformation("Sending WhatsApp template document message to {MobileNumbers} with template {TemplateId}",
                    string.Join(", ", mobileNumbers), templateId);

                var (template, config, settings) = await _dataAccess.GetWhatsAppTemplateAndConfig(templateId);

                if (template == null)
                {
                    throw new Exception($"WhatsApp template {templateId} not found or disabled");
                }

                if (config == null)
                {
                    throw new Exception($"WhatsApp configuration for provider {template.ProviderName} not found");
                }

                if (settings == null)
                {
                    throw new Exception($"WhatsApp settings not found for template {templateId}");
                }

                if (string.IsNullOrEmpty(documentUrl))
                {
                    throw new ArgumentException("Document URL cannot be null or empty");
                }

                // Validate document URL
                if (!Uri.TryCreate(documentUrl, UriKind.Absolute, out Uri documentUri))
                {
                    throw new ArgumentException($"Invalid document URL: {documentUrl}");
                }

                // Check if provider is Azure Communication Services
                if (config.ProviderName.Equals("AzureCommunicationServices", StringComparison.OrdinalIgnoreCase))
                {
                    return await SendAzureTemplateDocumentMessageAsync(template, config, parameters, documentUrl, mobileNumbers);
                }
                else
                {
                    throw new NotSupportedException($"Document messages not supported for provider: {config.ProviderName}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send WhatsApp template document message to {MobileNumbers}", string.Join(", ", mobileNumbers));
                throw;
            }
        }
        public async Task<WhatsAppResponse> SendOnlyDocumentMessageAsync(string providerName, List<string> mobileNumbers, Dictionary<string, string> parameters, string documentUrl)
        {
            try
            {
                _logger.LogInformation("Sending WhatsApp document message to {MobileNumbers} using {providerName}",
                    string.Join(", ", mobileNumbers), providerName);

                var config = await _dataAccess.GetWhatsAppConfigByProviderName(providerName)
                    ?? throw new Exception($"WhatsApp configuration for provider {providerName} not found");

                if (string.IsNullOrEmpty(documentUrl))
                {
                    throw new ArgumentException("Document URL cannot be null or empty");
                }

                // Validate document URL
                if (!Uri.TryCreate(documentUrl, UriKind.Absolute, out Uri documentUri))
                {
                    throw new ArgumentException($"Invalid document URL: {documentUrl}");
                }

                // Check if provider is Azure Communication Services
                if (config.ProviderName.Equals("AzureCommunicationServices", StringComparison.OrdinalIgnoreCase))
                {
                    // return await SendAzureTemplateDocumentMessageAsync(template, config, parameters, documentUrl, mobileNumbers);
                    return await SendAzureOnlyDocumentMessageAsync(config, parameters, documentUrl, mobileNumbers);
                }
                else
                {
                    throw new NotSupportedException($"Document messages not supported for provider: {config.ProviderName}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send WhatsApp template document message to {MobileNumbers}", string.Join(", ", mobileNumbers));
                throw;
            }
        }

        private async Task<WhatsAppResponse> SendAzureTemplateDocumentMessageAsync(WhatsAppTemplate template, WhatsAppConfig config, Dictionary<string, string> parameters, string documentUrl, List<string> mobileNumbers)
        {
            try
            {
                // Configure authentication
                var endpoint = new Uri(config.ApiEndpoint);
                var credential = new DefaultAzureCredential();

                // Instantiate the client
                var notificationMessagesClient = new NotificationMessagesClient(endpoint, credential);

                var channelRegistrationId = new Guid(config.ProviderKey);

                // Create document parameter
                var documentUri = new Uri(documentUrl);
                var document = new MessageTemplateDocument("document", documentUri);

                // Create template message with bindings
                WhatsAppMessageTemplateBindings bindings = new();
                bindings.Header.Add(new(document.Name));

                // Create template message
                MessageTemplate templateMessage = new(template.ProviderTemplateName, template.Language ?? "en_us");
                templateMessage.Values.Add(document);

                // Log the expected parameter count from the template (if available)
                _logger.LogInformation("Adding parameters to template {TemplateName}. Parameter count: {ParameterCount}",
                    template.ProviderTemplateName, parameters.Count);

                // Add all parameters from the dictionary to the template
                _logger.LogInformation("Template parameters to be added: {Parameters}",
                    string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}")));

                foreach (var param in parameters)
                {
                    if (string.IsNullOrEmpty(param.Key))
                    {
                        _logger.LogError("Parameter key is null or empty. Value: {Value}", param.Value);
                        continue; // Skip this parameter to avoid the error
                    }

                    _logger.LogDebug("Adding parameter: {Key}={Value}", param.Key, param.Value);
                    var paramValue = new MessageTemplateText(param.Key, param.Value);
                    templateMessage.Values.Add(paramValue);

                    // Add to body bindings
                    bindings.Body.Add(new(param.Key));
                }

                templateMessage.Bindings = bindings;

                // Log the final template message
                _logger.LogInformation("Final template message: {TemplateMessage}",
                    JsonSerializer.Serialize(templateMessage));

                // Process each mobile number
                var responses = new List<WhatsAppResponse>();
                foreach (var mobileNumber in mobileNumbers)
                {
                    try
                    {
                        var recipientList = new List<string> { "+" + mobileNumber };

                        // Assemble template message
                        var templateContent = new TemplateNotificationContent(channelRegistrationId, recipientList, templateMessage);

                        _logger.LogInformation("Final template content for {MobileNumber}: {TemplateMessage}, Bindings: {Bindings}, Values: {Values}",
                            mobileNumber, JsonSerializer.Serialize(templateContent), JsonSerializer.Serialize(templateContent.Template.Bindings), JsonSerializer.Serialize(templateContent.Template.Values));

                        // Send template message
                        Response<SendMessageResult> sendTemplateMessageResult = await notificationMessagesClient.SendAsync(templateContent);

                        // Get the message ID from the first receipt
                        var messageId = sendTemplateMessageResult.Value.Receipts.Count > 0
                            ? sendTemplateMessageResult.Value.Receipts[0].MessageId
                            : Guid.NewGuid().ToString();

                        responses.Add(new WhatsAppResponse
                        {
                            Success = true,
                            MessageId = messageId
                        });

                        _logger.LogInformation("Successfully sent WhatsApp document message to {MobileNumber}", mobileNumber);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send WhatsApp document message to {MobileNumber}", mobileNumber);
                        responses.Add(new WhatsAppResponse
                        {
                            Success = false,
                            ErrorCode = "500",
                            ErrorMessage = $"Exception: {ex.Message}"
                        });
                    }
                }

                // If all messages failed, throw an exception
                if (responses.All(r => !r.Success))
                {
                    throw new Exception("Failed to send WhatsApp document message to all recipients");
                }

                // Return success if at least one message was sent successfully
                return responses.FirstOrDefault(r => r.Success) ?? responses.First();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendAzureTemplateDocumentMessageAsync");
                throw;
            }
        }

        /// <summary>
        /// Sends a document message using Azure Communication Services. This can only be sent when there is already an active conversation. First a template based message should be sent to the user.
        /// </summary>
        /// <param name="config"></param>
        /// <param name="parameters"></param>
        /// <param name="documentUrl"></param>
        /// <param name="mobileNumbers"></param>
        /// <returns></returns>

        private async Task<WhatsAppResponse> SendAzureOnlyDocumentMessageAsync(WhatsAppConfig config, Dictionary<string, string> parameters, string documentUrl, List<string> mobileNumbers)
        {
            try
            {
                // Configure authentication
                var endpoint = new Uri(config.ApiEndpoint);
                var credential = new DefaultAzureCredential();

                // Instantiate the client
                var notificationMessagesClient = new NotificationMessagesClient(endpoint, credential);

                var channelRegistrationId = new Guid(config.ProviderKey);

                // Process each mobile number
                var responses = new List<WhatsAppResponse>();
                foreach (var mobileNumber in mobileNumbers)
                {
                    try
                    {
                        var recipientList = new List<string> { "+" + mobileNumber };

                        // Create document parameter
                        var documentLink = new Uri(documentUrl);
                        var documentNotificationContent = new DocumentNotificationContent(channelRegistrationId, recipientList, documentLink)
                        {
                            Caption = $"{parameters["Caption"]} date range from {parameters["1"]} to {parameters["2"]}",
                            FileName = documentLink.Segments.Last()
                        };

                        // Send document notification
                        Response<SendMessageResult> sendDocumentNotificationResult = await notificationMessagesClient.SendAsync(documentNotificationContent);

                        // Get the message ID from the first receipt
                        var messageId = sendDocumentNotificationResult.Value.Receipts.Count > 0
                            ? sendDocumentNotificationResult.Value.Receipts[0].MessageId
                            : Guid.NewGuid().ToString();

                        responses.Add(new WhatsAppResponse
                        {
                            Success = true,
                            MessageId = messageId
                        });

                        _logger.LogInformation("Successfully sent WhatsApp document notification to {MobileNumber}", mobileNumber);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send WhatsApp document notification to {MobileNumber}", mobileNumber);
                        responses.Add(new WhatsAppResponse
                        {
                            Success = false,
                            ErrorCode = "500",
                            ErrorMessage = $"Exception: {ex.Message}"
                        });
                    }
                }

                // If all messages failed, throw an exception
                if (responses.All(r => !r.Success))
                {
                    throw new Exception("Failed to send WhatsApp document notification to all recipients");
                }

                // Return success if at least one message was sent successfully
                return responses.FirstOrDefault(r => r.Success) ?? responses.First();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendAzureOnlyDocumentNotificationAsync");
                throw;
            }
        }

        private async Task<WhatsAppResponse> SendRequestWithRetryAsync(RestRequest request, RestClient client)
        {
            var retryCount = 0;
            while (retryCount <= _settings.MaxRetryAttempts)
            {
                RestResponse response;
                try
                {
                    response = await client.ExecuteAsync(request, Method.Post);

                    if (response.IsSuccessStatusCode)
                    {
                        _logger.LogInformation("WhatsApp message sent successfully. Response: {Response}", response.Content);
                        var responseObj = JsonSerializer.Deserialize<JsonDocument>(response.Content);
                        var messageId = responseObj.RootElement.GetProperty("messageId").GetString();
                        return new WhatsAppResponse { Success = true, MessageId = messageId };
                    }

                    _logger.LogError("WhatsApp API error. StatusCode: {StatusCode}, Response: {Response}",
                        response.StatusCode, response.Content);

                    string errorCode = ((int)response.StatusCode).ToString();
                    string errorMessage = !string.IsNullOrEmpty(response.Content)
                        ? response.Content
                        : response.ErrorMessage ?? "Unknown error occurred";

                    if (_settings.EnableRetry &&
                        retryCount < _settings.MaxRetryAttempts &&
                        (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests ||
                         response.StatusCode == System.Net.HttpStatusCode.ServiceUnavailable))
                    {
                        retryCount++;
                        await Task.Delay(retryCount * 1000);
                        _logger.LogWarning("Retrying WhatsApp message. Attempt {RetryCount}", retryCount);
                        continue;
                    }

                    return new WhatsAppResponse
                    {
                        Success = false,
                        ErrorCode = errorCode,
                        ErrorMessage = errorMessage
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Exception occurred while sending WhatsApp message");

                    if (_settings.EnableRetry && retryCount < _settings.MaxRetryAttempts)
                    {
                        retryCount++;
                        await Task.Delay(retryCount * 1000);
                        _logger.LogWarning(ex, "Retry attempt {RetryCount} for WhatsApp message", retryCount);
                        continue;
                    }

                    return new WhatsAppResponse
                    {
                        Success = false,
                        ErrorCode = "500",
                        ErrorMessage = $"Exception: {ex.Message}. Inner Exception: {ex.InnerException?.Message}"
                    };
                }
            }

            return new WhatsAppResponse
            {
                Success = false,
                ErrorCode = "500",
                ErrorMessage = $"Failed to send WhatsApp message after {_settings.MaxRetryAttempts} attempts"
            };
        }
    }
}