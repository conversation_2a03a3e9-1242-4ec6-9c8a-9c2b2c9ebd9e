using System.Collections.Generic;
using System.Threading.Tasks;
using PmsCore.Notifications.Interfaces;
using PmsCore.Notifications.Models;
using System;
using Microsoft.Extensions.Logging;
namespace PmsCommon.Services.Notifications.Implementations
{
    public class NotificationRepository : INotificationRepository
    {
        private readonly ILogger<NotificationRepository> _logger;
        private readonly INotificationDataAccess _dataAccess;


        public NotificationRepository(ILogger<NotificationRepository> logger, INotificationDataAccess dataAccess)
        {
            _logger = logger;
            _dataAccess = dataAccess;
        }

        public virtual async Task<List<NotificationRecipient>> GetReportRecipients(string notificationType, string reportName = null, string TriggerType = null)
        {
            return await _dataAccess.GetReportRecipients(notificationType, reportName, TriggerType);
        }

        public virtual async Task<NotificationTemplate> GetNotificationTemplate(string notificationType, string reportType)
        {
            return await _dataAccess.GetNotificationTemplate(notificationType, reportType);
        }

        public virtual async Task<(NotificationTemplate template, Dictionary<string, string> parameters)> GetSaleOrderStatusNotification(long saleOrderId, long stageId)
        {
            return await _dataAccess.GetSaleOrderStatusNotification(saleOrderId, stageId);
        }

        public virtual async Task<ReportData> GenerateReportData(string reportType)
        {
            return await _dataAccess.GenerateReportData(reportType);
        }

        public virtual async Task TrackNotification(NotificationTrackingModel tracking)
        {
            await _dataAccess.TrackNotification(tracking);
        }

        public virtual async Task UpdateNotificationStatus(string providerMessageId, string status, DateTime? deliveredTime, DateTime? readTime)
        {
            await _dataAccess.UpdateNotificationStatus(providerMessageId, status, deliveredTime, readTime);
        }

        public virtual async Task<bool> CheckRateLimit(string notificationType, long recipientId)
        {
            return await _dataAccess.CheckRateLimit(notificationType, recipientId);
        }

        public virtual async Task UpdateRateLimit(string notificationType, long recipientId)
        {
            await _dataAccess.UpdateRateLimit(notificationType, recipientId);
        }

        public virtual async Task<NotificationRecipient> GetRecipientById(long recipientId)
        {
            return await _dataAccess.GetRecipientById(recipientId);
        }

        public virtual async Task<NotificationLimitConfig> GetNotificationLimits(string notificationType)
        {
            return await _dataAccess.GetNotificationLimits(notificationType);
        }

        public virtual async Task<List<long>> GetRecipientTemplateIds(long recipientId, string notificationType)
        {
            return await _dataAccess.GetRecipientTemplateIds(recipientId, notificationType);
        }

        public virtual async Task<Dictionary<string, string>> GenerateLowStockParameters(long productId, long templateMasterId, ProductStockAvailabilityVm productStockAvailabilityVm)
        {
            return await _dataAccess.GenerateLowStockParameters(productId, templateMasterId, productStockAvailabilityVm);
        }

        public virtual PmsCore.Notifications.Models.NotificationParameterCollection GenerateLowStockParametersEnhanced(long templateMasterId, ProductStockAvailabilityVm productStockAvailabilityVm)
        {
            return _dataAccess.GenerateLowStockParametersEnhanced(templateMasterId, productStockAvailabilityVm);
        }

        public virtual async Task<(WhatsAppTemplate template, WhatsAppConfig config, WhatsAppSettings settings)> GetWhatsAppTemplateAndConfig(long? templateMasterId)
        {
            return await _dataAccess.GetWhatsAppTemplateAndConfig(templateMasterId);
        }

        public virtual async Task<ProductStockAvailabilityVm> GetProductStockAvailabilty(long productId)
        {
            return await _dataAccess.GetProductStockAvailabilty(productId);
        }

        public virtual async Task<WhatsAppConfig> GetDefaultWhatsAppConfig()
        {
            return await _dataAccess.GetDefaultWhatsAppConfig();
        }
        public virtual async Task<WhatsAppConfig> GetWhatsAppConfigByProviderName(string providerName)
        {
            return await _dataAccess.GetWhatsAppConfigByProviderName(providerName);
        }

        public virtual async Task<EmailSettings> GetPrimaryProviderEmailConfig()
        {
            return await _dataAccess.GetPrimaryProviderEmailConfig();
        }

        public virtual async Task<EmailSettings> GetSecondaryProviderEmailConfig()
        {
            return await _dataAccess.GetSecondaryProviderEmailConfig();
        }
        public virtual Task<bool> ValidateNotification(NotificationTemplate template)
        {
            try
            {
                // Add business validation logic here
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating notification template");
                throw;
            }
        }

        public virtual Task<bool> ProcessNotification(string notification)
        {
            try
            {
                // Add business processing logic here
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing notification");
                throw;
            }
        }

        public virtual async Task<string> GenerateYieldSummaryReportPdfAndUploadToStorageAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dataAccess.GenerateYieldSummaryReportPdfAndUploadToStorageAsync(fromDate, toDate);
        }

        public virtual async Task<Dictionary<string, string>> GenerateYieldSummaryReportParameters(long templateMasterId, DateTime fromDate, DateTime toDate)
        {
            return await _dataAccess.GenerateYieldSummaryReportParameters(templateMasterId, fromDate, toDate);
        }

        public virtual async Task<List<ScheduledNotification>> GetPendingScheduledNotifications()
        {
            return await _dataAccess.GetPendingScheduledNotifications();
        }

        public virtual async Task UpdateNextRunTime(long reportId, DateTime lastRunTime, DateTime nextRunTime)
        {
            await _dataAccess.UpdateNextRunTime(reportId, lastRunTime, nextRunTime);
        }

        public virtual async Task<string> GenerateLowStockReportPdfAndUploadToStorageAsync()
        {
            return await _dataAccess.GenerateLowStockReportPdfAndUploadToStorageAsync();
        }

        public virtual async Task<Dictionary<string, string>> GenerateLowStockReportParameters(long templateMasterId)
        {
            return await _dataAccess.GenerateLowStockReportParameters(templateMasterId);
        }

        // Pending Returnable OutPass methods
        public virtual async Task<string> GeneratePendingReturnableOutPassReminderPdfAndUploadToStorageAsync(long outpassId)
        {
            return await _dataAccess.GeneratePendingReturnableOutPassReminderPdfAndUploadToStorageAsync(outpassId);
        }

        public virtual async Task<string> GeneratePendingReturnableOutPassReportPdfAndUploadToStorageAsync()
        {
            return await _dataAccess.GeneratePendingReturnableOutPassReportPdfAndUploadToStorageAsync();
        }

        public virtual async Task<Dictionary<string, string>> GeneratePendingReturnableOutPassReminderParameters(long outpassId, long templateMasterId)
        {
            return await _dataAccess.GeneratePendingReturnableOutPassReminderParameters(outpassId, templateMasterId);
        }

        public virtual async Task<Dictionary<string, string>> GeneratePendingReturnableOutPassReportParameters(long templateMasterId)
        {
            return await _dataAccess.GeneratePendingReturnableOutPassReportParameters(templateMasterId);
        }

        public virtual async Task UpdateOnDemandNotificationStatus(long notificationGroupId)
        {
            await _dataAccess.UpdateOnDemandNotificationStatus(notificationGroupId);
        }

        public virtual async Task<List<NotificationRecipient>> GetNotificationsByTriggerType(string triggerType)
        {
            return await _dataAccess.GetNotificationsByTriggerType(triggerType);
        }

        public virtual async Task<List<long>> GetOverdueReturnableOutPassesForScheduledReminders()
        {
            return await _dataAccess.GetOverdueReturnableOutPassesForScheduledReminders();
        }

        // Costing Report methods
        public virtual async Task<string> GenerateCostingReportPdfAndUploadToStorageAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dataAccess.GenerateCostingReportPdfAndUploadToStorageAsync(fromDate, toDate);
        }

        public virtual async Task<Dictionary<string, string>> GenerateCostingReportParameters(long templateMasterId, DateTime fromDate, DateTime toDate)
        {
            return await _dataAccess.GenerateCostingReportParameters(templateMasterId, fromDate, toDate);
        }

        // Overhead Cost Reminder methods
        public virtual async Task<List<OverheadCostReminderData>> GetOverdueOverheadCostData()
        {
            return await _dataAccess.GetOverdueOverheadCostData();
        }

        public virtual async Task<Dictionary<string, string>> GenerateOverheadCostReminderParameters(long templateMasterId, DateTime lastUpdatedDate, DateTime applicableOnDate, int daysDelayed)
        {
            return await _dataAccess.GenerateOverheadCostReminderParameters(templateMasterId, lastUpdatedDate, applicableOnDate, daysDelayed);
        }

        public virtual async Task UpdateNotificationLastTriggered(long notificationGroupUserId, DateTime currentTimeUtc)
        {
            await _dataAccess.UpdateNotificationLastTriggered(notificationGroupUserId, currentTimeUtc);
        }
    }
}
