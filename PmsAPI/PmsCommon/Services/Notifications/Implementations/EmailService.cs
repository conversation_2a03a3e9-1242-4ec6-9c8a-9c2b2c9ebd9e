using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PmsCore.Notifications.Interfaces;
using PmsCore.Notifications.Models;
using Amazon.SimpleEmailV2;
using Amazon.SimpleEmailV2.Model;
using Amazon.Runtime;
using Amazon;
using System.Threading;

namespace PmsCommon.Services.Notifications.Implementations
{
    public class EmailService : IEmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly INotificationDataAccess _dataAccess;
        private EmailSettings _settings;
        private AmazonSimpleEmailServiceV2Client _sesClient;
        private readonly SemaphoreSlim _initLock = new SemaphoreSlim(1, 1);


        public EmailService(ILogger<EmailService> logger, INotificationDataAccess dataAccess)
        {
            _logger = logger;
            _dataAccess = dataAccess;
        }
        private async Task InitializeClientAsync()
        {
            if (_sesClient != null) return;

            await _initLock.WaitAsync();
            try
            {
                if (_sesClient != null) return;

                _settings = await _dataAccess.GetPrimaryProviderEmailConfig();
                _sesClient = new AmazonSimpleEmailServiceV2Client(
                    new BasicAWSCredentials(_settings.AccessKey, _settings.SecretKey),
                    RegionEndpoint.GetBySystemName(_settings.Region)
                );
            }
            finally
            {
                _initLock.Release();
            }
        }

        public async Task SendEmailAsync(EmailMessage message)
        {
            try
            {
                await InitializeClientAsync();
                using var mailMessage = new MailMessage();
                using var smtpClient = new SmtpClient(_settings.SmtpServer, _settings.Port);

                // Configure message
                foreach (var to in message.To)
                    mailMessage.To.Add(to);

                if (message.Cc?.Any() == true)
                    foreach (var cc in message.Cc)
                        mailMessage.CC.Add(cc);

                if (message.Bcc?.Any() == true)
                    foreach (var bcc in message.Bcc)
                        mailMessage.Bcc.Add(bcc);

                mailMessage.From = new MailAddress(_settings.FromEmail, _settings.FromName);
                mailMessage.Subject = message.Subject;
                mailMessage.Body = message.Body;
                mailMessage.IsBodyHtml = message.IsHtml;

                // Add attachments if any
                if (message.Attachments?.Any() == true)
                {
                    foreach (var attachment in message.Attachments)
                    {
                        using var ms = new MemoryStream(attachment.Content);
                        mailMessage.Attachments.Add(new Attachment(ms, attachment.FileName, attachment.ContentType));
                    }
                }

                // Configure SMTP client
                smtpClient.EnableSsl = _settings.EnableSsl;
                smtpClient.Credentials = new NetworkCredential(_settings.Username, _settings.Password);

                await smtpClient.SendMailAsync(mailMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email to {Recipients}", string.Join(", ", message.To));
                throw;
            }
        }

        public async Task SendEmailWithAttachmentAsync(EmailMessage message, byte[] attachment, string fileName, string contentType)
        {
            try
            {
                var request = new SendEmailRequest
                {
                    FromEmailAddress = _settings.FromEmail,
                    Destination = new Destination
                    {
                        ToAddresses = message.To.ToList()
                    },
                    Content = new EmailContent
                    {
                        Simple = new Message
                        {
                            Subject = new Content { Data = message.Subject },
                            Body = new Body
                            {
                                Html = new Content { Data = message.Body }
                            }
                        }
                    }
                };

                // Add attachment if provided
                if (attachment != null && attachment.Length > 0)
                {
                    var attachmentContent = Convert.ToBase64String(attachment);
                    request.Content.Raw = new RawMessage
                    {
                        Data = new MemoryStream(Encoding.UTF8.GetBytes($@"
                        Content-Type: multipart/mixed; boundary=""boundary""
                        MIME-Version: 1.0

                        --boundary
                        Content-Type: text/html; charset=utf-8
                        Content-Transfer-Encoding: 7bit

                        {message.Body}

                        --boundary
                        Content-Type: {contentType}; name=""{fileName}""
                        Content-Disposition: attachment; filename=""{fileName}""
                        Content-Transfer-Encoding: base64

                        {attachmentContent}
                        --boundary--"))
                    };
                }

                var response = await _sesClient.SendEmailAsync(request);
                _logger.LogInformation("Email sent successfully to {Recipient}", message.To);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email with attachment to {Recipient}", message.To);
                throw;
            }
        }
    }
}
