using System.Threading.Tasks;
using PmsCore.DataAccessRepository.Interfaces;
using PmsCore.PDFGeneration.Models;
using Microsoft.Extensions.Logging;
namespace PmsCommon.Services.DataAccess.Implementations
{
    public class DataAccessRepository : IDataAccessRepository
    {
        private readonly ILogger<DataAccessRepository> _logger;
        private readonly IDataAccessRepository _dataAccess;
        private readonly IStorageRepository _storageAccess;
        public DataAccessRepository(ILogger<DataAccessRepository> logger, IDataAccessRepository dataAccess)
        {
            _logger = logger;
            _dataAccess = dataAccess;
        }
        public virtual async Task<PdfConfiguration> GetPdfConfiguration()
        {
            return await _dataAccess.GetPdfConfiguration();
        }
        public virtual async Task<string> GetServiceSasUriForContainer(string containerName)
        {
            return await _storageAccess.GetServiceSasUriForContainer(containerName);
        }
        public virtual async Task<long> AddFileUploadData(object fileUpload)
        {
            return await _storageAccess.AddFileUploadData(fileUpload);
        }
        public virtual async Task<object> GetPurchaseOrderById(long id)
        {
            return await _dataAccess.GetPurchaseOrderById(id);
        }
    }
}
