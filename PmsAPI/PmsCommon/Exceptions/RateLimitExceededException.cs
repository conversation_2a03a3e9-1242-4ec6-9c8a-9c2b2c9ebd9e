using System;

public class RateLimitExceededException : Exception
{
    public string NotificationType { get; }
    public long RecipientId { get; }
    public int CurrentDailyCount { get; }
    public int MaxDailyLimit { get; }

    public RateLimitExceededException(string notificationType, long recipientId, int currentCount, int maxLimit)
        : base($"Rate limit exceeded for {notificationType} to {recipientId}. Current count: {currentCount}, Max limit: {maxLimit}")
    {
        NotificationType = notificationType;
        RecipientId = recipientId;
        CurrentDailyCount = currentCount;
        MaxDailyLimit = maxLimit;
    }
} 