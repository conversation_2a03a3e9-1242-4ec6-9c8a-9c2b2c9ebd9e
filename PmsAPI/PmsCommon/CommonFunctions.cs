﻿using Azure.Identity;
using Azure.Storage;
using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using System;
using System.Linq;
using System.Text;
using System.IO;
namespace PmsCommon
{
    public static class CommonFunctions
    {
        public static string RemoveSpecialCharacters(this string str)
        {
            StringBuilder sb = new StringBuilder();
            foreach (char c in str)
            {
                if ((c >= '0' && c <= '9') || (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || c == '.' || c == '_')
                {
                    sb.Append(c);
                }
            }
            return sb.ToString();
        }

        public static string GRNFormat { get { return "ZB/GRN/" + DateTime.Now.Year + "/"; } }

        public static Uri GetServiceSasUriForContainer(string storageAccountName, string storageAccountKey, string container_name)
        {
            try
            {
                StorageSharedKeyCredential credential = new StorageSharedKeyCredential(storageAccountName, storageAccountKey);
                string blobcontainer_url = string.Format("https://{0}.blob.core.windows.net/{1}", storageAccountName, container_name);
                BlobContainerClient blobContainer = new BlobContainerClient(new Uri(blobcontainer_url), credential);
                //BlobContainerClient blobContainer = new BlobContainerClient(new Uri(blobcontainer_url), new DefaultAzureCredential());
                //var UserDelegationKey = blobContainer.GetUserDelegationKey
                if (blobContainer.CanGenerateSasUri)
                {
                    BlobSasBuilder sasBuilder = new BlobSasBuilder()
                    {
                        BlobContainerName = blobContainer.Name,
                        Resource = "c"
                    };

                    sasBuilder.StartsOn = DateTimeOffset.Now;
                    sasBuilder.ExpiresOn = DateTimeOffset.Now.AddMinutes(5);
                    sasBuilder.SetPermissions(BlobContainerSasPermissions.List | BlobContainerSasPermissions.Read | BlobContainerSasPermissions.Write);

                    Uri sasUri = blobContainer.GenerateSasUri(sasBuilder);
                    return sasUri;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public static Uri GetServiceSasUriForBlob(string storageAccountName, string storageAccountKey, string container_name, string blob_name)
        {
            try
            {
                // Create credentials and client
                var credential = new StorageSharedKeyCredential(storageAccountName, storageAccountKey);
                var blobUri = new Uri($"https://{storageAccountName}.blob.core.windows.net/{container_name}/{blob_name}");
                var blobClient = new BlobClient(blobUri, credential);

                if (!blobClient.CanGenerateSasUri)
                {
                    return null;
                }

                // Create SAS token with specific permissions
                var sasBuilder = new BlobSasBuilder
                {
                    BlobContainerName = container_name,
                    BlobName = blob_name,
                    Resource = "b", // b for blob
                    StartsOn = DateTimeOffset.UtcNow,
                    ExpiresOn = DateTimeOffset.UtcNow.AddHours(1)
                };

                // Set specific permissions
                sasBuilder.SetPermissions(BlobSasPermissions.Read); // Only read permission

                return blobClient.GenerateSasUri(sasBuilder);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error generating SAS token: {ex.Message}", ex);
            }
        }
        public static string FindFileType(string fileName)
        {
            var imageTypes = new[] { "jpg", "jpeg", "png" };
            var fileTypes = new[] { "pdf", "doc", "docx", "xls", "xlsx", "ppt" };
            var extension = fileName.Split('.').LastOrDefault();
            return imageTypes.Contains(extension) ? "image" : fileTypes.Contains(extension) ? "file" : "other";
        }

        public static (DateTime, DateTime) GetFinancialYearDates()
        {
            var currentDate = System.DateTime.Now;
            //testing
            //currentDate = new DateTime(2024, 4, 1);

            int year = currentDate.Month >= 4 ? currentDate.Year : currentDate.Year - 1;
            DateTime financialYearStart = new DateTime(year, 4, 1);
            DateTime financialYearEnd = new DateTime(year + 1, 3, 31);

            return (financialYearStart, financialYearEnd);
        }

        public static string FinancialYearUniqueNumber(long newRecordId, long? PreviousRecordId, string PreviousRecordSeqNumber = null, bool AddPredefinedPrefix = false)
        {

            long previousRecordSeq = 0;
            string newRecordSeqNumber = "00001";

            if (PreviousRecordId.HasValue && PreviousRecordSeqNumber != null)
            {
                string[] parts = PreviousRecordSeqNumber.Split('/');
                if (parts.Length == 2 && long.TryParse(parts[1], out long result))
                {
                    previousRecordSeq = result;
                }

                long differenceInSaleOrderId = newRecordId - PreviousRecordId.Value - 1;
                newRecordSeqNumber = (previousRecordSeq + differenceInSaleOrderId + 1).ToString("D5");
            }

            var newUniqueNumber = "";
            if (AddPredefinedPrefix == true)
            {
                newUniqueNumber = System.DateTime.Now.Year + "-" + System.DateTime.Now.Month + "/" + newRecordSeqNumber;
            }
            else
            {
                newUniqueNumber = newRecordSeqNumber;
            }

            return newUniqueNumber;
        }

        /// <summary>
        /// Uploads a file to Azure Blob Storage with optional SAS token
        /// </summary>
        /// <param name="containerName">The container name</param>
        /// <param name="fileName">The file name</param>
        /// <param name="fileBytes">The file content as byte array</param>
        /// <param name="appendSasToken">Whether to append SAS token to the returned URL (default: true)</param>
        /// <returns>The blob URL with or without SAS token based on appendSasToken parameter</returns>
        public static string UploadToBlob(string containerName, string fileName, byte[] fileBytes, bool appendSasToken = true)
        {
            try
            {
                var storageAccountName = KeyVault.GetKeyValue("StorageAccountName");
                var storageAccountKey = KeyVault.GetKeyValue("StorageAccountKey");
                string serviceUri = $"https://{storageAccountName}.blob.core.windows.net";

                // Add logging to check which credential is being used
                var credential = new DefaultAzureCredential(new DefaultAzureCredentialOptions
                {
                    Diagnostics = { LoggedHeaderNames = { "WWW-Authenticate" }, LoggedQueryParameters = { "resource" } },
                    Retry = { MaxRetries = 3, NetworkTimeout = TimeSpan.FromSeconds(5) }
                });

                var blobServiceClient = new BlobServiceClient(new Uri(serviceUri), credential);
                var blobContainerClient = blobServiceClient.GetBlobContainerClient(containerName);

                // Test container access
                var exists = blobContainerClient.Exists();

                // Continue with upload if container exists
                if (exists)
                {
                    var blobClient = blobContainerClient.GetBlobClient(fileName);
                    blobClient.Upload(new MemoryStream(fileBytes), overwrite: true);

                    if (appendSasToken)
                    {
                        // Return URL with SAS token for secure access
                        Uri res = CommonFunctions.GetServiceSasUriForBlob(storageAccountName, storageAccountKey, containerName, fileName);
                        var blobUri = $"{blobClient.Uri.ToString()}{res.Query}";
                        return blobUri;
                    }
                    else
                    {
                        // Return direct blob URL without SAS token for public access (WhatsApp compatibility)
                        return blobClient.Uri.ToString();
                    }
                }

                throw new Exception($"Container {containerName} does not exist");
            }
            catch (Exception ex)
            {
                // Log the specific error
                throw new Exception($"Blob upload failed: {ex.GetType().Name} - {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Uploads a file to Azure Blob Storage with enhanced WhatsApp document support
        /// </summary>
        /// <param name="fileStream">The file content as stream</param>
        /// <param name="fileName">The original file name</param>
        /// <param name="documentType">The document type for path organization (e.g., "yieldsummaryreport", "lowstockreport")</param>
        /// <param name="appendSasToken">Whether to append SAS token to the returned URL (default: true)</param>
        /// <returns>The blob URL with or without SAS token based on appendSasToken parameter</returns>
        public static string UploadToBlob(Stream fileStream, string fileName, string documentType = null, bool appendSasToken = true)
        {
            try
            {
                var storageAccountName = KeyVault.GetKeyValue("StorageAccountName");
                var storageAccountKey = KeyVault.GetKeyValue("StorageAccountKey");

                // Determine container name based on document type
                string containerName;
                string filePath;

                if (!string.IsNullOrEmpty(documentType))
                {
                    // Use WhatsApp documents container with hierarchical structure
                    containerName = GetWhatsAppDocumentsContainer();

                    // Create hierarchical path: /whatsappdocs/{documentType}/YYYY/MM/DD/{fileName}
                    var currentDate = DateTime.UtcNow;
                    var dateFolder = $"{currentDate:yyyy/MM/dd}";
                    filePath = $"{documentType}/{dateFolder}/{fileName}";
                }
                else
                {
                    // Use default container and file path
                    containerName = "reports"; // Default container
                    filePath = fileName;
                }

                string serviceUri = $"https://{storageAccountName}.blob.core.windows.net";

                var credential = new DefaultAzureCredential(new DefaultAzureCredentialOptions
                {
                    Diagnostics = { LoggedHeaderNames = { "WWW-Authenticate" }, LoggedQueryParameters = { "resource" } },
                    Retry = { MaxRetries = 3, NetworkTimeout = TimeSpan.FromSeconds(5) }
                });

                var blobServiceClient = new BlobServiceClient(new Uri(serviceUri), credential);
                var blobContainerClient = blobServiceClient.GetBlobContainerClient(containerName);

                // Test container access
                var exists = blobContainerClient.Exists();

                // Continue with upload if container exists
                if (exists)
                {
                    var blobClient = blobContainerClient.GetBlobClient(filePath);

                    // Set content type based on file extension for better WhatsApp compatibility
                    var contentType = GetContentType(fileName);
                    var uploadOptions = new Azure.Storage.Blobs.Models.BlobUploadOptions
                    {
                        HttpHeaders = new Azure.Storage.Blobs.Models.BlobHttpHeaders
                        {
                            ContentType = contentType
                        }
                    };

                    blobClient.Upload(fileStream, uploadOptions);

                    if (appendSasToken)
                    {
                        // Return URL with SAS token for secure access
                        Uri res = CommonFunctions.GetServiceSasUriForBlob(storageAccountName, storageAccountKey, containerName, filePath);
                        var blobUri = $"{blobClient.Uri.ToString()}{res.Query}";
                        return blobUri;
                    }
                    else
                    {
                        // Return direct blob URL without SAS token for public access (WhatsApp compatibility)
                        return blobClient.Uri.ToString();
                    }
                }

                throw new Exception($"Container {containerName} does not exist");
            }
            catch (Exception ex)
            {
                // Log the specific error
                throw new Exception($"Blob upload failed: {ex.GetType().Name} - {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the WhatsApp documents container name from configuration or uses default
        /// </summary>
        /// <returns>The WhatsApp documents container name</returns>
        private static string GetWhatsAppDocumentsContainer()
        {
            try
            {
                // Try to get from Key Vault first, fallback to default
                var containerName = KeyVault.GetKeyValue("WhatsAppDocumentsContainer");
                return !string.IsNullOrEmpty(containerName) ? containerName : "whatsappdocs";
            }
            catch
            {
                // If Key Vault access fails, use default container name
                return "whatsappdocs";
            }
        }

        /// <summary>
        /// Gets the appropriate content type based on file extension
        /// </summary>
        /// <param name="fileName">The file name</param>
        /// <returns>The MIME content type</returns>
        private static string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName)?.ToLowerInvariant();
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".txt" => "text/plain",
                _ => "application/octet-stream"
            };
        }
        public static string ConvertToIndianWords(decimal number)
        {
            var wholePart = (long)number;
            var decimalPart = (int)((number - wholePart) * 100);

            var result = ConvertWholeNumber(wholePart);

            if (decimalPart > 0)
                result += $" and {ConvertWholeNumber(decimalPart)} Paise";

            return result + " Only";
        }

        private static string ConvertWholeNumber(long number)
        {
            if (number == 0) return "Zero";

            string[] units = { "", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen" };
            string[] tens = { "", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety" };

            if (number < 20) return units[number];

            if (number < 100) return tens[number / 10] + (number % 10 > 0 ? " " + units[number % 10] : "");

            if (number < 1000) return units[number / 100] + " Hundred" + (number % 100 > 0 ? " and " + ConvertWholeNumber(number % 100) : "");

            if (number < 100000) return ConvertWholeNumber(number / 1000) + " Thousand" + (number % 1000 > 0 ? " " + ConvertWholeNumber(number % 1000) : "");

            if (number < 10000000) return ConvertWholeNumber(number / 100000) + " Lakh" + (number % 100000 > 0 ? " " + ConvertWholeNumber(number % 100000) : "");

            return ConvertWholeNumber(number / 10000000) + " Crore" + (number % 10000000 > 0 ? " " + ConvertWholeNumber(number % 10000000) : "");
        }
    }
}
