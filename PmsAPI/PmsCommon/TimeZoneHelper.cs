using System;
using System.Text;
using Microsoft.Azure.KeyVault;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;

namespace PmsCommon
{
    public static class TimeZoneHelper
    {
        public static DateTime ConvertToTimeZone(DateTime dateTime, TimeZoneId timeZoneId)
        {
            TimeZoneInfo timeZoneInfo;
            switch (timeZoneId)
            {
                case TimeZoneId.IndiaStandardTime:
                    timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
                    break;
                // Add other time zones as needed
                default:
                    timeZoneInfo = TimeZoneInfo.Utc;
                    break;
            }
            if (dateTime.Kind == DateTimeKind.Local)
            {
                return dateTime;
            }
            else
                return TimeZoneInfo.ConvertTimeFromUtc(dateTime, timeZoneInfo);
        }

        public static DateTime ConvertToUtc(DateTime dateTime, TimeZoneId timeZoneId = default)
        {
            TimeZoneInfo timeZoneInfo;
            switch (timeZoneId)
            {
                case TimeZoneId.IndiaStandardTime:
                    timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
                    break;
                // Add other time zones as needed
                default:
                    timeZoneInfo = TimeZoneInfo.Utc;
                    break;
            }
            return TimeZoneInfo.ConvertTimeToUtc(dateTime, timeZoneInfo);
        }
    }
}