using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.Extensions.Logging;
using PmsCommon;
using PmsEntity.ViewModel;
using PmsData.Adapters;
using PmsCommon.Services.PdfGeneration.Documents;
using PmsCore.PDFGeneration.Models;
using QuestPDF.Infrastructure;
using QuestPDF.Fluent;

namespace Tests
{
    /// <summary>
    /// Test class to generate example PDF documents for the restructured Low Stock Report
    /// </summary>
    public class LowStockReportPdfGenerationTest
    {
        private readonly ILogger<LowStockReportPdfGenerationTest> _logger;
        private readonly GlobalDataEntity _globalData;

        public LowStockReportPdfGenerationTest()
        {
            // Initialize logging
            using var loggerFactory = LoggerFactory.Create(builder =>
                builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            _logger = loggerFactory.CreateLogger<LowStockReportPdfGenerationTest>();

            // Initialize global data
            _globalData = new GlobalDataEntity();

            // Set QuestPDF license
            QuestPDF.Settings.License = LicenseType.Community;
        }

        /// <summary>
        /// Generate an example PDF with comprehensive sample data demonstrating all three table categories
        /// </summary>
        public void GenerateExamplePdf()
        {
            try
            {
                _logger.LogInformation("🎯 Generating Example Low Stock Report PDF with Three-Table Structure...");

                // Create comprehensive sample data that will populate all three tables
                var sampleData = CreateComprehensiveSampleData();

                // Create PDF adapter
                var adapter = new LowStockReportPdfAdapter(sampleData, DateTime.UtcNow);

                // Create PDF configuration
                var config = new PdfConfiguration
                {
                    CompanyLogoNew = "logo-globe.jpg", // Optional: will work even if logo doesn't exist
                    LogoContainer = "assets"
                };

                // Create the document
                var document = new LowStockReportDocument(adapter, config);

                _logger.LogInformation($"📊 Sample data created with {adapter.TotalItemsCount} total items");
                _logger.LogInformation("📋 Data distribution across tables:");

                // Analyze data distribution
                var adequateStock = sampleData.Where(x => x.TotalAvailableQty > (x.MinimumLevel * 0.5m)).Count();
                var lowStock = sampleData.Where(x => x.TotalAvailableQty <= (x.MinimumLevel * 0.5m) && x.TotalAvailableQty > 0).Count();
                var outOfStock = sampleData.Where(x => x.TotalAvailableQty == 0).Count();

                _logger.LogInformation($"   🟡 Adequate Stock: {adequateStock} items");
                _logger.LogInformation($"   🔴 Low Stock: {lowStock} items");
                _logger.LogInformation($"   🔴 Out of Stock: {outOfStock} items");

                // Generate PDF using QuestPDF
                var pdfBytes = QuestPDF.Fluent.Document.Create(document.Compose).GeneratePdf();

                // Save to file
                var fileName = $"LowStockReport_Example_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                var filePath = Path.Combine(Directory.GetCurrentDirectory(), fileName);

                File.WriteAllBytes(filePath, pdfBytes);

                _logger.LogInformation($"✅ PDF generated successfully!");
                _logger.LogInformation($"📁 File saved to: {filePath}");
                _logger.LogInformation($"📄 File size: {pdfBytes.Length / 1024:N0} KB");

                _logger.LogInformation("🎨 PDF Features Demonstrated:");
                _logger.LogInformation("   ✓ Three distinct tables with color-coded headers");
                _logger.LogInformation("   ✓ Yellow header for Adequate Stock items");
                _logger.LogInformation("   ✓ Red headers for Low Stock and Out of Stock items");
                _logger.LogInformation("   ✓ Descriptive headers explaining filtering criteria");
                _logger.LogInformation("   ✓ Proper spacing between tables");
                _logger.LogInformation("   ✓ Consistent column structure across all tables");
                _logger.LogInformation("   ✓ Professional layout suitable for management reports");

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Failed to generate example PDF");
                throw;
            }
        }

        /// <summary>
        /// Create comprehensive sample data that will populate all three table categories
        /// </summary>
        private List<LowStockReportVm> CreateComprehensiveSampleData()
        {
            var sampleData = new List<LowStockReportVm>();

            // ADEQUATE STOCK ITEMS (TotalAvailableQty > MinimumLevel * 0.5)
            sampleData.AddRange(new[]
            {
                new LowStockReportVm
                {
                    ProductId = 1,
                    ProductName = "Premium Cotton Fabric",
                    ProductType = "Raw Material",
                    ProductCategory = "Textiles",
                    ProductFirstSubCategory = "Cotton",
                    ProductSecSubCategory = "Premium Grade",
                    MinimumLevel = 1000,
                    TotalAvailableQty = 750, // 75% of minimum - ADEQUATE
                    DomesticQty = 450,
                    ImportedQty = 300,
                    Unit = "MTR"
                },
                new LowStockReportVm
                {
                    ProductId = 2,
                    ProductName = "Synthetic Leather Base",
                    ProductType = "Raw Material",
                    ProductCategory = "Leather",
                    ProductFirstSubCategory = "Synthetic",
                    ProductSecSubCategory = "Base Material",
                    MinimumLevel = 500,
                    TotalAvailableQty = 400, // 80% of minimum - ADEQUATE
                    DomesticQty = 250,
                    ImportedQty = 150,
                    Unit = "SQM"
                }
            });

            // LOW STOCK ITEMS (TotalAvailableQty <= MinimumLevel * 0.5 AND > 0)
            sampleData.AddRange(new[]
            {
                new LowStockReportVm
                {
                    ProductId = 3,
                    ProductName = "Industrial Thread - Polyester",
                    ProductType = "Raw Material",
                    ProductCategory = "Threads",
                    ProductFirstSubCategory = "Polyester",
                    ProductSecSubCategory = "Industrial Grade",
                    MinimumLevel = 2000,
                    TotalAvailableQty = 800, // 40% of minimum - LOW STOCK
                    DomesticQty = 500,
                    ImportedQty = 300,
                    Unit = "MTR"
                },
                new LowStockReportVm
                {
                    ProductId = 4,
                    ProductName = "Zipper - Metal Heavy Duty",
                    ProductType = "Component",
                    ProductCategory = "Hardware",
                    ProductFirstSubCategory = "Zippers",
                    ProductSecSubCategory = "Metal",
                    MinimumLevel = 1000,
                    TotalAvailableQty = 350, // 35% of minimum - LOW STOCK
                    DomesticQty = 200,
                    ImportedQty = 150,
                    Unit = "PCS"
                },
                new LowStockReportVm
                {
                    ProductId = 5,
                    ProductName = "Adhesive - Contact Cement",
                    ProductType = "Chemical",
                    ProductCategory = "Adhesives",
                    ProductFirstSubCategory = "Contact",
                    ProductSecSubCategory = "Industrial",
                    MinimumLevel = 200,
                    TotalAvailableQty = 75, // 37.5% of minimum - LOW STOCK
                    DomesticQty = 45,
                    ImportedQty = 30,
                    Unit = "LTR"
                }
            });

            // OUT OF STOCK ITEMS (TotalAvailableQty = 0)
            sampleData.AddRange(new[]
            {
                new LowStockReportVm
                {
                    ProductId = 6,
                    ProductName = "Buckle - Stainless Steel",
                    ProductType = "Component",
                    ProductCategory = "Hardware",
                    ProductFirstSubCategory = "Buckles",
                    ProductSecSubCategory = "Stainless Steel",
                    MinimumLevel = 500,
                    TotalAvailableQty = 0, // OUT OF STOCK
                    DomesticQty = 0,
                    ImportedQty = 0,
                    Unit = "PCS"
                },
                new LowStockReportVm
                {
                    ProductId = 7,
                    ProductName = "Dye - Reactive Black",
                    ProductType = "Chemical",
                    ProductCategory = "Dyes",
                    ProductFirstSubCategory = "Reactive",
                    ProductSecSubCategory = "Black",
                    MinimumLevel = 100,
                    TotalAvailableQty = 0, // OUT OF STOCK
                    DomesticQty = 0,
                    ImportedQty = 0,
                    Unit = "KG"
                },
                new LowStockReportVm
                {
                    ProductId = 8,
                    ProductName = "Velcro Strip - Industrial",
                    ProductType = "Component",
                    ProductCategory = "Fasteners",
                    ProductFirstSubCategory = "Velcro",
                    ProductSecSubCategory = "Industrial",
                    MinimumLevel = 1000,
                    TotalAvailableQty = 0, // OUT OF STOCK
                    DomesticQty = 0,
                    ImportedQty = 0,
                    Unit = "MTR"
                }
            });

            return sampleData;
        }

        /// <summary>
        /// Run the PDF generation test
        /// </summary>
        public static void RunTest()
        {
            var test = new LowStockReportPdfGenerationTest();
            test.GenerateExamplePdf();
        }

        /// <summary>
        /// Main method to run the test directly
        /// </summary>
        public static void Main(string[] args)
        {
            Console.WriteLine("🎯 Low Stock Report PDF Generation Example");
            Console.WriteLine("==========================================");
            Console.WriteLine();

            Console.WriteLine("This will generate an example PDF demonstrating the new three-table structure:");
            Console.WriteLine("🟡 Table 1: Adequate Stock (Yellow Header) - Items with >50% of minimum level");
            Console.WriteLine("🔴 Table 2: Low Stock (Red Header) - Items with ≤50% of minimum level but >0");
            Console.WriteLine("🔴 Table 3: Out of Stock (Red Header) - Items with zero available quantity");
            Console.WriteLine();

            try
            {
                RunTest();

                Console.WriteLine();
                Console.WriteLine("✅ PDF generation completed successfully!");
                Console.WriteLine("📁 Check the current directory for the generated PDF file.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
