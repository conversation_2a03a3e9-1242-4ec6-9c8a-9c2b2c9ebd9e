using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsCommonFunction
    {
        [Function("PmsCommonFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsCommonFunction_dataGetItems", tags: new[] { "Common" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "data/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("Function1");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getmeasureunits"))
                {
                    var res = new PmsBusiness.CommonFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetMeasureUnits());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getstoragetokenforinvoice"))
                {
                    var res = new PmsBusiness.CommonFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetStorageTokenForInvoice());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getstoragetokenforpoupload"))
                {
                    var res = new PmsBusiness.CommonFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetStorageTokenForPOUpload());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getstoragetokenforgate"))
                {
                    var res = new PmsBusiness.CommonFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetStorageTokenForGate());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getcurrencylist"))
                {
                    var res = new PmsBusiness.CommonFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetCurrency());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallpackagingtypes"))
                {
                    var res = new PmsBusiness.CommonFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllPackagingTypes());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsCommonFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsCommonFunction_dataUpdateItems", tags: new[] { "Common" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "data/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsCommonFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addupdatemeasureunits"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var rec = JsonConvert.DeserializeObject<MeasureUnitMasterVm>(reqbody);
                    rec.AddedBy = GlobalData.loggedInUser;
                    var pf = new PmsBusiness.CommonFunctions(GlobalData);
                    var res = pf.AddUpdateMeasureUnit(rec);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Unit added successfully");
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Unit already added");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("disablemeasureunit"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var id = JsonConvert.DeserializeObject<int>(reqbody);
                    var pf = new PmsBusiness.CommonFunctions(GlobalData);
                    var res = pf.DisableMeasureUnit(id);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Unit disabled successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addupdatepackagingtype"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var rec = JsonConvert.DeserializeObject<PackagingTypeMasterVm>(reqbody);
                        var pf = new PmsBusiness.CommonFunctions(GlobalData);
                        var res = pf.AddUpdatePackagingType(rec);
                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            var response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync("Packaging Type added successfully");
                            return response;
                        }
                        if (res.StatusCode == HttpStatusCode.BadRequest)
                        {
                            logger.LogError("Packaging Type already exists");
                            var response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync("Packaging Type already exists");
                            response.StatusCode = HttpStatusCode.BadRequest;
                            return response;
                        }
                        else
                        {
                            logger.LogError("Error occurred" + res.ResponseBody);
                            var response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                            response.StatusCode = HttpStatusCode.BadRequest;
                            return response;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("disablepackagingtype"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var id = JsonConvert.DeserializeObject<int>(reqbody);
                    var pf = new PmsBusiness.CommonFunctions(GlobalData);
                    var res = pf.DisablePackagingType(id);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Packaging Type disabled successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

    }
}
