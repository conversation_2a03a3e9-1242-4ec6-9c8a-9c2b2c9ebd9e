using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsConsumeStockFunction
    {
        [Function("PmsConsumeStockFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsConsumeStockFunction_dataGetItems", tags: new[] { "ConsumeStock" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "consume/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsConsumeStockFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsConsumeStockFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            //logger.LogInformation("KeyURL :" + PmsCommon.KeyVault.KeyVaultUrl);
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallconsumestockproducts"))
                {
                    var res = new ConsumeStockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllConsumeStockProducts());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsConsumeStockFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsConsumeStockFunction_dataUpdateItems", tags: new[] { "ConsumeStock" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "consume/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsConsumeStockFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsConsumeStockFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addupdateconsumestockproduct"))
                {
                    try
                    {
                        var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                        var ConsumeStock = JsonConvert.DeserializeObject<List<ConsumeStockProductMasterVm>>(reqbody);
                        ConsumeStock[0].AddedBy = GlobalData.loggedInUser;
                        var pf = new ConsumeStockFunctions(GlobalData);
                        var res = pf.AddUpdateConsumeStockProduct(ConsumeStock);
                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            var response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync("Consumption Saved Successfully");
                            return response;
                        }
                        if (res.StatusCode == HttpStatusCode.BadRequest)
                        {
                            var response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync(res);
                            response.StatusCode = HttpStatusCode.BadRequest;
                            return response;
                        }
                        else
                        {
                            var response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync(res);
                            response.StatusCode = HttpStatusCode.BadRequest;
                            return response;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("getallpendingconsumptionorderswithfilter"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<PendingConsumptionOrdersRequestVm>(reqbody);
                        var res = new ConsumeStockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllPendingConsumptionOrderswithFilter(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsConsumeStockFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsConsumeStockFunction_dataGetItemById", tags: new[] { "ConsumeStock" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "consume/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {

            var logger = executionContext.GetLogger("PmsConsumeStockFunction_dataGetItemById");
            logger.LogInformation("C# HTTP PmsConsumeStockFunction_dataGetItemById processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallsaleorderproductstoconsume"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new ConsumeStockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllSaleOrderProductsToConsume(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getallsaleorderconsumedproducts"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new ConsumeStockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllSaleOrderConsumedProducts(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getallproductstoconsumebystoreid"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new ConsumeStockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllProductsToConsumeByStoreID(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals(("GetConsumptionbyStockProductId").ToLower()))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new ConsumeStockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetConsumptionbyStockProductId(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }

            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }
    }
}
