using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsUserFunction
    {
        [Function("PmsUserFunction_daUseretItems")]
        [OpenApiOperation(operationId: "PmsUserFunction_daUseretItems", tags: new[] { "User" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "user/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsUserFunction_daUseretItems");
            logger.LogInformation("C# HTTP PmsUserFunction_daUseretItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            //logger.LogInformation("KeyURL :" + PmsCommon.KeyVault.KeyVaultUrl);
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                //if (entity.ToLowerInvariant().Equals("getalluserrole"))
                //{
                //    var res = new UserFunctions();
                //    var response = req.CreateResponse(HttpStatusCode.OK);
                //    await response.WriteAsJsonAsync(res.GetAllUserRole());
                //    return response;
                //}
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsUserFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsUserFunction_dataGetItemById", tags: new[] { "User" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "user/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsUserFunction_dataGetItemById");
            logger.LogInformation("C# HTTP PmsUserFunction_dataGetItemById processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                //if (entity.ToLowerInvariant().Equals("getuserrole"))
                //{
                //    if (id != null)
                //    {
                //        var res = new UserFunctions();
                //        var response = req.CreateResponse(HttpStatusCode.OK);
                //        await response.WriteAsJsonAsync(res.GetUserRole(id));
                //        return response;
                //    }
                //    else
                //    {
                //        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                //        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                //        return response;
                //    }

                //}
                if (entity.ToLowerInvariant().Equals("getuserstores"))
                {
                    if (id != null)
                    {
                        var res = new UserFunctions();
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetUserStores(id));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsUserFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsUserFunction_dataUpdateItems", tags: new[] { "User" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "user/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsUserFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsUserFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                //if (entity.ToLowerInvariant().Equals("addupdaterole"))
                //{
                //    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                //    var User = JsonConvert.DeserializeObject<UserRoleVm>(reqbody);
                //    var pf = new UserFunctions();
                //    var res = pf.AddUpdateRole(User);
                //    if (res.StatusCode == HttpStatusCode.OK)
                //    {
                //        var response = req.CreateResponse(HttpStatusCode.OK);
                //        await response.WriteAsJsonAsync("User added successfully");
                //        return response;
                //    }
                //    if (res.StatusCode == HttpStatusCode.BadRequest)
                //    {
                //        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                //        await response.WriteAsJsonAsync("User already added");
                //        response.StatusCode = HttpStatusCode.BadRequest;
                //        return response;
                //    }
                //    else
                //    {
                //        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                //        await response.WriteAsJsonAsync("Error occurred in AddUpdateUser");
                //        return response;
                //    }
                //}
                if (entity.ToLowerInvariant().Equals("addupdateuserstoremapping"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var User = JsonConvert.DeserializeObject<List<UserStoreMappingTableVm>>(reqbody);
                    var pf = new UserFunctions();
                    var res = pf.AddUpdateUserStoreMapping(User);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("User added successfully");
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error Occured");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

    }
}
