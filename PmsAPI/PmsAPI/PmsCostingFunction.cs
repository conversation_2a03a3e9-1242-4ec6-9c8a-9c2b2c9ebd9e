using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsCostingFunction
    {
        // [Function("PmsCostingFunction_dataGetItems")]
        // [OpenApiOperation(operationId: "PmsCostingFunction_dataGetItems", tags: new[] { "Costing" })]
        // [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        // public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "costing/{entity}")] HttpRequestData req, string entity,
        //     FunctionContext executionContext)
        // {
        //     var logger = executionContext.GetLogger("Function1");
        //     logger.LogInformation("C# HTTP trigger function processed a request.");
        //     IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
        //     var authHeader = headerValues.FirstOrDefault();
        //     var handler = new JwtSecurityTokenHandler();
        //     authHeader = authHeader.Replace("Bearer ", "");
        //     var jsonToken = handler.ReadToken(authHeader);
        //     var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
        //     var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
        //     GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
        //     try
        //     {
        //         if (entity.ToLowerInvariant().Equals("getfilteredcostinglist"))
        //         {
        //             var res = new CostingFunctions(GlobalData);
        //             var response = req.CreateResponse(HttpStatusCode.OK);
        //             await response.WriteAsJsonAsync(res.GetFilteredCostingList());
        //             return response;
        //         }
        //         else
        //         {
        //             var response = req.CreateResponse(HttpStatusCode.BadRequest);
        //             await response.WriteAsJsonAsync("Entity not found");
        //             return response;
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         logger.LogError("Exception Message:" + ex.Message);
        //         logger.LogError("Exception StackTrace:" + ex.StackTrace);
        //         logger.LogError("Exception InnerException:" + ex.InnerException);
        //         var response = req.CreateResponse(HttpStatusCode.InternalServerError);
        //         await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
        //         return response;
        //     }
        // }
        [Function("PmsCostingFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsCostingFunction_dataUpdateItems", tags: new[] { "Costing" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "costing/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsCostingFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getfilteredcostinglist"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var filters = JsonConvert.DeserializeObject<SaleOrderCostingRequestFilter>(reqbody);
                    var pf = new CostingFunctions(GlobalData);
                    var res = pf.GetFilteredCostingList(filters);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res);
                    return response;

                }
                if (entity.ToLowerInvariant().Equals("addupdatecosting"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Costing = JsonConvert.DeserializeObject<SaleOrderCostingTableVm>(reqbody);
                    var pf = new CostingFunctions(GlobalData);
                    var res = pf.AddUpdateCosting(Costing);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateCosting");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator." + ex.Message);
                return response;
            }
        }
    }
}
