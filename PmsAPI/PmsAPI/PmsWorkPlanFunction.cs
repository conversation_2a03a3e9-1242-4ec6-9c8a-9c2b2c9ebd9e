using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public class PmsWorkPlanFunction
    {
        [Function("PmsWorkPlanFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsWorkPlanFunction_dataGetItems", tags: new[] { "WorkPlan" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get",
            Route = "workplan/{entity}")] HttpRequestData req, string entity, FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsWorkPlanFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsWorkPlanFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = "Not all required parameters provided" });
                    return response;
                }

                var res = new WorkPlanFunctions(GlobalData);

                if (entity.ToLowerInvariant().Equals("getallworkplan"))
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllWorkPlan());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallworkplanjumbo"))
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllWorkPlanJumbo());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals(("GetJumboListWithInspectionCount").ToLower()))
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetJumboListWithInspectionCount());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getjumboprintbyorderidjumboid"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string querysaleorderid = query.Get("saleorderid");
                    string queryJumbodId = query.Get("jumboid");

                    if (long.TryParse(querysaleorderid, out long saleorderid) && long.TryParse(queryJumbodId, out long jumboId))
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetJumboPrintByOrderIdJumboId(saleorderid, jumboId));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse values");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = "Entity not found" });
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsWorkPlanFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsWorkPlanFunction_dataGetItemById", tags: new[] { "WorkPlan" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get",
            Route = "workplan/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsWorkPlanFunction_dataGetItemById");
            logger.LogInformation("C# HTTP PmsWorkPlanFunction_dataGetItemById processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = "Not all required parameters provided" });
                    return response;
                }

                var res = new WorkPlanFunctions(GlobalData);

                switch (entity.ToLowerInvariant())
                {
                    case "getallworkplanbyid":
                        {
                            long pid;
                            if (long.TryParse(id, out pid))
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(res.GetAllWorkPlanById(pid));
                                return response;
                            }
                            else
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = $"Unable to parse id {id}" });
                                return response;
                            }

                        }
                    case "getworkplanorderbyworkplanid":
                        {
                            long pid;
                            if (long.TryParse(id, out pid))
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(res.GetWorkPlanOrderByWorkplanId(pid));
                                return response;
                            }
                            else
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = $"Unable to parse id {id}" });
                                return response;
                            }
                        }
                    case "getpostprocessworkplanorderbyworkplanid":
                        {
                            long pid;
                            if (long.TryParse(id, out pid))
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(res.GetPostProcessWorkPlanOrderByWorkplanId(pid));
                                return response;
                            }
                            else
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = $"Unable to parse id {id}" });
                                return response;
                            }
                        }
                    case "getworkplanorderbyworkplanidforconsume":
                        {
                            long pid;
                            if (long.TryParse(id, out pid))
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(res.GetWorkPlanOrderByWorkplanIdForConsume(pid));
                                return response;
                            }
                            else
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = $"Unable to parse id {id}" });
                                return response;
                            }
                        }
                    case "getworkplanjumbobyworkplanordersid":
                        {
                            long pid;
                            if (long.TryParse(id, out pid))
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(res.GetWorkPlanJumboByWorkPlanOrdersId(pid));
                                return response;
                            }
                            else
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = $"Unable to parse id {id}" });
                                return response;
                            }
                        }
                    case "getworkplanjumbobyjumbonumber":
                        {
                            var response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync(res.GetWorkPlanJumboByJumboNumber(id));
                            return response;
                        }
                    case "getworkplanbysaleorderid":
                        {
                            long pid;
                            if (long.TryParse(id, out pid))
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(res.GetWorkPlanBysaleorderid(pid));
                                return response;
                            }
                            else
                            {
                                var response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = $"Unable to parse id {id}" });
                                return response;
                            }

                        }
                    default:
                        {
                            var response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = "Entity not found" });
                            return response;
                        }
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsWorkPlanFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsWorkPlanFunction_dataUpdateItems", tags: new[] { "WorkPlan" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post",
            Route = "workplan/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsWorkPlanFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsWorkPlanFunction_dataUpdateItems processed a request.");

            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = "Not all required parameters provided" });
                    return response;
                }

                var pf = new WorkPlanFunctions(GlobalData);

                if (entity.ToLowerInvariant().Equals("addworkplanwithorders"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<WorkPlanMasterVm>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync(pf.AddWorkPlanWithOrders(data));
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("changeworkplanforsingleorder"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<WorkPlanMasterVm>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    var res = pf.ChangeWorkPlanForSingleOrder(data);

                    if (res.Result.Succeeded == true)
                    {
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else if (res.Result.Code == EMessageCode.BadRequest)
                    {
                        response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.Result.Message);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(res.Result.Message);
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("reviewworkplan"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<ReviewWorkPlanVm>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync(pf.ReviewWorkplan(data));
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallworkplanbysaleorderstatus"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<List<string>>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync(pf.GetAllWorkPlanBysaleorderstatus(data));
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallworkplanbysaleorderstatusforconsume"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<List<string>>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync(pf.GetAllWorkPlanBySaleOrderStatusForConsume(data));
                    return response;
                }
                else if (entity.ToLowerInvariant().Equals("addworkplanjumbo"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<List<WorkPlanJumboMasterVm>>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.OK);
                    var res = pf.AddWorkPlanJumbo(data);
                    if (res.Result.Succeeded == true)
                    {
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.Result.Message);
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("addworkplanjumbosingleobj"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<WorkPlanJumboMasterVm>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.OK);
                    var res = pf.AddWorkPlanJumboSingleObj(data);
                    if (res.Result.Succeeded == true)
                    {
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.Result.Message);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals(("GetWorkPlanJumboForFinalInspection").ToLower()))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<JumboFinalInspectionFilter>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync(pf.GetWorkPlanJumboForFinalInspection(data));
                    return response;
                }
                else if (entity.ToLowerInvariant().Equals(("GetAllWorkPlanJumboWithFilters").ToLower()))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<JumboFinalInspectionFilter>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync(pf.GetAllWorkPlanJumboWithFilters(data));
                    return response;
                }
                else if (entity.ToLowerInvariant().Equals("getallworkplanreport"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var data = JsonConvert.DeserializeObject<SearchParamWorkPlanMasterReportVm>(reqbody);

                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync(pf.GetAllWorkPlanReport(data));
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.BadRequest, Succeeded = false, Message = "Entity not found" });
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }
    }
}
