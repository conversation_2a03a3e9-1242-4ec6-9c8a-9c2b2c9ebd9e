using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsStockFunction
    {
        [Function("PmsStockFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsStockFunction_dataGetItems", tags: new[] { "Stock" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "stock/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsStockFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsStockFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallstocks"))
                {
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllStocks());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallunstockedinvoices"))
                {
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllUnstockedInvoices());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getstockreport"))
                {
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetstockReport());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getproductwisestock"))
                {
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetProductWisestock());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getproductwisestorestock"))
                {
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetProductWiseStorestock());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getproductwisestorerackstock"))
                {
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetProductWiseStoreRackstock());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getsupplierproductnames"))
                {
                    var supplierId = req.Query["supplierId"];
                    var productId = req.Query["productId"];
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    if (!string.IsNullOrEmpty(supplierId) && !string.IsNullOrEmpty(productId) && long.TryParse(supplierId, out long supplierIdValue) && long.TryParse(productId, out long productIdValue))
                    {
                        await response.WriteAsJsonAsync(res.GetSupplierProductNames(supplierIdValue, productIdValue));
                    }
                    else if (!string.IsNullOrEmpty(supplierId) && long.TryParse(supplierId, out long supplierIdValue1))
                    {
                        await response.WriteAsJsonAsync(res.GetSupplierProductNames(supplierIdValue1));
                    }
                    else
                    {
                        await response.WriteAsJsonAsync(new List<SupplierProductNameVm>());
                    }
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getsuppliermappings"))
                {
                    var supplierId = req.Query["supplierId"];
                    if (!string.IsNullOrEmpty(supplierId) && long.TryParse(supplierId, out long supplierIdValue))
                    {
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSupplierMappings(supplierIdValue));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("SupplierId parameter is required");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getproductstockbystoreidbyproductid"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string querystoreid = query.Get("storeid");
                    string queryproductId = query.Get("productid");

                    long storeid;
                    long productId;
                    if (long.TryParse(querystoreid, out storeid) && long.TryParse(queryproductId, out productId))
                    {
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetProductStockByStoreIdByProductId(storeid, productId));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse values");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getproducttransferhistory"))
                {
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetProductTransferHistory());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsStockFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsStockFunction_dataGetItemById", tags: new[] { "Stock" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "stock/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsStockFunction_dataGetItemById");
            logger.LogInformation("C# HTTP PmsStockFunction_dataGetItemById processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getstockbyid"))
                {
                    long stockid;
                    if (long.TryParse(id, out stockid))
                    {
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetstockById(stockid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getpendingstockinspectionbyid"))
                {
                    long stockid;
                    if (long.TryParse(id, out stockid))
                    {
                        var dataFn = new StockFunctions(GlobalData);
                        var res = dataFn.GetPendingStockInspectionById(stockid);

                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            var response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            return response;
                        }
                        else
                        {
                            var response = req.CreateResponse(res.StatusCode);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            response.StatusCode = res.StatusCode;
                            return response;
                        }
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getproductwisestorerackstockbystoreid"))
                {
                    long storeid;
                    if (long.TryParse(id, out storeid))
                    {
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetProductWiseStoreRackstockByStoreId(storeid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getproductstockwithsupplierbystoreid"))
                {
                    long storeid;
                    if (long.TryParse(id, out storeid))
                    {
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetProductStockWithSupplierByStoreId(storeid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getproductavailablestockbyproductid"))
                {
                    long productId;
                    if (long.TryParse(id, out productId))
                    {
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetProductAvailableStockByProductId(productId));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getstoresforproduct"))
                {
                    long productId;
                    if (long.TryParse(id, out productId))
                    {
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetStoresForProduct(productId));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getracksforproductinstore"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string queryStoreId = query.Get("storeid");

                    long productId;
                    long storeId;
                    if (long.TryParse(id, out productId) && long.TryParse(queryStoreId, out storeId))
                    {
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetRacksForProductInStore(productId, storeId));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse values");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsStockFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsStockFunction_dataUpdateItems", tags: new[] { "Stock" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "stock/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsStockFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsStockFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addstock"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var stock = JsonConvert.DeserializeObject<StockVm>(reqbody);
                    var pf = new StockFunctions(GlobalData);
                    var res = pf.AddStock(stock);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Stock added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in addstock");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addstockmaster"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var stock = JsonConvert.DeserializeObject<StockProductTableMasterVm>(reqbody);
                    var pf = new StockFunctions(GlobalData);
                    var res = pf.AddStockMaster(stock);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Stock added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in addstock");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("createproductsuppliermapping"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var mapping = JsonConvert.DeserializeObject<CreateProductSupplierMappingVm>(reqbody);
                    var pf = new StockFunctions(GlobalData);
                    var res = pf.CreateOrUpdateProductSupplierMapping(mapping);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("updatesupplierproductname"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var updateRequest = JsonConvert.DeserializeObject<UpdateSupplierProductNameVm>(reqbody);
                    var pf = new StockFunctions(GlobalData);
                    var res = pf.UpdateSupplierProductName(updateRequest);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(res.StatusCode);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("stockinspection"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var stock = JsonConvert.DeserializeObject<StockVm>(reqbody);
                    var pf = new StockFunctions(GlobalData);
                    var res = pf.StockInspection(stock);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("An error has been occured. Please try again");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("stockqualityinspection"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var stock = JsonConvert.DeserializeObject<StockVm>(reqbody);
                    var pf = new StockFunctions(GlobalData);
                    var res = pf.StockQualityInspection(stock);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("stockallocation"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var stockP = JsonConvert.DeserializeObject<StockProductTableVm>(reqbody);
                    var pf = new StockFunctions(GlobalData);
                    var res = pf.SaveStockAllocation(stockP);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("An error has been occured. Please try again");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("stockmanagerejected"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var stockP = JsonConvert.DeserializeObject<List<StockProductManageRejectedVm>>(reqbody);
                    var pf = new StockFunctions(GlobalData);
                    var res = pf.SaveStockProductManageRejected(stockP);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Stock added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("An error has been occured. Please try again");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals(("GetProductWisestockWithSupplierWithConsumption").ToLower()))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var param = JsonConvert.DeserializeObject<SearchParamsProductStockReportVm>(reqbody);
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetProductWisestockWithSupplierWithConsumption(param));
                    return response;
                }
                if (entity.ToLowerInvariant().Equals(("GetProductWisestockByFilter").ToLower()))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var param = JsonConvert.DeserializeObject<SearchParamsProductCategoryReportVm>(reqbody);
                    var res = new StockFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetProductWisestockByFilter(param));
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("updatestockproductpricebystockproductid"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var stock = JsonConvert.DeserializeObject<StockProductPriceVm>(reqbody);
                    var pf = new StockFunctions(GlobalData);
                    var res = pf.UpdateStockProductPriceByStockProductId(stock);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Price Updated successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(res.StatusCode);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = res.StatusCode;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getproductwisestockwithsupplier"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<StockAvailabilityReportRequestVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetProductWisestockWithSupplier(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getallstockswithfilters"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<StockRequestVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllStocksWithFilters(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getsupplierproductmappingreport"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<SupplierProductMappingReportRequestVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSupplierProductMappingReport(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("generateproductstocklabel"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<StockLabelTableVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GenerateProductStockLabel(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("splitproductstocklabel"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<SplitLabelRequestVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.SplitProductStockLabel(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("updatestockproductlabel"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<StockLabelTableVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.UpdateStockProductLabel(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("splitproductstocklabel"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<SplitLabelRequestVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.SplitProductStockLabel(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getfullstockdetailbylabelserialno"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<StockLabelSearchVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        HttpResponseData response;
                        try
                        {
                            var stockLabel = res.GetFullStockDetailByLabelSerialNo(request);
                            if (stockLabel.StatusCode == HttpStatusCode.OK)
                            {
                                response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(stockLabel.ResponseBody);
                                return response;
                            }
                            else
                            {
                                logger.LogError($"Stock label not found for user {GlobalData.loggedInUser} and serial no: {request.SerialNo} or short code: {request.ShortCode}");
                                response = req.CreateResponse(HttpStatusCode.NotFound);
                                await response.WriteAsJsonAsync(stockLabel.ResponseBody);
                                response.StatusCode = HttpStatusCode.NotFound;
                                return response;
                            }
                        }
                        catch (Exception innerEx)
                        {
                            logger.LogError($"Inner Exception for user {GlobalData.loggedInUser} in GetFullStockDetailByLabelSerialNo: {innerEx.Message}");
                            logger.LogError($"Inner Exception StackTrace: {innerEx.StackTrace}");
                            response = req.CreateResponse(HttpStatusCode.InternalServerError);
                            await response.WriteAsJsonAsync(new { ResponseBody = innerEx.Message, ResponseStackTrace = innerEx.StackTrace });
                            response.StatusCode = HttpStatusCode.InternalServerError;
                            return response;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"Outer Exception for user {GlobalData.loggedInUser} in GetFullStockDetailByLabelSerialNo: {ex.Message}");
                        logger.LogError($"Outer Exception StackTrace: {ex.StackTrace}");
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(new { ResponseBody = "An error occurred while processing your request", ResponseStackTrace = ex.Message });
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getstocklabelbyserialno"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<StockLabelSearchVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        HttpResponseData response;
                        try
                        {
                            var stockLabel = res.GetStockLabelBySerialNo(request);
                            if (stockLabel.StatusCode == HttpStatusCode.OK)
                            {
                                response = req.CreateResponse(HttpStatusCode.OK);
                                await response.WriteAsJsonAsync(stockLabel.ResponseBody);
                                return response;
                            }
                            else
                            {
                                logger.LogError($"Stock label not found for user {GlobalData.loggedInUser} and serial no: {request.SerialNo} or short code: {request.ShortCode}");
                                response = req.CreateResponse(HttpStatusCode.NotFound);
                                await response.WriteAsJsonAsync(stockLabel.ResponseBody);
                                response.StatusCode = HttpStatusCode.NotFound;
                                return response;
                            }
                        }
                        catch (Exception innerEx)
                        {
                            logger.LogError($"Inner Exception for user {GlobalData.loggedInUser} in GetStockLabelBySerialNo: {innerEx.Message}");
                            logger.LogError($"Inner Exception StackTrace: {innerEx.StackTrace}");
                            response = req.CreateResponse(HttpStatusCode.InternalServerError);
                            await response.WriteAsJsonAsync(new { ResponseBody = innerEx.Message, ResponseStackTrace = innerEx.StackTrace });
                            response.StatusCode = HttpStatusCode.InternalServerError;
                            return response;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"Outer Exception for user {GlobalData.loggedInUser} in GetStockLabelBySerialNo: {ex.Message}");
                        logger.LogError($"Outer Exception StackTrace: {ex.StackTrace}");
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(new { ResponseBody = "An error occurred while processing your request", ResponseStackTrace = ex.Message });
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getstocklabelsbyproductstock"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<StockLabelTableVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetStockLabelsByProductStock(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getstocklabelsbybatchproductid"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<StockLabelTableVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetStockLabelsByBatchProductId(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getstocklabelsbystocklabelid"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<List<long>>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetStockLabelsByStockLabelId(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getstocklabeltimeline"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<long>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetStockLabelTimeline(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getstocklabelcomprehensivetimeline"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<long>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetStockLabelComprehensiveTimeline(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getallstocklabelsbyfilters"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<StockLabelListFilterVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllStockLabelsbyFilters(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("updatestocklabelstatus"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<UpdateStockLabelStatusVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.UpdateStockLabelStatus(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getproductavailablestockbyproductids"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<ProductAvailableStockRequestVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetProductAvailableStockByProductIds(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("requestproductstocktransfer"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<ProductStockTransferRequestVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.RequestProductStockTransfer(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("actionproductstocktransfer"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<ProductStockTransferRequestVm>(reqbody);
                        var res = new StockFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ActionProductStockTransfer(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(new { ResponseBody = "An error has occured. Please contact administrator", ResponseStackTrace = ex.Message });
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync(new { ResponseBody = "An error has occured. Please contact administrator", ResponseStackTrace = ex.Message });
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

    }
}
