using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsGateFunction
    {
        [Function("PmsGateFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsGateFunction_dataGetItems", tags: new[] { "GateManagement" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "gate/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsGateFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsGateFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getgateinrecords"))
                {
                    var res = new GateFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetGateInRecords());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getvehiclestatus"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string vehicleidq = query.Get("vehicleid");
                    long vehicleidl;
                    if (long.TryParse(vehicleidq, out vehicleidl))
                    {
                        var res = new GateFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetVehicleStatus(vehicleidl));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse values");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getgateinrecordbyid"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string getinidq = query.Get("gateinid");
                    if (long.TryParse(getinidq, out long getinidl))
                    {
                        var res = new GateFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetGateInRecordById(getinidl));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse values");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsGateFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsGateFunction_dataUpdateItems", tags: new[] { "GateManagement" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "gate/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsGateFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addgatein"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var trans = JsonConvert.DeserializeObject<GateInVm>(reqbody);
                    var pf = new GateFunctions(GlobalData);
                    var res = pf.AddGateInRecord(trans);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateProduct");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addgateinforsaleorderdispatch"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var trans = JsonConvert.DeserializeObject<GateInVm>(reqbody);
                    var pf = new GateFunctions(GlobalData);
                    var res = pf.AddGateInRecordForSaleOrderDispatch(trans);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateProduct");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("issuegatepass"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var trans = JsonConvert.DeserializeObject<GateInVm>(reqbody);
                    var pf = new GateFunctions(GlobalData);
                    var res = pf.IssueGatePass(trans);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in Issue Gate Pass");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("gateout"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var trans = JsonConvert.DeserializeObject<GateInVm>(reqbody);
                    var pf = new GateFunctions(GlobalData);
                    var res = pf.GateOut(trans);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in Issue Gate Pass");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getgateinrecordswithfilter"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var data = JsonConvert.DeserializeObject<GateInFilter>(reqbody);
                        var res = new GateFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetGateInRecordsWithFilter(data));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

    }
}
