using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsProductionFunction
    {
        [Function("PmsProductionFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsProductionFunction_dataGetItems", tags: new[] { "Production" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "production/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsProductionFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsProductionFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getproductiondowntimereasonlist"))
                {
                    var res = new ProductionFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetProductionDowntimeReasonList());
                    return response;
                }
                else if (entity.ToLowerInvariant().Equals("getproductiondowntimeactivereasonlist"))
                {
                    var res = new ProductionFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetProductionDowntimeActiveReasonList());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

        [Function("PmsProductionFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsProductionFunction_dataGetItemById", tags: new[] { "Production" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "production/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsProductionFunction_dataGetItemById");
            logger.LogInformation("C# HTTP PmsProductionFunction_dataGetItemById processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getproductiondowntimereasonbyid"))
                {
                    var pf = new ProductionFunctions(GlobalData);
                    var res = pf.GetProductionDowntimeReasonById(long.Parse(id));
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = res.StatusCode;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("deleteproductiondowntime"))
                {
                    var pf = new ProductionFunctions(GlobalData);
                    var res = pf.DeleteProductionDowntime(long.Parse(id));
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Production downtime deleted successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = res.StatusCode;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("getproductiondowntimebyid"))
                {
                    var pf = new ProductionFunctions(GlobalData);
                    var res = pf.GetProductionDowntimeById(long.Parse(id));
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = res.StatusCode;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("getproductiondowntimeschedulebyid"))
                {
                    var pf = new ProductionFunctions(GlobalData);
                    var res = pf.GetProductionDowntimeScheduleById(long.Parse(id));
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = res.StatusCode;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("getproductiondowntimeschedulelistbyreasonid"))
                {
                    var pf = new ProductionFunctions(GlobalData);
                    var res = pf.GetProductionDowntimeScheduleListByReasonId(long.Parse(id));
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = res.StatusCode;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("deleteproductiondowntimeschedule"))
                {
                    var pf = new ProductionFunctions(GlobalData);
                    var res = pf.DeleteProductionDowntimeSchedule(long.Parse(id));
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = res.StatusCode;
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

        [Function("PmsProductionFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsProductionFunction_dataUpdateItems", tags: new[] { "Production" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "production/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsProductionFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsProductionFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getproductiondowntimelist"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var filter = JsonConvert.DeserializeObject<ProductionDowntimeFilterVm>(reqbody);
                    var pf = new ProductionFunctions(GlobalData);
                    HttpResponseData response;
                    try
                    {
                        var res = pf.GetProductionDowntimeList(filter);
                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            return response;
                        }
                        else
                        {
                            response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            response.StatusCode = res.StatusCode;
                            return response;
                        }
                    }
                    catch (Exception innerEx)
                    {
                        logger.LogError($"Inner Exception for user {GlobalData.loggedInUser} in GetProductionDowntimeList: {innerEx.Message}");
                        logger.LogError($"Inner Exception StackTrace: {innerEx.StackTrace}");
                        response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(new { ResponseBody = innerEx.Message, ResponseStackTrace = innerEx.StackTrace });
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addupdateproductiondowntimereason"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Production = JsonConvert.DeserializeObject<ProductionDowntimeReasonMasterVm>(reqbody);
                    var pf = new ProductionFunctions(GlobalData);
                    HttpResponseData response;
                    try
                    {
                        var res = pf.AddUpdateProductionDowntimeReason(Production);
                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            response = req.CreateResponse(res.StatusCode);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            return response;
                        }
                        else
                        {
                            response = req.CreateResponse(res.StatusCode);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            response.StatusCode = res.StatusCode;
                            return response;
                        }
                    }
                    catch (Exception innerEx)
                    {
                        logger.LogError($"Inner Exception for user {GlobalData.loggedInUser} in AddUpdateProductionDowntimeReason: {innerEx.Message}");
                        logger.LogError($"Inner Exception StackTrace: {innerEx.StackTrace}");
                        response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(new { ResponseBody = innerEx.Message, ResponseStackTrace = innerEx.StackTrace });
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("addupdateproductiondowntime"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Production = JsonConvert.DeserializeObject<ProductionDowntimeTableVm>(reqbody);
                    var pf = new ProductionFunctions(GlobalData);
                    HttpResponseData response;
                    try
                    {
                        var res = pf.AddUpdateProductionDowntime(Production);
                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync("Production downtime added successfully");
                            return response;
                        }
                        else
                        {
                            response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            response.StatusCode = res.StatusCode;
                            return response;
                        }
                    }
                    catch (Exception innerEx)
                    {
                        logger.LogError($"Inner Exception for user {GlobalData.loggedInUser} in AddUpdateProductionDowntime: {innerEx.Message}");
                        logger.LogError($"Inner Exception StackTrace: {innerEx.StackTrace}");
                        response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(new { ResponseBody = innerEx.Message, ResponseStackTrace = innerEx.StackTrace });
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("productiondowntimereasonstatuschange"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Production = JsonConvert.DeserializeObject<ProductionDowntimeReasonMasterVm>(reqbody);
                    var pf = new ProductionFunctions(GlobalData);
                    HttpResponseData response;
                    try
                    {
                        var res = pf.ProductionDowntimeReasonStatusChange(Production);
                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            return response;
                        }
                        else
                        {
                            response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            response.StatusCode = res.StatusCode;
                            return response;
                        }
                    }
                    catch (Exception innerEx)
                    {
                        logger.LogError($"Inner Exception for user {GlobalData.loggedInUser} in ProductionDowntimeReasonStatusChange: {innerEx.Message}");
                        logger.LogError($"Inner Exception StackTrace: {innerEx.StackTrace}");
                        response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(new { ResponseBody = innerEx.Message, ResponseStackTrace = innerEx.StackTrace });
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                else if (entity.ToLowerInvariant().Equals("addupdateproductiondowntimeschedule"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Production = JsonConvert.DeserializeObject<ProductionDowntimeScheduledVm>(reqbody);
                    var pf = new ProductionFunctions(GlobalData);
                    HttpResponseData response;
                    try
                    {
                        var res = pf.AddUpdateProductionDowntimeSchedule(Production);
                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            return response;
                        }
                        else
                        {
                            response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            response.StatusCode = res.StatusCode;
                            return response;
                        }
                    }
                    catch (Exception innerEx)
                    {
                        logger.LogError($"Inner Exception for user {GlobalData.loggedInUser} in AddUpdateProductionDowntimeSchedule: {innerEx.Message}");
                        logger.LogError($"Inner Exception StackTrace: {innerEx.StackTrace}");
                        response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(new { ResponseBody = innerEx.Message, ResponseStackTrace = innerEx.StackTrace });
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

    }
}
