using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using System.Security.Claims;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using PmsBusiness;
using PmsEntity.ViewModel;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System;
using PmsCommon;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using PmsCore.Notifications.Interfaces;

namespace PmsAPI
{
    public class PmsIssueProductFunction
    {
        public static INotificationService notificationService;
        private readonly ILogger<PmsIssueProductFunction> _logger;
        private readonly GlobalDataEntity _globalData;
        public PmsIssueProductFunction(ILogger<PmsIssueProductFunction> logger, INotificationService _notificationService, GlobalDataEntity globalData)
        {
            _logger = logger;
            notificationService = _notificationService;
            _globalData = globalData;
        }


        [Function("PmsIssueProductFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsIssueProductFunction_dataGetItems", tags: new[] { "IssueProduct" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "issueproduct/{entity}")] HttpRequestData req, string entity, ClaimsPrincipal claimIdentity, ILogger log,
            FunctionContext executionContext)
        {

            var logger = executionContext.GetLogger("PmsIssueProductFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsIssueProductFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            // var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            _globalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            //logger.LogInformation("KeyURL :" + PmsCommon.KeyVault.KeyVaultUrl);
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getstockforsaleorderwithconsumptionstoreid"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string querysaleorderid = query.Get("saleorderid");
                    string queryconsumptionStoreId = query.Get("consumptionstoreid");

                    long saleorderid;
                    long consumptionStoreId;
                    if (long.TryParse(querysaleorderid, out saleorderid) && long.TryParse(queryconsumptionStoreId, out consumptionStoreId))
                    {
                        var res = new IssueProductFunctions(_globalData, notificationService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetStockForSaleOrderWithConsumptionstoreId(saleorderid, consumptionStoreId));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse values");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsIssueFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsIssueFunction_dataGetItemById", tags: new[] { "IssueProduct" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "issueproduct/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsIssueFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsSupplierFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            // var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            _globalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getstockforsaleorder"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new IssueProductFunctions(_globalData, notificationService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetStockForSaleOrder(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getstockforproduct"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new IssueProductFunctions(_globalData, notificationService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetStockForProduct(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getstockbyslipid"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new IssueProductFunctions(_globalData, notificationService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetProductBySlipId(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsIssueProductFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsIssueProductFunction_dataUpdateItems", tags: new[] { "IssueProduct" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "issueproduct/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsIssueProductFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            // var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            _globalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("issueproductrequest"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var IssueProduct = JsonConvert.DeserializeObject<List<IssueProductTableVm>>(reqbody);
                    var pf = new IssueProductFunctions(_globalData, notificationService);
                    var res = pf.IssueProductRequest(IssueProduct);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }

                if (entity.ToLowerInvariant().Equals("saleorderissueproductrequest"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var IssueProduct = JsonConvert.DeserializeObject<List<IssueProductTableVm>>(reqbody);
                    var pf = new IssueProductFunctions(_globalData, notificationService);
                    var res = pf.SaleOrderIssueProductRequest(IssueProduct);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("IssueProductRequest added successfully");
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("IssueProductRequest already added");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in IssueProductRequest");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("saleorderinspectionproductrequest"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var IssueProduct = JsonConvert.DeserializeObject<SaleOrderTableVm>(reqbody);
                    var pf = new IssueProductFunctions(_globalData, notificationService);
                    var res = pf.SaleOrderInspectionProductRequest(IssueProduct);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("cancelsaleorderinspectionproductrequest"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var IssueProduct = JsonConvert.DeserializeObject<InspectionCancellationTrackingTableVm>(reqbody);
                    var pf = new IssueProductFunctions(_globalData, notificationService);
                    var res = pf.CancelSaleOrderInspectionProductRequest(IssueProduct);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.Forbidden)
                    {
                        var response = req.CreateResponse(HttpStatusCode.Forbidden);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.Forbidden;
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("actionissueproductrequest"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var IssueProduct = JsonConvert.DeserializeObject<IssueProductActionVm>(reqbody);
                    var pf = new IssueProductFunctions(_globalData, notificationService);
                    var res = pf.ActionIssueProductRequest(IssueProduct);
                    try
                    {
                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            var response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            return response;
                        }
                        if (res.StatusCode == HttpStatusCode.BadRequest)
                        {
                            var response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            response.StatusCode = HttpStatusCode.BadRequest;
                            return response;
                        }
                        else
                        {
                            logger.LogError("Exception Message:" + res.ResponseBody);
                            var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                            await response.WriteAsJsonAsync("An error has occured. Please contact administrator.");
                            response.StatusCode = HttpStatusCode.InternalServerError;
                            return response;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator." + ex.Message);
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addissuesliprequest"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var IssueList = JsonConvert.DeserializeObject<List<IssueSlipRequestVm>>(reqbody);
                    var pf = new IssueProductFunctions(_globalData, notificationService);
                    var res = pf.AddIssueSlipRequest(IssueList);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("Issue slip generation failed.");
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals(("GetIssueProductRequests").ToLower()))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var recJI = JsonConvert.DeserializeObject<IssueFilterVm>(reqbody);
                        var res = new IssueProductFunctions(_globalData, notificationService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetIssueProductRequests(recJI));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                response.StatusCode = HttpStatusCode.InternalServerError;
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

    }
}
