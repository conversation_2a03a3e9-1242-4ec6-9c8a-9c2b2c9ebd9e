using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsProductCategoryFunction
    {
        [Function("PmsProductCategoryFunction_daProductCategoryetItems")]
        [OpenApiOperation(operationId: "PmsProductCategoryFunction_daProductCategoryetItems", tags: new[] { "ProductCategory" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "productcategory/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsProductCategoryFunction_daProductCategoryetItems");
            logger.LogInformation("C# HTTP PmsProductCategoryFunction_daProductCategoryetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            //logger.LogInformation("KeyURL :" + PmsCommon.KeyVault.KeyVaultUrl);
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallproductcategories"))
                {
                    var res = new ProductCategoryFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllProductCategories());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallproductcategoriesforlisting"))
                {
                    var res = new ProductCategoryFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllProductCategoriesForListing());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallproductfirstsubcategories"))
                {
                    var res = new ProductCategoryFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllProductFirstSubCategories());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallproductsecsubcategories"))
                {
                    var res = new ProductCategoryFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllProducSecSubCategories());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsProductCategoryFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsProductCategoryFunction_dataUpdateItems", tags: new[] { "ProductCategory" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "productcategory/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsProductCategoryFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addupdateproductcategory"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var ProductCategory = JsonConvert.DeserializeObject<ProductCategoryMasterVm>(reqbody);
                    var pf = new ProductCategoryFunctions(GlobalData);
                    var res = pf.AddUpdateProductCategory(ProductCategory);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred while added product category");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addupdateproductfirstsubcategory"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var ProductCategory = JsonConvert.DeserializeObject<ProductFirstSubCategoryMasterVm>(reqbody);
                    var pf = new ProductCategoryFunctions(GlobalData);
                    var res = pf.AddUpdateProductFirstSubCategory(ProductCategory);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("ProductCategory added successfully");
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("ProductCategory already added");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateProductCategory");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addupdateproductsecsubcategory"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var ProductCategory = JsonConvert.DeserializeObject<ProductSecSubCategoryMasterVm>(reqbody);
                    var pf = new ProductCategoryFunctions(GlobalData);
                    var res = pf.AddUpdateProductSecSubCategory(ProductCategory);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("ProductCategory added successfully");
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("ProductCategory already added");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateProductCategory");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("deleteproductcategory"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var ProductCategory = JsonConvert.DeserializeObject<ProductCategoryMasterVm>(reqbody);
                    var pf = new ProductCategoryFunctions(GlobalData);
                    var res = pf.DeleteProductCategory(ProductCategory.ProductCategoryId);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in deleting Product Category.");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("deleteproductfirstsubcategory"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var ProductCategory = JsonConvert.DeserializeObject<ProductFirstSubCategoryMasterVm>(reqbody);
                    var pf = new ProductCategoryFunctions(GlobalData);
                    var res = pf.DeleteProductFirstSubCategory(ProductCategory.ProductFirstSubCategoryId);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in deleting Product First Sub Category.");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("deleteproductsecsubcategory"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var ProductCategory = JsonConvert.DeserializeObject<ProductSecSubCategoryMasterVm>(reqbody);
                    var pf = new ProductCategoryFunctions(GlobalData);
                    var res = pf.DeleteProductSecSubCategory(ProductCategory.ProductSecSubCategoryId);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in deleting Product Second Sub Category.");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator. " + ex);
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

    }
}
