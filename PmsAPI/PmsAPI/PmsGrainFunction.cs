using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsGrainFunction
    {
        [Function("PmsGrainFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsGrainFunction_dataGetItems", tags: new[] { "Grain" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "grain/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsGrainFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsGrainFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            //logger.LogInformation("KeyURL :" + PmsCommon.KeyVault.KeyVaultUrl);
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallgrains"))
                {
                    var res = new GrainFunctions();
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllGrains());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsGrainFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsGrainFunction_dataUpdateItems", tags: new[] { "Grain" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "grain/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsGrainFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addupdategrain"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Grain = JsonConvert.DeserializeObject<GrainMasterVm>(reqbody);
                    Grain.GrainAddedBy = GlobalData.loggedInUser;
                    var pf = new GrainFunctions();
                    var res = pf.AddUpdateGrain(Grain);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Grain added successfully");
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Department already added");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateGrain");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("deletegrain"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Grain = JsonConvert.DeserializeObject<GrainMasterVm>(reqbody);
                    Grain.DisabledBy = GlobalData.loggedInUser;
                    var pf = new GrainFunctions();
                    var res = pf.DeleteGrain(Grain);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Grain deleted successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

    }
}
