using System;
using System.Net;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using PmsBusiness;
using PmsEntity.ViewModel;
using PmsCommon;

namespace PmsAPI
{
    public class PmsSchedulerFunction_YieldReport
    {
        private readonly ILogger _logger;
        public GlobalDataEntity GlobalData;

        public PmsSchedulerFunction_YieldReport(ILoggerFactory loggerFactory)
        {
            _logger = loggerFactory.CreateLogger<PmsSchedulerFunction_YieldReport>();
        }

        [Function("PmsSchedulerFunction_YieldReport")]
        public void Run([TimerTrigger("0 30 16 * * *")] SchedulerVm myTimer)
        {
            try
            {
                _logger.LogInformation($"C# Timer trigger function executed at: {DateTime.Now}");
                _logger.LogInformation($"Next timer schedule at: {myTimer.ScheduleStatus.Next}");
                SchedulerFunctions sfn = new SchedulerFunctions(GlobalData);
                sfn.SendYieldReportData();
            }
            catch (Exception ex)
            {
                _logger.LogError("Exception Message:" + ex.Message);
                _logger.LogError("Exception StackTrace:" + ex.StackTrace);
                _logger.LogError("Exception InnerException:" + ex.InnerException);
            }
        }
    }
}
