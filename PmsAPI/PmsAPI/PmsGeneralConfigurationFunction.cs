using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public class PmsGeneralConfigurationFunction
    {
        [Function("PmsGeneralConfigurationFunction_dataAddItems")]
        [OpenApiOperation(operationId: "PmsGeneralConfigurationFunction_dataAddItems", tags: new[] { "GeneralConfiguration" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> AddItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "generalconfig/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsGeneralConfigurationFunction_dataAddItems");
            logger.LogInformation("C# HTTP PmsGeneralConfigurationFunction_dataAddItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

            if (entity == null)
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Not all required parameters provided");
                return response;
            }
            if (entity.ToLowerInvariant().Equals("addconfig"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Config = JsonConvert.DeserializeObject<ConfigTableVm>(reqbody);

                    var pf = new GeneralConfigurationFunction(GlobalData);
                    var res = pf.AddConfig(Config);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("getconfigbyconfigitem"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Config = JsonConvert.DeserializeObject<ConfigTableVm>(reqbody);
                    var pf = new GeneralConfigurationFunction(GlobalData);
                    var res = pf.GetConfigByConfigItem(Config.ConfigItem);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("editconfig"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Config = JsonConvert.DeserializeObject<ConfigTableVm>(reqbody);

                    var pf = new GeneralConfigurationFunction(GlobalData);
                    var res = pf.EditConfig(Config);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            else
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Entity not found");
                return response;
            }

        }

        [Function("PmsGeneralConfigurationFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsGeneralConfigurationFunction_dataGetItems", tags: new[] { "GeneralConfiguration" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItem([HttpTrigger(AuthorizationLevel.Function, "get", Route = "generalconfig/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsGeneralConfigurationFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsGeneralConfigurationFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            GlobalData.loggedInUserName = tokenS.Claims.First(claim => claim.Type == "name").Value;

            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getconfig"))
                {
                    var res = new GeneralConfigurationFunction(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetConfig());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex) 
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }
    }
}
