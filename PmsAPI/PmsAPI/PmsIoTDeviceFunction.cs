using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsIoTDeviceFunction
    {
        [Function("PmsIoTDeviceFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsIoTDeviceFunction_dataGetItems", tags: new[] { "IoT" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "iot/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsIoTDeviceFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsIoTDeviceFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            //logger.LogInformation("KeyURL :" + PmsCommon.KeyVault.KeyVaultUrl);
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallknittingfabricstocks"))
                {
                    var res = new IoTDeviceFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllKnittingFabricStocks());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsIoTDeviceFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsIoTDeviceFunction_dataUpdateItems", tags: new[] { "IoT" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "iot/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsIoTDeviceFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsIoTDeviceFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addknittingfabricwithweight"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var item = JsonConvert.DeserializeObject<KnittingFabricWeightInputTableVm>(reqbody);
                    var pf = new IoTDeviceFunctions(GlobalData);
                    var res = pf.AddKnittingFabricWithWeight(item);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.InternalServerError;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("deleteknittingfabricrecord"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var recordId = JsonConvert.DeserializeObject<long>(reqbody);
                    var pf = new IoTDeviceFunctions(GlobalData);
                    var res = pf.DeleteKnittingFabricRecord(recordId);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getknittingfabricstockswithfilter"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var filter = JsonConvert.DeserializeObject<KnittingFabricWeightInputTableFilterVm>(reqbody);
                        var res = new IoTDeviceFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllKnittingFabricStocksWithFilter(filter));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getknittingfabricstocksbyids"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var fabricStockIds = JsonConvert.DeserializeObject<List<long>>(reqbody);
                        var res = new IoTDeviceFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetKnittingFabricStocksByIds(fabricStockIds));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

    }
}
