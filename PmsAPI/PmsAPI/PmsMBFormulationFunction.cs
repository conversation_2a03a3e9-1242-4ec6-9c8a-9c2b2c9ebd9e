using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsData.ViewModel;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsMBFormulationFunction
    {
        [Function("PmsMBFormulationFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsMBFormulationFunction_dataGetItems", tags: new[] { "MBFormulation" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "mbformulation/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsMBFormulationFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsMBFormulationFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallmbformulations"))
                {
                    var res = new MBFormulationFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllMBFormulationMaster());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsMBFormulationFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsMBFormulationFunction_dataUpdateItems", tags: new[] { "MBFormulation" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "mbformulation/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsMBFormulationFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addmbformulationmaster"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var MBFormulation = JsonConvert.DeserializeObject<MbformulationMasterVm>(reqbody);
                    var pf = new MBFormulationFunctions(GlobalData);
                    var res = pf.AddMBFormulationMaster(MBFormulation);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("MBFormulation added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in Add MBFormulation");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addmbformulationproduct"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var MBFormulation = JsonConvert.DeserializeObject<MbformulationProductTableVm>(reqbody);
                    var pf = new MBFormulationFunctions(GlobalData);
                    var res = pf.AddMBFormulationProduct(MBFormulation);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("updatembformulationmaster"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var MBFormulation = JsonConvert.DeserializeObject<MbformulationProductTableVm>(reqbody);
                    var pf = new MBFormulationFunctions(GlobalData);
                    var res = pf.UpdateMBFormulationMaster(MBFormulation);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("MBFormulation added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in Add MBFormulation");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator. " + ex);
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

        [Function("PmsMBFormulationFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsMBFormulationFunction_dataGetItemById", tags: new[] { "MBFormulationProduct" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "mbformulationproduct/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsMBFormulationFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsMBFormulationFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }


                if (entity.ToLowerInvariant().Equals(("GetMBFormulationProductById").ToLower()))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new MBFormulationFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetMBFormulationProductById(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }
    }
}
