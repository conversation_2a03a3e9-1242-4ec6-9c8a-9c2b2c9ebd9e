﻿using System;
using Microsoft.AspNetCore.Routing;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace PmsAPI
{
    public class PmsOverHeadFunction
    {
        [Function("PmsOverHeadFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsOverHeadFunction_dataGetItems", tags: new[] { "overhead" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "overhead/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsOverHeadFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsOverHeadFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getoverheadcoloumnlist"))
                {
                    var pf = new OverHeadFunction(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(pf.GetOverHeadColoumnList());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getalloverheadcost"))
                {
                    var pf = new OverHeadFunction(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(pf.GetAllOverheadCost());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }
        [Function("PmsOverHeadFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsOverHeadFunction_dataUpdateItems", tags: new[] { "overhead" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "overhead/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsOverHeadFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsOverHeadFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addoverheadcolumn"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var overHeadMaster = JsonConvert.DeserializeObject<OverheadColumnMasterVm>(reqbody);
                    var pf = new OverHeadFunction(GlobalData);
                    var res = pf.AddOverHeadColumn(overHeadMaster);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("OverHeadColoumn added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in OverHeadColoumn");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addoverheadcost"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var overHeadCosts = JsonConvert.DeserializeObject<List<OverheadCostVm>>(reqbody);
                    var pf = new OverHeadFunction(GlobalData);
                    var res = pf.AddOverHeadCost(overHeadCosts);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("OverHeadCosts added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in OverHeadCosts");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addupdateoverheadcost"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var overHeadCosts = JsonConvert.DeserializeObject<OverheadCostingTableVm>(reqbody);
                    var pf = new OverHeadFunction(GlobalData);
                    var res = pf.AddUpdateOverheadCost(overHeadCosts);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        await response.WriteAsJsonAsync(res.ResponseBody, HttpStatusCode.BadRequest);
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    response.StatusCode = HttpStatusCode.BadRequest;
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsOverHeadFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsOverHeadFunction_dataGetItemById", tags: new[] { "overhead" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "overhead/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsOverHeadFunction_dataGetItemById");
            logger.LogInformation("C# HTTP PmsOverHeadFunction_dataGetItemById processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                // if (entity.ToLowerInvariant().Equals("getcustomeroverheadcosts"))
                // {
                //     long cid;
                //     if (long.TryParse(id, out cid))
                //     {
                //         var res = new OverHeadFunction(GlobalData);
                //         var response = req.CreateResponse(HttpStatusCode.OK);
                //         await response.WriteAsJsonAsync(res.GetCustomerOverheadCosts(cid));
                //         return response;
                //     }
                //     else
                //     {
                //         var response = req.CreateResponse(HttpStatusCode.BadRequest);
                //         await response.WriteAsJsonAsync($"Unable to parse id {id}");
                //         return response;
                //     }

                // }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }
    }
}



