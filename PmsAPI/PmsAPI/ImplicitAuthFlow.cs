using System;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Configurations;
using Microsoft.OpenApi.Models;


namespace PmsAPI

{
    public class ImplicitAuthFlow : OpenApiOAuthSecurityFlows
    {
        private const string AuthorisationUrl =
            "https://login.microsoftonline.com/{0}/oauth2/v2.0/authorize";
        private const string RefreshUrl =
            "https://login.microsoftonline.com/{0}/oauth2/v2.0/token";

        public ImplicitAuthFlow()
        {
            //var tenantId = Environment.GetEnvironmentVariable("OpenApi__Auth__TenantId");
            var tenantId = "b41089a7-f3f9-49bb-b918-07be8606e9ed";

            this.Implicit = new OpenApiOAuthFlow()
            {
                AuthorizationUrl = new Uri(string.Format(AuthorisationUrl, tenantId)),
                RefreshUrl = new Uri(string.Format(RefreshUrl, tenantId)),

                Scopes = { { "https://graph.microsoft.com/.default", "openid" } }

            };
        }
    }
}