using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

using PmsCore.Notifications.Interfaces;
using PmsData.Models;
using PmsEntity.ViewModel;
using PmsCommon;
using PmsCommon.Services.Scheduling.Interfaces;

namespace PmsAPI.PmsAPI
{
    public class ReportSchedulerFunction
    {
        private readonly ILogger _logger;
        private readonly INotificationService _notificationService;
        private readonly pmsdbContext _dbContext;
        private readonly ISchedulingService _schedulingService;

        public ReportSchedulerFunction(
            ILoggerFactory loggerFactory,
            INotificationService notificationService,
            pmsdbContext dbContext,
            ISchedulingService schedulingService)
        {
            _logger = loggerFactory.CreateLogger<ReportSchedulerFunction>();
            _notificationService = notificationService;
            _dbContext = dbContext;
            _schedulingService = schedulingService;
        }

        [Function("ReportSchedulerFunction")]
        public async Task Run([TimerTrigger("0 */5 * * * *")] SchedulerVm myTimer)
        {
            try
            {
                _logger.LogInformation("Report scheduler executing at: {time}", DateTime.Now);

                // Process scheduled notifications (including YieldReport)
                await ProcessScheduledNotifications();

                // Process scheduled overdue returnable out pass reminders
                await _notificationService.SendScheduledOverdueReturnableOutPassReminders();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Report scheduler failed");
                throw;
            }
        }

        private async Task ProcessScheduledNotifications()
        {
            try
            {
                _logger.LogInformation("Processing scheduled notifications with recipient-specific scheduling");

                // Get all scheduled report configurations (including YieldReportSummary)
                var schedules = await _dbContext.NotificationReportScheduleMappingTables
                    .Where(s => s.IsActive && !s.Disabled && s.ReportType == "Scheduled")
                    .ToListAsync();

                _logger.LogInformation("Found {ScheduleCount} active scheduled report configurations", schedules.Count);

                foreach (var schedule in schedules)
                {
                    try
                    {
                        _logger.LogDebug("Processing schedule {ScheduleId} for report {ReportName} and recipient {RecipientId}: CRON='{CronExpression}', TimeZone={TimeZone}",
                            schedule.ReportId, schedule.ReportName, schedule.NotificationGroupUserId, schedule.CronExpression, schedule.TimeZone);

                        // Initialize NextRunTime for new schedules
                        if (!schedule.NextRunTime.HasValue)
                        {
                            var nextRunTime = _schedulingService.CalculateNextRunTime(schedule.CronExpression, schedule.TimeZone, DateTime.UtcNow);
                            if (nextRunTime.HasValue)
                            {
                                schedule.NextRunTime = nextRunTime.Value;
                                _logger.LogInformation("Initialized NextRunTime for new schedule {ScheduleId} ({ReportName}) to {NextRunTime} UTC",
                                    schedule.ReportId, schedule.ReportName, schedule.NextRunTime);
                            }
                            continue; // Skip execution this time, just initialize
                        }

                        // Use the shared scheduling service to check if it's time to send
                        var isTimeToSend = _schedulingService.IsTimeToSendNotification(
                            schedule.CronExpression,
                            schedule.TimeZone,
                            schedule.LastRunTime,
                            DateTime.UtcNow,
                            10, // 10-minute execution window
                            schedule.ReportId
                        );

                        if (isTimeToSend)
                        {
                            _logger.LogInformation("Executing scheduled report {ReportName} (ID: {ReportId}) for recipient {RecipientId} - within execution window",
                                schedule.ReportName, schedule.ReportId, schedule.NotificationGroupUserId);

                            // Send the scheduled report to the specific recipient
                            if (schedule.NotificationGroupUserId.HasValue)
                            {
                                // Use the consolidated methods with recipientId parameter for direct calls
                                if (schedule.ReportName.Equals("LowStockReport", StringComparison.OrdinalIgnoreCase))
                                {
                                    await _notificationService.SendLowStockReportNotification("Scheduled", schedule.NotificationGroupUserId.Value);
                                }
                                else if (schedule.ReportName.Equals("ReturnableOutPassReport", StringComparison.OrdinalIgnoreCase))
                                {
                                    await _notificationService.SendPendingReturnableOutPassReportNotification("Scheduled", schedule.NotificationGroupUserId.Value);
                                }
                                else if (schedule.ReportName.Equals("YieldReportSummary", StringComparison.OrdinalIgnoreCase))
                                {
                                    // Calculate the date range for YieldReport (24-hour period from previous day 8:00 AM IST to current day 8:00 AM IST)
                                    var currentTimeIST = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);
                                    var currentDay8AMIST = currentTimeIST.Date.AddHours(8);
                                    var previousDay8AMIST = currentDay8AMIST.AddDays(-1);
                                    var fromDate = TimeZoneHelper.ConvertToUtc(previousDay8AMIST, TimeZoneId.IndiaStandardTime);
                                    var toDate = TimeZoneHelper.ConvertToUtc(currentDay8AMIST, TimeZoneId.IndiaStandardTime);

                                    _logger.LogInformation("YieldReport data period: {FromDateIST} to {ToDateIST} (IST), {FromDateUTC} to {ToDateUTC} (UTC)",
                                        previousDay8AMIST.ToString("dd-MMM-yyyy hh:mm tt"),
                                        currentDay8AMIST.ToString("dd-MMM-yyyy hh:mm tt"),
                                        fromDate.ToString("dd-MMM-yyyy HH:mm"),
                                        toDate.ToString("dd-MMM-yyyy HH:mm"));

                                    await _notificationService.SendYieldReportSummaryWhatsApp(fromDate, toDate, "Scheduled", schedule.NotificationGroupUserId.Value);
                                }
                                else if (schedule.ReportName.Equals("ExecutiveCostingReport", StringComparison.OrdinalIgnoreCase))
                                {
                                    // Calculate the date range for YieldReport (24-hour period from previous day 8:00 AM IST to current day 8:00 AM IST)
                                    var currentTimeIST = TimeZoneHelper.ConvertToTimeZone(DateTime.UtcNow, TimeZoneId.IndiaStandardTime);
                                    var currentDay10AMIST = currentTimeIST.Date.AddHours(10);
                                    var previousDay10AMIST = currentDay10AMIST.AddDays(-1);
                                    var fromDate = TimeZoneHelper.ConvertToUtc(previousDay10AMIST, TimeZoneId.IndiaStandardTime);
                                    var toDate = TimeZoneHelper.ConvertToUtc(currentDay10AMIST, TimeZoneId.IndiaStandardTime);

                                    _logger.LogInformation("YieldReport data period: {FromDateIST} to {ToDateIST} (IST), {FromDateUTC} to {ToDateUTC} (UTC)",
                                        previousDay10AMIST.ToString("dd-MMM-yyyy hh:mm tt"),
                                        currentDay10AMIST.ToString("dd-MMM-yyyy hh:mm tt"),
                                        fromDate.ToString("dd-MMM-yyyy HH:mm"),
                                        toDate.ToString("dd-MMM-yyyy HH:mm"));

                                    await _notificationService.SendCostingReportPDFNotification(fromDate, toDate, "Scheduled", schedule.NotificationGroupUserId.Value);
                                }
                                if (schedule.ReportName.Equals("OverheadCostUpdateReminder", StringComparison.OrdinalIgnoreCase))
                                {
                                    await _notificationService.SendScheduledOverheadCostUpdateReminders("Scheduled");
                                }
                                else
                                {
                                    await _notificationService.SendScheduledReportToRecipient(
                                        schedule.ReportName,
                                        schedule.NotificationGroupUserId.Value,
                                        "Scheduled"
                                    );
                                }

                                // Update the schedule directly
                                schedule.LastRunTime = DateTime.UtcNow;

                                // Calculate next run time
                                var nextRunTime = _schedulingService.CalculateNextRunTime(schedule.CronExpression, schedule.TimeZone, DateTime.UtcNow);
                                if (nextRunTime.HasValue)
                                {
                                    schedule.NextRunTime = nextRunTime.Value;
                                    _logger.LogInformation("Next run for report {ReportName} (recipient {RecipientId}) scheduled for {NextRunTime} UTC",
                                        schedule.ReportName, schedule.NotificationGroupUserId, schedule.NextRunTime);
                                }
                            }
                            else
                            {
                                _logger.LogWarning("Schedule {ScheduleId} for report {ReportName} has no recipient assigned",
                                    schedule.ReportId, schedule.ReportName);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process schedule {ScheduleId} for report {ReportName} and recipient {RecipientId}",
                            schedule.ReportId, schedule.ReportName, schedule.NotificationGroupUserId);
                    }
                }

                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("Completed processing scheduled notifications");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process scheduled notifications");
                throw;
            }
        }


    }
}