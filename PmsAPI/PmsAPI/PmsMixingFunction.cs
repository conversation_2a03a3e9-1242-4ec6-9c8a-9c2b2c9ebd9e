using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsMixingFunction
    {
        [Function("PmsMixingFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsMixingFunction_dataGetItems", tags: new[] { "Mixing" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "mixing/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsMixingFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsMixingFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallmixings"))
                {
                    var res = new MixingFunctions();
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllMixings());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsMixingFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsMixingFunction_dataUpdateItems", tags: new[] { "Mixing" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "mixing/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsMixingFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addmixing"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Mixing = JsonConvert.DeserializeObject<MixingMasterVm>(reqbody);
                    Mixing.AddedBy = GlobalData.loggedInUser;
                    var pf = new MixingFunctions();
                    var res = pf.AddMixings(Mixing);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Mixing added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in Add Mixing");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("updatemixing"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Mixing = JsonConvert.DeserializeObject<MixingMasterVm>(reqbody);
                    Mixing.AddedBy = GlobalData.loggedInUser;
                    var pf = new MixingFunctions();
                    var res = pf.UpdateMixings(Mixing);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Mixing added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in Add Mixing");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("deletemixing"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var Mixing = JsonConvert.DeserializeObject<MixingMasterVm>(reqbody);
                    Mixing.DisabledBy = GlobalData.loggedInUser;
                    var pf = new MixingFunctions();
                    var res = pf.DeleteMixing(Mixing);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Mixing deleted successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

    }
}
