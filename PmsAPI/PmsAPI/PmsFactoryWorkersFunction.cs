using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsFactoryWorkersFunction
    {
        [Function("PmsFactoryWorkersFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsFactoryWorkersFunction_dataUpdateItems", tags: new[] { "FactoryWorkers" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "factoryworkers/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsFactoryWorkersFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsFactoryWorkersFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallfactoryworkerswithfilters"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var request = JsonConvert.DeserializeObject<FactoryWorkersRequestVm>(reqbody);
                        var res = new FactoryWorkersFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllFactoryWorkersWithFilters(request));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addupdatefactoryworker"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var factoryworker = JsonConvert.DeserializeObject<FactoryWorkersMasterVm>(reqbody);
                    var pf = new FactoryWorkersFunctions(GlobalData);
                    var res = pf.AddUpdateFactoryWorker(factoryworker);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Factory Worker Added Successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in adding or update Factory Worker");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("deletefactoryworker"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var factoryworker = JsonConvert.DeserializeObject<FactoryWorkersMasterVm>(reqbody);
                    var pf = new FactoryWorkersFunctions(GlobalData);
                    var res = pf.DeleteFactoryWorker(factoryworker.WorkerId);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

    }
}
