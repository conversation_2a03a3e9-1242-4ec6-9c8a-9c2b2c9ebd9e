using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Azure.Core;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsUserRolesResponsibilityFunction
    {
        [Function("PmsUserRolesResponsibilityFunction_daUserRolesResponsibilityetItems")]
        [OpenApiOperation(operationId: "PmsUserRolesResponsibilityFunction_daUserRolesResponsibilityetItems", tags: new[] { "UserRolesResponsibility" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "userrolesresponsibility/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsUserRolesResponsibilityFunction_daUserRolesResponsibilityetItems");
            logger.LogInformation("C# HTTP PmsUserRolesResponsibilityFunction_daUserRolesResponsibilityetItems processed a request.");

            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallresponsibility"))
                {
                    var res = new UserRolesResponsibilityFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllResponsibility());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallresponsibilitywithroles"))
                {
                    var res = new UserRolesResponsibilityFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllResponsibilityWithRoles());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getalluserroles"))
                {
                    var res = new UserRolesResponsibilityFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllUserRoles());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getalluserdata"))
                {
                    var res = new UserRolesResponsibilityFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllUserData());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getalluserexceptionforcelogout"))
                {
                    var res = new UserRolesResponsibilityFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllUserExceptionForceLogout());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsUserRolesResponsibilityFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsUserRolesResponsibilityFunction_dataUpdateItems", tags: new[] { "UserRolesResponsibility" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "userrolesresponsibility/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsUserRolesResponsibilityFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsUserRolesResponsibilityFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("addupdateresponsibility"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var reqdata = JsonConvert.DeserializeObject<ResponsibilityMasterVm>(reqbody);
                    var pf = new UserRolesResponsibilityFunctions(GlobalData);
                    var res = pf.AddUpdateResponsibility(reqdata);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody, res.StatusCode);
                        return response;
                    }
                    else if (res.StatusCode == HttpStatusCode.Unauthorized)
                    {
                        var response = req.CreateResponse(HttpStatusCode.Unauthorized);
                        await response.WriteAsJsonAsync(res.ResponseBody, res.StatusCode);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addupdateuserrole"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var reqdata = JsonConvert.DeserializeObject<UserRoleMasterVm>(reqbody);
                    var pf = new UserRolesResponsibilityFunctions(GlobalData);
                    var res = pf.AddUpdateUserRole(reqdata);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateUserRolesResponsibility");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("adduser"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var reqdata = JsonConvert.DeserializeObject<UserMasterVm>(reqbody);
                    var pf = new UserRolesResponsibilityFunctions(GlobalData);
                    var res = pf.AddUser(reqdata);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateUserRolesResponsibility");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("addupdateroleresposibilitymapping"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var reqdata = JsonConvert.DeserializeObject<List<UserRoleResponsibilityMappingTableVm>>(reqbody);
                    var pf = new UserRolesResponsibilityFunctions(GlobalData);
                    var res = pf.AddUpdateRoleResposibilityMapping(reqdata);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateUserRolesResponsibility");
                        return response;
                    }
                }

                if (entity.ToLowerInvariant().Equals("addupdateusernameuserrolemapping"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var reqdata = JsonConvert.DeserializeObject<List<UsernameUserRoleMappingTableVm>>(reqbody);
                    var pf = new UserRolesResponsibilityFunctions(GlobalData);
                    var res = pf.AddUpdateUsernameUserRoleMapping(reqdata);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else if (res.StatusCode == HttpStatusCode.Forbidden)
                    {
                        var response = req.CreateResponse(HttpStatusCode.Forbidden);
                        await response.WriteAsJsonAsync(new ApiResult { Code = EMessageCode.Forbid, Succeeded = false, Message = $"{res.ResponseBody}" });
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdateUserRolesResponsibility");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                response.StatusCode = HttpStatusCode.InternalServerError;
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsUserRolesResponsibilityFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsUserRolesResponsibilityFunction_dataGetItemById", tags: new[] { "UserRolesResponsibility" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "userrolesresponsibility/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsUserRolesResponsibilityFunction_dataGetItemById");
            logger.LogInformation("C# HTTP PmsUserRolesResponsibilityFunction_dataGetItemById processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getalluserrolesbyusername"))
                {
                    if (id != null)
                    {
                        var res = new UserRolesResponsibilityFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllUserRolesByUserName(id));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("RemoveUser".ToLower()))
                {
                    if (id != null)
                    {
                        var res = new UserRolesResponsibilityFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.RemoveUser(id));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals(("GetAllUsersByResposibilityId").ToLower()))
                {
                    long respid;
                    if (long.TryParse(id, out respid))
                    {
                        var res = new UserRolesResponsibilityFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllUsersByResposibilityId(respid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                response.StatusCode = HttpStatusCode.InternalServerError;
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

    }
}
