using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsReportingAPI
{
    /// <summary>
    /// Dashboard Configuration API Functions
    /// Following PMS Azure Function standard pattern with internal routing
    /// </summary>
    public static class PmsDashboardConfigFunction
    {
        /// <summary>
        /// Dashboard Configuration GET operations with entity-based routing
        /// </summary>
        [Function("PmsDashboardConfigFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsDashboardConfigFunction_dataGetItems", tags: new[] { "DashboardConfig" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "dashboardconfig/{entity}")] HttpRequestData req,
            string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsDashboardConfigFunction_dataGetItems");
            logger.LogInformation($"PmsDashboardConfigFunction_dataGetItems processed a request for entity: {entity}");

            try
            {
                // Extract JWT token and user information (following PMS pattern)
                var GlobalData = ExtractUserFromJWT(req);

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                // Route based on entity parameter
                if (entity.ToLowerInvariant().Equals("getuserconfig"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string dashboardType = query.Get("dashboardType");

                    if (string.IsNullOrEmpty(dashboardType))
                    {
                        var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                        await badResponse.WriteAsJsonAsync("Dashboard type is required");
                        return badResponse;
                    }

                    var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                    var result = dashboardConfigFunctions.GetUserDashboardConfig(GlobalData.loggedInUser, dashboardType);

                    var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                    await httpResponse.WriteAsJsonAsync(result);
                    return httpResponse;
                }
                else if (entity.ToLowerInvariant().Equals("getallconfigs"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string dashboardType = query.Get("dashboardType");

                    if (string.IsNullOrEmpty(dashboardType))
                    {
                        var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                        await badResponse.WriteAsJsonAsync("Dashboard type is required");
                        return badResponse;
                    }

                    var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                    var result = dashboardConfigFunctions.GetAllUserDashboardConfigs(GlobalData.loggedInUser, dashboardType);

                    var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                    await httpResponse.WriteAsJsonAsync(result);
                    return httpResponse;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                return await HandleError(req, ex, logger);
            }
        }

        /// <summary>
        /// Dashboard Configuration POST/PUT operations with entity-based routing
        /// </summary>
        [Function("PmsDashboardConfigFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsDashboardConfigFunction_dataUpdateItems", tags: new[] { "DashboardConfig" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItems(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "dashboardconfig/{entity}")] HttpRequestData req,
            string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsDashboardConfigFunction_dataUpdateItems");
            logger.LogInformation($"PmsDashboardConfigFunction_dataUpdateItems processed a request for entity: {entity}");

            try
            {
                // Extract JWT token and user information (following PMS pattern)
                var GlobalData = ExtractUserFromJWT(req);

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                // Route based on entity parameter
                if (entity.ToLowerInvariant().Equals("saveconfig"))
                {
                    // Read request body
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                    var request = JsonConvert.DeserializeObject<SaveDashboardConfigRequest>(requestBody);

                    if (request == null)
                    {
                        var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                        await badResponse.WriteAsJsonAsync("Invalid request body");
                        return badResponse;
                    }

                    var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                    var result = dashboardConfigFunctions.SaveUserDashboardConfig(request, GlobalData.loggedInUser);

                    var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                    await httpResponse.WriteAsJsonAsync(result);
                    return httpResponse;
                }
                else if (entity.ToLowerInvariant().Equals("updateconfig"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string configIdStr = query.Get("configId");

                    if (!long.TryParse(configIdStr, out long configId))
                    {
                        var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                        await badResponse.WriteAsJsonAsync("Invalid config ID");
                        return badResponse;
                    }

                    // Read request body
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                    var request = JsonConvert.DeserializeObject<UpdateDashboardConfigRequest>(requestBody);

                    if (request == null)
                    {
                        var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                        await badResponse.WriteAsJsonAsync("Invalid request body");
                        return badResponse;
                    }

                    // Set the config ID from query parameter
                    request.ConfigId = configId;

                    var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                    var result = dashboardConfigFunctions.UpdateUserDashboardConfig(request, GlobalData.loggedInUser);

                    var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                    await httpResponse.WriteAsJsonAsync(result);
                    return httpResponse;
                }
                else if (entity.ToLowerInvariant().Equals("deleteconfig"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string configIdStr = query.Get("configId");

                    if (!long.TryParse(configIdStr, out long configId))
                    {
                        var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                        await badResponse.WriteAsJsonAsync("Invalid config ID");
                        return badResponse;
                    }

                    var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                    var result = dashboardConfigFunctions.DeleteUserDashboardConfig(configId, GlobalData.loggedInUser);

                    var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                    await httpResponse.WriteAsJsonAsync(result);
                    return httpResponse;
                }
                else if (entity.ToLowerInvariant().Equals("setdefault"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string configIdStr = query.Get("configId");
                    string dashboardType = query.Get("dashboardType");

                    if (!long.TryParse(configIdStr, out long configId) || string.IsNullOrEmpty(dashboardType))
                    {
                        var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                        await badResponse.WriteAsJsonAsync("Invalid config ID or dashboard type");
                        return badResponse;
                    }

                    var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                    var result = dashboardConfigFunctions.SetDefaultConfig(configId, GlobalData.loggedInUser, dashboardType);

                    var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                    await httpResponse.WriteAsJsonAsync(result);
                    return httpResponse;
                }
                else if (entity.ToLowerInvariant().Equals("resettodefault"))
                {
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string dashboardType = query.Get("dashboardType");

                    if (string.IsNullOrEmpty(dashboardType))
                    {
                        var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                        await badResponse.WriteAsJsonAsync("Dashboard type is required");
                        return badResponse;
                    }

                    var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                    var result = dashboardConfigFunctions.ResetToSystemDefault(GlobalData.loggedInUser, dashboardType);

                    var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                    await httpResponse.WriteAsJsonAsync(result);
                    return httpResponse;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                return await HandleError(req, ex, logger);
            }
        }

        /// <summary>
        /// Extract user information from JWT token (following PMS pattern)
        /// </summary>
        private static GlobalDataEntity ExtractUserFromJWT(HttpRequestData req)
        {
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity();
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            return GlobalData;
        }

        /// <summary>
        /// Handle errors consistently (following PMS pattern)
        /// </summary>
        private static async Task<HttpResponseData> HandleError(HttpRequestData req, Exception ex, ILogger logger)
        {
            logger.LogError("Exception Message:" + ex.Message);
            logger.LogError("Exception StackTrace:" + ex.StackTrace);
            logger.LogError("Exception InnerException:" + ex.InnerException);
            var response = req.CreateResponse(HttpStatusCode.InternalServerError);
            await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
            return response;
        }
    }
}
