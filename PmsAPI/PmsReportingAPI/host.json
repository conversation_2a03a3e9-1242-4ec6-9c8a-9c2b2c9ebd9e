{"version": "2.0", "functionTimeout": "00:05:00", "healthMonitor": {"enabled": true, "healthCheckInterval": "00:00:10", "healthCheckWindow": "00:02:00", "healthCheckThreshold": 6, "counterThreshold": 0.8}, "logging": {"fileLoggingMode": "debugOnly", "logLevel": {"default": "Information"}, "applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 100, "evaluationInterval": "01:00:00", "initialSamplingPercentage": 100.0, "samplingPercentageIncreaseTimeout": "00:00:01", "samplingPercentageDecreaseTimeout": "00:00:01", "minSamplingPercentage": 0.1, "maxSamplingPercentage": 100.0, "movingAverageRatio": 1.0}, "enableLiveMetrics": true, "enableDependencyTracking": true, "enablePerformanceCountersCollection": true, "httpAutoCollectionOptions": {"enableHttpTriggerExtendedInfoCollection": true, "enableW3CDistributedTracing": true, "enableResponseHeaderInjection": true}, "snapshotConfiguration": {"agentEndpoint": null, "captureSnapshotMemoryWeight": 0.5, "failedRequestLimit": 3, "handleUntrackedExceptions": true, "isEnabled": true, "isEnabledInDeveloperMode": false, "isEnabledWhenProfiling": true, "isExceptionSnappointsEnabled": false, "isLowPrioritySnapshotUploader": true, "maximumCollectionPlanSize": 50, "maximumSnapshotsRequired": 3, "problemCounterResetInterval": "24:00:00", "provideAnonymousTelemetry": true, "reconnectInterval": "00:15:00", "shadowCopyFolder": null, "shareUploaderProcess": true, "snapshotInLowPriorityThread": true, "snapshotsPerDayLimit": 30, "snapshotsPerTenMinutesLimit": 1, "tempFolder": null, "thresholdForSnapshotting": 1, "uploaderProxy": null}}}}