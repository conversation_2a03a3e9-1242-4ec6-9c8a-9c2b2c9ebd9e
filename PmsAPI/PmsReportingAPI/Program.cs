using Microsoft.Azure.Functions.Worker.Extensions.OpenApi.Extensions;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Abstractions;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Configurations;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using PmsCore.Notifications.Interfaces;
using Microsoft.Extensions.Logging;
using PmsData.DataFn;
using PmsCommon.Services;
using PmsCommon;
using Microsoft.Extensions.Configuration;

namespace PmsReportingAPI
{
    public class Program
    {
        public static void Main()
        {
            var host = new HostBuilder()
                .ConfigureFunctionsWorkerDefaults()
                .ConfigureServices((context, services) =>
                {
                    services.Configure<JsonSerializerOptions>(options =>
                    {
                        options.PropertyNamingPolicy = null;
                        options.Converters.Add(new JsonStringEnumConverter());
                    });
                    // 2. Core services and configurations
                    services.AddScoped<GlobalDataEntity>();

                    // 3. Logging
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.AddApplicationInsights();
                    });
                    services.AddSingleton(typeof(ILogger<>), typeof(Logger<>));
                    services.AddApplicationInsightsTelemetryWorkerService();

                })
                .ConfigureOpenApi()
                .ConfigureServices(services =>
                {
                    services.AddSingleton<IOpenApiConfigurationOptions>(_ =>
                            {
                                var options = new OpenApiConfigurationOptions()
                                {
                                    Info = new OpenApiInfo()
                                    {
                                        Version = "1.0.0",
                                        Title = "PMS Reporting Swagger",
                                        Description = "Developed by Damasinfo",
                                        Contact = new OpenApiContact()
                                        {
                                            Name = "Enquiry",
                                            Email = "<EMAIL>",
                                            Url = new Uri("https://www.damasinfo.com"),
                                        },
                                        License = new OpenApiLicense()
                                        {
                                            Name = "MIT",
                                            Url = new Uri("http://opensource.org/licenses/MIT"),
                                        }
                                    },
                                    Servers = DefaultOpenApiConfigurationOptions.GetHostNames(),
                                    OpenApiVersion = OpenApiVersionType.V3,
                                    //IncludeRequestingHostName = true,
                                    ForceHttps = false,
                                    ForceHttp = false,
                                };
                                return options;
                            });
                })
                .Build();

            host.Run();
        }
    }
}