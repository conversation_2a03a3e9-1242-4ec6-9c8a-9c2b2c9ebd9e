using System;

namespace PmsCore.DataAccessRepository.Models
{
    public partial class YieldReportDto
    {
        public string SaleOrderNumber { get; set; }
        public string CustomerName { get; set; }
        public long? SaleOrderId { get; set; }
        public decimal? JumboCount { get; set; }
        public decimal? Yield { get; set; }
        public decimal? FirstGrade { get; set; }
        public decimal? FirstGradeCount { get; set; }
        public decimal? AGrade { get; set; }
        public decimal? AGradeCount { get; set; }
        public decimal? CUTPCGrade { get; set; }
        public decimal? CUTPCGradeCount { get; set; }
        public decimal? FILMGrade { get; set; }
        public decimal? FILMGradeCount { get; set; }
        public string FabricName { get; set; }
        public decimal? LOTGrade { get; set; }
        public decimal? LOTGradeCount { get; set; }
        public decimal? NSGrade { get; set; }
        public decimal? NSGradeCount { get; set; }
        public decimal? WASTEGrade { get; set; }
        public decimal? WASTEGradeCount { get; set; }
        public decimal? SampleQuantity { get; set; }
        public decimal? SampleCount { get; set; }
        public string SaleOrderCode { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? Amount { get; set; }
        public decimal? ActualQuantity { get; set; }
        public decimal? JumboRolQty { get; set; }
        public decimal? WastageEmbossing { get; set; }
        public decimal? WastageLacquer { get; set; }
        public decimal? WastagePrint { get; set; }
        public decimal? WastageTumbling { get; set; }
        public decimal? WastageVacuum { get; set; }
        public decimal? ManufacturingQuantity { get; set; }
        public decimal? SaleOrderQuantity { get; set; }
    }
}