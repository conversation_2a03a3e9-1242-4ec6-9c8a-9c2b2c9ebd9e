using System.Threading.Tasks;
using PmsCore.PDFGeneration.Models;
namespace PmsCore.PDFGeneration.Interfaces
{
    public interface IPdfService
    {
        Task<byte[]> GeneratePdfAsync(IPdfDocumentData data);
        Task<string> GeneratePdfAndUploadToStorageAsync(IPdfDocumentData data, string fileName);
        Task<string> GeneratePdfAndUploadToWhatsAppStorageAsync(IPdfDocumentData data, string fileName, string documentType);
    }
}
