using System;
using System.Collections.Generic;

namespace PmsCore.PDFGeneration.Models
{
    /// <summary>
    /// Interface for single pending returnable outpass PDF data (reminder notification)
    /// </summary>
    public interface IPendingReturnableOutPassPdfData : IPdfDocumentData
    {
        long OutpassId { get; }
        string OutpassNumber { get; }
        string OutpassTo { get; }
        DateTime? OutpassDate { get; }
        DateTime? ExpectedReturnDate { get; }
        string Purpose { get; }
        string Remark { get; }
        string AddedBy { get; }
        DateTime? AddedDate { get; }
        int DaysOverdue { get; }
        bool IsOverdue { get; }
        string Status { get; }
        decimal TotalAmount { get; }
        DateTime GeneratedDate { get; }
        IEnumerable<IPendingReturnableOutPassItemData> OutpassItems { get; }
    }

    /// <summary>
    /// Interface for pending returnable outpass item data
    /// </summary>
    public interface IPendingReturnableOutPassItemData
    {
        long OutpassItemId { get; }
        long? ProductId { get; }
        string ProductName { get; }
        decimal? Quantity { get; }
        decimal? Amount { get; }
        string Unit { get; }
        string RackName { get; }
        string StoreName { get; }
        string BatchNo { get; }
        decimal? ReturnedQuantity { get; }
    }

    /// <summary>
    /// Interface for pending returnable outpass report PDF data (comprehensive report)
    /// </summary>
    public interface IPendingReturnableOutPassReportPdfData : IPdfDocumentData
    {
        IEnumerable<IPendingReturnableOutPassReportItemData> PendingOutPasses { get; }
        int TotalPendingCount { get; }
        int OverdueCount { get; }
        DateTime GeneratedDate { get; }
        decimal TotalPendingAmount { get; }
    }

    /// <summary>
    /// Interface for pending returnable outpass report item data
    /// </summary>
    public interface IPendingReturnableOutPassReportItemData
    {
        long OutpassId { get; }
        string OutpassNumber { get; }
        string OutpassTo { get; }
        DateTime? OutpassDate { get; }
        DateTime? ExpectedReturnDate { get; }
        string Purpose { get; }
        string AddedBy { get; }
        int DaysOverdue { get; }
        bool IsOverdue { get; }
        string Status { get; }
        decimal TotalAmount { get; }
        int ItemCount { get; }
    }
}
