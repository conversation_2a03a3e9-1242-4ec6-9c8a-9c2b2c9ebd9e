using System;
using System.Collections.Generic;

namespace PmsCore.PDFGeneration.Models
{
    /// <summary>
    /// Interface for OutPass PDF data
    /// </summary>
    public interface IOutPassPdfData : IPdfDocumentData
    {
        long OutpassId { get; }
        string OutpassNumber { get; }
        string OutpassTo { get; }
        DateTime? OutpassDate { get; }
        string OutpassType { get; }
        string Purpose { get; }
        string Status { get; }
        string Remark { get; }
        string AddedBy { get; }
        DateTime? AddedDate { get; }
        IEnumerable<IOutPassItemData> OutpassItems { get; }
    }

    /// <summary>
    /// Interface for OutPass item data
    /// </summary>
    public interface IOutPassItemData
    {
        long OutpassItemId { get; }
        long? ProductId { get; }
        string ProductName { get; }
        decimal? Quantity { get; }
        string Unit { get; }
        decimal? Amount { get; }
        decimal? Total { get; }
        string BatchNo { get; }
    }
}
