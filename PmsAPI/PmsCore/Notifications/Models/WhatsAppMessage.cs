using System.Collections.Generic;

namespace PmsCore.Notifications.Models
{
    public class WhatsAppMessage
    {
        public List<string> contactNumbers { get; set; }
        public string senderNumber { get; set; }
        public long? templateId { get; set; }
        public Dictionary<string, string> Params { get; set; }
    }
    public class WhatsAppCustomMessage
    {
        public List<string> contactNumbers { get; set; }
        public string senderNumber { get; set; }
        public string xtext { get; set; }
        public Dictionary<string, string> Params { get; set; }
    }

    public class WhatsAppTemplateMessage : WhatsAppMessage
    {
        public string TemplateName { get; set; }
        public string LanguageCode { get; set; } = "en";
        public List<WhatsAppTemplateComponent> Components { get; set; }
    }

    public class WhatsAppTemplateComponent
    {
        public string Type { get; set; }  // header, body, footer
        public List<WhatsAppTemplateParameter> Parameters { get; set; }
    }

    public class WhatsAppTemplateParameter
    {
        public string Type { get; set; }  // text, image, document, video
        public string Text { get; set; }
        public string MediaUrl { get; set; }
        public string FileName { get; set; }
    }
    public class CustomNotificationRequest
    {
        public string Message { get; set; }
        public string[] To { get; set; }
    }

    public class WhatsAppResponse
    {
        public bool Success { get; set; }
        public string MessageId { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
    }
}
