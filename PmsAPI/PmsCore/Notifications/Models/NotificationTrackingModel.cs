using System;

namespace PmsCore.Notifications.Models
{
    /// <summary>
    /// Model for tracking notification messages
    /// </summary>
    public class NotificationTrackingModel
    {
        /// <summary>
        /// Type of notification (e.g., WhatsApp, Email, SMS)
        /// </summary>
        public string NotificationType { get; set; }

        /// <summary>
        /// Type of message (e.g., <PERSON><PERSON>, Report, OnDemand)
        /// </summary>
        public string MessageType { get; set; }

        /// <summary>
        /// ID of the recipient from NotificationGroupsTable
        /// </summary>
        public long RecipientId { get; set; }

        /// <summary>
        /// Content of the message (may be JSON or HTML)
        /// </summary>
        public string MessageContent { get; set; }

        /// <summary>
        /// ID of the template used for the message
        /// </summary>
        public long MasterTemplateId { get; set; }

        /// <summary>
        /// Status of the message (e.g., Sent, Delivered, Read, Failed)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Error message if the notification failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Message ID from the provider (e.g., WhatsApp message ID)
        /// </summary>
        public string ProviderMessageId { get; set; }

        /// <summary>
        /// Time the message was sent
        /// </summary>
        public DateTime SentTime { get; set; }

        /// <summary>
        /// Time the message was delivered
        /// </summary>
        public DateTime? DeliveredTime { get; set; }

        /// <summary>
        /// Time the message was read
        /// </summary>
        public DateTime? ReadTime { get; set; }

        /// <summary>
        /// ID of the notification group user
        /// </summary>
        public long NotificationGroupUserId { get; set; }

        /// <summary>
        /// Unique ID for the notification message across all channels
        /// </summary>
        public string NotificationMessageId { get; set; }

        /// <summary>
        /// Specific mobile number the message was sent to
        /// </summary>
        public string RecipientMobileNumber { get; set; }

        /// <summary>
        /// Specific email address the message was sent to
        /// </summary>
        public string RecipientEmail { get; set; }
    }
}