using System;
using System.Collections.Generic;
using PmsCore.PDFGeneration.Interfaces;
using PmsCore.PDFGeneration.Models;

namespace PmsCore.Notifications.Models
{
    public interface ICostingReportPdfData : IPdfDocumentData
    {
        DateTime FromDate { get; set; }
        DateTime ToDate { get; set; }
        List<CostingReportItem> CostingData { get; set; }
        CostingReportSummary Summary { get; set; }
        List<TopProductItem> TopProducts { get; set; }
        CostingReportAnalytics Analytics { get; set; }
    }

    public class CostingReportPdfData : ICostingReportPdfData
    {
        public string DocumentType => "CostingReport";
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public List<CostingReportItem> CostingData { get; set; } = new List<CostingReportItem>();
        public CostingReportSummary Summary { get; set; } = new CostingReportSummary();
        public List<TopProductItem> TopProducts { get; set; } = new List<TopProductItem>();
        public CostingReportAnalytics Analytics { get; set; } = new CostingReportAnalytics();

        // IPdfDocumentData implementation
        public string DocumentTitle => $"Costing Report - {FromDate:dd-MMM-yyyy} to {ToDate:dd-MMM-yyyy}";
        public DateTime GeneratedDate => DateTime.UtcNow;
        public string GeneratedBy => "System";
        public Dictionary<string, object> Metadata => new Dictionary<string, object>
        {
            { "FromDate", FromDate },
            { "ToDate", ToDate },
            { "TotalRecords", CostingData?.Count ?? 0 },
            { "ReportType", "Executive Costing Analysis" }
        };
    }

    public class CostingReportItem
    {
        public int SerialNo { get; set; }
        public string SaleOrderNo { get; set; }
        public string CustomerName { get; set; }
        public string Alias { get; set; }
        public string Category { get; set; }
        public decimal OrderQty { get; set; }
        public decimal MfdQty { get; set; }
        public decimal RejectionPercent { get; set; }
        public decimal SalePrice { get; set; }
        public decimal TotalCostLm { get; set; }
        public decimal FinalTotalCost { get; set; }
        public decimal ProfitLossLm { get; set; }
        public decimal TotalProfitLoss { get; set; }
        public decimal OverheadCost { get; set; }
        public string CostingStatus { get; set; }
        public DateTime SubmittedDate { get; set; }
        public string SubmittedBy { get; set; }
        // Added for top products calculation following GetTopSellingProducts pattern
        public string FormulationCode { get; set; }
        public string ThicknessNumber { get; set; }
    }

    public class CostingReportSummary
    {
        public decimal GrandTotalOrderQty { get; set; }
        public decimal GrandTotalManufacturedQty { get; set; }
        public decimal TotalAverageRejectionPercent { get; set; }
        public decimal AverageOverheadCostLm { get; set; }
        public decimal GrandTotalCost { get; set; }
        public decimal AverageProfitLossLm { get; set; }
        public decimal GrandTotalProfitLoss { get; set; }
        public int TotalRecords { get; set; }
    }

    public class TopProductItem
    {
        public string ProductName { get; set; }
        public decimal SalesVolume { get; set; }
        public decimal SalesValue { get; set; }
        public int OrderCount { get; set; }
    }

    public class CostingReportAnalytics
    {
        public decimal ProfitPercentage { get; set; }
        public decimal LossPercentage { get; set; }
        public decimal OrderFulfillmentRate { get; set; }
        public decimal AverageOrderValue { get; set; }
        public List<CategoryAnalytics> CategoryBreakdown { get; set; } = new List<CategoryAnalytics>();
    }

    public class CategoryAnalytics
    {
        public string CategoryName { get; set; }
        public decimal TotalValue { get; set; }
        public decimal Percentage { get; set; }
        public int OrderCount { get; set; }
    }
}
