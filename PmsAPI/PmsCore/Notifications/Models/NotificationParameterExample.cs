using System;
using System.Collections.Generic;

namespace PmsCore.Notifications.Models
{
    /// <summary>
    /// Example demonstrating how the enhanced parameter collection handles duplicate parameter names
    /// </summary>
    public static class NotificationParameterExample
    {
        /// <summary>
        /// Example showing how duplicate parameter names are handled
        /// Template: "Product {{1}} has {{2}} {{3}} available. Minimum required: {{4}} {{5}}"
        /// Where: {{1}}=ProductName, {{2}}=Quantity, {{3}}=UNIT, {{4}}=MinQuantity, {{5}}=UNIT
        /// </summary>
        public static void DemonstrateParameterHandling()
        {
            var collection = new NotificationParameterCollection();

            // Simulate template parameters with duplicate "UNIT" parameter name
            var templateParams = new[]
            {
                new { ParameterName = "ProductName", Sequence = 1, Value = "Fabric A" },
                new { ParameterName = "TotalQuantity", Sequence = 2, Value = "50" },
                new { ParameterName = "UNIT", Sequence = 3, Value = "Meters" },
                new { ParameterName = "MinimumQuantity", Sequence = 4, Value = "100" },
                new { ParameterName = "UNIT", Sequence = 5, Value = "Meters" } // Duplicate parameter name
            };

            // Add parameters to collection (allows duplicates)
            foreach (var param in templateParams)
            {
                collection.Add(new NotificationParameter
                {
                    Name = param.ParameterName,
                    Value = param.Value,
                    Sequence = param.Sequence,
                    IsRequired = true,
                    DefaultValue = "N/A"
                });
            }

            // For Azure Communication Services (numbered parameters)
            var azureParams = collection.ToNumberedParameterDictionary();
            // Result: {"1": "Fabric A", "2": "50", "3": "Meters", "4": "100", "5": "Meters"}

            // For Brevo (named parameters - first occurrence wins for duplicates)
            var brevoParams = collection.ToNamedParameterDictionary();
            // Result: {"ProductName": "Fabric A", "TotalQuantity": "50", "UNIT": "Meters", "MinimumQuantity": "100"}

            Console.WriteLine("Azure Parameters (numbered):");
            foreach (var param in azureParams)
            {
                Console.WriteLine($"  {param.Key}: {param.Value}");
            }

            Console.WriteLine("\nBrevo Parameters (named):");
            foreach (var param in brevoParams)
            {
                Console.WriteLine($"  {param.Key}: {param.Value}");
            }
        }

        /// <summary>
        /// Validates that the parameter collection correctly handles edge cases
        /// </summary>
        public static bool ValidateParameterHandling()
        {
            var collection = new NotificationParameterCollection();

            // Test case: Multiple parameters with same name but different sequences
            collection.Add(new NotificationParameter { Name = "UNIT", Value = "Meters", Sequence = 1 });
            collection.Add(new NotificationParameter { Name = "ProductName", Value = "Fabric", Sequence = 2 });
            collection.Add(new NotificationParameter { Name = "UNIT", Value = "Meters", Sequence = 3 });

            var numberedParams = collection.ToNumberedParameterDictionary();
            var namedParams = collection.ToNamedParameterDictionary();

            // Validate numbered parameters (should have 3 entries)
            if (numberedParams.Count != 3)
                return false;

            // Validate named parameters (should have 2 unique entries)
            if (namedParams.Count != 2)
                return false;

            // Validate sequence ordering
            if (numberedParams["1"] != "Meters" || numberedParams["2"] != "Fabric" || numberedParams["3"] != "Meters")
                return false;

            return true;
        }
    }
}
