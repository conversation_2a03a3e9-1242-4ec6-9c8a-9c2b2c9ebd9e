using System.Collections.Generic;
using System.Linq;

namespace PmsCore.Notifications.Models
{
    /// <summary>
    /// Enhanced parameter model that supports both named and numbered parameter formats
    /// for different WhatsApp providers (Brevo uses named, Azure uses numbered)
    /// </summary>
    public class NotificationParameter
    {
        /// <summary>
        /// Parameter name used for value mapping and Brevo templates
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Parameter value
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// Sequence number for Azure Communication Services numbered parameters
        /// </summary>
        public int Sequence { get; set; }

        /// <summary>
        /// Whether this parameter is required
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// Default value if the actual value is null or empty
        /// </summary>
        public string DefaultValue { get; set; }
    }

    /// <summary>
    /// Collection of notification parameters with provider-specific conversion methods
    /// Handles duplicate parameter names by maintaining sequence-based ordering
    /// </summary>
    public class NotificationParameterCollection
    {
        private readonly List<NotificationParameter> _parameters;

        public NotificationParameterCollection()
        {
            _parameters = new List<NotificationParameter>();
        }

        public NotificationParameterCollection(IEnumerable<NotificationParameter> parameters)
        {
            _parameters = new List<NotificationParameter>(parameters);
        }

        /// <summary>
        /// Add a parameter to the collection (allows duplicates by name)
        /// </summary>
        public void Add(NotificationParameter parameter)
        {
            _parameters.Add(parameter);
        }

        /// <summary>
        /// Get all parameters
        /// </summary>
        public IReadOnlyList<NotificationParameter> GetAll()
        {
            return _parameters.AsReadOnly();
        }

        /// <summary>
        /// Convert to named parameter dictionary for Brevo provider
        /// For duplicate parameter names, uses the first occurrence
        /// </summary>
        public Dictionary<string, string> ToNamedParameterDictionary()
        {
            var result = new Dictionary<string, string>();

            foreach (var param in _parameters)
            {
                if (!string.IsNullOrEmpty(param.Name) && !result.ContainsKey(param.Name))
                {
                    var value = !string.IsNullOrEmpty(param.Value) ? param.Value : param.DefaultValue ?? "N/A";
                    result[param.Name] = value;
                }
            }

            return result;
        }

        /// <summary>
        /// Convert to numbered parameter dictionary for Azure Communication Services
        /// Parameters are ordered by Sequence, handles duplicate parameter names properly
        /// Each parameter gets a unique numbered position regardless of name duplicates
        /// </summary>
        public Dictionary<string, string> ToNumberedParameterDictionary()
        {
            var result = new Dictionary<string, string>();

            // Sort parameters by sequence first, then by name for consistent ordering
            var sortedParams = _parameters
                .OrderBy(p => p.Sequence)
                .ThenBy(p => p.Name)
                .ToList();

            for (int i = 0; i < sortedParams.Count; i++)
            {
                var param = sortedParams[i];
                var value = !string.IsNullOrEmpty(param.Value) ? param.Value : param.DefaultValue ?? "N/A";

                // Azure uses 1-based numbering for parameters
                // Each parameter gets a unique position regardless of duplicate names
                result[(i + 1).ToString()] = value;
            }

            return result;
        }

        /// <summary>
        /// Get parameter count
        /// </summary>
        public int Count => _parameters.Count;
    }
}
