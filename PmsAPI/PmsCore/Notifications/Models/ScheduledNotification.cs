using System;
using System.Collections.Generic;

namespace PmsCore.Notifications.Models
{
    public class ScheduledNotification
    {
        public long ReportId { get; set; }
        public string ReportType { get; set; }
        public string ReportName { get; set; }
        public long NotificationGroupUserId { get; set; }
        public long TemplateMasterId { get; set; }
        public string CronExpression { get; set; }
        public DateTime? LastRunTime { get; set; }
        public DateTime? NextRunTime { get; set; }
        public bool IsActive { get; set; }
        public string TimeZone { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool Disabled { get; set; }
        
        // Recipient information
        public NotificationRecipient Recipient { get; set; }
        
        // Parameters for the notification
        public Dictionary<string, string> Parameters { get; set; }
    }
}
