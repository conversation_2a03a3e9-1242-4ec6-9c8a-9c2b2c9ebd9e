using System.Collections.Generic;

namespace PmsCore.Notifications.Models
{
    public class OnDemandNotificationRequest
    {
        /// <summary>
        /// The ID of the notification group to trigger
        /// </summary>
        public long? NotificationGroupUserId { get; set; }

        /// <summary>
        /// The type of notification to trigger
        /// </summary>
        public string NotificationType { get; set; }

        /// <summary>
        /// The name of the report to generate
        /// </summary>
        public string ReportName { get; set; }

        /// <summary>
        /// Additional parameters required for the notification
        /// </summary>
        public Dictionary<string, string> Parameters { get; set; } = new Dictionary<string, string>();
    }
}
