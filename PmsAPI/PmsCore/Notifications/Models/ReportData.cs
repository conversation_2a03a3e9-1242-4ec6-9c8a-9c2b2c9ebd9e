using System.Collections.Generic;

namespace PmsCore.Notifications.Models
{
    public class ReportData
    {
        public string HtmlContent { get; set; }
        public Dictionary<string, string> WhatsAppParameters { get; set; }
        public string Subject { get; set; }
    }
    public class ProductStockAvailabilityVm
    {
        public long ProductId { get; set; }
        public string PRODUCT<PERSON>ME { get; set; }
        public string PRODUCTCATEGORY { get; set; }
        public string PRODUCTFIRSTCATEGORY { get; set; }
        public string PRODUCTSECSUBCATEGORY { get; set; }
        public decimal? AVAILABLEQTY { get; set; }
        public string UNIT { get; set; }
        public decimal? MINIMUMQTY { get; set; }
        public string PRODUCTQUALITY { get; set; }
        public decimal? DOMESTICQTY { get; set; }
        public decimal? IMPORTEDQTY { get; set; }
    }
}
