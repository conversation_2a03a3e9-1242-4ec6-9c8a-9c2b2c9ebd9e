using System.Collections.Generic;
using System.Threading.Tasks;
using PmsCore.Notifications.Models;
namespace PmsCore.Notifications.Interfaces
{
    public interface IWhatsAppService
    {
        Task<WhatsAppResponse> SendTemplateMessageAsync(long? templateId, List<string> mobileNumbers, Dictionary<string, string> parameters);
        Task<WhatsAppResponse> SendTemplateMessageAsync(long? templateId, List<string> mobileNumbers, PmsCore.Notifications.Models.NotificationParameterCollection parameterCollection);
        Task<WhatsAppResponse> SendCustomMessageAsync(string mobileNumber, string message);
        Task<WhatsAppResponse> SendTemplateDocumentMessageAsync(long? templateId, List<string> mobileNumbers, Dictionary<string, string> parameters, string documentUrl);
        Task<WhatsAppResponse> SendOnlyDocumentMessageAsync(string providerName, List<string> mobileNumbers, Dictionary<string, string> parameters, string documentUrl);

    }
}
