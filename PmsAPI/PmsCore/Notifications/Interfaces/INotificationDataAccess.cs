using PmsCore.Notifications.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

namespace PmsCore.Notifications.Interfaces
{
    public interface INotificationDataAccess
    {
        // Recipient and template retrieval
        Task<List<NotificationRecipient>> GetReportRecipients(string notificationType, string reportName = null, string TriggerType = null);
        Task<NotificationTemplate> GetNotificationTemplate(string notificationType, string reportType);
        Task<(NotificationTemplate template, Dictionary<string, string> parameters)> GetSaleOrderStatusNotification(long saleOrderId, long stageId);
        Task<NotificationRecipient> GetRecipientById(long recipientId);
        Task<List<long>> GetRecipientTemplateIds(long recipientId, string notificationType);

        // Report generation
        Task<ReportData> GenerateReportData(string reportType);
        Task<string> GenerateYieldSummaryReportPdfAndUploadToStorageAsync(DateTime fromDate, DateTime toDate);
        Task<Dictionary<string, string>> GenerateYieldSummaryReportParameters(long templateMasterId, DateTime fromDate, DateTime toDate);
        Task<string> GenerateLowStockReportPdfAndUploadToStorageAsync();
        Task<Dictionary<string, string>> GenerateLowStockReportParameters(long templateMasterId);

        // Pending Returnable OutPass methods
        Task<string> GeneratePendingReturnableOutPassReminderPdfAndUploadToStorageAsync(long outpassId);
        Task<string> GeneratePendingReturnableOutPassReportPdfAndUploadToStorageAsync();
        Task<Dictionary<string, string>> GeneratePendingReturnableOutPassReminderParameters(long outpassId, long templateMasterId);
        Task<Dictionary<string, string>> GeneratePendingReturnableOutPassReportParameters(long templateMasterId);

        // Notification tracking and status
        Task TrackNotification(NotificationTrackingModel tracking);
        Task UpdateNotificationStatus(string providerMessageId, string status, DateTime? deliveredTime, DateTime? readTime);

        // Rate limiting
        Task<bool> CheckRateLimit(string notificationType, long recipientId);
        Task UpdateRateLimit(string notificationType, long recipientId);
        Task<NotificationLimitConfig> GetNotificationLimits(string notificationType);

        // WhatsApp related methods
        Task<Dictionary<string, string>> GenerateLowStockParameters(long productId, long templateMasterId, ProductStockAvailabilityVm productStockAvailabilityVm);
        PmsCore.Notifications.Models.NotificationParameterCollection GenerateLowStockParametersEnhanced(long templateMasterId, ProductStockAvailabilityVm productStockAvailabilityVm);
        Task<(WhatsAppTemplate template, WhatsAppConfig config, WhatsAppSettings settings)> GetWhatsAppTemplateAndConfig(long? templateMasterId);
        Task<ProductStockAvailabilityVm> GetProductStockAvailabilty(long productId);
        Task<WhatsAppConfig> GetDefaultWhatsAppConfig();
        Task<WhatsAppConfig> GetWhatsAppConfigByProviderName(string providerName);

        // Email settings
        Task<EmailSettings> GetPrimaryProviderEmailConfig();
        Task<EmailSettings> GetSecondaryProviderEmailConfig();

        // Notification scheduling methods
        Task<List<ScheduledNotification>> GetPendingScheduledNotifications();
        Task UpdateNextRunTime(long reportId, DateTime lastRunTime, DateTime nextRunTime);

        // On-demand notification methods
        Task UpdateOnDemandNotificationStatus(long notificationGroupId);
        Task<List<NotificationRecipient>> GetNotificationsByTriggerType(string triggerType);

        // Scheduled overdue returnable out pass reminders
        Task<List<long>> GetOverdueReturnableOutPassesForScheduledReminders();

        // Costing Report methods
        Task<string> GenerateCostingReportPdfAndUploadToStorageAsync(DateTime fromDate, DateTime toDate);
        Task<Dictionary<string, string>> GenerateCostingReportParameters(long templateMasterId, DateTime fromDate, DateTime toDate);

        // Overhead Cost Reminder methods
        Task<List<OverheadCostReminderData>> GetOverdueOverheadCostData();
        Task<Dictionary<string, string>> GenerateOverheadCostReminderParameters(long templateMasterId, DateTime lastUpdatedDate, DateTime applicableOnDate, int daysDelayed);
        Task UpdateNotificationLastTriggered(long notificationGroupUserId, DateTime currentTimeUtc);
    }
}
