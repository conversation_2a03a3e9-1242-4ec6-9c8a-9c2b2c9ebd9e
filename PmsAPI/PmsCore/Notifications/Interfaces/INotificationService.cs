using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PmsCore.Notifications.Interfaces
{
    public interface INotificationService
    {
        // Event-based notifications
        Task SendSaleOrderStatusNotification(long saleOrderId, long stageId);
        Task SendLowStockNotification(long productId);

        // Schedule-based notifications
        Task SendScheduledReportToRecipient(string reportType, long recipientId, string triggerType);
        Task SendYieldReportSummaryWhatsApp(DateTime fromDate, DateTime toDate, string TriggerType, long? recipientId = null);
        Task SendLowStockReportNotification(string triggerType, long? recipientId = null);
        Task SendPendingReturnableOutPassReminderNotification(long outpassId, string triggerType);
        Task SendPendingReturnableOutPassReportNotification(string triggerType, long? recipientId = null);
        Task SendScheduledOverdueReturnableOutPassReminders();

        // Costing notifications
        Task SendCostingReportPDFNotification(DateTime fromDate, DateTime toDate, string triggerType, long? recipientId = null);
        Task SendOverheadCostUpdateReminderNotification(string triggerType, long? recipientId = null);
        Task SendScheduledOverheadCostUpdateReminders(string triggerType);

        // On-demand notifications
        Task SendNotification(string notificationType, long recipientId, Dictionary<string, string> parameters, string messageType, long templateId);
        Task TriggerOnDemandNotification(long notificationGroupId, Dictionary<string, string> parameters);
    }
}
