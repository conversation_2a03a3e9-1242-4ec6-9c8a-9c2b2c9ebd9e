using PmsCore.Notifications.Models;
using System.Threading.Tasks;
using System;

namespace PmsCore.Notifications.Interfaces
{
    public interface INotificationTrackingService
    {
        Task TrackNotification(NotificationTrackingModel tracking);
        Task UpdateNotificationStatus(string providerMessageId, string status, DateTime? deliveredTime = null, DateTime? readTime = null);
        Task<bool> CheckRateLimit(string notificationType, long recipientId);
    }
}