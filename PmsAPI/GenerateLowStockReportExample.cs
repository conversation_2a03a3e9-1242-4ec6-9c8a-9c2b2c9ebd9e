using System;
using Tests;

namespace PmsAPI
{
    /// <summary>
    /// Console application to generate an example Low Stock Report PDF
    /// </summary>
    class GenerateLowStockReportExample
    {
        static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("🎯 Low Stock Report PDF Generation Example");
                Console.WriteLine("==========================================");
                Console.WriteLine();
                
                Console.WriteLine("This will generate an example PDF demonstrating the new three-table structure:");
                Console.WriteLine("🟡 Table 1: Adequate Stock (Yellow Header) - Items with >50% of minimum level");
                Console.WriteLine("🔴 Table 2: Low Stock (Red Header) - Items with ≤50% of minimum level but >0");
                Console.WriteLine("🔴 Table 3: Out of Stock (Red Header) - Items with zero available quantity");
                Console.WriteLine();
                
                Console.WriteLine("Press any key to generate the PDF...");
                Console.ReadKey();
                Console.WriteLine();
                
                // Run the PDF generation test
                LowStockReportPdfGenerationTest.RunTest();
                
                Console.WriteLine();
                Console.WriteLine("✅ PDF generation completed successfully!");
                Console.WriteLine("📁 Check the current directory for the generated PDF file.");
                Console.WriteLine();
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }
    }
}
