# WhatsApp Document Storage Setup Guide

This guide explains how to configure Azure Blob Storage for WhatsApp document delivery compatibility.

## Overview

The PMS system has been enhanced to support WhatsApp document uploads with the following features:

1. **Separate Container**: WhatsApp documents are stored in a dedicated container (`whatsappdocs`)
2. **Public Access**: Documents are accessible without SAS tokens for WhatsApp compatibility
3. **Hierarchical Organization**: Files are organized by document type and date
4. **Automatic Cleanup**: Files can be configured for automatic deletion after 7 days

## Azure Storage Container Setup

### 1. Create WhatsApp Documents Container

1. Navigate to your Azure Storage Account
2. Go to **Containers** section
3. Click **+ Container**
4. Set the following properties:
   - **Name**: `whatsappdocs`
   - **Public access level**: **Blob (anonymous read access for blobs only)**
   - Click **Create**

### 2. Configure Container Access Policy (Optional)

For automatic file cleanup, you can set up a lifecycle management policy:

1. Go to **Lifecycle management** in your storage account
2. Add a rule with the following settings:
   - **Rule name**: `whatsapp-docs-cleanup`
   - **Rule scope**: Limit blobs with filters
   - **Blob type**: Block blobs
   - **Blob subtype**: Base blobs
   - **Prefix filter**: `whatsappdocs/`
   - **Actions**: Delete blob after 7 days

### 3. Key Vault Configuration

Add the following secret to your Azure Key Vault:

- **Secret Name**: `WhatsAppDocumentsContainer`
- **Secret Value**: `whatsappdocs`

## File Organization Structure

Files are automatically organized in the following hierarchy:

```
whatsappdocs/
├── yieldsummaryreport/
│   ├── 2024/
│   │   ├── 01/
│   │   │   ├── 15/
│   │   │   │   ├── YieldReport_32024-01-15_143022.pdf
│   │   │   │   └── YieldReport_2024-01-15_160045.pdf
│   │   │   └── 16/
│   │   └── 02/
│   └── 2025/
├── lowstockreport/
│   └── 2024/
└── other-document-types/
```

## Code Usage

### Uploading WhatsApp-Compatible Documents

```csharp
// For WhatsApp documents (no SAS token)
var whatsappUrl = CommonFunctions.UploadToBlob(
    fileStream,
    "document.pdf",
    "yieldsummaryreport",  // Document type
    appendSasToken: false  // No SAS token for WhatsApp
);

// For regular documents (with SAS token)
var secureUrl = CommonFunctions.UploadToBlob(
    "container-name",
    "document.pdf",
    fileBytes,
    appendSasToken: true   // Include SAS token for security
);
```

### PDF Service Integration

```csharp
// Generate and upload PDF for WhatsApp
var pdfUrl = await _pdfService.GeneratePdfAndUploadToWhatsAppStorageAsync(
    pdfData,
    "YieldReport_2024-01-15.pdf",
    "yieldsummaryreport"
);
```

## Security Considerations

1. **Public Access**: The WhatsApp container allows anonymous read access. Ensure no sensitive data is stored here.
2. **File Naming**: Use non-predictable file names to prevent unauthorized access.
3. **Automatic Cleanup**: Implement lifecycle policies to automatically delete old files.
4. **Monitoring**: Set up monitoring and alerts for unusual access patterns.

## Troubleshooting

### Common Issues

1. **403 Forbidden Errors**
   - Verify container public access level is set to "Blob"
   - Check that the container exists and is accessible

2. **WhatsApp Document Delivery Failures**
   - Ensure URLs don't contain SAS tokens
   - Verify content-type headers are set correctly
   - Check that files are directly accessible via browser

3. **File Not Found Errors**
   - Verify the container name in Key Vault matches the actual container
   - Check that the file path is constructed correctly

### Testing Document Access

Test that documents are publicly accessible:

```bash
# Test direct access (should work)
curl -I https://yourstorageaccount.blob.core.windows.net/whatsappdocs/yieldsummaryreport/2024/01/15/document.pdf

# Should return 200 OK with proper content-type header
```

## Configuration Files

### appsettings.json
```json
{
  "WhatsAppDocumentsContainer": "whatsappdocs"
}
```

### Key Vault Secrets
- `WhatsAppDocumentsContainer`: Container name for WhatsApp documents
- `StorageAccountName`: Azure Storage account name
- `StorageAccountKey`: Azure Storage account key

## Migration Notes

Existing code using the old `UploadToBlob` method will continue to work unchanged. The new overloads provide additional functionality for WhatsApp compatibility while maintaining backward compatibility.
