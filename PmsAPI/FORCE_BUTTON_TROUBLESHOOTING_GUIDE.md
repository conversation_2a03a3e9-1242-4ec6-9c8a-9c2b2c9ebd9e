# 🚨 Force Button Not Hitting Backend - Complete Troubleshooting Guide

## 📋 Issue Analysis

### **🔍 Root Cause Identified:**
The force button is **correctly implemented** in the frontend, but the backend API is **not receiving the requests**. This indicates a **backend service availability issue**.

## 🛠️ Backend Service Configuration

### **📊 API Architecture:**
- **Frontend calls**: `environment.Reporting_Api_Url` = `http://localhost:7072/api/`
- **Endpoint**: `report/gatedashboard` 
- **Full URL**: `http://localhost:7072/api/report/gatedashboard`
- **Backend Service**: **PmsReportingAPI** (not PmsAPI)

### **🔧 Backend Services Required:**

| Service | Port | Project | Status Required |
|---------|------|---------|-----------------|
| **PmsAPI** | 7071 | `pms-backend-api\PmsAPI\PmsAPI` | ✅ Running |
| **PmsReportingAPI** | 7072 | `pms-backend-api\PmsAPI\PmsReportingAPI` | ❌ **NOT RUNNING** |

## 🚨 Critical Issue: PmsReportingAPI Not Running

### **🔍 Problem:**
The gate dashboard endpoint exists in **PmsReportingAPI** (port 7072), but only **PmsAPI** (port 7071) is running.

### **📍 Endpoint Location:**
```csharp
// File: pms-backend-api\PmsAPI\PmsReportingAPI\PmsReportFunction.cs
// Line 70-78
else if (entity.ToLowerInvariant().Equals("gatedashboard"))
{
    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
    try
    {
        var request = JsonConvert.DeserializeObject<GateDashboardRequestVm>(reqbody);
        var res = new ReportFunctions(GlobalData);
        var response = req.CreateResponse(HttpStatusCode.OK);
        await response.WriteAsJsonAsync(res.GetGateDashboardMetrics(request));
        return response;
    }
    // ... error handling
}
```

## 🛠️ Solution Steps

### **🚀 Step 1: Start PmsReportingAPI Service**

#### **Option A: Start PmsReportingAPI Separately**
```bash
# Navigate to PmsReportingAPI directory
cd pms-backend-api\PmsAPI\PmsReportingAPI

# Start the reporting API service
func start --port 7072
```

#### **Option B: Start Both Services Simultaneously**
```bash
# Terminal 1: Start PmsAPI
cd pms-backend-api\PmsAPI\PmsAPI
func start --port 7071

# Terminal 2: Start PmsReportingAPI  
cd pms-backend-api\PmsAPI\PmsReportingAPI
func start --port 7072
```

### **🚀 Step 2: Verify Services Are Running**

#### **Check PmsAPI (Port 7071):**
```bash
curl http://localhost:7071/api/
```

#### **Check PmsReportingAPI (Port 7072):**
```bash
curl http://localhost:7072/api/
```

#### **Test Gate Dashboard Endpoint:**
```bash
curl -X POST http://localhost:7072/api/report/gatedashboard \
  -H "Content-Type: application/json" \
  -d '{"DateFilterType":"all"}'
```

### **🚀 Step 3: Alternative Solutions**

#### **Option 1: Move Endpoint to PmsAPI**
If you prefer to keep only one service running, move the gate dashboard endpoint from PmsReportingAPI to PmsAPI.

#### **Option 2: Update Frontend Configuration**
Change the frontend to use PmsAPI instead of PmsReportingAPI:

```typescript
// In GateDashboard.component.ts, change:
ApiUrl = environment.Api_Url; // Instead of environment.Reporting_Api_Url
```

## 🧪 Testing Procedures

### **📋 Frontend Testing:**

1. **Open Browser Developer Tools**
2. **Navigate to Gate Dashboard**
3. **Click Force Button**
4. **Check Network Tab:**
   - ✅ **Should see**: POST request to `http://localhost:7072/api/report/gatedashboard`
   - ❌ **If missing**: Frontend issue
   - ❌ **If 404/500**: Backend service issue

### **📋 Backend Testing:**

1. **Check Console Logs:**
   ```bash
   # PmsReportingAPI should show:
   [2024-01-XX XX:XX:XX] Executing 'PmsReportFunction' (Reason='This function was programmatically called via the host APIs.', Id=...)
   ```

2. **Test Direct API Call:**
   ```bash
   curl -X POST http://localhost:7072/api/report/gatedashboard \
     -H "Content-Type: application/json" \
     -d '{
       "DateFrom": "2024-01-01T00:00:00.000Z",
       "DateTo": "2024-01-31T23:59:59.999Z",
       "DateFilterType": "fullday"
     }'
   ```

## 🔍 Debugging Steps

### **🔧 Step 1: Verify Frontend Request**

Add additional logging to the frontend:

```typescript
// In GateDashboard.component.ts, add before HTTP call:
console.log('=== FORCE BUTTON DEBUG ===');
console.log('API URL:', this.ApiUrl);
console.log('Full URL:', url);
console.log('Request Body:', request);
console.log('Bypass Cache:', bypassCache);
```

### **🔧 Step 2: Check Network Connectivity**

```bash
# Test if port 7072 is accessible
telnet localhost 7072

# Check if any service is running on port 7072
netstat -an | findstr 7072
```

### **🔧 Step 3: Verify Backend Logs**

```bash
# Check if PmsReportingAPI is receiving requests
# Look for these log entries:
# - Function execution started
# - Request body received
# - Response sent
```

## 🛡️ Common Issues & Solutions

### **❌ Issue 1: Port 7072 Not Available**
**Solution:** Check if another service is using port 7072
```bash
netstat -ano | findstr 7072
# Kill process if needed: taskkill /PID <process_id> /F
```

### **❌ Issue 2: CORS Errors**
**Solution:** Ensure CORS is configured in PmsReportingAPI
```csharp
// In host.json or startup configuration
{
  "extensions": {
    "http": {
      "routePrefix": "api",
      "cors": {
        "allowedOrigins": ["*"],
        "allowedMethods": ["GET", "POST", "PUT", "DELETE"],
        "allowedHeaders": ["*"]
      }
    }
  }
}
```

### **❌ Issue 3: Environment Configuration**
**Solution:** Verify environment.ts points to correct URLs
```typescript
// For local development:
Reporting_Api_Url: "http://localhost:7072/api/"
Api_Url: "http://localhost:7071/api/"
```

## ✅ Verification Checklist

### **🔧 Backend Services:**
- [ ] **PmsAPI running on port 7071**
- [ ] **PmsReportingAPI running on port 7072**
- [ ] **Both services responding to health checks**
- [ ] **Gate dashboard endpoint accessible**

### **🎨 Frontend Configuration:**
- [ ] **environment.ts has correct URLs**
- [ ] **Force button calls refreshDashboard(true)**
- [ ] **Network requests visible in dev tools**
- [ ] **Console logs show API calls**

### **🧪 Integration Testing:**
- [ ] **Force button triggers network request**
- [ ] **Backend receives and processes request**
- [ ] **Response data updates dashboard tiles**
- [ ] **All PO tiles show correct values**

## 🎯 Expected Results After Fix

### **✅ Force Button Behavior:**
1. **Click Force Button** → Console shows "Forcing fresh data reload..."
2. **Network Request** → POST to `http://localhost:7072/api/report/gatedashboard`
3. **Backend Processing** → PmsReportingAPI logs show request received
4. **Response Received** → Dashboard tiles update with fresh data
5. **Cache Bypassed** → Data loads even if cache exists

### **✅ Dashboard Data Flow:**
1. **Frontend** → Calls PmsReportingAPI (port 7072)
2. **PmsReportingAPI** → Processes gate dashboard request
3. **ReportDataFn** → Executes database queries with date filtering
4. **Response** → Returns all tile data including PO metrics
5. **Frontend** → Updates all tiles with real values

## 🚀 Quick Fix Command

```bash
# Start PmsReportingAPI service
cd pms-backend-api\PmsAPI\PmsReportingAPI
func start --port 7072
```

**After starting PmsReportingAPI, the force button should work correctly and hit the backend!** 🎉
