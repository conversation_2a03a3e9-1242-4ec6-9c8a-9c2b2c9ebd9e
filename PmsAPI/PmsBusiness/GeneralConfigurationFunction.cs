﻿using PmsCommon;
using PmsEntity.ViewModel;
using System.Collections.Generic;
using PmsData.DataFn;

namespace PmsBusiness
{
    public class GeneralConfigurationFunction
    {
        public GlobalDataEntity GlobalData;
        public GeneralConfigurationFunction(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ApiFunctionResponseVm AddConfig(ConfigTableVm Config)
        {
            var data = new GeneralConfigurationDataFn(GlobalData);
            return data.AddConfig(Config);
        }
        public List<ConfigTableVm> GetConfig()
        {
            var data = new GeneralConfigurationDataFn(GlobalData);
            return data.GetConfig();
        }
        public ApiFunctionResponseVm GetConfigByConfigItem(string configItem)
        {
            var data = new GeneralConfigurationDataFn(GlobalData);
            return data.GetConfigByConfigItem(configItem);
        }
        public ApiFunctionResponseVm EditConfig(ConfigTableVm Config)
        {
            var data = new GeneralConfigurationDataFn(GlobalData);
            return data.EditConfig(Config);
        }
    }
}
