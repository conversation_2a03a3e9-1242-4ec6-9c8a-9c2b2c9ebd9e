﻿using PmsCommon;
using PmsData.DataFn;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PmsBusiness
{
    public class UserRolesResponsibilityFunctions
    {
        public GlobalDataEntity GlobalData;
        public UserRolesResponsibilityFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<ResponsibilityMasterVm> GetAllResponsibility()
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.GetAllResponsibility();
        }
        public List<ResponsibilityMasterVm> GetAllResponsibilityWithRoles()
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.GetAllResponsibilityWithRoles();
        }
        public ApiFunctionResponseVm AddUpdateResponsibility(ResponsibilityMasterVm br)
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.AddUpdateResponsibility(br);
        }
        public List<UserRoleMasterVm> GetAllUserRoles()
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.GetAllUserRoles();
        }
        public List<UserMasterVm> GetAllUserData()
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.GetAllUserData();
        }
        public ApiFunctionResponseVm AddUpdateUserRole(UserRoleMasterVm br)
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.AddUpdateUserRole(br);
        }
        public ApiFunctionResponseVm AddUpdateRoleResposibilityMapping(List<UserRoleResponsibilityMappingTableVm> br)
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.AddUpdateRoleResposibilityMapping(br);
        }
        public ApiFunctionResponseVm AddUpdateUsernameUserRoleMapping(List<UsernameUserRoleMappingTableVm> br)
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.AddUpdateUsernameUserRoleMapping(br);
        }

        public List<UserRoleMasterVm> GetAllUserRolesByUserName(string username)
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.GetAllUserRolesByUserName(username);
        }
        public List<UserMasterVm> GetAllUsersByResposibilityId(long respId)
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.GetAllUsersByResposibilityId(respId);
        }
        public ApiFunctionResponseVm AddUser(UserMasterVm br)
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.AddUser(br);
        }
        public ApiFunctionResponseVm RemoveUser(string username)
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.RemoveUser(username);
        }
        public List<string> GetAllUserExceptionForceLogout()
        {
            var data = new UserRolesResponsibilityDataFn(GlobalData);
            return data.GetAllUserExceptionForceLogout();
        }
    }
}
