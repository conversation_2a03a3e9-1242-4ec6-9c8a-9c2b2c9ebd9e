﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class StoreFunctions
    {
        public List<StoreMasterVm> GetAllStores()
        {
            var data = new StoreDataFn();
            return data.GetAllStores();
        }

        public ApiFunctionResponseVm AddUpdateStore(StoreMasterVm Store)
        {
            var data = new StoreDataFn();
            return data.AddUpdateStore(Store);
        }
    }
}
