﻿using PmsCommon;
using PmsCore.Notifications.Interfaces;
using PmsData.DataFn;
using PmsEntity.ViewModel;
using System;
using System.Net;
using Microsoft.Extensions.Logging;
using PmsCore.Notifications.Models;
using PmsCore.PDFGeneration.Interfaces;

namespace PmsBusiness
{
    public class NotificationProcessorFunction
    {
        public GlobalDataEntity GlobalData;
        public ILogger _logger;
        public INotificationService _notificationService;
        public IPdfService _pdfService;
        public NotificationProcessorFunction(GlobalDataEntity gd, ILogger logger, INotificationService notificationService, IPdfService pdfService)
        {
            GlobalData = gd;
            _logger = logger;
            _notificationService = notificationService;
            _pdfService = pdfService;
        }

        /// <summary>
        /// Triggers an on-demand notification for the specified notification group
        /// </summary>
        /// <param name="notificationRequest">The on-demand notification request containing group ID and parameters</param>
        /// <returns>Response indicating success or failure</returns>
        public ApiFunctionResponseVm TriggerOnDemandNotification(OnDemandNotificationRequest notificationRequest)
        {
            _logger.LogInformation("Business layer: Processing on-demand notification for type {NotificationType}, report {ReportName}",
                notificationRequest.NotificationType, notificationRequest.ReportName);

            try
            {
                // Call the data layer to process the notification
                // Create a simple wrapper around the existing logger
                var data = new NotificationProcessorDataFn(
                    new LoggerWrapper<NotificationProcessorDataFn>(_logger),
                    GlobalData,
                    _notificationService,
                    _pdfService);
                return data.TriggerOnDemandNotification(notificationRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Business layer: Error processing on-demand notification");
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError,
                    "An error occurred while processing the notification: " + ex.Message);
            }
        }
    }
}
