﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class CustomerFunctions
    {
        public List<CustomerMasterVm> GetAllCustomers()
        {
            var data = new CustomerDataFn();
            return data.GetAllCustomers();
        }

        public ApiFunctionResponseVm AddUpdateCustomer(CustomerMasterVm cust)
        {
            var data = new CustomerDataFn();
            return data.AddUpdateCustomer(cust);
        }
        public ApiFunctionResponseVm DeleteCustomer(CustomerMasterVm cust)
        {
            var data = new CustomerDataFn();
            return data.DeleteCustomer(cust);
        }
    }
}
