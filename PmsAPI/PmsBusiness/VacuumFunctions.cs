﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class VacuumFunctions
    {
        public List<VacuumMasterVm> GetAllVacuums()
        {
            var data = new VacuumDataFn();
            return data.GetAllVacuums();
        }

        public ApiFunctionResponseVm AddUpdateVacuum(VacuumMasterVm Vacuum)
        {
            var data = new VacuumDataFn();
            return data.AddUpdateVacuum(Vacuum);
        }
        public ApiFunctionResponseVm DeleteVacuum(VacuumMasterVm Vacuum)
        {
            var data = new VacuumDataFn();
            return data.DeleteVacuum(Vacuum);
        }
    }
}
