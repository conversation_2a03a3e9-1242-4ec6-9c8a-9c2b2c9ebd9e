﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class WidthFunctions
    {
        public List<WidthMasterVm> GetAllWidthData()
        {
            var data = new WidthDataFn();
            return data.GetAllWidthData();
        }

        public ApiFunctionResponseVm AddUpdateWidth(WidthMasterVm br)
        {
            var data = new WidthDataFn();
            return data.AddUpdateWidth(br);
        }public ApiFunctionResponseVm DeleteWidth(WidthMasterVm br)
        {
            var data = new WidthDataFn();
            return data.DeleteWidth(br);
        }
    }
}
