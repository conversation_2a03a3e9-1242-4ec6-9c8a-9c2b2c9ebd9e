﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class TransportFunctions
    {
        public List<TransportCompanyVm> GetAllTransport()
        {
            var data = new TransportDataFn();
            return data.GetAllTransport();
        }

        public ApiFunctionResponseVm AddUpdateTransport(TransportCompanyVm trans)
        {
            var data = new TransportDataFn();
            return data.AddUpdateTransport(trans);
        }

        public ApiFunctionResponseVm DeleteTransport(long transportId)
        {
            var data = new TransportDataFn();
            return data.DeleteTransport(transportId);
        }

        public ApiFunctionResponseVm AddUpdateTransportVehicle(TransportVehicleVm vehicle)
        {
            var data = new TransportDataFn();
            return data.AddUpdateTransportVehicle(vehicle);
        }
        public ApiFunctionResponseVm DisableTransportCompany(TransportCompanyVm transport)
        {
            var data = new TransportDataFn();
            return data.DisableTransportCompany(transport);
        }
        public ApiFunctionResponseVm DisableTransportVehicle(TransportVehicleVm vehicle)
        {
            var data = new TransportDataFn();
            return data.DisableTransportVehicle(vehicle);
        }
    }
}
