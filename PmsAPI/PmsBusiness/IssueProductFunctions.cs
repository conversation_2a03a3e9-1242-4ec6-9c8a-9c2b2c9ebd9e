﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;
using PmsCore.Notifications.Interfaces;
namespace PmsBusiness
{
    public class IssueProductFunctions
    {
        public GlobalDataEntity GlobalData;
        public INotificationService notificationService;
        public IssueProductFunctions(GlobalDataEntity gd, INotificationService notificationService)
        {
            GlobalData = gd;
            this.notificationService = notificationService;
        }
        public List<IssueProductTableVm> GetIssueProductRequests(IssueFilterVm filter)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.GetIssueProductRequests(filter);
        }

        public ApiFunctionResponseVm IssueProductRequest(List<IssueProductTableVm> ipt)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.IssueProductRequest(ipt);
        }
        public ApiFunctionResponseVm SaleOrderIssueProductRequest(List<IssueProductTableVm> ipt)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.SaleOrderIssueProductRequest(ipt);
        }

        public ApiFunctionResponseVm ActionIssueProductRequest(IssueProductActionVm ipt)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.ActionIssueProductRequest(ipt);
        }
        public ApiFunctionResponseVm AddIssueSlipRequest(List<IssueSlipRequestVm> ipt)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.AddIssueSlipRequest(ipt);
        }
        public ApiFunctionResponseVm SaleOrderInspectionProductRequest(SaleOrderTableVm ipt)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.SaleOrderInspectionProductRequest(ipt);
        }
        public ApiFunctionResponseVm CancelSaleOrderInspectionProductRequest(InspectionCancellationTrackingTableVm request)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.CancelSaleOrderInspectionProductRequest(request);
        }

        public List<FormulationCodeMixingRawMaterialTableVm> GetStockForSaleOrder(long saleorderId)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.GetStockForSaleOrder(saleorderId);
        }
        public List<IssueSaleOrderProductsStockVm> GetStockForProduct(long productId)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.GetStockForProduct(productId);
        }

        public List<FormulationCodeMixingRawMaterialTableVm> GetStockForSaleOrderWithConsumptionstoreId(long saleorderId, long consumptionStoreId)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.GetStockForSaleOrderWithConsumptionstoreId(saleorderId, consumptionStoreId);
        }

        public List<IssueProductsSlipIdVm> GetProductBySlipId(long IssueSlipId)
        {
            var data = new IssueProductDataFn(GlobalData, notificationService);
            return data.GetIssueProductBySlipId(IssueSlipId);
        }

    }
}
