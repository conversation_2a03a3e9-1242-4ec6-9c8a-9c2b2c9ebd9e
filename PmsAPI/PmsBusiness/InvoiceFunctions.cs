using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;

namespace PmsBusiness
{
    public class InvoiceFunctions
    {
        public GlobalDataEntity GlobalData;
        public InvoiceFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }

        public PaginatedInvoiceResult GetAllInvoices(InvoiceRequestVm request = null)
        {
            var data = new InvoiceDataFn(GlobalData);
            return data.GetAllInvoices(request);
        }

        public PaginatedInvoiceResult GetAllInvoicesWithFilters(InvoiceRequestVm filters)
        {
            var data = new InvoiceDataFn(GlobalData);
            return data.GetAllInvoicesWithFilters(filters);
        }

        public InvoiceMasterVm GetInvoiceById(long invoiceId)
        {
            var data = new InvoiceDataFn(GlobalData);
            return data.GetInvoiceById(invoiceId);
        }

        public ApiFunctionResponseVm UpdateInvoice(InvoiceMasterVm invoice)
        {
            var data = new InvoiceDataFn(GlobalData);
            return data.UpdateInvoice(invoice);
        }

        public ApiFunctionResponseVm DeleteInvoice(long invoiceId)
        {
            var data = new InvoiceDataFn(GlobalData);
            return data.DeleteInvoice(invoiceId);
        }

        public InvoiceProductDetailsVm GetInvoiceProductDetails(long invoiceId)
        {
            var data = new InvoiceDataFn(GlobalData);
            return data.GetInvoiceProductDetails(invoiceId);
        }
    }
}
