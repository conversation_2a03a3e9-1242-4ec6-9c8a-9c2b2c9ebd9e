﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;
using PmsCore.PDFGeneration.Interfaces;

namespace PmsBusiness
{
    public class OutpassFunctions
    {
        public GlobalDataEntity GlobalData;
        public OutpassFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public PaginatedResult<OutpassMasterVm> GetAllOutpasswithfilters(OutpassFilterVm filters)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.GetAllOutpasswithfilters(filters);
        }
        public OutpassMasterVm GetOutpassById(long id)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.GetOutpassById(id);
        }
        public ApiFunctionResponseVm AddOutpasss(OutpassMasterVm Outpass)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.AddOutpasss(Outpass);
        }
        public ApiFunctionResponseVm ModifyOutpass(OutpassMasterVm Outpass)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.ModifyOutpass(Outpass);
        }
        public ApiFunctionResponseVm InOutpass(OutpassMasterVm Outpass)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.InOutpass(Outpass);
        }
        public List<OutPassPurposeMasterVm> GetAllOutPassPurposes()
        {
            var data = new OutpassDataFn(GlobalData);
            return data.GetAllOutPassPurposes();
        }
        public ApiFunctionResponseVm AddUpdateOutPassPurpose(OutPassPurposeMasterVm outpass)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.AddUpdateOutPassPurpose(outpass);
        }
        public ApiFunctionResponseVm DeleteOutPassPurpose(long id)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.DeleteOutPassPurpose(id);
        }
        public ApiFunctionResponseVm OutpassStatusActions(OutpassStatusActionVm actionVm)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.OutpassStatusActions(actionVm);
        }
        public List<OutpassStatusHistoryVm> GetOutpassTimeline(long outpassId)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.GetOutpassTimeline(outpassId);
        }
        public ApiFunctionResponseVm GetOutPassPdf(long outpassId, IPdfService pdfService)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.GetOutPassPdf(outpassId, pdfService);
        }
        public BarcodeValidationResult ValidateBarcodeLabelsForBatch(long stockProductId)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.ValidateBarcodeLabelsForBatch(stockProductId);
        }
        public OutpassPrintDataVm GetOutpassPrintData(long outpassId)
        {
            var data = new OutpassDataFn(GlobalData);
            return data.GetOutpassPrintData(outpassId);
        }
    }
}
