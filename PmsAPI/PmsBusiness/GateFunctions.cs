﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;

namespace PmsBusiness
{
    public class GateFunctions
    {
        public GlobalDataEntity GlobalData;
        public GateFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<GateInVm> GetGateInRecords()
        {
            var data = new GateDataFn(GlobalData);
            return data.GetGateInRecords();
        }
        public List<GateInVm> GetGateInRecordsWithFilter(GateInFilter filters)
        {
            var data = new GateDataFn(GlobalData);
            return data.GetGateInRecordsWithFilter(filters);
        }
        public GateInVm GetGateInRecordById(long GateInId)
        {
            var data = new GateDataFn(GlobalData);
            return data.GetGateInRecordById(GateInId);
        }

        public ApiFunctionResponseVm AddGateInRecord(GateInVm gatein)
        {
            var data = new GateDataFn(GlobalData);
            return data.AddGateInRecord(gatein);
        }
        public ApiFunctionResponseVm AddGateInRecordForSaleOrderDispatch(GateInVm gatein)
        {
            var data = new GateDataFn(GlobalData);
            return data.AddGateInRecordForSaleOrderDispatch(gatein);
        }
        public ApiFunctionResponseVm IssueGatePass(GateInVm gatein)
        {
            var data = new GateDataFn(GlobalData);
            return data.IssueGatePass(gatein);
        }
        public ApiFunctionResponseVm GateOut(GateInVm gatein)
        {
            var data = new GateDataFn(GlobalData);
            return data.GateOut(gatein);
        }
        public GateInVm GetVehicleStatus(long vehicleId)
        {
            var data = new GateDataFn(GlobalData);
            return data.GetVehicleStatus(vehicleId);
        }
    }
}
