﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;

namespace PmsBusiness
{
    public class ProductCategoryFunctions
    {
        public GlobalDataEntity GlobalData;
        public ProductCategoryFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<ProductCategoryMasterVm> GetAllProductCategories()
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.GetAllProductCategories();
        }
        public List<ProductCategoryListingVm> GetAllProductCategoriesForListing()
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.GetAllProductCategoriesForListing();
        }
        public List<ProductFirstSubCategoryMasterVm> GetAllProductFirstSubCategories()
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.GetAllProductFirstSubCategories();
        }
        public List<ProductSecSubCategoryMasterVm> GetAllProducSecSubCategories()
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.GetAllProducSecSubCategories();
        }

        public ApiFunctionResponseVm AddUpdateProductCategory(ProductCategoryMasterVm ProductCategory)
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.AddUpdateProductCategory(ProductCategory);
        }

        public ApiFunctionResponseVm AddUpdateProductFirstSubCategory(ProductFirstSubCategoryMasterVm ProductCategory)
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.AddUpdateProductFirstSubCategory(ProductCategory);
        }

        public ApiFunctionResponseVm AddUpdateProductSecSubCategory(ProductSecSubCategoryMasterVm ProductCategory)
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.AddUpdateProductSecSubCategory(ProductCategory);
        }

        public ApiFunctionResponseVm DeleteProductCategory(long ProductCategoryId)
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.DeleteProductCategory(ProductCategoryId);
        }

        public ApiFunctionResponseVm DeleteProductFirstSubCategory(long ProductCategoryId)
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.DeleteProductFirstSubCategory(ProductCategoryId);
        }

        public ApiFunctionResponseVm DeleteProductSecSubCategory(long ProductCategoryId)
        {
            var data = new ProductCategoryDataFn(GlobalData);
            return data.DeleteProductSecSubCategory(ProductCategoryId);
        }
    }
}
