﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class MeasurementConversionFunctions
    {
        public List<MeasurementConversionMasterVm> GetAllMeasurementConversion()
        {
            var data = new MeasurementConversionDataFn();
            return data.GetAllMeasurementConversion();
        }

        public ApiFunctionResponseVm AddUpdateMeasurementConversion(MeasurementConversionMasterVm br)
        {
            var data = new MeasurementConversionDataFn();
            return data.AddUpdateMeasurementConversion(br);
        }
    }
}
