using PmsCommon;
using PmsData.DataFn;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;

namespace PmsBusiness
{
    /// <summary>
    /// Dashboard Configuration Business Functions
    /// Following PMS business function naming pattern
    /// </summary>
    public class DashboardConfigFunctions
    {
        private readonly GlobalDataEntity _globalData;

        public DashboardConfigFunctions(GlobalDataEntity globalData)
        {
            _globalData = globalData;
        }

        /// <summary>
        /// Get user's dashboard configuration with fallback mechanisms
        /// </summary>
        public DashboardConfigResponse GetUserDashboardConfig(string userId, string dashboardType)
        {
            try
            {
                var data = new DashboardConfigDataFn(_globalData);
                var config = data.GetUserDefaultConfig(userId, dashboardType);

                return new DashboardConfigResponse
                {
                    Success = true,
                    Message = config.ConfigId == -1 ? "Using fallback configuration" : "Configuration loaded successfully",
                    Data = config
                };
            }
            catch (Exception ex)
            {
                // PMS pattern: Return error response instead of throwing
                return new DashboardConfigResponse
                {
                    Success = false,
                    Message = $"Error loading dashboard configuration: {ex.Message}",
                    Data = null
                };
            }
        }

        /// <summary>
        /// Save user dashboard configuration
        /// </summary>
        public DashboardConfigResponse SaveUserDashboardConfig(SaveDashboardConfigRequest request, string userId)
        {
            try
            {
                // Validate request
                if (string.IsNullOrEmpty(request.DashboardType) || string.IsNullOrEmpty(request.ConfigJson))
                {
                    return new DashboardConfigResponse
                    {
                        Success = false,
                        Message = "Dashboard type and configuration JSON are required",
                        Data = null
                    };
                }

                var data = new DashboardConfigDataFn(_globalData);
                
                var config = new UserDashboardConfigVm
                {
                    UserId = userId,
                    DashboardType = request.DashboardType,
                    ConfigJson = request.ConfigJson,
                    ConfigName = request.ConfigName ?? "User Configuration",
                    Description = request.Description ?? "",
                    IsDefault = request.IsDefault,
                    Version = 1,
                    Tags = request.Tags ?? ""
                };

                var configId = data.SaveUserDashboardConfig(config);
                config.ConfigId = configId;

                return new DashboardConfigResponse
                {
                    Success = true,
                    Message = "Configuration saved successfully",
                    Data = config
                };
            }
            catch (Exception ex)
            {
                return new DashboardConfigResponse
                {
                    Success = false,
                    Message = $"Failed to save configuration: {ex.Message}",
                    Data = null
                };
            }
        }

        /// <summary>
        /// Update existing dashboard configuration
        /// </summary>
        public DashboardConfigResponse UpdateUserDashboardConfig(UpdateDashboardConfigRequest request, string userId)
        {
            try
            {
                // Validate request
                if (request.ConfigId <= 0 || string.IsNullOrEmpty(request.ConfigJson))
                {
                    return new DashboardConfigResponse
                    {
                        Success = false,
                        Message = "Valid configuration ID and JSON are required",
                        Data = null
                    };
                }

                var data = new DashboardConfigDataFn(_globalData);
                
                var config = new UserDashboardConfigVm
                {
                    ConfigId = request.ConfigId,
                    UserId = userId,
                    DashboardType = request.DashboardType,
                    ConfigJson = request.ConfigJson,
                    ConfigName = request.ConfigName,
                    Description = request.Description,
                    IsDefault = request.IsDefault,
                    Version = 1,
                    Tags = request.Tags
                };

                var success = data.UpdateUserDashboardConfig(config);

                if (success)
                {
                    return new DashboardConfigResponse
                    {
                        Success = true,
                        Message = "Configuration updated successfully",
                        Data = config
                    };
                }
                else
                {
                    return new DashboardConfigResponse
                    {
                        Success = false,
                        Message = "Configuration not found or update failed",
                        Data = null
                    };
                }
            }
            catch (Exception ex)
            {
                return new DashboardConfigResponse
                {
                    Success = false,
                    Message = $"Failed to update configuration: {ex.Message}",
                    Data = null
                };
            }
        }

        /// <summary>
        /// Get all user dashboard configurations
        /// </summary>
        public DashboardConfigResponse GetAllUserDashboardConfigs(string userId, string dashboardType)
        {
            try
            {
                var data = new DashboardConfigDataFn(_globalData);
                var configs = data.GetAllUserConfigs(userId, dashboardType);

                return new DashboardConfigResponse
                {
                    Success = true,
                    Message = "Configurations retrieved successfully",
                    DataList = configs
                };
            }
            catch (Exception ex)
            {
                return new DashboardConfigResponse
                {
                    Success = false,
                    Message = $"Error retrieving configurations: {ex.Message}",
                    DataList = new List<UserDashboardConfigVm>()
                };
            }
        }

        /// <summary>
        /// Delete user dashboard configuration
        /// </summary>
        public DashboardConfigResponse DeleteUserDashboardConfig(long configId, string userId)
        {
            try
            {
                if (configId <= 0)
                {
                    return new DashboardConfigResponse
                    {
                        Success = false,
                        Message = "Valid configuration ID is required",
                        Data = null
                    };
                }

                var data = new DashboardConfigDataFn(_globalData);
                var success = data.DeleteUserDashboardConfig(configId, userId);

                if (success)
                {
                    return new DashboardConfigResponse
                    {
                        Success = true,
                        Message = "Configuration deleted successfully",
                        Data = null
                    };
                }
                else
                {
                    return new DashboardConfigResponse
                    {
                        Success = false,
                        Message = "Configuration not found or delete failed",
                        Data = null
                    };
                }
            }
            catch (Exception ex)
            {
                return new DashboardConfigResponse
                {
                    Success = false,
                    Message = $"Failed to delete configuration: {ex.Message}",
                    Data = null
                };
            }
        }

        /// <summary>
        /// Set configuration as default for user
        /// </summary>
        public DashboardConfigResponse SetDefaultConfig(long configId, string userId, string dashboardType)
        {
            try
            {
                if (configId <= 0)
                {
                    return new DashboardConfigResponse
                    {
                        Success = false,
                        Message = "Valid configuration ID is required",
                        Data = null
                    };
                }

                var data = new DashboardConfigDataFn(_globalData);
                var success = data.SetAsDefaultConfig(configId, userId, dashboardType);

                if (success)
                {
                    return new DashboardConfigResponse
                    {
                        Success = true,
                        Message = "Configuration set as default successfully",
                        Data = null
                    };
                }
                else
                {
                    return new DashboardConfigResponse
                    {
                        Success = false,
                        Message = "Configuration not found or set default failed",
                        Data = null
                    };
                }
            }
            catch (Exception ex)
            {
                return new DashboardConfigResponse
                {
                    Success = false,
                    Message = $"Failed to set default configuration: {ex.Message}",
                    Data = null
                };
            }
        }

        /// <summary>
        /// Reset to system default configuration
        /// </summary>
        public DashboardConfigResponse ResetToSystemDefault(string userId, string dashboardType)
        {
            try
            {
                var data = new DashboardConfigDataFn(_globalData);
                var systemDefault = data.GetSystemDefault(dashboardType);

                if (systemDefault != null)
                {
                    // Create a new user configuration based on system default
                    var request = new SaveDashboardConfigRequest
                    {
                        DashboardType = dashboardType,
                        ConfigJson = systemDefault.ConfigJson,
                        ConfigName = "Reset to System Default",
                        Description = "Configuration reset to system default",
                        IsDefault = true,
                        Tags = "system,reset"
                    };

                    return SaveUserDashboardConfig(request, userId);
                }
                else
                {
                    return new DashboardConfigResponse
                    {
                        Success = false,
                        Message = "System default configuration not available",
                        Data = null
                    };
                }
            }
            catch (Exception ex)
            {
                return new DashboardConfigResponse
                {
                    Success = false,
                    Message = $"Failed to reset to system default: {ex.Message}",
                    Data = null
                };
            }
        }
    }
}
