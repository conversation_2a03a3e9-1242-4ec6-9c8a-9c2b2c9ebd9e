﻿using PmsEntity.ViewModel;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsData.DataFn;
using PmsCommon;

namespace PmsBusiness
{
    public class CommonFunctions
    {
        public GlobalDataEntity GlobalData;
        public CommonFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<MeasureUnitMasterVm> GetMeasureUnits()
        {
            var data = new CommonDataFn(GlobalData);
            return data.GetMeasureUnits();
        }

        public ApiFunctionResponseVm AddUpdateMeasureUnit(MeasureUnitMasterVm br)
        {
            var data = new CommonDataFn(GlobalData);
            return data.AddUpdateMeasureUnit(br);
        }
        public ApiFunctionResponseVm DisableMeasureUnit(int id)
        {
            var data = new CommonDataFn(GlobalData);
            return data.DisableMeasureUnit(id);
        }
        public StorageTokenVm GetStorageTokenForGate()
        {
            var data = new CommonDataFn(GlobalData);
            return data.GetStorageTokenForGate();
        }

        public StorageTokenVm GetStorageTokenForInvoice()
        {
            var data = new CommonDataFn(GlobalData);
            return data.GetStorageTokenForInvoice();
        }
        public StorageTokenVm GetStorageTokenForPOUpload()
        {
            var data = new CommonDataFn(GlobalData);
            return data.GetStorageTokenForPOUpload();
        }
        public List<string> GetCurrency()
        {
            var data = new CommonDataFn(GlobalData);
            return data.GetCurrency();
        }
        public List<PackagingTypeMasterVm> GetAllPackagingTypes()
        {
            var data = new CommonDataFn(GlobalData);
            return data.GetAllPackagingTypes();
        }
        public ApiFunctionResponseVm AddUpdatePackagingType(PackagingTypeMasterVm br)
        {
            var data = new CommonDataFn(GlobalData);
            return data.AddUpdatePackagingType(br);
        }
        public ApiFunctionResponseVm DisablePackagingType(int id)
        {
            var data = new CommonDataFn(GlobalData);
            return data.DisablePackagingType(id);
        }
    }
}
