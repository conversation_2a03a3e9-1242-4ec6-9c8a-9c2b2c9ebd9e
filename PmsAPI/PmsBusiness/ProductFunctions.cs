﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;

namespace PmsBusiness
{
    public class ProductFunctions
    {
        public GlobalDataEntity GlobalData;
        public ProductFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<ProductMasterVm> GetAllProducts()
        {
            var data = new ProductDataFn(GlobalData);
            return data.GetAllProducts();
        }

        public ApiFunctionResponseVm AddUpdateProduct(ProductMasterVm product)
        {
            var data = new ProductDataFn(GlobalData);
            return data.AddUpdateProduct(product);
        }

        public ApiFunctionResponseVm DeleteProduct(long productid)
        {
            var data = new ProductDataFn(GlobalData);
            return data.DeleteProduct(productid);
        }


        public decimal? GetProductDefaultPerUnitCost(long productId)
        {
            var data = new ProductDataFn(GlobalData);
            return data.GetProductDefaultPerUnitCost(productId);
        }
        public List<ProductMasterVm> GetProductsByCategories(SearchParamsProductCategoryReportVm filters)
        {
            var data = new ProductDataFn(GlobalData);
            return data.GetProductsByCategories(filters);
        }
        public List<ProductMasterVm> GetProductsByCategoryNames(SearchParamsProductCategoryByNameVm filters)
        {
            var data = new ProductDataFn(GlobalData);
            return data.GetProductsByCategoryNames(filters);
        }

        public List<ProductMasterVm> GetAllProductsByProductType(string producttype)
        {
            var data = new ProductDataFn(GlobalData);
            return data.GetAllProductsByProductType(producttype);
        }
    }
}
