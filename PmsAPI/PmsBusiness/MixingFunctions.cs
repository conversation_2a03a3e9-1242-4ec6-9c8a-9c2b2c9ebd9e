﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class MixingFunctions
    {
        public List<MixingMasterVm> GetAllMixings()
        {
            var data = new MixingDataFn();
            return data.GetAllMixings();
        }

        public ApiFunctionResponseVm AddMixings(MixingMasterVm Mixing)
        {
            var data = new MixingDataFn();
            return data.AddMixings(Mixing);
        }
        public ApiFunctionResponseVm UpdateMixings(MixingMasterVm Mixing)
        {
            var data = new MixingDataFn();
            return data.UpdateMixings(Mixing);
        }
        public ApiFunctionResponseVm DeleteMixing(MixingMasterVm Mixing)
        {
            var data = new MixingDataFn();
            return data.DeleteMixing(Mixing);
        }
    }
}
