﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class RackFunctions
    {
        public List<RackMasterVm> GetAllRacks()
        {
            var data = new RackDataFn();
            return data.GetAllRacks();
        }
        public List<RackMasterVm> GetAllRacksByStoreId(long storeId)
        {
            var data = new RackDataFn();
            return data.GetAllRacksByStoreId(storeId);
        }

        public ApiFunctionResponseVm AddUpdateRack(RackMasterVm Rack)
        {
            var data = new RackDataFn();
            return data.AddUpdateRack(Rack);
        }
    }
}
