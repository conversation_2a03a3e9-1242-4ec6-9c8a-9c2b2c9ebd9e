﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class UserFunctions
    {
        //public UserRoleVm GetUserRole(string username)
        //{
        //    var data = new UserDataFn();
        //    return data.GetUserRole(username);
        //}
        public List<long> GetUserStores(string username)
        {
            var data = new UserDataFn();
            return data.GetUserStores(username);
        }

        //public List<UserRoleVm> GetAllUserRole()
        //{
        //    var data = new UserDataFn();
        //    return data.GetAllUserRole();

        //}
        //public ApiFunctionResponseVm AddUpdateRole(UserRoleVm br)
        //{
        //    var data = new UserDataFn();
        //    return data.AddUpdateRole(br);
        //}
        public ApiFunctionResponseVm AddUpdateUserStoreMapping(List<UserStoreMappingTableVm> br)
        {
            var data = new UserDataFn();
            return data.AddUpdateUserStoreMapping(br);
        }
    }
}
