﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using System.Collections.Generic;
using PmsData.Models;
using System.IO;
using PmsCommon;
using PmsCore.PDFGeneration.Interfaces;

namespace PmsBusiness
{
    public class PurchaseOrderFunctions
    {
        private readonly IPdfService _pdfService;
        public GlobalDataEntity GlobalData;
        public PurchaseOrderFunctions(GlobalDataEntity gd, IPdfService pdfService)
        {
            GlobalData = gd;
            _pdfService = pdfService;
        }
        public List<PurchaseOrderListVm> GetAllPurchaseOrders()
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.GetAllPurchaseOrders();
        }
        public List<PurchaseOrderVm> GetAllPurchaseOrdersWithFilters(PurchaseReportRequestVm filter)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.GetAllPurchaseOrdersWithFilters(filter);
        }

        public List<PurchaseOrderVm> GetAllPurchaseOrdersForList()
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.GetAllPurchaseOrdersForList();
        }

        public ApiFunctionResponseVm AddPurchaseOrder(PurchaseOrderVm po)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.AddPurchaseOrder(po);
        }

        public ApiFunctionResponseVm UpdatePurchaseOrder(PurchaseOrderVm po)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.UpdatePurchaseOrder(po);
        }
        public ApiFunctionResponseVm CancelPurchaseOrder(long poid)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.CancelPurchaseOrder(poid);
        }
        public ApiFunctionResponseVm ApprovePurchaseOrder(long poid)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.ApprovePurchaseOrder(poid);
        }
        public ApiFunctionResponseVm PurchaseOrderEventActions(POActionVm action)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.PurchaseOrderEventActions(action);
        }

        public PurchaseOrderVm GetPurchaseOrderById(long id)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.GetPurchaseOrderById(id);
        }
        public List<DemandTableVm> GetAllDemands()
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.GetAllDemands();
        }

        public List<DemandTableVm> GetAllDemandsWithFilter(DemandFilterRequestVm filter)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.GetAllDemandsWithFilter(filter);
        }

        public ApiFunctionResponseVm AddDemand(DemandTableVm po)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.AddDemand(po);
        }

        public ApiFunctionResponseVm ChangeDemandStatus(DemandTableVm po)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.ChangeDemandStatus(po);
        }
        public ApiFunctionResponseVm SendPOInEmail(Stream st, long poid, string[] emaillist)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.SendPOInEmail(st, poid, emaillist);
        }
        public List<PurchaseOrderTimelineVm> GetPurchaseOrderTimelineById(long id)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.GetPurchaseOrderTimelineById(id);
        }
        public List<POProductPriceHistoryVm> GetPOProductPriceHistory(POProductPriceHistoryRequestVm request)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.GetPOProductPriceHistory(request);
        }
        public ApiFunctionResponseVm GetPurchaseOrderPdf(long poid)
        {
            var data = new PurchaseOrderDataFn(GlobalData, _pdfService);
            return data.GetPurchaseOrderPdf(poid);
        }
    }
}
