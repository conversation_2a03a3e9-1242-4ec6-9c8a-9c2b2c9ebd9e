﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class SupplierFunctions
    {
        public List<SupplierMasterVm> GetAllSuppliers()
        {
            var data = new SuppliertDataFn();
            return data.GetAllSuppliers();
        }

        public ApiFunctionResponseVm AddUpdateSupplier(SupplierMasterVm supplier)
        {
            var data = new SuppliertDataFn();
            return data.AddUpdateSupplier(supplier);
        }

        public ApiFunctionResponseVm DeleteSupplier(long supplierId)
        {
            var data = new SuppliertDataFn();
            return data.DeleteSupplier(supplierId);
        }
    }
}
