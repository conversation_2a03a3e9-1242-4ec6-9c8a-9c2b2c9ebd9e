﻿using PmsCommon;
using PmsData.DataFn;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PmsBusiness
{
    public class WorkPlanFunctions
    {
        public GlobalDataEntity GlobalData;
        public WorkPlanFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ResultWorkPlanMasterList GetAllWorkPlan()
        {
            var data = new WorkPlanMasterFn(GlobalData);
            return data.GetAllWorkPlan();
        }
        public ResultWorkPlanMasterShortList GetAllWorkPlanReport(SearchParamWorkPlanMasterReportVm param)
        {
            var data = new WorkPlanMasterFn(GlobalData);
            return data.GetAllWorkPlanReport(param);
        }
        public ResultWorkPlanMasterList GetAllWorkPlanBysaleorderstatus(List<string> saleorderstatus)
        {
            var data = new WorkPlanMasterFn(GlobalData);
            return data.GetAllWorkPlanBysaleorderstatus(saleorderstatus);
        }
        public ResultWorkPlanMasterList GetAllWorkPlanBySaleOrderStatusForConsume(List<string> saleorderstatus)
        {
            var data = new WorkPlanMasterFn(GlobalData);
            return data.GetAllWorkPlanBySaleOrderStatusForConsume(saleorderstatus);
        }

        public ResultWorkPlanMaster GetAllWorkPlanById(long Id)
        {
            var data = new WorkPlanMasterFn(GlobalData);
            return data.GetAllWorkPlanById(Id);
        }
        public List<WorkPlanOrderVm> GetWorkPlanOrderByWorkplanId(long WorkPlanId)
        {
            var data = new WorkPlanOrderFn();
            return data.GetWorkPlanOrderByWorkplanId(WorkPlanId);
        }
        public List<WorkPlanOrderVm> GetPostProcessWorkPlanOrderByWorkplanId(long WorkPlanId)
        {
            var data = new WorkPlanOrderFn();
            return data.GetPostProcessWorkPlanOrderByWorkplanId(WorkPlanId);
        }
        public List<WorkPlanOrderVm> GetWorkPlanOrderByWorkplanIdForConsume(long WorkPlanId)
        {
            var data = new WorkPlanOrderFn();
            return data.GetWorkPlanOrderByWorkplanIdForConsume(WorkPlanId);
        }

        public ResultWorkPlanJumboMasterList GetWorkPlanJumboByWorkPlanOrdersId(long WorkPlanOrdersId)
        {
            var data = new WorkPlanJumboMasterFn(GlobalData);
            return data.GetWorkPlanJumboByWorkPlanOrdersId(WorkPlanOrdersId);
        }

        public ResultWorkPlanJumboMasterList GetAllWorkPlanJumbo()
        {
            var data = new WorkPlanJumboMasterFn(GlobalData);
            return data.GetAllWorkPlanJumbo();
        }
        public ResultWorkPlanJumboMasterList GetAllWorkPlanJumboWithFilters(JumboFinalInspectionFilter filter)
        {
            var data = new WorkPlanJumboMasterFn(GlobalData);
            return data.GetAllWorkPlanJumboWithFilters(filter);
        }

        public List<WorkPlanJumboMasterVm> GetJumboListWithInspectionCount()
        {
            var data = new WorkPlanJumboMasterFn(GlobalData);
            return data.GetJumboListWithInspectionCount();
        }

        public WorkPlanJumboMasterVm GetWorkPlanJumboByJumboNumber(string jumbonumber)
        {
            var data = new WorkPlanJumboMasterFn(GlobalData);
            return data.GetWorkPlanJumboByJumboNumber(jumbonumber);
        }

        //ADD Functons For All Work Plan

        public ResultWorkPlanMaster AddWorkPlanWithOrders(WorkPlanMasterVm model)
        {
            var data = new WorkPlanMasterFn(GlobalData);
            return data.AddWorkPlanWithOrders(model);
        }
        public ResultWorkPlanMaster ChangeWorkPlanForSingleOrder(WorkPlanMasterVm model)
        {
            var data = new WorkPlanMasterFn(GlobalData);
            return data.ChangeWorkPlanForSingleOrder(model);
        }
        public ResultWorkPlanJumboMaster AddWorkPlanJumbo(List<WorkPlanJumboMasterVm> model)
        {
            var data = new WorkPlanJumboMasterFn(GlobalData);
            return data.AddWorkPlanJumbo(model);
        }

        public ResultWorkPlanJumboMaster AddWorkPlanJumboSingleObj(WorkPlanJumboMasterVm model)
        {
            var data = new WorkPlanJumboMasterFn(GlobalData);
            return data.AddWorkPlanJumboSingleObj(model);
        }
        public List<WorkPlanJumboMasterVm> GetWorkPlanJumboForFinalInspection(JumboFinalInspectionFilter filter)
        {
            var data = new WorkPlanJumboMasterFn(GlobalData);
            return data.GetWorkPlanJumboForFinalInspection(filter);
        }

        public ApiFunctionResponseVm ReviewWorkplan(ReviewWorkPlanVm obj)
        {
            var data = new WorkPlanMasterFn(GlobalData);
            return data.ReviewWorkplan(obj);
        }

        public WorkPlanDetailPlanVm GetWorkPlanBysaleorderid(long SaleOrderId)
        {
            var data = new WorkPlanMasterFn(GlobalData);
            return data.GetWorkPlanBysaleorderid(SaleOrderId);
        }
        public ResultJumboPrintData GetJumboPrintByOrderIdJumboId(long SaleOrderId, long JumboId)
        {
            var data = new WorkPlanJumboMasterFn(GlobalData);
            return data.GetJumboPrintByOrderIdJumboId(SaleOrderId, JumboId);
        }
    }
}
