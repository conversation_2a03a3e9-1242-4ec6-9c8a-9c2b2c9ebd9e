﻿using PmsCommon;
using PmsData.DataFn;
using PmsEntity.ViewModel;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;

namespace PmsBusiness
{
    public class NotificationConfigFunction
    {
        public GlobalDataEntity GlobalData;
        public ILogger _logger;
        public NotificationConfigFunction(GlobalDataEntity gd, ILogger logger)
        {
            GlobalData = gd;
            _logger = logger;
        }

        public ApiFunctionResponseVm AddNotificationGroup(NotificationGroupsTableVm Notification)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.AddNotificationGroup(Notification);
        }
        public List<NotificationGroupsTableVm> GetAllNotificationGroup()
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.GetAllNotificationGroup();
        }

        public NotificationGroupDetailsVm GetNotificationGroupDetails(long notificationGroupUserId)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.GetNotificationGroupDetails(notificationGroupUserId);
        }
        public ApiFunctionResponseVm NotificationGroupUpdate(NotificationGroupsTableVm Notification)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.NotificationGroupUpdate(Notification);
        }
        public ApiFunctionResponseVm AddNotificationConfig(EmailConfigTableVm Mail)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.AddNotificationConfig(Mail);
        }
        public List<EmailConfigTableVm> GetNotificationConfig()
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.GetNotificationConfig();
        }
        public ApiFunctionResponseVm NotificationConfigEdit(EmailConfigTableVm Mail)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.NotificationConfigEdit(Mail);
        }

        public ApiFunctionResponseVm AddWhatsappTemplateMaster(WhatsAppTemplateMasterVm whatsAppTemplateMaster)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.AddWhatsappTemplateMaster(whatsAppTemplateMaster);
        }
        public List<WhatsAppTemplateMasterVm> GetWhatsappTemplateMaster()
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.GetWhatsappTemplateMaster();
        }
        public ApiFunctionResponseVm WhatsappTemplateMasterEdit(WhatsAppTemplateMasterVm whatsAppTemplateMaster)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.WhatsappTemplateMasterEdit(whatsAppTemplateMaster);
        }
        public ApiFunctionResponseVm NotificationTemplateParameterAddUpdate(NotificationTemplateParameterTableVm notificationTemplateParameterTableVm)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.NotificationTemplateParameterAddUpdate(notificationTemplateParameterTableVm);
        }
        public ApiFunctionResponseVm NotificationTemplateParameterDelete(long parameterId)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.NotificationTemplateParameterDelete(parameterId);
        }
        public ApiFunctionResponseVm CopyWhatsappTemplateParameter(WhatsAppTemplateMasterVm whatsAppTemplateMaster)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.CopyWhatsappTemplateParameter(whatsAppTemplateMaster);
        }

        public ApiFunctionResponseVm AddWhatsappConfig(WhatsAppConfigTableVm whatsAppConfigTable)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.AddWhatsappConfig(whatsAppConfigTable);
        }
        public List<WhatsAppConfigTableVm> GetWhatsappConfig()
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.GetWhatsappConfig();
        }
        public ApiFunctionResponseVm WhatsappconfigEdit(WhatsAppConfigTableVm whatsAppConfigTableVm)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.WhatsappconfigEdit(whatsAppConfigTableVm);
        }
        public ApiFunctionResponseVm AddEmailGroup(EmailGroupMappingTableVm emailGroupMappingTableVm)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.AddEmailGroupMapping(emailGroupMappingTableVm);
        }
        public List<EmailGroupMappingTableVm> GetEmailGroupMappings()
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.GetEmailGroupMappings();
        }
        public ApiFunctionResponseVm EmailGroupEdit(EmailGroupMappingTableVm emailGroupMappingTableVm)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.EmailGropMappingEdit(emailGroupMappingTableVm);
        }

        public ApiFunctionResponseVm UpdateNotificationSchedule(UpdateNotificationScheduleVm updateScheduleVm)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.UpdateNotificationSchedule(updateScheduleVm);
        }

        #region NotificationTemplateConfiguration Methods

        public List<NotificationTemplateConfigurationVm> GetNotificationTemplateConfigurations()
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.GetNotificationTemplateConfigurations();
        }

        public List<NotificationTypeListVm> GetNotificationTypes()
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.GetNotificationTypes();
        }

        public ApiFunctionResponseVm AddNotificationTemplateConfiguration(NotificationTemplateConfigurationVm configuration)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.AddNotificationTemplateConfiguration(configuration);
        }

        public ApiFunctionResponseVm UpdateNotificationTemplateConfiguration(NotificationTemplateConfigurationVm configuration)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.UpdateNotificationTemplateConfiguration(configuration);
        }

        public ApiFunctionResponseVm DeleteNotificationTemplateConfiguration(long configurationId)
        {
            var data = new NotificationConfigDataFn(GlobalData, _logger);
            return data.DeleteNotificationTemplateConfiguration(configurationId);
        }

        #endregion
    }
}
