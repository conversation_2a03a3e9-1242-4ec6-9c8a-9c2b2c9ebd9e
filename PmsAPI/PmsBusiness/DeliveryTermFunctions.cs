﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class DeliveryTermFunctions
    {
        public List<DeliveryTermMasterVm> GetAllDeliveryTerms()
        {
            var data = new DeliveryTermDataFn();
            return data.GetAllDeliveryTerms();
        }

        public ApiFunctionResponseVm AddUpdateDeliveryTerm(DeliveryTermMasterVm DeliveryTerm)
        {
            var data = new DeliveryTermDataFn();
            return data.AddUpdateDeliveryTerm(DeliveryTerm);
        }
        public ApiFunctionResponseVm DeleteDeliveryTerm(DeliveryTermMasterVm DeliveryTerm)
        {
            var data = new DeliveryTermDataFn();
            return data.DeleteDeliveryTerm(DeliveryTerm);
        }
    }
}
