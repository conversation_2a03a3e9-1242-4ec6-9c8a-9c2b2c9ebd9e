﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class TumblingFunctions
    {
        public List<TumblingMasterVm> GetAllTumblings()
        {
            var data = new TumblingDataFn();
            return data.GetAllTumblings();
        }

        public ApiFunctionResponseVm AddUpdateTumbling(TumblingMasterVm Tumbling)
        {
            var data = new TumblingDataFn();
            return data.AddUpdateTumbling(Tumbling);
        }
    }
}
