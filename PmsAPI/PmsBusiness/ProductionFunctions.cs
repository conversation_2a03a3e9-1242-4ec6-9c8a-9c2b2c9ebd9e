﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;

namespace PmsBusiness
{
    public class ProductionFunctions
    {
        private readonly GlobalDataEntity _gd;
        public ProductionFunctions(GlobalDataEntity gd)
        {
            _gd = gd;
        }
        public ApiFunctionResponseVm GetProductionDowntimeList(ProductionDowntimeFilterVm filter)
        {
            var data = new ProductionDataFn(_gd);
            return data.GetProductionDowntimeList(filter);
        }
        public ApiFunctionResponseVm AddUpdateProductionDowntime(ProductionDowntimeTableVm productionDowntime)
        {
            var data = new ProductionDataFn(_gd);
            return data.AddUpdateProductionDowntime(productionDowntime);
        }
        public ApiFunctionResponseVm GetProductionDowntimeById(long productionDowntimeId)
        {
            var data = new ProductionDataFn(_gd);
            return data.GetProductionDowntimeById(productionDowntimeId);
        }
        public ApiFunctionResponseVm DeleteProductionDowntime(long productionDowntimeId)
        {
            var data = new ProductionDataFn(_gd);
            return data.DeleteProductionDowntime(productionDowntimeId);
        }
        public ApiFunctionResponseVm GetProductionDowntimeReasonList()
        {
            var data = new ProductionDataFn(_gd);
            return data.GetProductionDowntimeReasonList();
        }
        public ApiFunctionResponseVm GetProductionDowntimeActiveReasonList()
        {
            var data = new ProductionDataFn(_gd);
            return data.GetProductionDowntimeActiveReasonList();
        }
        public ApiFunctionResponseVm AddUpdateProductionDowntimeReason(ProductionDowntimeReasonMasterVm productionDowntimeReason)
        {
            var data = new ProductionDataFn(_gd);
            return data.AddUpdateProductionDowntimeReason(productionDowntimeReason);
        }
        public ApiFunctionResponseVm GetProductionDowntimeReasonById(long productionDowntimeReasonId)
        {
            var data = new ProductionDataFn(_gd);
            return data.GetProductionDowntimeReasonById(productionDowntimeReasonId);
        }
        public ApiFunctionResponseVm ProductionDowntimeReasonStatusChange(ProductionDowntimeReasonMasterVm productionDowntimeReason)
        {
            var data = new ProductionDataFn(_gd);
            return data.ProductionDowntimeReasonStatusChange(productionDowntimeReason);
        }
        public ApiFunctionResponseVm GetProductionDowntimeScheduleById(long productionDowntimeScheduleId)
        {
            var data = new ProductionDataFn(_gd);
            return data.GetProductionDowntimeScheduleById(productionDowntimeScheduleId);
        }
        public ApiFunctionResponseVm GetProductionDowntimeScheduleListByReasonId(long productionDowntimeReasonId)
        {
            var data = new ProductionDataFn(_gd);
            return data.GetProductionDowntimeScheduleListByReasonId(productionDowntimeReasonId);
        }
        public ApiFunctionResponseVm BulkCreateDowntimeSchedule(List<ProductionDowntimeScheduledVm> models)
        {
            var data = new ProductionDataFn(_gd);
            return data.BulkCreateDowntimeSchedule(models);
        }
        public ApiFunctionResponseVm DeleteProductionDowntimeSchedule(long productionDowntimeScheduleId)
        {
            var data = new ProductionDataFn(_gd);
            return data.DeleteProductionDowntimeSchedule(productionDowntimeScheduleId);
        }
        public ApiFunctionResponseVm AddUpdateProductionDowntimeSchedule(ProductionDowntimeScheduledVm productionDowntimeSchedule)
        {
            var data = new ProductionDataFn(_gd);
            return data.AddUpdateProductionDowntimeSchedule(productionDowntimeSchedule);
        }
    }
}