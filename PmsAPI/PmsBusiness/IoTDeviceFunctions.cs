﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;

namespace PmsBusiness
{
    public class IoTDeviceFunctions
    {
        public GlobalDataEntity GlobalData;
        public IoTDeviceFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<KnittingFabricWeightInputTableVm> GetAllKnittingFabricStocks()
        {
            var data = new IoTDeviceDataFn(GlobalData);
            return data.GetAllKnittingFabricStocks();
        }
        public List<KnittingFabricWeightInputTableVm> GetAllKnittingFabricStocksWithFilter(KnittingFabricWeightInputTableFilterVm filter)
        {
            var data = new IoTDeviceDataFn(GlobalData);
            return data.GetAllKnittingFabricStocksWithFilter(filter);
        }
        public List<KnittingFabricWeightInputTableVm> GetKnittingFabricStocksByIds(List<long> fabricStockIds)
        {
            var data = new IoTDeviceDataFn(GlobalData);
            return data.GetKnittingFabricStocksByIds(fabricStockIds);
        }
        public ApiFunctionResponseVm AddKnittingFabricWithWeight(KnittingFabricWeightInputTableVm item)
        {
            var data = new IoTDeviceDataFn(GlobalData);
            return data.AddKnittingFabricWithWeight(item);
        }

        public ApiFunctionResponseVm DeleteKnittingFabricRecord(long recordId)
        {
            var data = new IoTDeviceDataFn(GlobalData);
            return data.DeleteKnittingFabricRecord(recordId);
        }
    }
}
