﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class BranchFunctions
    {
        public List<BranchMasterVm> GetAllBranches()
        {
            var data = new BranchDataFn();
            return data.GetAllBranches();
        }

        public ApiFunctionResponseVm AddUpdateBranch(BranchMasterVm Branch)
        {
            var data = new BranchDataFn();
            return data.AddUpdateBranch(Branch);
        }
    }
}
