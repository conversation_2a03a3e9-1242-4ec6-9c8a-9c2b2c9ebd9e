﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;

namespace PmsBusiness
{
    public class StockFunctions
    {
        public GlobalDataEntity GlobalData;
        public StockFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public StockReportVm GetstockReport()
        {
            var data = new StockDataFn(GlobalData);
            return data.GetstockReport();
        }
        public ApiFunctionResponseVm AddStock(StockVm stock)
        {
            var data = new StockDataFn(GlobalData);
            return data.AddStock(stock);
        }
        public ApiFunctionResponseVm StockInspection(StockVm stock)
        {
            var data = new StockDataFn(GlobalData);
            return data.StockInspection(stock);
        }
        public ApiFunctionResponseVm StockQualityInspection(StockVm stock)
        {
            var data = new StockDataFn(GlobalData);
            return data.StockQualityInspection(stock);
        }
        public List<StockVm> GetAllStocks()
        {
            var data = new StockDataFn(GlobalData);
            return data.GetAllStocks();
        }
        public List<StockVm> GetAllStocksWithFilters(StockRequestVm filters)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetAllStocksWithFilters(filters);
        }
        public List<InvoiceMasterVm> GetAllUnstockedInvoices()
        {
            var data = new StockDataFn(GlobalData);
            return data.GetAllUnstockedInvoices();
        }

        public StockVm GetstockById(long stockId)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetstockById(stockId);
        }
        public ApiFunctionResponseVm GetPendingStockInspectionById(long stockId)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetPendingStockInspectionById(stockId);
        }
        public ApiFunctionResponseVm SaveStockAllocation(StockProductTableVm stockproduct)
        {
            var data = new StockDataFn(GlobalData);
            return data.SaveStockAllocation(stockproduct);
        }
        public ApiFunctionResponseVm SaveStockProductManageRejected(List<StockProductManageRejectedVm> stockproduct)
        {
            var data = new StockDataFn(GlobalData);
            return data.SaveStockProductManageRejected(stockproduct);
        }
        public ApiFunctionResponseVm AddStockMaster(StockProductTableMasterVm item)
        {
            var data = new StockDataFn(GlobalData);
            return data.AddStockMaster(item);
        }

        public List<ProductStockReportVm> GetProductWisestock()
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductWisestock();
        }

        public List<SupplierProductNameVm> GetSupplierProductNames(long supplierId, long? productId = null)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetSupplierProductNames(supplierId, productId);
        }

        public List<ProductSupplierMappingVm> GetSupplierMappings(long supplierId)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetSupplierMappings(supplierId);
        }

        public ApiFunctionResponseVm CreateOrUpdateProductSupplierMapping(CreateProductSupplierMappingVm mapping)
        {
            var data = new StockDataFn(GlobalData);
            return data.CreateOrUpdateProductSupplierMapping(mapping);
        }

        public ApiFunctionResponseVm UpdateSupplierProductName(UpdateSupplierProductNameVm updateRequest)
        {
            var data = new StockDataFn(GlobalData);
            return data.UpdateSupplierProductName(updateRequest);
        }
        public List<ProductStockReportVm> GetProductWisestockByFilter(SearchParamsProductCategoryReportVm param)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductWisestockByFilter(param);
        }
        public List<ProductStockReportVm> GetProductWisestockWithSupplier(StockAvailabilityReportRequestVm filters)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductWisestockWithSupplier(filters);
        }
        public List<ProductStockReportVm> GetProductStockWithSupplierByStoreId(long storeid)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductStockWithSupplierByStoreId(storeid);
        }
        public List<ProductStockReportVm> GetProductStockByStoreIdByProductId(long storeid, long productid)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductStockByStoreIdByProductId(storeid, productid);
        }

        public List<ProductStockStoreReportVm> GetProductWiseStorestock()
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductWiseStorestock();
        }
        public List<ProductStockStoreRackReportVm> GetProductWiseStoreRackstock()
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductWiseStoreRackstock();
        }
        public List<ProductStockStoreRackReportVm> GetProductWiseStoreRackstockByStoreId(long storeId)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductWiseStoreRackstockByStoreId(storeId);
        }
        public List<ProductStockReportVm> GetProductWisestockWithSupplierWithConsumption(SearchParamsProductStockReportVm param)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductWisestockWithSupplierWithConsumption(param);
        }

        public ApiFunctionResponseVm UpdateStockProductPriceByStockProductId(StockProductPriceVm stockproductvm)
        {
            var data = new StockDataFn(GlobalData);
            return data.UpdateStockProductPriceByStockProductId(stockproductvm);
        }
        public List<StockLabelResponseVm> GenerateProductStockLabel(StockLabelTableVm label)
        {
            var data = new StockDataFn(GlobalData);
            return data.GenerateProductStockLabel(label);
        }
        public ApiFunctionResponseVm UpdateStockProductLabel(StockLabelTableVm label)
        {
            var data = new StockDataFn(GlobalData);
            return data.UpdateStockProductLabel(label);
        }

        public List<StockLabelTableVm> SplitProductStockLabel(SplitLabelRequestVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.SplitProductStockLabel(request);
        }
        public ApiFunctionResponseVm GetFullStockDetailByLabelSerialNo(StockLabelSearchVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetFullStockDetailByLabelSerialNo(request);
        }
        public List<StockLabelTimelineVm> GetStockLabelTimeline(long stockLabelId)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetStockLabelTimeline(stockLabelId);
        }

        public List<StockLabelComprehensiveTimelineVm> GetStockLabelComprehensiveTimeline(long stockLabelId)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetStockLabelComprehensiveTimeline(stockLabelId);
        }
        public StockLabelDetailsListingVm GetAllStockLabelsbyFilters(StockLabelListFilterVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetAllStockLabelsbyFilters(request);
        }
        public ApiFunctionResponseVm GetStockLabelBySerialNo(StockLabelSearchVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetStockLabelBySerialNo(request);
        }
        public List<StockLabelResponseVm> GetStockLabelsByProductStock(StockLabelTableVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetStockLabelsByProductStock(request);
        }
        public List<StockLabelResponseVm> GetStockLabelsByBatchProductId(StockLabelTableVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetStockLabelsByBatchProductId(request);
        }
        public List<StockLabelResponseVm> GetStockLabelsByStockLabelId(List<long> StockLabelIds)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetStockLabelsByStockLabelId(StockLabelIds);
        }
        public ApiFunctionResponseVm UpdateStockLabelStatus(UpdateStockLabelStatusVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.UpdateStockLabelStatus(request);
        }
        public ProductAvailableStock GetProductAvailableStockByProductId(long productId)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductAvailableStockByProductId(productId);
        }
        public List<ProductAvailableStock> GetProductAvailableStockByProductIds(ProductAvailableStockRequestVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductAvailableStockByProductIds(request);
        }

        public List<StoreMasterVm> GetStoresForProduct(long productId)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetStoresForProduct(productId);
        }

        public List<RackMasterVm> GetRacksForProductInStore(long productId, long storeId)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetRacksForProductInStore(productId, storeId);
        }

        public List<ProductTransferTableVm> GetProductTransferHistory()
        {
            var data = new StockDataFn(GlobalData);
            return data.GetProductTransferHistory();
        }
        public ApiFunctionResponseVm ActionProductStockTransfer(ProductStockTransferRequestVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.ActionProductStockTransfer(request);
        }

        public SupplierProductMappingReportResponseVm GetSupplierProductMappingReport(SupplierProductMappingReportRequestVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.GetSupplierProductMappingReport(request);
        }
        public ApiFunctionResponseVm RequestProductStockTransfer(ProductStockTransferRequestVm request)
        {
            var data = new StockDataFn(GlobalData);
            return data.RequestProductStockTransfer(request);
        }
    }
}
