﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class BankFunctions
    {
        public List<BankMasterVm> GetAllBanks()
        {
            var data = new BankDataFn();
            return data.GetAllBanks();
        }

        public ApiFunctionResponseVm AddUpdateBank(BankMasterVm Bank)
        {
            var data = new BankDataFn();
            return data.AddUpdateBank(Bank);
        }
    }
}
