﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class EmbossingFunctions
    {
        public List<EmbossingMasterVm> GetAllEmbossings()
        {
            var data = new EmbossingDataFn();
            return data.GetAllEmbossings();
        }

        public ApiFunctionResponseVm AddUpdateEmbossing(EmbossingMasterVm Embossing)
        {
            var data = new EmbossingDataFn();
            return data.AddUpdateEmbossing(Embossing);
        }
        public ApiFunctionResponseVm DeleteEmbossing(EmbossingMasterVm Embossing)
        {
            var data = new EmbossingDataFn();
            return data.DeleteEmbossing(Embossing);
        }
    }
}
