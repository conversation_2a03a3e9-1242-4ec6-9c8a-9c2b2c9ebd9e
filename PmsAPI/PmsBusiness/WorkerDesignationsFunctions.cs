﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;

namespace PmsBusiness
{
    public class WorkerDesignationsFunctions
    {
        public GlobalDataEntity GlobalData;
        public WorkerDesignationsFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<WorkerDesignationMasterVm> GetAllWorkersDesignations()
        {
            var data = new WorkersDesignationDataFn(GlobalData);
            return data.GetAllWorkersDesignations();
        }

        public ApiFunctionResponseVm AddUpdateWorkerDesignation(WorkerDesignationMasterVm product)
        {
            var data = new WorkersDesignationDataFn(GlobalData);
            return data.AddUpdateWorkerDesignation(product);
        }

        public ApiFunctionResponseVm DeleteWorkerDesignation(long itemsid)
        {
            var data = new WorkersDesignationDataFn(GlobalData);
            return data.DeleteWorkerDesignation(itemsid);
        }
    }
}
