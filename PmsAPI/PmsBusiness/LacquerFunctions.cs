﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class LacquerFunctions
    {
        public List<LacquerMasterVm> GetAllLacquers()
        {
            var data = new LacquerDataFn();
            return data.GetAllLacquers();
        }

        public ApiFunctionResponseVm AddLacquers(LacquerMasterVm Lacquer)
        {
            var data = new LacquerDataFn();
            return data.AddLacquers(Lacquer);
        }
        public ApiFunctionResponseVm UpdateLacquers(LacquerMasterVm Lacquer)
        {
            var data = new LacquerDataFn();
            return data.UpdateLacquers(Lacquer);
        }
        public ApiFunctionResponseVm DeleteLacquer(LacquerMasterVm Lacquer)
        {
            var data = new LacquerDataFn();
            return data.DeleteLacquer(Lacquer);
        }
    }
}
