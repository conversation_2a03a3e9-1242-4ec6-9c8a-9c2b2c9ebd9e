﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsData.ViewModel;
using PmsCommon;

namespace PmsBusiness
{
    public class MBFormulationFunctions
    {
        public GlobalDataEntity GlobalData;
        public MBFormulationFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<MbformulationMasterVm> GetAllMBFormulationMaster()
        {
            var data = new MBFormulationDataFn(GlobalData);
            return data.GetAllMBFormulationMaster();
        }

        public ApiFunctionResponseVm AddMBFormulationMaster(MbformulationMasterVm mix)
        {
            var data = new MBFormulationDataFn(GlobalData);
            return data.AddMBFormulationMaster(mix);
        }
        public ApiFunctionResponseVm UpdateMBFormulationMaster(MbformulationProductTableVm mix)
        {
            var data = new MBFormulationDataFn(GlobalData);
            return data.UpdateMBFormulationMaster(mix);
        }
        public MbformulationProductTableVm GetMBFormulationProductById(long id)
        {
            var data = new MBFormulationDataFn(GlobalData);
            return data.GetMBFormulationProductById(id);
        }
        public ApiFunctionResponseVm AddMBFormulationProduct(MbformulationProductTableVm mix)
        {
            var data = new MBFormulationDataFn(GlobalData);
            return data.AddMBFormulationProduct(mix);
        }
    }
}
