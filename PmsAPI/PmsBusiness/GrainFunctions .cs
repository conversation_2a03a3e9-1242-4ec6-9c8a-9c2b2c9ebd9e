﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class GrainFunctions
    {
        public List<GrainMasterVm> GetAllGrains()
        {
            var data = new GrainDataFn();
            return data.GetAllGrains();
        }

        public ApiFunctionResponseVm AddUpdateGrain(GrainMasterVm Grain)
        {
            var data = new GrainDataFn();
            return data.AddUpdateGrain(Grain);
        }
        public ApiFunctionResponseVm DeleteGrain(GrainMasterVm Grain)
        {
            var data = new GrainDataFn();
            return data.DeleteGrain(Grain);
        }
    }
}
