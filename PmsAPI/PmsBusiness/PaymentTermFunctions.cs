﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class PaymentTermFunctions
    {
        public List<PaymentTermMasterVm> GetAllPaymentTerms()
        {
            var data = new PaymentTermDataFn();
            return data.GetAllPaymentTerms();
        }

        public ApiFunctionResponseVm AddUpdatePaymentTerm(PaymentTermMasterVm PaymentTerm)
        {
            var data = new PaymentTermDataFn();
            return data.AddUpdatePaymentTerm(PaymentTerm);
        }
        public ApiFunctionResponseVm DeletePaymentTerm(PaymentTermMasterVm PaymentTerm)
        {
            var data = new PaymentTermDataFn();
            return data.DeletePaymentTerm(PaymentTerm);
        }
    }
}
