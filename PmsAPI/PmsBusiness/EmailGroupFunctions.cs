﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsData.ViewModel;

namespace PmsBusiness
{
    public class EmailGroupFunctions
    {
        public List<EmailGroupTableVm> GetAllEmailGroups()
        {
            var data = new EmailGroupDataFn();
            return data.GetAllEmailGroups();
        }

        public ApiFunctionResponseVm AddEmailGroup(EmailGroupTableVm EmailGroup)
        {
            var data = new EmailGroupDataFn();
            return data.AddEmailGroup(EmailGroup);
        }
        public ApiFunctionResponseVm DeleteEmailGroup(EmailGroupTableVm EmailGroup)
        {
            var data = new EmailGroupDataFn();
            return data.DeleteEmailGroup(EmailGroup);
        }
    }
}
