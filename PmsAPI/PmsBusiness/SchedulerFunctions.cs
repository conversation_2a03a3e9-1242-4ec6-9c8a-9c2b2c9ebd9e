﻿using PmsEntity.ViewModel;
using PmsCommon;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class SchedulerFunctions
    {
        public GlobalDataEntity GlobalData;
        public SchedulerFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public void SendProductionPlanningReportData()
        {
            var data = new SchedularDataFn(GlobalData);
            data.SendProductionPlanningReportData();
        }
        public void SendYieldReportData()
        {
            var data = new SchedularDataFn(GlobalData);
            data.SendYieldReportData();
        }
    }
}
