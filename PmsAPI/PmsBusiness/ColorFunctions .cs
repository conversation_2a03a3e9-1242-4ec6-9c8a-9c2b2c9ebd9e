﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class ColorFunctions
    {
        public List<ColorMasterVm> GetAllColors()
        {
            var data = new ColorDataFn();
            return data.GetAllColors();
        }

        public ApiFunctionResponseVm AddUpdateColor(ColorMasterVm Color)
        {
            var data = new ColorDataFn();
            return data.AddUpdateColor(Color);
        }
        public ApiFunctionResponseVm DeleteColor(ColorMasterVm Color)
        {
            var data = new ColorDataFn();
            return data.DeleteColor(Color);
        }
    }
}
