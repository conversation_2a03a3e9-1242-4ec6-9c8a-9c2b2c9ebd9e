﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class DeptFunctions
    {
        public List<DeptMasterVm> GetAllDepts()
        {
            var data = new DeptDataFn();
            return data.GetAllDepts();
        }

        public ApiFunctionResponseVm AddUpdateDept(DeptMasterVm Dept)
        {
            var data = new DeptDataFn();
            return data.AddUpdateDept(Dept);
        }
    }
}
