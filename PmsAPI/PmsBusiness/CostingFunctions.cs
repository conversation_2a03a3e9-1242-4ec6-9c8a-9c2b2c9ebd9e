﻿using PmsEntity.ViewModel;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsData.DataFn;
using PmsCommon;

namespace PmsBusiness
{
    public class CostingFunctions
    {
        public GlobalDataEntity GlobalData;
        public CostingFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ApiFunctionResponseVm GetFilteredCostingList(SaleOrderCostingRequestFilter filter)
        {
            var data = new CostingDataFn(GlobalData);
            return data.GetFilteredCostingList(filter);
        }

        public ApiFunctionResponseVm AddUpdateCosting(SaleOrderCostingTableVm soc)
        {
            var data = new CostingDataFn(GlobalData);
            return data.AddUpdateCosting(soc);
        }
    }
}
