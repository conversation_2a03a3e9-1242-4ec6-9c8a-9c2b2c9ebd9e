﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class ProformaInvoiceFunctions
    {
        public List<ProformaInvoiceTableVm> GetAllProformaInvoice()
        {
            var data = new ProformaInvoiceDataFn();
            return data.GetAllProformaInvoice();
        }

        public ApiFunctionResponseVm AddProformaInvoice(ProformaInvoiceTableVm mix)
        {
            var data = new ProformaInvoiceDataFn();
            return data.AddProformaInvoice(mix);
        }
        public ApiFunctionResponseVm UpdateProformaInvoice(ProformaInvoiceTableVm mix)
        {
            var data = new ProformaInvoiceDataFn();
            return data.UpdateProformaInvoice(mix);
        }
        public List<ProformaInvoiceTableCustomerVm> GetProformaInvoicesByCustomerId(long customerId)
        {
            var data = new ProformaInvoiceDataFn();
            return data.GetProformaInvoicesByCustomerId(customerId);
        }
        public ProformaInvoiceTableVm GetProformaInvoiceById(long proformaInvoiceId)
        {
            var data = new ProformaInvoiceDataFn();
            return data.GetProformaInvoiceById(proformaInvoiceId);
        }
        public List<ProformaInvoiceTableVm> GetAllProformaInvoiceByFilter(ProformaInvoiceRequestFilter filters)
        {
            var data = new ProformaInvoiceDataFn();
            return data.GetAllProformaInvoiceByFilter(filters);
        }
    }
}
