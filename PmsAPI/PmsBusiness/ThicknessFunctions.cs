﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class ThicknessFunctions
    {
        public List<ThicknessMasterVm> GetAllThicknessData()
        {
            var data = new ThicknessDataFn();
            return data.GetAllThicknessData();
        }

        public ApiFunctionResponseVm AddUpdateThickness(ThicknessMasterVm br)
        {
            var data = new ThicknessDataFn();
            return data.AddUpdateThickness(br);
        }
        public ApiFunctionResponseVm DeleteThickness(ThicknessMasterVm br)
        {
            var data = new ThicknessDataFn();
            return data.DeleteThickness(br);
        }
    }
}
