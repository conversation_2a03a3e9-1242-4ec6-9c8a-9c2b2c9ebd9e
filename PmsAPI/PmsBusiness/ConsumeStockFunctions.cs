﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsCommon;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace PmsBusiness
{
    public class ConsumeStockFunctions
    {
        public GlobalDataEntity GlobalData;
        public ConsumeStockFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<ConsumeStockProductMasterVm> GetAllConsumeStockProducts()
        {
            var data = new ConsumeStockProductDataFn(GlobalData);
            return data.GetAllConsumeStockProducts();
        }
        public List<ConsumeStockProductMasterVm> GetConsumptionbyStockProductId(long stockProductId)
        {
            var data = new ConsumeStockProductDataFn(GlobalData);
            return data.GetConsumptionbyStockProductId(stockProductId);
        }
        public List<PendingConsumptionOrdersResponseVm> GetAllPendingConsumptionOrderswithFilter(PendingConsumptionOrdersRequestVm filters)
        {
            var data = new ConsumeStockProductDataFn(GlobalData);
            return data.GetAllPendingConsumptionOrderswithFilter(filters);
        }
        public ApiFunctionResponseVm AddUpdateConsumeStockProduct(List<ConsumeStockProductMasterVm> br)
        {
            var data = new ConsumeStockProductDataFn(GlobalData);
            return data.AddUpdateConsumeStockProduct(br);
        }

        public List<IssueSaleOrderProductsStockVm> GetAllSaleOrderProductsToConsume(long saleorderid)
        {
            var data = new ConsumeStockProductDataFn(GlobalData);
            return data.GetAllSaleOrderProductsToConsume(saleorderid);
        }
        public List<ConsumedSaleOrderProductsStockVm> GetAllSaleOrderConsumedProducts(long saleorderid)
        {
            var data = new ConsumeStockProductDataFn(GlobalData);
            return data.GetAllSaleOrderConsumedProducts(saleorderid);
        }
        public List<IssueSaleOrderProductsStockVm> GetAllProductsToConsumeByStoreID(long storeid)
        {
            var data = new ConsumeStockProductDataFn(GlobalData);
            return data.GetAllProductsToConsumeByStoreID(storeid);
        }
    }
}
