﻿using System;
using PmsCommon;
using PmsData.DataFn;
using PmsEntity.ViewModel;
using System.Collections.Generic;

namespace PmsBusiness
{
    public class OverHeadFunction
    {
        public GlobalDataEntity GlobalData;
        public OverHeadFunction(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<OverheadColumnMasterListVm> GetOverHeadColoumnList()
        {
            var data = new OverHeadDataFn(GlobalData);
            return data.GetOverHeadColoumnList();
        }

        public ApiFunctionResponseVm AddOverHeadColumn(OverheadColumnMasterVm overHeadMasterVm)
        {
            var data = new OverHeadDataFn(GlobalData);
            return data.AddOverHeadColumn(overHeadMasterVm);
        }

        public ApiFunctionResponseVm AddOverHeadCost(List<OverheadCostVm> overHeadCostVms)
        {
            var data = new OverHeadDataFn(GlobalData);
            return data.AddOverHeadCost(overHeadCostVms);
        }

        // public CustomerOverheadCostsVm GetCustomerOverheadCosts(long customerId)
        // {
        //     var data = new OverHeadDataFn(GlobalData);
        //     return data.GetCustomerOverheadCosts(customerId);
        // }
        public List<OverheadCostingTableVm> GetAllOverheadCost()
        {
            var data = new OverHeadDataFn(GlobalData);
            return data.GetAllOverheadCost();
        }
        public ApiFunctionResponseVm AddUpdateOverheadCost(OverheadCostingTableVm item)
        {
            var data = new OverHeadDataFn(GlobalData);
            return data.AddUpdateOverheadCost(item);
        }
    }
}

