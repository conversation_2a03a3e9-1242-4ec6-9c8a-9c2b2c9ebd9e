﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class PrintFunctions
    {
        public List<PrintMasterVm> GetAllPrints()
        {
            var data = new PrintDataFn();
            return data.GetAllPrints();
        }

        public ApiFunctionResponseVm AddUpdatePrint(PrintMasterVm Print)
        {
            var data = new PrintDataFn();
            return data.AddUpdatePrint(Print);
        }
        public ApiFunctionResponseVm DeletePrint(PrintMasterVm Print)
        {
            var data = new PrintDataFn();
            return data.DeletePrint(Print);
        }
    }
}
