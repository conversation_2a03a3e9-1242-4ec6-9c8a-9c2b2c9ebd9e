﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using PmsCommon;

namespace PmsBusiness
{
    public class FactoryWorkersFunctions
    {
        public GlobalDataEntity GlobalData;
        public FactoryWorkersFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<FactoryWorkersMasterVm> GetAllFactoryWorkersWithFilters(FactoryWorkersRequestVm filters)
        {
            var data = new FactoryWorkersDataFn(GlobalData);
            return data.GetAllFactoryWorkersWithFilters(filters);
        }

        public ApiFunctionResponseVm AddUpdateFactoryWorker(FactoryWorkersMasterVm product)
        {
            var data = new FactoryWorkersDataFn(GlobalData);
            return data.AddUpdateFactoryWorker(product);
        }

        public ApiFunctionResponseVm DeleteFactoryWorker(long itemsid)
        {
            var data = new FactoryWorkersDataFn(GlobalData);
            return data.DeleteFactoryWorker(itemsid);
        }
    }
}
