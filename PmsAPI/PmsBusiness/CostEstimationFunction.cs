﻿using System;
using PmsCommon;
using PmsData.DataFn;
using PmsEntity.ViewModel;
using System.Collections.Generic;

namespace PmsBusiness
{
	public class CostEstimationFunction
	{
        public GlobalDataEntity GlobalData;
        public CostEstimationFunction(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public ApiFunctionResponseVm AddEstimationOrder(EstimationOrderTableVm estimationorder)
        {
            var data = new CostEstimationDataFn(GlobalData);
            return data.AddEstimationOrder(estimationorder);
        }

        
        public ApiFunctionResponseVm DisableEstimationOrderById(long estimationorderid)
        {
            var data = new CostEstimationDataFn(GlobalData);
            return data.DisableEstimationOrderById(estimationorderid);
        }

        public List<EstimationOrderTableVm> GetEstimationListByFilter(EstimationFilterVm filters)
        {
            var data = new CostEstimationDataFn(GlobalData);
            return data.GetEstimationListByFilter(filters);
        }
        public EstimationOrderTableVm GetEstimationOrderById(long estimationorderid)
        {
            var data = new CostEstimationDataFn(GlobalData);
            return data.GetEstimationOrderById(estimationorderid);
        }

        public ApiFunctionResponseVm UpdateEstimationOrderStatus(EstimationStatusVm estimationStatus)
        {
            var data = new CostEstimationDataFn(GlobalData);
            return data.UpdateEstimationOrderStatus(estimationStatus);
        }

    }
}

