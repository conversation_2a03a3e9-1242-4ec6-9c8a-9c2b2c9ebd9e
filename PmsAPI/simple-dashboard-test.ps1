# Simple Dashboard API Test
Write-Host "🧪 TESTING DASHBOARD API VALUES" -ForegroundColor Cyan

$ReportingApiUrl = "http://localhost:7073/api/report/gatedashboard"

$AllDataPayload = @{
    DateFilterType = "all"
} | ConvertTo-Json

Write-Host "📡 Testing Dashboard API..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri $ReportingApiUrl -Method POST -Body $AllDataPayload -ContentType "application/json" -TimeoutSec 30
    
    Write-Host "✅ API Response received" -ForegroundColor Green
    
    # Display current tile values
    Write-Host ""
    Write-Host "📊 CURRENT DASHBOARD VALUES" -ForegroundColor Cyan
    Write-Host "===========================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Gate Operations:" -ForegroundColor White
    Write-Host "  Pending Gate-Out: $($response.PendingGateOutCount)" -ForegroundColor White
    Write-Host "  Pending Gate Pass: $($response.PendingGatePassCount)" -ForegroundColor White
    Write-Host "  Invoices Without PO: $($response.InvoicesWithoutPOCount)" -ForegroundColor White
    Write-Host ""
    Write-Host "Purchase Orders:" -ForegroundColor White
    Write-Host "  Active POs: $($response.ActivePOCount)" -ForegroundColor White
    Write-Host "  Revised POs: $($response.RevisedPOCount)" -ForegroundColor White
    Write-Host "  Delayed Delivery POs: $($response.DelayedDeliveryPOCount)" -ForegroundColor White
    Write-Host "  Delayed Payment POs: $($response.DelayedPaymentPOCount)" -ForegroundColor White
    Write-Host ""
    Write-Host "Product Management:" -ForegroundColor White
    Write-Host "  Delayed Demands: $($response.DelayedDemandsCount)" -ForegroundColor White
    Write-Host "  Products Below Min Quantity: $($response.ProductsBelowMinQuantityCount)" -ForegroundColor White
    Write-Host "  Pending Issue Requests: $($response.PendingIssueRequestsCount)" -ForegroundColor White
    Write-Host "  Total Products: $($response.TotalProductsCount)" -ForegroundColor White
    Write-Host "  Low Stock Products: $($response.LowStockProductsCount)" -ForegroundColor White
    Write-Host "  Out of Stock Products: $($response.OutOfStockProductsCount)" -ForegroundColor White
    Write-Host ""
    
    # Focus on the problematic tiles
    Write-Host "🎯 FOCUS: PROBLEMATIC TILES" -ForegroundColor Red
    Write-Host "===========================" -ForegroundColor Red
    Write-Host "Low Stock Products (Dashboard): $($response.LowStockProductsCount)" -ForegroundColor Yellow
    Write-Host "Out of Stock Products (Dashboard): $($response.OutOfStockProductsCount)" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "✅ Dashboard API is working correctly" -ForegroundColor Green
}
catch {
    Write-Host "❌ API test failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red

    # Check if services are running
    Write-Host ""
    Write-Host "💡 Make sure PmsReportingAPI is running:" -ForegroundColor Yellow
    Write-Host "   cd pms-backend-api\PmsAPI\PmsReportingAPI" -ForegroundColor White
    Write-Host "   func start --port 7072" -ForegroundColor White
}
