CREATE TABLE [dbo].[SaleOrderTimelineTable] (
    [SaleOrderId] BIGINT       NOT NULL,
    [Status]      INT          NOT NULL,
    [AddedDate]   DATETIME     NOT NULL,
    [AddedBy]     VARCHAR (50) NULL,
    [WorkPlanId]  BIGINT       NULL
);
GO

ALTER TABLE [dbo].[SaleOrderTimelineTable]
    ADD CONSTRAINT [PK_SaleOrderTimelineTable] PRIMARY KEY CLUSTERED ([SaleOrderId] ASC, [Status] ASC);
GO


CREATE NONCLUSTERED INDEX [IX_SaleOrderTimelineTables_Composite]
    ON [dbo].[SaleOrderTimelineTable]([SaleOrderId] ASC, [Status] ASC)
    INCLUDE([AddedDate], [AddedBy]);
GO

