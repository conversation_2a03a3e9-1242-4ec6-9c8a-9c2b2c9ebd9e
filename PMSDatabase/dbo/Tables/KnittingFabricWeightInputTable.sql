CREATE TABLE [dbo].[KnittingFabricWeightInputTable] (
    [KnittingFabricWeightInputId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [ProductId]                   BIGINT          NOT NULL,
    [Weight]                      DECIMAL (18, 2) NOT NULL,
    [Unit]                        VARCHAR (50)    NULL,
    [IsStockAccepted]             BIT             NULL,
    [POId]                        BIGINT          NULL,
    [AddedBy]                     VARCHAR (50)    NULL,
    [AddedDate]                   DATETIME        NULL,
    [Disabled]                    BIT             NULL,
    [DisabledBy]                  VARCHAR (50)    NULL,
    [DisabledDate]                DATETIME        NULL,
    [BambooRollWeightInKgs]       DECIMAL (18, 2) NULL
);
GO

ALTER TABLE [dbo].[KnittingFabricWeightInputTable]
    ADD CONSTRAINT [PK_KnittingFabricWeightInputTable] PRIMARY KEY CLUSTERED ([KnittingFabricWeightInputId] ASC);
GO


CREATE NONCLUSTERED INDEX [IX_KnittingFabricWeightInputTables_Composite]
    ON [dbo].[KnittingFabricWeightInputTable]([ProductId] ASC, [IsStockAccepted] ASC, [Disabled] ASC)
    INCLUDE([Weight], [Unit], [POId]);
GO

