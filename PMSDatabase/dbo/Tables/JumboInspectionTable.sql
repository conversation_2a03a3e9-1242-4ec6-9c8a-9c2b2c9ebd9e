CREATE TABLE [dbo].[JumboInspectionTable] (
    [JumboInspectionId]     BIGINT          IDENTITY (1, 1) NOT NULL,
    [WorkPlanJumboMasterId] BIGINT          NULL,
    [Grade]                 VARCHAR (50)    NULL,
    [Quantity]              DECIMAL (18, 2) NULL,
    [Weight]                DECIMAL (18, 2) NULL,
    [Code]                  VARCHAR (50)    NULL,
    [Unit]                  VARCHAR (50)    NULL,
    [AddedBy]               VARCHAR (50)    NULL,
    [AddedDate]             DATETIME        NULL,
    [DispatchedQuantity]    DECIMAL (18, 2) NULL,
    [RollType]              VARCHAR (50)    NULL,
    [DispatchStatus]        VARCHAR (50)    NULL,
    [InspectedBy]           VARCHAR (100)   NULL,
    [JumboDispatchId]       BIGINT          NULL,
    [StockId]               BIGINT          NULL,
);
GO

ALTER TABLE [dbo].[JumboInspectionTable]
    ADD CONSTRAINT [PK_JumboInspectionTable] PRIMARY KEY CLUSTERED ([JumboInspectionId] ASC);
GO


CREATE NONCLUSTERED INDEX [NCI_JumboInspectionTable_AddedDate]
    ON [dbo].[JumboInspectionTable]([AddedDate] ASC);
GO


CREATE NONCLUSTERED INDEX [NCI_JumboInspectionTable_Code]
    ON [dbo].[JumboInspectionTable]([Code] ASC);
GO

