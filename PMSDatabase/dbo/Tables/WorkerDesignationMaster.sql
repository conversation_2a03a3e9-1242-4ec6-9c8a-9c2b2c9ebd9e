CREATE TABLE [dbo].[WorkerDesignationMaster] (
    [DesignationId] BIGINT       IDENTITY (1, 1) NOT NULL,
    [Name]          VARCHAR (50) NULL,
    [ShortName]     VARCHAR (20) NULL,
    [AddedBy]       VARCHAR (50) NULL,
    [AddedDate]     D<PERSON><PERSON>IME     NULL,
    [Disabled]      BIT          NULL,
    [DisabledBy]    VARCHAR (50) NULL,
    [DisabledDate]  DATETIME     NULL
);
GO

ALTER TABLE [dbo].[WorkerDesignationMaster]
    ADD CONSTRAINT [PK_WorkerDesignationMaster] PRIMARY KEY CLUSTERED ([DesignationId] ASC);
GO


CREATE NONCLUSTERED INDEX [IX_WorkerDesignationMasters_Composite]
    ON [dbo].[WorkerDesignationMaster]([DesignationId] ASC)
    INCLUDE([Name]);
GO

