-- Add Return Stock fields to StockMaster table
ALTER TABLE [dbo].[StockMaster]
ADD [IsReturnStock] BIT DEFAULT 0,
    [OriginalSaleOrderId] BIGINT NULL,
    [OriginalDispatchId] BIGINT NULL,
    [ReturnReason] VARCHAR(500) NULL,
    [ReturnDate] DATETIME NULL,
    [ReturnedBy] VARCHAR(50) NULL,
    [CustomerId] BIGINT NULL;

-- Add Return Stock fields to InvoiceMaster table
ALTER TABLE [dbo].[InvoiceMaster]
ADD [InvoiceType] VARCHAR(50) DEFAULT 'Purchase',
    [OriginalInvoiceId] BIGINT NULL;

-- Add foreign key constraints
ALTER TABLE [dbo].[StockMaster]
ADD CONSTRAINT [FK_StockMaster_OriginalSaleOrder] 
    FOREIGN KEY ([OriginalSaleOrderId]) REFERENCES [dbo].[SaleOrderTable] ([SaleOrderId]);

ALTER TABLE [dbo].[StockMaster]
ADD CONSTRAINT [FK_StockMaster_Customer] 
    FOREIGN KEY ([CustomerId]) REFERENCES [dbo].[CustomerMaster] ([CustomerId]);

ALTER TABLE [dbo].[InvoiceMaster]
ADD CONSTRAINT [FK_InvoiceMaster_OriginalInvoice] 
    FOREIGN KEY ([OriginalInvoiceId]) REFERENCES [dbo].[InvoiceMaster] ([InvoiceId]);

-- Create index for better performance
CREATE INDEX [IX_StockMaster_IsReturnStock] ON [dbo].[StockMaster] ([IsReturnStock]);
CREATE INDEX [IX_StockMaster_OriginalSaleOrderId] ON [dbo].[StockMaster] ([OriginalSaleOrderId]);
CREATE INDEX [IX_InvoiceMaster_InvoiceType] ON [dbo].[InvoiceMaster] ([InvoiceType]);
