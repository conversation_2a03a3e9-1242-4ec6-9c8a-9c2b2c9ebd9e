CREATE TABLE [dbo].[TransportVehicleTable] (
    [VehicleId]     BIGINT        IDENTITY (1, 1) NOT NULL,
    [TransportId]   BIGINT        NOT NULL,
    [VehicleNumber] VARCHAR (100) NOT NULL,
    [VehicleType]   VARCHAR (100) NULL,
    [Disabled]      BIT           NULL,
    [DisabledBy]    VARCHAR (50)  NULL,
    [DisabledDate]  DATETIME      NULL
);


GO


ALTER TABLE [dbo].[TransportVehicleTable]
    ADD CONSTRAINT [PK_TransportVehicleTable] PRIMARY KEY CLUSTERED ([VehicleId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TransportVehicleTables_Composite]
    ON [dbo].[TransportVehicleTable]([VehicleId] ASC, [TransportId] ASC)
    INCLUDE([VehicleNumber]);
GO

