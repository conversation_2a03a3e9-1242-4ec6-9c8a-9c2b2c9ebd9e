CREATE TABLE [dbo].[LacquerMaster] (
    [LacquerMasterId] BIGINT        IDENTITY (1, 1) NOT NULL,
    [Code]            VARCHAR (50)  NULL,
    [Name]            VARCHAR (500) NULL,
    [Description]     VARCHAR (500) NULL,
    [AddedBy]         VARCHAR (50)  NULL,
    [AddedDate]       DATETIME      NOT NULL,
    [Disabled]        BIT           NULL,
    [DisabledBy]      VARCHAR (50)  NULL,
    [DisabledDate]    DATETIME      NULL
);
GO

ALTER TABLE [dbo].[LacquerMaster]
    ADD CONSTRAINT [PK_LacquerMaster] PRIMARY KEY CLUSTERED ([LacquerMasterId] ASC);
GO


CREATE NONCLUSTERED INDEX [IX_LacquerMasters_Composite]
    ON [dbo].[LacquerMaster]([LacquerMasterId] ASC, [Disabled] ASC)
    INCLUDE([Name], [Code]);
GO

