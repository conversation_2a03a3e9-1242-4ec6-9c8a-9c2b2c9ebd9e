CREATE TABLE [dbo].[ConsumeStockProductMaster] (
    [ConsumeStockProductId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [RackId]                BIGINT          NULL,
    [StoreId]               BIGINT          NULL,
    [ProductId]             BIGINT          NOT NULL,
    [Quantity]              DECIMAL (18, 2) NOT NULL,
    [Unit]                  VARCHAR (50)    NULL,
    [ConsumedDate]          DATETIME        NULL,
    [AddedBy]               VARCHAR (50)    NULL,
    [AddedDate]             DATETIME        NULL,
    [SaleOrderId]           BIGINT          NULL,
    [StockId]               BIGINT          NULL,
    [StockProductId]        BIGINT          NULL,
    [SCQuantity]            DECIMAL (18, 2) NULL,
    [Purpose]               VARCHAR (200)   NULL,
    [IsDamaged]             BIT             NULL,
    [MaterialCategory]      VARCHAR (50)    NULL,
);
GO

ALTER TABLE [dbo].[ConsumeStockProductMaster]
    ADD CONSTRAINT [PK_ConsumeStockProductMaster] PRIMARY KEY CLUSTERED ([ConsumeStockProductId] ASC);
GO


CREATE NONCLUSTERED INDEX [IX_ConsumeStockProductMasters_Composite]
    ON [dbo].[ConsumeStockProductMaster]([SaleOrderId] ASC, [StockProductId] ASC)
    INCLUDE([ProductId], [Unit]);
GO

