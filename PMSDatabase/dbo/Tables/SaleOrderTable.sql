CREATE TABLE [dbo].[SaleOrderTable] (
    [SaleOrderId]                 BIGINT         IDENTITY (1, 1) NOT NULL,
    [SaleOrderType]               VARCHAR (50)   NULL,
    [CustomerId]                  BIGINT         NULL,
    [SaleOrderNumber]             VARCHAR (50)   NULL,
    [SaleOrderDate]               DATETIME       NULL,
    [DeliveryDate]                DATETIME       NULL,
    [AddedBy]                     VARCHAR (50)   NULL,
    [AddedDate]                   DATETIME       NULL,
    [Remarks]                     VARCHAR (1000) NULL,
    [CostingAdded]                BIT            NULL,
    [SaleOrderStatus]             VARCHAR (50)   NULL,
    [CategoryId]                  BIGINT         NULL,
    [WorkPlanStatus]              BIT            NULL,
    [IsRawMaterialIssued]         BIT            NULL,
    [Status]                      INT            NOT NULL,
    [SaleFormulationCodeId]       BIGINT         NULL,
    [ProductionCompletionRemarks] VARCHAR (5000) NULL,
    [IsJumboRequired]             BIT            NULL,
    [ProformaInvoiceId]           BIGINT         NULL,
    [BORNumber]                   VARCHAR (50)   NULL,
    [SaleOrderCode]               VARCHAR (500)  NULL,
    [FinishCode]                  VARCHAR (200)  NULL,
    [HoldBy]                      VARCHAR (50)   NULL,
    [HoldDate]                    DATETIME       NULL,
    [ApprovedBy]                  VARCHAR (50)   NULL,
    [ApprovedDate]                DATETIME       NULL,
    [UpdatedBy]                   VARCHAR (500)  NULL,
    [UpdatedDate]                 DATETIME       NULL
);
GO

ALTER TABLE [dbo].[SaleOrderTable]
    ADD CONSTRAINT [PK_SaleOrderTable] PRIMARY KEY CLUSTERED ([SaleOrderId] ASC);
GO


CREATE NONCLUSTERED INDEX [IX_SaleOrderTables_Composite_1]
    ON [dbo].[SaleOrderTable]([Status] ASC, [CustomerId] ASC, [SaleOrderId] ASC)
    INCLUDE([DeliveryDate]);
GO


CREATE NONCLUSTERED INDEX [IX_SaleOrderTables_Composite_2]
    ON [dbo].[SaleOrderTable]([CustomerId] ASC, [DeliveryDate] ASC)
    INCLUDE([Status], [SaleOrderId]);
GO


CREATE NONCLUSTERED INDEX [NCI_SaleOrderTable_AddedDate]
    ON [dbo].[SaleOrderTable]([AddedDate] ASC);
GO


CREATE NONCLUSTERED INDEX [NCI_SaleOrderTable_CustomerId]
    ON [dbo].[SaleOrderTable]([CustomerId] ASC);
GO


CREATE NONCLUSTERED INDEX [NCI_SaleOrderTable_SaleFormulationCodeId]
    ON [dbo].[SaleOrderTable]([SaleFormulationCodeId] ASC);
GO


CREATE NONCLUSTERED INDEX [NCI_SaleOrderTable_SaleOrderNumber]
    ON [dbo].[SaleOrderTable]([SaleOrderNumber] ASC);
GO


CREATE NONCLUSTERED INDEX [NCI_SaleOrderTable_Status]
    ON [dbo].[SaleOrderTable]([Status] ASC);
GO

